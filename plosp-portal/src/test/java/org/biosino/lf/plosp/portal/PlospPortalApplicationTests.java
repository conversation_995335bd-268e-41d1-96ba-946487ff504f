package org.biosino.lf.plosp.portal;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * PLOSP Portal Application Tests
 * Basic integration tests for the portal application
 *
 * <AUTHOR> Portal Team
 */
@SpringBootTest
@ActiveProfiles("test")
class PlospPortalApplicationTests {

    @Test
    void contextLoads() {
        // This test verifies that the Spring application context loads successfully
        // If this test passes, it means all the basic configuration is correct
    }
}
