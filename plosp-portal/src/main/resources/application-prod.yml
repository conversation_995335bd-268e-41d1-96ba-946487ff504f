# 生产环境配置
server:
  # 服务器的HTTP端口
  port: 8082
  servlet:
    # 应用的访问路径
    context-path: /
  # 生产环境优化配置
  tomcat:
    # 最大连接数
    max-connections: 8000
    # 最大等待队列长度
    accept-count: 1000
    threads:
      # 最大工作线程数
      max: 800
      # 最小工作线程数
      min-spare: 100

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: org.postgresql.Driver
    # 生产环境数据库连接 (请根据实际环境修改)
    url: **********************************************************************************************************************************************************************
    username: ${DB_USERNAME:plosp_portal}
    password: ${DB_PASSWORD:your_secure_password}
    druid:
      # 生产环境连接池配置
      initialSize: 10
      minIdle: 20
      maxActive: 100
      maxWait: 60000
      connectTimeout: 30000
      socketTimeout: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      validationQuery: SELECT version()
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: false  # 生产环境关闭
      statViewServlet:
        enabled: false  # 生产环境关闭
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 2000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  data:
    redis:
      # 生产环境Redis配置 (请根据实际环境修改)
      host: ${REDIS_HOST:your-prod-redis-host}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DATABASE:13}
      password: ${REDIS_PASSWORD:your_redis_password}
      timeout: 10s
      lettuce:
        pool:
          min-idle: 5
          max-idle: 20
          max-active: 50
          max-wait: -1ms
  # 邮件配置 (生产环境)
  mail:
    host: ${MAIL_HOST:smtp.your-domain.com}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your_mail_password}
    port: ${MAIL_PORT:587}
    properties.mail.smtp:
      auth: true
      starttls:
        enable: true
  # Elasticsearch配置 (使用Spring Data Elasticsearch)
  elasticsearch:
    uris: http://************:30161
    connection-timeout: 30s
    socket-timeout: 300s  # 增加到5分钟，支持大数据量删除操作
  # Spring AI配置
  ai:
    embedding:
      transformer:
        enabled: false
        onnx:
          model-uri: file:D:/model/all-MiniLM-L6-v2/model.onnx
          model-output-name: token_embeddings
        tokenizer:
          uri: file:D:/model/all-MiniLM-L6-v2/tokenizer.json
          options:
            maxLength: 512
  # 定时任务线程池配置
  task:
    scheduling:
      pool:
        size: 2                      # 定时任务线程池大小
      thread-name-prefix: "es-scheduled-"

# 向量化服务配置
embedding:
  service:
    url: https://dev.biosino.org/zj-knowledge-api/text_embedding
    api-key: sk-8DjtyDqlvKwy1915krV74Oac5-mcWvEt
    timeout: 15000          # 请求超时时间（毫秒）- 减少到15秒
    retry:
      maxAttempts: 2        # 最大重试次数 - 减少到2次
      baseDelay: 500        # 基础延迟时间（毫秒）- 减少到500ms
      maxDelay: 3000        # 最大延迟时间（毫秒）- 减少到3秒

es:
  batch:
    size: 1000  # 每批处理的数据量
  # 线程池配置
  thread:
    pool:
      core: 2      # 核心线程数
      max: 15       # 最大线程数
      queue: 100   # 队列容量
  # 向量配置
  vector:
    enabled: true   # 是否启用向量生成
    dimension: 1536 # 向量维度（确认为1536）

  # 启动配置
  startup:
    fullRefresh: true  # 启动时是否执行全量刷新
  # 定时任务配置
  scheduled:
    enabled: true                    # 是否启用定时任务
    cron: "0 0 2 * * ?"             # 每天凌晨2点执行
    incremental-hours: 24           # 增量更新时间范围（小时）
# 生产环境日志配置
logging:
  level:
    org.biosino.lf.plosp.portal: info
    org.biosino.lf.pds: info
    org.springframework.security: warn
    org.springframework.web: warn
    org.springframework: warn
    root: warn
  file:
    name: /var/log/plosp-portal/plosp-portal.log
    max-size: 100MB
    max-history: 30
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# 应用配置
app:
  # 生产环境文件路径
  profile: ${APP_PROFILE_PATH:/opt/plosp-portal/uploadPath}
  data-home: ${APP_DATA_HOME:/opt/plosp-portal/data}
  # estoken
  es-token: 3c8661430cdd56d329af2914b93bdc07tzrG6kLxvMOV
  pds-service-url: https://idc.biosino.org/pds-admin
  article-transmit-token: plospApiToken-b8a3188b600383A1a079Cc45F5899afbA7tkXuvU

# 生产环境token配置
token:
  # 生产环境使用环境变量配置密钥
  secret: ${JWT_SECRET:your-super-secure-jwt-secret-key-for-production}
  # 生产环境令牌有效期（单位分钟）
  expireTime: ${JWT_EXPIRE_TIME:480}

# 生产环境安全配置
security:
  # 启用安全头
  headers:
    frame-options: DENY
    content-type-options: nosniff
    xss-protection: 1; mode=block
    referrer-policy: strict-origin-when-cross-origin

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: never
  server:
    port: 8083  # 管理端口与应用端口分离

