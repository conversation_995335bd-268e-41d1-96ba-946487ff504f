# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8082
  servlet:
    # 应用的访问路径
    context-path: /

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: org.postgresql.Driver
    url: **************************************************************************************************************************************************************
    username: postgres
    password: Biosino+2025
    druid:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT version()
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        # allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: portal
        login-password: portal123
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  data:
    redis:
      # 地址
      host: ************
      # 端口，默认为6379
      port: 32641
      # 数据库索引 (使用不同的数据库索引以避免与admin冲突)
      database: 3
      # 密码
      password:
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
  # Elasticsearch配置 (使用Spring Data Elasticsearch)
  elasticsearch:
    uris: http://************:30161
    connection-timeout: 30s
    socket-timeout: 300s  # 增加到5分钟，支持大数据量删除操作
  # Spring AI配置
  ai:
    embedding:
      transformer:
        enabled: false
        onnx:
          model-uri: file:D:/model/all-MiniLM-L6-v2/model.onnx
          model-output-name: token_embeddings
        tokenizer:
          uri: file:D:/model/all-MiniLM-L6-v2/tokenizer.json
          options:
            maxLength: 512
  # 定时任务线程池配置
  task:
    scheduling:
      pool:
        size: 2                      # 定时任务线程池大小
      thread-name-prefix: "es-scheduled-"

# 向量化服务配置
embedding:
  service:
    url: https://dev.biosino.org/zj-knowledge-api/text_embedding
    api-key: sk-8DjtyDqlvKwy1915krV74Oac5-mcWvEt
    timeout: 15000          # 请求超时时间（毫秒）- 减少到15秒
    retry:
      maxAttempts: 2        # 最大重试次数 - 减少到2次
      baseDelay: 500        # 基础延迟时间（毫秒）- 减少到500ms
      maxDelay: 3000        # 最大延迟时间（毫秒）- 减少到3秒

es:
  batch:
    size: 1000  # 每批处理的数据量
  # 线程池配置
  thread:
    pool:
      core: 2      # 核心线程数
      max: 15       # 最大线程数
      queue: 100   # 队列容量
  # 向量配置
  vector:
    enabled: true   # 是否启用向量生成
    dimension: 1536 # 向量维度（确认为1536）

  # 启动配置
  startup:
    fullRefresh: true  # 启动时是否执行全量刷新
  # 定时任务配置
  scheduled:
    enabled: true                    # 是否启用定时任务
    cron: "0 0 2 * * ?"             # 每天凌晨2点执行
    incremental-hours: 24           # 增量更新时间范围（小时）

# 日志配置
logging:
  level:
    org.biosino.lf.plosp.portal: debug
    org.biosino.lf.pds: debug
    org.springframework.security: info
    org.springframework.web: info
    org.springframework: info
  file:
    name: logs/plosp-portal-dev.log

# 应用配置
app:
  # 公开文件路径(在ResourcesConfig已配置为资源路径，可通过url直接访问)
  profile: D:/tmp/plosp-portal/uploadPath
  # 数据文件路径
  data-home: D:/tmp/plosp-portal/data
  # estoken
  es-token: 3c8661430cdd56d329af2914b93bdc07tzrG6kLxvMOV
  pds-service-url: http://localhost:8081/pds-admin
#  pds-service-url: https://dev.biosino.org/keep-wh/pds-admin
  article-transmit-token: plospApiToken-b8a3188b600383A1a079Cc45F5899afbA7tkXuvU

# 开发环境特定的token配置
token:
  # 开发环境令牌有效期更长（单位分钟）
  expireTime: 1440

deep-seek:
  api: sk-d34831d3919e4689b1a8c6fffc341f22
