<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.plosp.portal.web.mapper.FavoriteDocMapper">

    <resultMap id="FavoriteDocResult" type="org.biosino.lf.plosp.portal.web.vo.FavoriteDocVO">
        <id property="id" column="id"/>
        <result property="source" column="source"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="title" column="title"/>
        <result property="authors" column="author"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="description" column="abstract"/>
        <result property="journal" column="journal_id"/>
        <result property="year" column="year"/>
        <result property="volume" column="volume"/>
        <result property="issue" column="issue"/>
        <result property="page" column="page"/>
        <result property="doi" column="doi"/>
        <result property="pmid" column="pmid"/>
        <result property="collectTime" column="create_time"/>
        <result property="folderId" column="folder_id"/>
    </resultMap>

    <select id="selectFavoriteDocsWithDetail" parameterType="org.biosino.lf.plosp.portal.web.dto.FavoriteDocDto"
            resultMap="FavoriteDocResult">
        SELECT
        f.id,
        d.source,
        d.title,
        d.author,
        d.abstract,
        d.journal_id,
        d.year,
        d.volume,
        d.issue,
        d.page,
        d.doi,
        d.pmid,
        d.hit_num,
        d.download,
        f.create_time,
        f.folder_id
        FROM tb_favorite_doc f
        INNER JOIN tb_dds_article d ON f.doc_id = d.id
        <where>
            <if test="userId != null">
                AND f.user_id = #{userId}
            </if>
            <if test="folderId != null">
                AND f.folder_id = #{folderId}
            </if>
            <if test="docId != null and docId != ''">
                AND f.doc_id = #{docId}
            </if>
            <if test="pmid != null and pmid != ''">
                AND d.pmid = #{pmid}
            </if>
            <if test="pmcId != null and pmcId != ''">
                AND d.pmc_id = #{pmcId}
            </if>
            <if test="customId != null and customId != ''">
                AND d.custom_id = #{customId}
            </if>
            <if test="doi != null and doi != ''">
                AND d.doi = #{doi}
            </if>
            <if test="title != null and title != ''">
                AND d.title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="author != null and author != ''">
                AND array_to_string(d.author, ',') LIKE CONCAT('%', #{author}, '%')
            </if>
            <if test="keyword != null and keyword != ''">
                AND d.keywords LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="publisherYear != null">
                AND d.year >= #{publisherYear}
            </if>
        </where>
        <choose>
            <when test="sortBy == 'hitNum'">
                ORDER BY d.hit_num DESC
            </when>
            <when test="sortBy == 'download'">
                ORDER BY d.download DESC
            </when>
            <otherwise>
                ORDER BY f.create_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>
