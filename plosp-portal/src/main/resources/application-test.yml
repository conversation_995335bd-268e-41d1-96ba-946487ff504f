# 测试环境配置
server:
  # 服务器的HTTP端口
  port: 8082
  servlet:
    # 应用的访问路径
    context-path: /

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: org.postgresql.Driver
    # 测试环境数据库连接 (请根据实际环境修改)
    url: **********************************************************************************************************************************************************************
    username: ${DB_USERNAME:plosp_test}
    password: ${DB_PASSWORD:test_password}
    druid:
      # 测试环境连接池配置
      initialSize: 5
      minIdle: 10
      maxActive: 50
      maxWait: 60000
      connectTimeout: 30000
      socketTimeout: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      validationQuery: SELECT version()
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        allow:
        url-pattern: /druid/*
        login-username: test
        login-password: test123
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  data:
    redis:
      # 测试环境Redis配置
      host: ${REDIS_HOST:test-redis-host}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DATABASE:14}
      password: ${REDIS_PASSWORD:}
      timeout: 10s
      lettuce:
        pool:
          min-idle: 2
          max-idle: 10
          max-active: 20
          max-wait: -1ms
  # 邮件配置 (测试环境)
  mail:
    host: ${MAIL_HOST:smtp.test-domain.com}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:test_mail_password}
    port: ${MAIL_PORT:587}
    properties.mail.smtp:
      auth: true
      starttls:
        enable: true
  # Elasticsearch配置 (使用Spring Data Elasticsearch)
  elasticsearch:
    uris: http://************:30161
    connection-timeout: 30s
    socket-timeout: 300s  # 增加到5分钟，支持大数据量删除操作
  # Spring AI配置
  ai:
    embedding:
      transformer:
        enabled: false
        onnx:
          model-uri: file:D:/model/all-MiniLM-L6-v2/model.onnx
          model-output-name: token_embeddings
        tokenizer:
          uri: file:D:/model/all-MiniLM-L6-v2/tokenizer.json
          options:
            maxLength: 512
  # 定时任务线程池配置
  task:
    scheduling:
      pool:
        size: 2                      # 定时任务线程池大小
      thread-name-prefix: "es-scheduled-"

# 向量化服务配置
embedding:
  service:
    url: https://dev.biosino.org/zj-knowledge-api/text_embedding
    api-key: sk-8DjtyDqlvKwy1915krV74Oac5-mcWvEt
    timeout: 15000          # 请求超时时间（毫秒）- 减少到15秒
    retry:
      maxAttempts: 2        # 最大重试次数 - 减少到2次
      baseDelay: 500        # 基础延迟时间（毫秒）- 减少到500ms
      maxDelay: 3000        # 最大延迟时间（毫秒）- 减少到3秒

es:
  batch:
    size: 1000  # 每批处理的数据量
  # 线程池配置
  thread:
    pool:
      core: 2      # 核心线程数
      max: 15       # 最大线程数
      queue: 100   # 队列容量
  # 向量配置
  vector:
    enabled: true   # 是否启用向量生成
    dimension: 1536 # 向量维度（确认为1536）

  # 启动配置
  startup:
    fullRefresh: true  # 启动时是否执行全量刷新
  # 定时任务配置
  scheduled:
    enabled: true                    # 是否启用定时任务
    cron: "0 0 2 * * ?"             # 每天凌晨2点执行
    incremental-hours: 24

# 测试环境日志配置
logging:
  level:
    org.biosino.lf.plosp.portal: debug
    org.biosino.lf.pds: debug
    org.springframework.security: info
    org.springframework.web: info
    org.springframework: info
  file:
    name: logs/plosp-portal-test.log
    max-size: 50MB
    max-history: 7

# 应用配置
app:
  # 测试环境文件路径
  profile: ${APP_PROFILE_PATH:/tmp/plosp-portal-test/uploadPath}
  data-home: ${APP_DATA_HOME:/tmp/plosp-portal-test/data}
  # estoken
  es-token: 3c8661430cdd56d329af2914b93bdc07tzrG6kLxvMOV
  pds-service-url: https://dev.biosino.org/keep-wh/pds-admin
  article-transmit-token: plospApiToken-b8a3188b600383A1a079Cc45F5899afbA7tkXuvU


# 测试环境token配置
token:
  # 测试环境令牌有效期（单位分钟）
  expireTime: ${JWT_EXPIRE_TIME:720}

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env
  endpoint:
    health:
      show-details: when-authorized

