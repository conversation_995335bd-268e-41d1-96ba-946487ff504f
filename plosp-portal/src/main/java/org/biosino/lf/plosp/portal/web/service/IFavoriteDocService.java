package org.biosino.lf.plosp.portal.web.service;

import org.biosino.lf.plosp.portal.web.vo.FavoriteDocVO;
import org.biosino.lf.plosp.portal.web.dto.FavoriteDocDto;

import java.text.ParseException;
import java.util.List;

/**
 * 个人收藏夹service层接口
 *
 * <AUTHOR>
 */
public interface IFavoriteDocService {

    /**
     * 获取当前用户，当前文件夹的所有文献
     */
    List<FavoriteDocVO> list(FavoriteDocDto favoriteDocDto) throws ParseException;

    /**
     * 添加文献
     */
    void add(FavoriteDocDto favoriteDocDto);

    /**
     * 删除文献，支持批量删除
     */
    void delete(Long[] ids);

    /**
     * 判断当前用户是否收藏了该文献
     */
    boolean isCollect(String docId);
}
