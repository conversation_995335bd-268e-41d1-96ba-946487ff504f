package org.biosino.lf.plosp.portal.web.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.custbean.dto.TransmitListQueryDTO;
import org.biosino.lf.pds.article.custbean.dto.api.ArticleTransmitDTO;
import org.biosino.lf.pds.article.service.ITbUserScoreLogService;
import org.biosino.lf.pds.common.constant.CacheConstants;
import org.biosino.lf.pds.common.core.domain.R;
import org.biosino.lf.pds.common.core.page.PageDomain;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.pds.common.core.redis.RedisCache;
import org.biosino.lf.pds.common.enums.task.ScoreSourceEnum;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.plosp.portal.dto.ArticleTransmitSubmitDTO;
import org.biosino.lf.plosp.portal.utils.PortalSecurityUtils;
import org.biosino.lf.plosp.portal.web.service.ArticleTransmitService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedHashSet;

/**
 * 文献传递服务层实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleTransmitServiceImpl implements ArticleTransmitService {
    private final RedisCache redisCache;
    private final ITbUserScoreLogService tbUserScoreLogService;

    @Value("${app.pds-service-url}")
    private String pdsServiceUrl;

    @Value("${app.article-transmit-token}")
    private String articleTransmitToken;

//    private static final String USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.142 Safari/537.36";

    @Override
    public R<String> articleTransmit(ArticleTransmitSubmitDTO transmitDTO) {
        HttpResponse response = null;
        try {
            // 1. 验证验证码
            final String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + transmitDTO.getUuid();
            final String captcha = redisCache.getCacheObject(verifyKey);

            if (StrUtil.isEmpty(captcha)) {
                return R.fail("验证码已过期，请重新获取");
            }

            if (!captcha.equalsIgnoreCase(transmitDTO.getCaptchaCode())) {
                return R.fail("验证码错误");
            }
            // 验证码使用后删除
            redisCache.deleteObject(verifyKey);

            // 2. 获取当前登录用户信息
            final Long userId = PortalSecurityUtils.getUserId();
            if (userId == null) {
                return R.fail("用户未登录");
            }

            // 3. 构建ArticleTransmitDTO对象
            ArticleTransmitDTO articleTransmitDTO = new ArticleTransmitDTO();
            articleTransmitDTO.setName(transmitDTO.getName());
            articleTransmitDTO.setDescription(transmitDTO.getDescription());
            articleTransmitDTO.setUserId(userId);
            // 文献传递优先级为最大值100
            articleTransmitDTO.setPriority(100);

            // 设置文章ID集合
            final LinkedHashSet<Long> ids = new LinkedHashSet<>();
            ids.add(transmitDTO.getArticleId());
            articleTransmitDTO.setIds(ids);

            articleTransmitDTO.setApiToken(articleTransmitToken);
            // 4. 使用hutool发送POST请求到TaskApiController
            response = HttpRequest.post(pdsServiceUrl + "/pds-api/pub-lds/taskPublish")
                    .timeout(80 * 1000)
                    .body(JSON.toJSONString(articleTransmitDTO))
//                    .header("User-Agent", USER_AGENT, true)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .execute();

            final String bodyStr = response.body();
            if (response.isOk()) {
                final JSONObject jsonObject = JSON.parseObject(bodyStr);
                final String statusStr = jsonObject.getString("status");
                if ("success".equals(statusStr)) {
                    return R.ok("文献传递-获取全文申请提交成功");
                } else {
                    return R.fail("文献传递-获取全文申请提交失败：" + jsonObject.getString("msg"));
                }
            } else {
                return R.fail("文献传递-获取全文申请提交失败：" + bodyStr);
            }

        } catch (Exception e) {
            return R.fail("文献传递-获取全文申请提交失败：" + e.getMessage());
        } finally {
            IoUtil.close(response);
        }
    }

    @Override
    public TableDataInfo transmitList(final TransmitListQueryDTO dto, final long userId, final PageDomain pageDomain) {
        dto.setUserId(userId);
        dto.setPageDomainVal(pageDomain);
        dto.setApiToken(articleTransmitToken);

        HttpResponse response = null;
        try {
            response = HttpRequest.post(pdsServiceUrl + "/pds-api/pub-lds/myTransmitList")
                    .timeout(80 * 1000)
                    .body(JSON.toJSONString(dto))
//                    .header("User-Agent", USER_AGENT, true)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .execute();

            final String bodyStr = response.body();
            if (response.isOk()) {
                final JSONObject jsonObject = JSON.parseObject(bodyStr);
                final String statusStr = jsonObject.getString("status");
                if ("success".equals(statusStr)) {
                    return jsonObject.getObject("data", TableDataInfo.class);
                } else {
                    return null;
                }
            } else {
                throw new ServiceException("文献传递-获取申请列表失败：" + bodyStr);
            }
        } finally {
            IoUtil.close(response);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void downloadArticlePDF(Long docId, Long userId, HttpServletRequest request, HttpServletResponse response) {
        if (userId == null) {
            throw new ServiceException("用户未登录");
        }
        if (docId == null) {
            throw new ServiceException("文献ID不能为空");
        }
        tbUserScoreLogService.downloadArticlePDF(docId, userId, ScoreSourceEnum.download_article_pdf, request, response);
    }

}
