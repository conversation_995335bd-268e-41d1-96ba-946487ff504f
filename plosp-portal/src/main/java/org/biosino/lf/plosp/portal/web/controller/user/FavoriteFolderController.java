package org.biosino.lf.plosp.portal.web.controller.user;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.TbFavoriteFolder;
import org.biosino.lf.pds.article.service.ITbFavoriteFolderService;
import org.biosino.lf.pds.common.core.domain.R;
import org.biosino.lf.plosp.portal.utils.PortalSecurityUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 收藏文件夹controller类
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/user/folder")
public class FavoriteFolderController {

    private final ITbFavoriteFolderService favoriteFolderService;

    /**
     * 获取用户的文件夹
     */
    @GetMapping("/list")
    public R<List<TbFavoriteFolder>> getFavoriteFolder() {
        final Long userId = PortalSecurityUtils.getUserId();
        List<TbFavoriteFolder> tbFavoriteFolders = favoriteFolderService.listByUserId(userId);
        return R.ok(tbFavoriteFolders);
    }

    /**
     * 创建文件夹
     */
    @PostMapping
    public R<String> createFavoriteFolder(@RequestParam String folderName) {
        final Long userId = PortalSecurityUtils.getUserId();
        try {
            favoriteFolderService.createFolder(userId, folderName);
            return R.ok();
        } catch (Exception e) {
            log.error("创建收藏夹失败: userId={}, folderName={}", userId, folderName, e);
            return R.fail("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新文件夹名称
     */
    @PostMapping("/update")
    public R<String> updateFavoriteFolder(@RequestParam Long id,
                                          @RequestParam String folderName) {
        try {
            favoriteFolderService.updateFolder(id, folderName);
            return R.ok();
        } catch (Exception e) {
            log.error("修改收藏夹失败: id={}, folderName={}", id, folderName, e);
            return R.fail("修改失败：" + e.getMessage());
        }
    }

    /**
     * 根据id删除文件夹
     */
    @DeleteMapping("/delete/{id}")
    public R<String> deleteFavoriteFolder(@PathVariable("id") Long id) {
        try {
            favoriteFolderService.deleteFolderWithDoc(id);
            return R.ok();
        } catch (Exception e) {
            log.error("删除收藏夹失败：id={}", id);
            return R.fail("删除失败：" + e.getMessage());
        }
    }
}
