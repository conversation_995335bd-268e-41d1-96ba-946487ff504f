package org.biosino.lf.plosp.portal.common.enums;

/**
 * PLOSP用户积分枚举
 *
 * <AUTHOR>
 */
public enum PlospUserPoints {
    INITIAL_POINTS(0, "初始积分"),
    REGISTER_REWARD(10, "注册奖励"),
    DOWNLOAD_COST(5, "下载消耗"),
    UPLOAD_REWARD(20, "上传奖励");

    private final Integer points;
    private final String description;

    PlospUserPoints(Integer points, String description) {
        this.points = points;
        this.description = description;
    }

    public Integer getPoints() {
        return points;
    }

    public String getDescription() {
        return description;
    }
}
