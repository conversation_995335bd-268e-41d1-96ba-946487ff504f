package org.biosino.lf.plosp.portal.utils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 作者信息处理工具类
 * <AUTHOR>
 */
public class AuthorUtils {
    /**
     * 对作者姓名进行格式化
     * 将 "<PERSON>" 格式化为 "<PERSON>"
     * 将 "<PERSON>" 格式化为 "Watson MJ"
     *
     * @param fullName 完整姓名
     * @return 格式化后的姓名
     */
    public static String formatAuthorName(String fullName) {
        if (fullName == null || fullName.trim().isEmpty()) {
            return fullName;
        }

        String[] parts = fullName.trim().split("\\s+");
        if (parts.length < 2) {
            return fullName;
        }

        StringBuilder formatted = new StringBuilder();

        // 最后一个词作为姓（完全显示）
        String lastName = parts[parts.length - 1];
        formatted.append(lastName).append(" ");

        // 前面的词作为名（只保留首字母）
        for (int i = 0; i < parts.length - 1; i++) {
            String firstName = parts[i];
            if (!firstName.isEmpty()) {
                formatted.append(Character.toUpperCase(firstName.charAt(0)));
            }
        }
        return formatted.toString();
    }

    /**
     * 批量格式化作者姓名列表
     *
     * @param authorNames 作者姓名列表
     * @return 格式化后的作者姓名列表
     */
    public static List<String> formatAuthorNames(List<String> authorNames) {
        if (authorNames == null || authorNames.isEmpty()) {
            return authorNames;
        }

        return authorNames.stream()
                .map(AuthorUtils::formatAuthorName)
                .collect(Collectors.toList());
    }
}
