package org.biosino.lf.plosp.portal.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.Article;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.dto.JournalSummaryDTO;
import org.biosino.lf.pds.article.service.IArticleService;
import org.biosino.lf.pds.article.service.IJournalService;
import org.biosino.lf.pds.common.core.redis.RedisCache;
import org.biosino.lf.pds.common.utils.bean.BeanUtils;
import org.biosino.lf.plosp.portal.es.service.PlospArticleService;
import org.biosino.lf.plosp.portal.utils.AuthorUtils;
import org.biosino.lf.plosp.portal.web.vo.KeywordSummaryVO;
import org.biosino.lf.plosp.portal.web.service.HomeService;
import org.biosino.lf.plosp.portal.web.vo.ArticleSummaryVO;
import org.biosino.lf.plosp.portal.web.vo.HomeStatsVO;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 首页服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HomeServiceImpl implements HomeService {

    private final RedisCache redisCache;
    private final IArticleService articleService;
    private final IJournalService journalService;
    private final PlospArticleService plospArticleService;

    // redis缓存键
    private static final String HOME_STATS_KEY = "home:stats";
    private static final String POPULAR_ARTICLES_KEY = "home:popular_articles";
    private static final String LATEST_ARTICLES_KEY = "home:latest_articles";
    private static final String POPULAR_JOURNALS_KEY = "home:popular_journals";
    private static final String TOP_KEYWORDS_KEY = "home:top_keywords";

    @Override
    public HomeStatsVO getHomeStats() {
        // 先从redis中获取缓存
        HomeStatsVO homeStatsVO = redisCache.getCacheObject(HOME_STATS_KEY);
        if (homeStatsVO != null) {
            return homeStatsVO;
        }
        // 如果redis中没有数据，从数据库中获取
        HomeStatsVO stats = getHomeStatsFromDB();
        // 缓存到Redis
        redisCache.setCacheObject(HOME_STATS_KEY, stats);

        return stats;
    }

    @Override
    public List<ArticleSummaryVO> getPopularArticles() throws ParseException {
        // 先从redis中获取
        List<ArticleSummaryVO> cacheArticle = redisCache.getCacheObject(POPULAR_ARTICLES_KEY);
        if (cacheArticle != null) {
            return cacheArticle;
        }
        // 如果redis中没有数据，从数据库中获取
        List<ArticleSummaryVO> articles = getPopularArticleFromDB();
        redisCache.setCacheObject(POPULAR_ARTICLES_KEY, articles);

        return articles;
    }

    @Override
    public List<ArticleSummaryVO> getLastArticles() throws ParseException {
        // 先从redis中获取数据
        List<ArticleSummaryVO> cacheArticles = redisCache.getCacheObject(LATEST_ARTICLES_KEY);
        if (cacheArticles != null) {
            return cacheArticles;
        }
        List<ArticleSummaryVO> articles = getLastArticlesFromDB();
        redisCache.setCacheObject(LATEST_ARTICLES_KEY, articles);
        return articles;
    }

    @Override
    public List<JournalSummaryDTO> getPopularJournals() {
        // 先从redis中获取缓存
        List<JournalSummaryDTO> cacheJournals = redisCache.getCacheObject(POPULAR_JOURNALS_KEY);
        if (cacheJournals != null) {
            return cacheJournals;
        }
        // 如果redis中没有数据，从数据库中获取
        List<JournalSummaryDTO> journals = articleService.selectPopularJournals();
        redisCache.setCacheObject(POPULAR_JOURNALS_KEY, journals);
        return journals;
    }

    @Override
    public List<KeywordSummaryVO> getTopKeywords() {
        // 先从Redis中获取缓存
        List<KeywordSummaryVO> cacheKeywords = redisCache.getCacheObject(TOP_KEYWORDS_KEY);
        if (cacheKeywords != null) {
            return cacheKeywords;
        }
        // 如果Redis中没有数据，从ES中获取
        List<KeywordSummaryVO> keywords = plospArticleService.getTopKeywords();
        redisCache.setCacheObject(TOP_KEYWORDS_KEY, keywords);
        return keywords;
    }

    @Override
    public void refreshHomeCache() {
        try {
            log.info("开始刷新首页缓存");
            long startTime = System.currentTimeMillis();
            // 并行执行四个缓存更新任务
            CompletableFuture<Void> statsFuture = CompletableFuture.runAsync(() -> {
                try {
                    HomeStatsVO stats = getHomeStatsFromDB();
                    redisCache.setCacheObject(HOME_STATS_KEY, stats);
                    log.debug("统计数据缓存更新完成");
                } catch (Exception e) {
                    log.error("统计数据缓存更新失败", e);
                }
            });

            CompletableFuture<Void> popularFuture = CompletableFuture.runAsync(() -> {
                try {
                    List<ArticleSummaryVO> popularArticles = getPopularArticleFromDB();
                    redisCache.setCacheObject(POPULAR_ARTICLES_KEY, popularArticles);
                    log.debug("最受欢迎文献刷新成功");
                } catch (Exception e) {
                    log.error("最受欢迎文献刷新成功", e);
                }
            });

            CompletableFuture<Void> lastFuture = CompletableFuture.runAsync(() -> {
                try {
                    List<ArticleSummaryVO> lastArticlesFromDB = getLastArticlesFromDB();
                    redisCache.setCacheObject(LATEST_ARTICLES_KEY, lastArticlesFromDB);
                    log.debug("最新文献刷新成功");
                } catch (Exception e) {
                    log.error("最新文献刷新失败", e);
                }
            });

            CompletableFuture<Void> journalsFuture = CompletableFuture.runAsync(() -> {
                try {
                    List<JournalSummaryDTO> popularJournals = articleService.selectPopularJournals();
                    redisCache.setCacheObject(POPULAR_JOURNALS_KEY, popularJournals);
                    log.debug("最受欢迎期刊刷新成功");
                } catch (Exception e) {
                    log.error("最受欢迎期刊刷新失败", e);
                }
            });

            CompletableFuture<Void> keywordsFuture = CompletableFuture.runAsync(() -> {
                try {
                    List<KeywordSummaryVO> topKeywords = plospArticleService.getTopKeywords();
                    redisCache.setCacheObject(TOP_KEYWORDS_KEY, topKeywords);
                    log.debug("高频关键词刷新成功");
                } catch (Exception e) {
                    log.error("高频关键词刷新失败", e);
                }
            });
            // 等待所有任务完成
            CompletableFuture.allOf(statsFuture, popularFuture, lastFuture, journalsFuture, keywordsFuture).join();

            long endTime = System.currentTimeMillis();
            log.info("首页缓存刷新完成，总耗时: {}ms", endTime - startTime);
        } catch (Exception e) {
            log.error("统计数据缓存更新失败", e);
            throw new RuntimeException("缓存刷新失败", e);
        }
    }


    private List<ArticleSummaryVO> getLastArticlesFromDB() throws ParseException {
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        // 只查询最近1年的数据，大幅减少扫描量
        LocalDate now = LocalDate.now();
        // 获取一个月内的数据
        LocalDate localDate = now.minusMonths(1);
        queryWrapper.select(Article::getId,
                        Article::getTitle,
                        Article::getAuthor,
                        Article::getJournalId,
                        Article::getYear,
                        Article::getVolume,
                        Article::getIssue,
                        Article::getPage,
                        Article::getCreateTime)
                .ge(Article::getCreateTime, localDate)
                .orderByDesc(Article::getCreateTime)
                .last("LIMIT 5");
        List<Article> articles = articleService.list(queryWrapper);
        return extracted(articles);
    }

    private List<ArticleSummaryVO> getPopularArticleFromDB() throws ParseException {
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Article::getId,
                        Article::getTitle,
                        Article::getAuthor,
                        Article::getJournalId,
                        Article::getYear,
                        Article::getVolume,
                        Article::getIssue,
                        Article::getPage,
                        Article::getHitNum,
                        Article::getCreateTime)
                .orderByDesc(Article::getHitNum)
                .last("LIMIT 5");
        List<Article> articles = articleService.list(queryWrapper);
        return extracted(articles);
    }

    private List<ArticleSummaryVO> extracted(List<Article> articles) throws ParseException {
        List<ArticleSummaryVO> articleSummaryVOList = articles.stream()
                .map(article -> {
                    ArticleSummaryVO summaryVO = new ArticleSummaryVO();
                    BeanUtils.copyProperties(article, summaryVO);
                    return summaryVO;
                })
                .toList();
        // 加入journalTitle字段，并对作者信息进行改写
        for (ArticleSummaryVO summaryVO : articleSummaryVOList) {
            Long journalId = summaryVO.getJournalId();
            // 获取期刊的标题
            LambdaQueryWrapper<Journal> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.select(Journal::getTitle)
                    .eq(Journal::getId, journalId)
                    .last("LIMIT 1");
            Journal journal = journalService.getOne(queryWrapper1);
            summaryVO.setJournalTitle(journal.getTitle());
            // 对作者信息进行改写
            List<String> author = summaryVO.getAuthor();
            List<String> authors = AuthorUtils.formatAuthorNames(author);
            summaryVO.setAuthor(authors);
            // 对时间进行改写
            Date createTime = summaryVO.getCreateTime();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String format = sdf.format(createTime);
            Date parse = sdf.parse(format);
            summaryVO.setCreateTime(parse);
        }
        return articleSummaryVOList;
    }

    /**
     * 从数据库中获取统计数据
     */
    private HomeStatsVO getHomeStatsFromDB() {
        // 获取article表中的数据
        HomeStatsVO result = new HomeStatsVO();
        // 总共的文献数量
        long totalArticles = articleService.count();

        // 获取本月新增文献
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        // 获取当前时间
        LocalDate now = LocalDate.now();
        // 一个月前的年月日
        LocalDate startDate = now.minusMonths(1);
        int startYear = startDate.getYear();
        int startMonth = startDate.getMonthValue();
        int startDay = startDate.getDayOfMonth();

        queryWrapper.ge(Article::getPublishedYear, startYear)
                .ge(Article::getPublishedMonth, startMonth)
                .ge(Article::getPublishedDay, startDay);

        long monthlyArticle = articleService.count(queryWrapper);

        // 获取覆盖期刊数量
        QueryWrapper<Article> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.select("DISTINCT journal_id");
        long totalJournals = articleService.count(queryWrapper1);

        // 获取本月新增期刊
        LambdaQueryWrapper<Journal> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.ge(Journal::getCreateTime, startDate)
                .lt(Journal::getCreateTime, now);
        long monthlyJournals = journalService.count(queryWrapper2);

        // 下载总量统计
        Long totalDownload = articleService.getTotalDownload();
        result.setTotalDownloadNum(totalDownload);

        result.setTotalArticles(totalArticles);
        result.setMonthlyArticle(monthlyArticle);
        result.setTotalJournals(totalJournals);
        result.setMonthlyJournals(monthlyJournals);
        return result;
    }
}
