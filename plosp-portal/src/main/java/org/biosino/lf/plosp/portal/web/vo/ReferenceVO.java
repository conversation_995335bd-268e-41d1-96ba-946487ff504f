package org.biosino.lf.plosp.portal.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ReferenceVO {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long docId;
    private String citation;
    private String pmid;
    private String pmcid;
    private String doi;
    private String title;
    private Boolean hasPlospLink;
    private Boolean hasPubmedLink;
    private Boolean hasPmcLink;
    private Boolean hasDoiLink;
}
