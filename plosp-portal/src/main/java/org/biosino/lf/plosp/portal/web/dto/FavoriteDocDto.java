package org.biosino.lf.plosp.portal.web.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.common.core.domain.BaseEntity;

/**
 * <AUTHOR>
 */
@Data
public class FavoriteDocDto extends BaseEntity {
    private String id;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long folderId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long docId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long customId;
    private Long pmid;
    private Long pmcId;
    private String doi;
    private Long userId;
    private String title;
    private String author;
    private String keyword;
    private Integer publisherYear;
    private String sortBy;
}
