package org.biosino.lf.plosp.portal.web.controller.auth;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.PlospUser;
import org.biosino.lf.pds.article.mapper.TbUserScoreLogMapper;
import org.biosino.lf.pds.article.service.IPlospUserService;
import org.biosino.lf.pds.common.constant.Constants;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.plosp.portal.domain.PortalLoginUser;
import org.biosino.lf.plosp.portal.utils.PortalSecurityUtils;
import org.biosino.lf.plosp.portal.web.dto.auth.LoginRequest;
import org.biosino.lf.plosp.portal.web.dto.auth.RegisterRequest;
import org.biosino.lf.plosp.portal.web.dto.auth.UserProfileResponse;
import org.biosino.lf.plosp.portal.web.service.PortalAuthService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 门户认证控制器
 * 处理门户用户认证、注册和个人资料管理
 */
@Tag(name = "门户认证", description = "门户用户认证和注册相关接口")
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Slf4j
public class PortalAuthController {

    private final PortalAuthService authService;
    private final IPlospUserService userService;
    private final TbUserScoreLogMapper tbUserScoreLogMapper;

    /**
     * 用户登录
     */
    @Operation(summary = "用户登录", description = "验证用户身份并返回JWT令牌")
    @PostMapping("/login")
    public AjaxResult login(@RequestBody @Validated LoginRequest loginRequest) {
        String token = authService.login(loginRequest.getEmail(), loginRequest.getPassword());
        AjaxResult result = AjaxResult.success("登录成功");
        result.put(Constants.TOKEN, token);
        return result;
    }

    /**
     * 用户注册
     */
    @Operation(summary = "用户注册", description = "注册新的门户用户")
    @PostMapping("/register")
    public AjaxResult register(@RequestBody @Validated RegisterRequest registerRequest) {
        authService.register(registerRequest);
        return AjaxResult.success("注册成功，请查收邮箱验证邮件");
    }

    /**
     * 发送注册验证码
     */
    @Operation(summary = "发送验证码", description = "发送邮箱验证码用于注册")
    @PostMapping("/send-verification-code")
    public AjaxResult sendVerificationCode(@Parameter(description = "邮箱地址") @RequestParam String email) {
        authService.sendVerificationCode(email);
        return AjaxResult.success("验证码已发送到您的邮箱");
    }

    /**
     * 验证邮箱
     */
    @Operation(summary = "验证邮箱", description = "使用验证码验证邮箱地址")
    @PostMapping("/verify-email")
    public AjaxResult verifyEmail(@RequestParam String email, @RequestParam String code) {
        authService.verifyEmail(email, code);
        return AjaxResult.success("邮箱验证成功");
    }

    /**
     * 用户退出
     */
    @Operation(summary = "用户退出", description = "退出当前用户并使令牌失效")
    @PostMapping("/logout")
    public AjaxResult logout() {
        authService.logout();
        return AjaxResult.success("退出成功");
    }

    /**
     * 获取当前用户资料
     */
    @Operation(summary = "获取用户资料", description = "获取当前已认证用户的个人资料信息")
    @GetMapping("/profile")
    public AjaxResult getProfile() {
        PortalLoginUser loginUser = PortalSecurityUtils.getLoginUser();
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }

        // 获取PlospUser详细信息
        PlospUser plospUser = userService.getById(loginUser.getUserId());
        if (plospUser == null) {
            return AjaxResult.error("用户信息不存在");
        }
        plospUser.setPoints(tbUserScoreLogMapper.sumUserScore(plospUser.getUserId()).intValue());

        UserProfileResponse profile = authService.buildUserProfile(plospUser);
        return AjaxResult.success(profile);
    }

    /**
     * 更新用户资料
     */
    @Operation(summary = "更新用户资料", description = "更新当前用户的个人资料信息")
    @PutMapping("/profile")
    public AjaxResult updateProfile(@RequestBody @Validated PlospUser userUpdate) {
        PortalLoginUser loginUser = PortalSecurityUtils.getLoginUser();
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }

        userUpdate.setUserId(loginUser.getUserId());
        authService.updateProfile(userUpdate);
        return AjaxResult.success("个人信息更新成功");
    }

    /**
     * 修改密码
     */
    @Operation(summary = "修改密码", description = "修改当前用户的密码")
    @PutMapping("/change-password")
    public AjaxResult changePassword(@RequestParam String oldPassword, @RequestParam String newPassword) {
        PortalLoginUser loginUser = PortalSecurityUtils.getLoginUser();
        if (loginUser == null) {
            return AjaxResult.error("用户未登录");
        }

        authService.changePassword(loginUser.getUserId(), oldPassword, newPassword);
        return AjaxResult.success("密码修改成功");
    }

    /**
     * 忘记密码 - 发送重置码
     */
    @Operation(summary = "忘记密码", description = "发送密码重置验证码到邮箱")
    @PostMapping("/forgot-password")
    public AjaxResult forgotPassword(@RequestParam String email) {
        authService.sendPasswordResetCode(email);
        return AjaxResult.success("密码重置验证码已发送到您的邮箱");
    }

    /**
     * 使用验证码重置密码
     */
    @Operation(summary = "重置密码", description = "使用验证码重置密码")
    @PostMapping("/reset-password")
    public AjaxResult resetPassword(@RequestParam String email,
                                    @RequestParam String code,
                                    @RequestParam String newPassword) {
        authService.resetPassword(email, code, newPassword);
        return AjaxResult.success("密码重置成功");
    }
}
