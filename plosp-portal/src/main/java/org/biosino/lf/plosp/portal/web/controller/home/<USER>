package org.biosino.lf.plosp.portal.web.controller.home;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.common.core.domain.R;
import org.biosino.lf.plosp.portal.web.service.HomeService;
import org.biosino.lf.plosp.portal.web.vo.ArticleSummaryVO;
import org.biosino.lf.plosp.portal.web.vo.HomeStatsVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/home")
public class HomeController {

    private final HomeService homeService;

    /**
     * 获取首页统计数据
     */
    @GetMapping("/stats")
    public R<HomeStatsVO> getHomeStats() {
        try {
            return R.ok(homeService.getHomeStats());
        } catch (Exception e) {
            log.error("获取首页统计数据失败{}", String.valueOf(e));
            return R.fail("获取首页统计数据失败");
        }
    }

    /**
     * 获取最受欢迎的5篇文献
     */
    @GetMapping("/popularArticles")
    public R<List<ArticleSummaryVO>> getPopularArticles() {
        try {
            return R.ok(homeService.getPopularArticles());
        } catch (Exception e) {
            log.error("获取最受欢迎文献失败{}", String.valueOf(e));
            return R.fail("获取最受欢迎的5篇文献失败");
        }
    }

    /**
     * 获取最新的5篇文献
     */
    @GetMapping("/latestArticles")
    public R<List<ArticleSummaryVO>> getLastArticles() {
        try {
            return R.ok(homeService.getLastArticles());
        } catch (Exception e) {
            log.error("获取最新的5篇文献失败{}", String.valueOf(e));
            return R.fail("获取最新的5篇文献失败");
        }
    }
}
