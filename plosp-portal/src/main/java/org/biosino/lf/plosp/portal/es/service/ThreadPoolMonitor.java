package org.biosino.lf.plosp.portal.es.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池监控工具类
 * 用于监控线程池状态，防止线程死锁或资源耗尽
 * <AUTHOR>
 */
@Slf4j
@Component
public class ThreadPoolMonitor {

    /**
     * 监控线程池状态
     */
    public void monitorThreadPool(ThreadPoolExecutor executor, String poolName) {
        if (executor == null) {
            log.warn("线程池 {} 为空，无法监控", poolName);
            return;
        }

        try {
            int corePoolSize = executor.getCorePoolSize();
            int maximumPoolSize = executor.getMaximumPoolSize();
            int activeCount = executor.getActiveCount();
            long completedTaskCount = executor.getCompletedTaskCount();
            long taskCount = executor.getTaskCount();
            int queueSize = executor.getQueue().size();
            int poolSize = executor.getPoolSize();

            log.info("线程池 {} 状态监控 - 核心线程数: {}, 最大线程数: {}, 当前活跃线程: {}, " +
                    "当前线程池大小: {}, 队列大小: {}, 已完成任务: {}, 总任务数: {}",
                    poolName, corePoolSize, maximumPoolSize, activeCount,
                    poolSize, queueSize, completedTaskCount, taskCount);

            // 检查是否有潜在问题
            if (activeCount == maximumPoolSize && queueSize > 0) {
                log.warn("线程池 {} 可能存在瓶颈：所有线程都在工作且队列中还有任务等待", poolName);
            }

            if (queueSize > 1000) {
                log.warn("线程池 {} 队列积压严重：队列大小 {}", poolName, queueSize);
            }

            // 检查线程池是否健康
            if (executor.isShutdown()) {
                log.warn("线程池 {} 已关闭", poolName);
            } else if (executor.isTerminating()) {
                log.warn("线程池 {} 正在关闭中", poolName);
            } else if (executor.isTerminated()) {
                log.warn("线程池 {} 已终止", poolName);
            }

        } catch (Exception e) {
            log.error("监控线程池 {} 时发生异常", poolName, e);
        }
    }

    /**
     * 安全关闭线程池
     */
    public void safeShutdown(ThreadPoolExecutor executor, String poolName, long timeoutSeconds) {
        if (executor == null || executor.isShutdown()) {
            return;
        }

        try {
            log.info("开始关闭线程池 {}", poolName);
            
            // 停止接收新任务
            executor.shutdown();
            
            // 等待现有任务完成
            if (!executor.awaitTermination(timeoutSeconds, TimeUnit.SECONDS)) {
                log.warn("线程池 {} 在 {} 秒内未能正常关闭，强制关闭", poolName, timeoutSeconds);
                
                // 强制关闭
                executor.shutdownNow();
                
                // 再次等待
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    log.error("线程池 {} 强制关闭后仍未终止", poolName);
                }
            }
            
            log.info("线程池 {} 已成功关闭", poolName);
            
        } catch (InterruptedException e) {
            log.error("关闭线程池 {} 时被中断", poolName);
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("关闭线程池 {} 时发生异常", poolName, e);
        }
    }


}
