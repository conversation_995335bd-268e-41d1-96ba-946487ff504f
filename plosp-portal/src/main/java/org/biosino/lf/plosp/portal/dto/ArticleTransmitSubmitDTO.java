package org.biosino.lf.plosp.portal.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 文献传递表单提交DTO
 *
 * <AUTHOR>
 */
@Data
public class ArticleTransmitSubmitDTO {
    @NotNull(message = "文章ID不能为空")
    private Long articleId;

    @NotBlank(message = "任务名称不能为空")
    private String name;

    @NotBlank(message = "任务描述不能为空")
    private String description;

    @NotBlank(message = "验证码不能为空")
    private String captchaCode;
    @NotBlank(message = "验证码不能为空")
    private String uuid;

}
