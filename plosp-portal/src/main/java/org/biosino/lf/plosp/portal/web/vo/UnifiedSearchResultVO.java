package org.biosino.lf.plosp.portal.web.vo;

import lombok.Data;
import org.biosino.lf.pds.common.core.page.TableDataInfo;

import java.util.List;
import java.util.Map;

/**
 * 统一搜索结果VO - 包含分页数据和聚合数据
 *
 * <AUTHOR>
 */
@Data
public class UnifiedSearchResultVO {

    /**
     * 分页数据
     */
    private TableDataInfo tableData;

    /**
     * 聚合数据 - 筛选选项
     */
    private Map<String, List<FilterOptionVO>> filterOptions;

    public UnifiedSearchResultVO(TableDataInfo tableData, Map<String, List<FilterOptionVO>> filterOptions) {
        this.tableData = tableData;
        this.filterOptions = filterOptions;
    }
}
