package org.biosino.lf.plosp.portal.web.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.domain.TbFavoriteDoc;
import org.biosino.lf.pds.article.service.IJournalService;
import org.biosino.lf.pds.article.service.ITbFavoriteDocService;
import org.biosino.lf.plosp.portal.utils.PortalSecurityUtils;
import org.biosino.lf.plosp.portal.web.vo.FavoriteDocVO;
import org.biosino.lf.plosp.portal.web.dto.FavoriteDocDto;
import org.biosino.lf.plosp.portal.web.mapper.FavoriteDocMapper;
import org.biosino.lf.plosp.portal.web.service.IFavoriteDocService;
import org.springframework.stereotype.Service;
import org.biosino.lf.plosp.portal.utils.AuthorUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 个人收藏夹service层实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class FavoriteDocServiceImpl implements IFavoriteDocService {

    private final FavoriteDocMapper favoriteDocMapper;
    private final ITbFavoriteDocService favoriteDocService;
    private final IJournalService journalService;

    @Override
    public List<FavoriteDocVO> list(FavoriteDocDto favoriteDocDto) throws ParseException {
        // 分析前端传来的文献id
        if (favoriteDocDto.getId() != null) {
            parseId(favoriteDocDto);
        }
        final Long userId = PortalSecurityUtils.getUserId();
        favoriteDocDto.setUserId(userId);
        List<FavoriteDocVO> favoriteDocVOS = favoriteDocMapper.selectFavoriteDocsWithDetail(favoriteDocDto);
        for (FavoriteDocVO favoriteDocVO : favoriteDocVOS) {
            List<String> authors = favoriteDocVO.getAuthors();
            String journalId = favoriteDocVO.getJournal();
            // 获取期刊的标题
            LambdaQueryWrapper<Journal> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(Journal::getTitle)
                    .eq(Journal::getId, Long.parseLong(journalId))
                    .last("LIMIT 1");
            Journal journal = journalService.getOne(queryWrapper);
            favoriteDocVO.setJournal(journal.getTitle());
            List<String> author = AuthorUtils.formatAuthorNames(authors);
            favoriteDocVO.setAuthors(author);
            // 对时间信息进行改写
            Date collectTime = favoriteDocVO.getCollectTime();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            String format = sdf.format(collectTime);
            Date parse = sdf.parse(format);
            favoriteDocVO.setCollectTime(parse);
        }
        return favoriteDocVOS;
    }

    @Override
    public void add(FavoriteDocDto favoriteDocDto) {
        final Long userId = PortalSecurityUtils.getUserId();
        favoriteDocDto.setUserId(userId);
        if (hasDoc(favoriteDocDto.getUserId(), favoriteDocDto.getDocId(), favoriteDocDto.getFolderId())) {
            throw new RuntimeException("文档已存在于收藏夹中");
        }
        TbFavoriteDoc favoriteDoc = new TbFavoriteDoc();
        favoriteDoc.setDocId(favoriteDocDto.getDocId());
        favoriteDoc.setFolderId(favoriteDocDto.getFolderId());
        favoriteDoc.setUserId(userId);
        favoriteDocService.save(favoriteDoc);
    }

    @Override
    public void delete(Long[] ids) {
        List<Long> list = new ArrayList<>(Arrays.asList(ids));
        favoriteDocService.removeBatchByIds(list);
    }

    @Override
    public boolean isCollect(String docid) {
        final Long userId = PortalSecurityUtils.getUserId();
        Long docId = Long.parseLong(docid);
        LambdaQueryWrapper<TbFavoriteDoc> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbFavoriteDoc::getDocId, docId)
                .eq(TbFavoriteDoc::getUserId, userId);
        long count = favoriteDocService.count(queryWrapper);
        return count > 0;
    }

    /**
     * 当前文件夹是否已经存在
     */
    private boolean hasDoc(Long userId, Long docId, Long folderId) {
        if (userId == null || docId == null || folderId == null) {
            throw new RuntimeException("参数不能为空");
        }
        LambdaQueryWrapper<TbFavoriteDoc> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbFavoriteDoc::getUserId, userId)
                .eq(TbFavoriteDoc::getDocId, docId)
                .eq(TbFavoriteDoc::getFolderId, folderId);
        long count = favoriteDocService.count(queryWrapper);
        return count > 0;
    }

    /**
     * 分析前端传来的id
     */
    private void parseId(FavoriteDocDto favoriteDocDto) {
        String id = favoriteDocDto.getId();
        if (id.toUpperCase().startsWith("PMC")) {
            // PMC ID格式：PMC + 数字
            String pmcIdStr = id.substring(3);
            if (StrUtil.isNumeric(pmcIdStr)) {
                Long pmcId = Long.parseLong(pmcIdStr);
                favoriteDocDto.setPmcId(pmcId);
            }
        } else if (id.matches("\\d+")) {
            // 纯数字，判断是自定义ID还是PMID
            Long numId = Long.parseLong(id);
            if (numId >= 800000000000L && numId < 802000000000L) {
                // 自定义ID范围
                favoriteDocDto.setCustomId(numId);
            } else if (numId.toString().length() > 18) {
                // 主键ID
                favoriteDocDto.setDocId(numId);
            } else {
                // pmid
                favoriteDocDto.setPmid(numId);
            }
        } else {
            // doi
            favoriteDocDto.setDoi(id);
        }
        favoriteDocDto.setId("");
    }
}
