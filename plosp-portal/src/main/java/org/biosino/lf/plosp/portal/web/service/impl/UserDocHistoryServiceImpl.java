package org.biosino.lf.plosp.portal.web.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.domain.UserDocHistory;
import org.biosino.lf.pds.article.mapper.UserDocHistoryMapper;
import org.biosino.lf.pds.article.service.IJournalService;
import org.biosino.lf.pds.common.utils.DateUtils;
import org.biosino.lf.plosp.portal.utils.PortalSecurityUtils;
import org.biosino.lf.plosp.portal.web.dto.UserDocHistoryDTO;
import org.biosino.lf.plosp.portal.web.mapper.UserHistoryMapper;
import org.biosino.lf.plosp.portal.web.service.IUserDocHistoryService;
import org.biosino.lf.plosp.portal.web.vo.UserDocHistoryVO;
import org.springframework.stereotype.Service;
import org.biosino.lf.plosp.portal.utils.AuthorUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 浏览历史service层实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDocHistoryServiceImpl implements IUserDocHistoryService {
    private final UserHistoryMapper userHistoryMapper;
    private final UserDocHistoryMapper userDocHistoryMapper;
    private final IJournalService journalService;

    @Override
    public List<UserDocHistoryVO> listByUserId (UserDocHistoryDTO userDocHistoryDTO) {
        try {
            if (userDocHistoryDTO.getSearch() != null) {
                parseSearch(userDocHistoryDTO);
            }
            final Long userId = PortalSecurityUtils.getUserId();
            if(userId == null){
                throw new RuntimeException("用户id为空");
            }
            userDocHistoryDTO.setUserId(userId);
            List<UserDocHistoryVO> userList = userHistoryMapper.listByUserId(userDocHistoryDTO);
            for (UserDocHistoryVO userVO : userList) {
                List<String> authors = userVO.getAuthor();
                List<String> author = AuthorUtils.formatAuthorNames(authors);
                userVO.setAuthor(author);
                // 对期刊title进行分析
                Long journalId = userVO.getJournalId();
                Journal journal = journalService.getById(journalId);
                userVO.setJournalTitle(journal.getTitle());
            }
            return userList;
        }catch (RuntimeException e){
            log.error("获取用户浏览历史失败", e);
            throw new RuntimeException("获取用户浏览历史失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void add(Long docId) {
        try {
            final Long userId = PortalSecurityUtils.getUserId();
            if(userId == null){
                throw new RuntimeException("用户id为空");
            }
            Date nowDate = DateUtils.getNowDate();
            // 检查当前浏览的文献以前是否浏览过
            UserDocHistory userDocHistory = isExist(userId, docId);
            if(userDocHistory != null){
                userDocHistory.setCreateTime(nowDate);
                userDocHistoryMapper.updateById(userDocHistory);
                return;
            }
            userDocHistory = new UserDocHistory();
            userDocHistory.setDocId(docId);
            userDocHistory.setUserId(userId);
            userDocHistoryMapper.insert(userDocHistory);
        } catch (RuntimeException e){
            log.error("新增用户浏览历史失败", e);
            throw new RuntimeException("新增用户浏览历史失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void deleteByIds(Long[] ids) {
        try{
            List<Long> list = new ArrayList<>(Arrays.asList(ids));
            userDocHistoryMapper.deleteByIds(list);
        }catch (RuntimeException e){
            log.error("删除用户浏览历史失败", e);
            throw new RuntimeException("删除用户浏览历史失败: " + e.getMessage(), e);
        }

    }

    private void parseSearch(UserDocHistoryDTO userDocHistoryDTO) {
        String search = userDocHistoryDTO.getSearch();
        if (search.toUpperCase().startsWith("PMC")) {
            // PMC ID格式：PMC + 数字
            String pmcIdStr = search.substring(3);
            if (StrUtil.isNumeric(pmcIdStr)) {
                Long pmcId = Long.parseLong(pmcIdStr);
                userDocHistoryDTO.setPmcId(pmcId);
            }
        } else if (search.matches("\\d+")) {
            // 纯数字:PMID
            Long numId = Long.parseLong(search);
            userDocHistoryDTO.setPmid(numId);
        } else {
            // 如果以“10.”开头为doi
            if (search.startsWith("10.")) {
                userDocHistoryDTO.setDoi(search);
            } else {
                userDocHistoryDTO.setTitle(search);
            }
        }
    }

    /**
     * 判断浏览历史是否有当前文献
     */
    private UserDocHistory isExist(Long userId, Long docId) {
        LambdaQueryWrapper<UserDocHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserDocHistory::getUserId, userId)
                .eq(UserDocHistory::getDocId, docId);
        return userDocHistoryMapper.selectOne(queryWrapper);
    }
}
