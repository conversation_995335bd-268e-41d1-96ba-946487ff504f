package org.biosino.lf.plosp.portal.web.controller.user;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.R;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.plosp.portal.web.dto.FavoriteDocDto;
import org.biosino.lf.plosp.portal.web.service.IFavoriteDocService;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/user/doc")
public class FavoriteDocController extends BaseController {

    private final IFavoriteDocService favoriteDocService;

    /**
     * 获取当前收藏夹的所有收藏
     */
    @GetMapping("/list")
    public TableDataInfo list(FavoriteDocDto favoriteDocDto) throws ParseException {
        startPage();
        return getDataTable(favoriteDocService.list(favoriteDocDto));
    }

    /**
     * 添加收藏
     */
    @PostMapping("/add")
    public R<String> add(@RequestBody FavoriteDocDto favoriteDocDto) {
        try {
            favoriteDocService.add(favoriteDocDto);
            return R.ok();
        } catch (Exception e) {
            log.error("收藏失败: {}", String.valueOf(e));
            return R.fail("收藏失败：" + e.getMessage());
        }
    }

    /**
     * 根据id删除收藏
     */
    @DeleteMapping("/delete/{ids}")
    public R<String> delete(@PathVariable("ids") Long[] ids) {
        try {
            favoriteDocService.delete(ids);
            return R.ok();
        } catch (Exception e) {
            log.error("删除失败：{}", String.valueOf(e));
            return R.fail("删除失败" + e.getMessage());
        }
    }

    /**
     * 判断文献是否已经收藏
     */
    @GetMapping("/isCollect")
    public R<String> isCollect(@RequestParam String docId) {
        boolean isCollect = favoriteDocService.isCollect(docId);
        if (isCollect) {
            return R.ok("该文献已收藏");
        }
        return R.ok();
    }
}
