package org.biosino.lf.plosp.portal.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;

/**
 * Elasticsearch配置类
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Configuration
@EnableElasticsearchRepositories(basePackages = "org.biosino.lf.plosp.portal.es.mapper")
public class EsConfig {
    // Spring Data Elasticsearch会自动配置，这里只需要指定Repository扫描路径

}
