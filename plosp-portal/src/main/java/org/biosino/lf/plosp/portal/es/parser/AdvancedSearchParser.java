package org.biosino.lf.plosp.portal.es.parser;

import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.plosp.portal.web.dto.SearchConditionDTO;
import org.biosino.lf.plosp.portal.web.enums.ScArcQueryItemEnum;
import org.springframework.stereotype.Component;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.*;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 高级检索解析器
 * 解析类似PubMed的高级检索语法：(cancer[Title]) AND (smith[Author])
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class AdvancedSearchParser {

    // 匹配检索项的正则表达式：(关键词[字段名])
    private static final Pattern SEARCH_ITEM_PATTERN = Pattern.compile("\\((.+?)\\[([^\\[\\]]+)\\]\\)");

    // 匹配不带括号的检索项：关键词[字段名]
   private static final Pattern SIMPLE_ITEM_PATTERN = Pattern.compile("(.+?)\\[([^\\[\\]]+)\\]");

    // 匹配日期范围查询的正则表达式：(* TO 20130101[Publication Date Range]) 或 (20100101 TO *[Publication Date Range])
    private static final Pattern DATE_RANGE_PATTERN = Pattern.compile("\\(([*\\d]+)\\s+TO\\s+([*\\d]+)\\[([^\\[\\]]+)\\]\\)", Pattern.CASE_INSENSITIVE);

    // 匹配不带括号的日期范围查询：* TO 20130101[Publication Date Range]
    private static final Pattern SIMPLE_DATE_RANGE_PATTERN = Pattern.compile("([*\\d]+)\\s+TO\\s+([*\\d]+)\\[([^\\[\\]]+)\\]", Pattern.CASE_INSENSITIVE);

    // 字段名映射，将PubMed风格的字段名映射到系统内部字段名
    private static final Map<String, String> FIELD_MAPPING = new HashMap<>();
    
    static {
        // 基础字段映射
        FIELD_MAPPING.put("AI", "ai");
        FIELD_MAPPING.put("PMID", "pmid");
        FIELD_MAPPING.put("PMCID", "pmc_id");
        FIELD_MAPPING.put("DOI", "doi");
        FIELD_MAPPING.put("Title", "title");
        FIELD_MAPPING.put("Author", "author");
        FIELD_MAPPING.put("Lastname", "lastname");
        FIELD_MAPPING.put("Forename", "forename");
        FIELD_MAPPING.put("Affiliation", "affiliation");
        FIELD_MAPPING.put("Abstract", "articleAbstract");
        FIELD_MAPPING.put("Keywords", "keywords");
        FIELD_MAPPING.put("pub_type", "pub_type");
        FIELD_MAPPING.put("PublisherName", "publisherName");
        FIELD_MAPPING.put("JournalName", "journalName");
        FIELD_MAPPING.put("Volume", "volume");
        FIELD_MAPPING.put("Issue", "issue");
        FIELD_MAPPING.put("Page", "page");
        FIELD_MAPPING.put("Publication Date Range", "published_date_range");
        FIELD_MAPPING.put("Language", "language");
        FIELD_MAPPING.put("Grant", "grant");
        FIELD_MAPPING.put("Databank", "databank");
        FIELD_MAPPING.put("Source", "source");
        FIELD_MAPPING.put("Publication Status", "pub_status");
        FIELD_MAPPING.put("Mesh", "meshUi");
        FIELD_MAPPING.put("Impact Factor", "impactFactor");
        FIELD_MAPPING.put("JCR", "jcr");
        FIELD_MAPPING.put("CAS Category", "largeCategorySection");

        // 添加小写和其他变体
        FIELD_MAPPING.put("ai", "ai");
        FIELD_MAPPING.put("pmid", "pmid");
        FIELD_MAPPING.put("pmc_id", "pmc_id");
        FIELD_MAPPING.put("doi", "doi");
        FIELD_MAPPING.put("title", "title");
        FIELD_MAPPING.put("author", "author");
        FIELD_MAPPING.put("lastname", "lastname");
        FIELD_MAPPING.put("forename", "forename");
        FIELD_MAPPING.put("affiliation", "affiliation");
        FIELD_MAPPING.put("articleAbstract", "articleAbstract");
        FIELD_MAPPING.put("keywords", "keywords");
        FIELD_MAPPING.put("pubtype", "pub_type");
        FIELD_MAPPING.put("publisher_name", "publisher_name");
        FIELD_MAPPING.put("journal_name", "journal_name");
        FIELD_MAPPING.put("volume", "volume");
        FIELD_MAPPING.put("issue", "issue");
        FIELD_MAPPING.put("page", "page");
        FIELD_MAPPING.put("published_date_range", "published_date_range");
        FIELD_MAPPING.put("language", "language");
        FIELD_MAPPING.put("grant", "grant");
        FIELD_MAPPING.put("databank", "databank");
        FIELD_MAPPING.put("source", "source");
        FIELD_MAPPING.put("pub_status", "pub_status");
        FIELD_MAPPING.put("meshUi", "meshUi");
        FIELD_MAPPING.put("impactFactor", "impactFactor");
        FIELD_MAPPING.put("jcr", "jcr");
        FIELD_MAPPING.put("largeCategorySection", "largeCategorySection");
    }

    /**
     * 标准化查询字符串
     */
    private String normalizeQuery(String query) {
        // 移除多余的空格
        String normalized = query.trim().replaceAll("\\s+", " ");
        
        // 标准化操作符（确保操作符前后有空格）
        normalized = normalized.replaceAll("\\s*\\bAND\\b\\s*", " AND ");
        normalized = normalized.replaceAll("\\s*\\bOR\\b\\s*", " OR ");
        normalized = normalized.replaceAll("\\s*\\bNOT\\b\\s*", " NOT ");
        
        // 处理大小写不敏感的操作符
        normalized = normalized.replaceAll("(?i)\\s*\\band\\b\\s*", " AND ");
        normalized = normalized.replaceAll("(?i)\\s*\\bor\\b\\s*", " OR ");
        normalized = normalized.replaceAll("(?i)\\s*\\bnot\\b\\s*", " NOT ");
        
        return normalized.trim();
    }

    /**
     * 创建检索条件
     */
    private SearchConditionDTO createSearchCondition(SearchItem item) {
        // 映射字段名
        String mappedField = mapFieldName(item.fieldName);
        if (mappedField == null) {
            log.warn("未知的字段名: {}", item.fieldName);
            return null;
        }

        // 验证字段是否存在于枚举中
        Optional<ScArcQueryItemEnum> enumOpt = ScArcQueryItemEnum.findByName(mappedField);
        if (enumOpt.isEmpty()) {
            log.warn("字段名不在支持的枚举中: {}", mappedField);
            return null;
        }

        SearchConditionDTO condition = new SearchConditionDTO();
        condition.setField(mappedField);
        condition.setValue(item.keyword);

        return condition;
    }

    /**
     * 创建日期范围检索条件
     */
    private SearchConditionDTO createDateRangeCondition(DateRangeSearchItem item) {
        // 映射字段名
        String mappedField = mapFieldName(item.fieldName);
        if (mappedField == null) {
            log.warn("未知的日期字段名: {}", item.fieldName);
            return null;
        }

        SearchConditionDTO condition = new SearchConditionDTO();
        condition.setField(mappedField);
        condition.setValue("");

        // 处理开始日期
        if (!"*".equals(item.startDate)) {
            condition.setStartDate(item.startDate);
        }

        // 处理结束日期
        if (!"*".equals(item.endDate)) {
            condition.setEndDate(item.endDate);
        }


        return condition;
    }

    /**
     * 映射字段名
     */
    private String mapFieldName(String fieldName) {
        // 直接查找映射
        String mapped = FIELD_MAPPING.get(fieldName);
        if (mapped != null) {
            return mapped;
        }
        
        // 尝试不区分大小写的查找
        for (Map.Entry<String, String> entry : FIELD_MAPPING.entrySet()) {
            if (entry.getKey().equalsIgnoreCase(fieldName)) {
                return entry.getValue();
            }
        }
        
        return null;
    }

    /**
     * 检索项内部类
     */
    private static class SearchItem {
        final String keyword;
        final String fieldName;
        final int start;
        final int end;

        SearchItem(String keyword, String fieldName, int start, int end) {
            this.keyword = keyword;
            this.fieldName = fieldName;
            this.start = start;
            this.end = end;
        }
    }

    /**
     * 日期范围检索项内部类
     */
    private static class DateRangeSearchItem extends SearchItem {
        final String startDate;
        final String endDate;

        DateRangeSearchItem(String startDate, String endDate, String fieldName, int start, int end) {
            super("", fieldName, start, end);
            this.startDate = startDate;
            this.endDate = endDate;
        }
    }

    /**
     * 验证高级检索字符串格式
     */
    public boolean isValidAdvancedQuery(String query) {
        if (StrUtil.isBlank(query)) {
            return false;
        }

        try {
            String normalized = normalizeQuery(query);

            // 检查是否包含字段标识符 [字段名]
            if (normalized.matches(".*[^\\[\\]]+\\[[^\\[\\]]+\\].*")) {
                return true;
            }

            return false;

        } catch (Exception e) {
            log.warn("验证高级检索字符串格式失败: {}", query, e);
            return false;
        }
    }

    /**
     * 查询节点 - 表示解析树的节点
     */
    private static abstract class QueryNode {
    }

    /**
     * 操作符节点 - 表示AND、OR、NOT操作
     */
    private static class OperatorNode extends QueryNode {
        final String operator;
        final List<QueryNode> children;

        OperatorNode(String operator) {
            this.operator = operator;
            this.children = new ArrayList<>();
        }

        void addChild(QueryNode child) {
            children.add(child);
        }
    }

    /**
     * 条件节点 - 表示具体的检索条件
     */
    private static class ConditionNode extends QueryNode {
        final SearchConditionDTO condition;

        ConditionNode(SearchConditionDTO condition) {
            this.condition = condition;
        }
    }

    /**
     * 解析表达式（递归处理括号优先级）
     * 从最内层括号开始逐步向外计算，每一步产生中间结果
     */
    private QueryNode parseExpression(String query) {
        try {
            return parseExpressionRecursive(query.trim());
        } catch (Exception e) {
            log.error("解析表达式失败: {}", query, e);
            return null;
        }
    }

    /**
     * 递归解析表达式 - 从外到内的递归下降解析
     */
    private QueryNode parseExpressionRecursive(String query) {
        query = query.trim();

        // 移除最外层括号（如果存在）
        if (query.startsWith("(") && query.endsWith(")") && isBalancedWithoutOuter(query)) {
            query = query.substring(1, query.length() - 1).trim();
        }

        // 如果没有操作符，直接解析为单个条件
        if (!containsOperatorAtTopLevel(query)) {
            return parseSingleCondition(query);
        }

        // 按优先级从低到高查找操作符：OR -> AND -> NOT

        // 1. 查找最外层的OR操作符
        int orPos = findTopLevelOperator(query, "OR");
        if (orPos != -1) {
            String leftPart = query.substring(0, orPos).trim();
            String rightPart = query.substring(orPos + 2).trim();

            QueryNode leftNode = parseExpressionRecursive(leftPart);
            QueryNode rightNode = parseExpressionRecursive(rightPart);

            OperatorNode orNode = new OperatorNode("OR");
            if (leftNode != null) orNode.addChild(leftNode);
            if (rightNode != null) orNode.addChild(rightNode);
            return orNode;
        }

        // 2. 查找最外层的AND操作符
        int andPos = findTopLevelOperator(query, "AND");
        if (andPos != -1) {
            String leftPart = query.substring(0, andPos).trim();
            String rightPart = query.substring(andPos + 3).trim();

            QueryNode leftNode = parseExpressionRecursive(leftPart);
            QueryNode rightNode = parseExpressionRecursive(rightPart);

            OperatorNode andNode = new OperatorNode("AND");
            if (leftNode != null) andNode.addChild(leftNode);
            if (rightNode != null) andNode.addChild(rightNode);
            return andNode;
        }

        // 3. 查找最外层的NOT操作符
        int notPos = findTopLevelOperator(query, "NOT");
        if (notPos != -1) {
            String leftPart = query.substring(0, notPos).trim();
            String rightPart = query.substring(notPos + 3).trim();

            QueryNode leftNode = leftPart.isEmpty() ? null : parseExpressionRecursive(leftPart);
            QueryNode rightNode = parseExpressionRecursive(rightPart);

            if (leftNode != null && rightNode != null) {
                // 左边有内容，构建 leftNode AND NOT rightNode
                OperatorNode andNode = new OperatorNode("AND");
                andNode.addChild(leftNode);

                OperatorNode notNode = new OperatorNode("NOT");
                notNode.addChild(rightNode);
                andNode.addChild(notNode);

                return andNode;
            } else if (rightNode != null) {
                // 只有右边，构建 NOT rightNode
                OperatorNode notNode = new OperatorNode("NOT");
                notNode.addChild(rightNode);
                return notNode;
            }
        }

        // 如果没有找到操作符，尝试解析为单个条件
        return parseSingleCondition(query);
    }

    /**
     * 检查移除最外层括号后是否平衡
     */
    private boolean isBalancedWithoutOuter(String query) {
        if (query.length() < 2) return false;

        String inner = query.substring(1, query.length() - 1);
        int count = 0;
        for (char c : inner.toCharArray()) {
            if (c == '(') count++;
            else if (c == ')') count--;
            if (count < 0) return false;
        }
        return count == 0;
    }

    /**
     * 检查是否在顶层包含操作符
     */
    private boolean containsOperatorAtTopLevel(String query) {
        return findTopLevelOperator(query, "OR") != -1 ||
               findTopLevelOperator(query, "AND") != -1 ||
               findTopLevelOperator(query, "NOT") != -1;
    }

    /**
     * 在顶层查找操作符位置（不在括号内）
     */
    private int findTopLevelOperator(String query, String operator) {
        int parenLevel = 0;
        String upperQuery = query.toUpperCase();
        String upperOp = operator.toUpperCase();

        for (int i = 0; i <= query.length() - operator.length(); i++) {
            char c = query.charAt(i);

            if (c == '(') {
                parenLevel++;
            } else if (c == ')') {
                parenLevel--;
            } else if (parenLevel == 0) {
                // 只在顶层（括号外）查找
                if (upperQuery.substring(i).startsWith(upperOp)) {
                    // 检查是否为完整单词（前后不是字母）
                    boolean validStart = (i == 0 || !Character.isLetter(query.charAt(i - 1)));
                    boolean validEnd = (i + operator.length() >= query.length() ||
                                     !Character.isLetter(query.charAt(i + operator.length())));

                    if (validStart && validEnd) {
                        return i;
                    }
                }
            }
        }

        return -1;
    }

    /**
     * 解析单个条件（不包含括号）
     */
    private QueryNode parseSingleCondition(String query) {
        query = query.trim();

        // 尝试匹配日期范围查询（先尝试带括号的格式）
        Matcher dateRangeMatcher = DATE_RANGE_PATTERN.matcher(query);
        if (dateRangeMatcher.find()) {
            String startDate = dateRangeMatcher.group(1).trim();
            String endDate = dateRangeMatcher.group(2).trim();
            String fieldName = dateRangeMatcher.group(3).trim();

            DateRangeSearchItem item = new DateRangeSearchItem(startDate, endDate, fieldName, 0, query.length());
            SearchConditionDTO condition = createDateRangeCondition(item);
            if (condition != null) {
                return new ConditionNode(condition);
            }
        }

        // 尝试匹配不带括号的日期范围查询
        Matcher simpleDateRangeMatcher = SIMPLE_DATE_RANGE_PATTERN.matcher(query);
        if (simpleDateRangeMatcher.find()) {
            String startDate = simpleDateRangeMatcher.group(1).trim();
            String endDate = simpleDateRangeMatcher.group(2).trim();
            String fieldName = simpleDateRangeMatcher.group(3).trim();

            DateRangeSearchItem item = new DateRangeSearchItem(startDate, endDate, fieldName, 0, query.length());
            SearchConditionDTO condition = createDateRangeCondition(item);
            if (condition != null) {
                return new ConditionNode(condition);
            }
        }

        // 尝试匹配普通检索项（先尝试带括号的格式）
        Matcher itemMatcher = SEARCH_ITEM_PATTERN.matcher(query);
        if (itemMatcher.find()) {
            String keyword = itemMatcher.group(1).trim();
            String fieldName = itemMatcher.group(2).trim();

            SearchItem item = new SearchItem(keyword, fieldName, 0, query.length());
            SearchConditionDTO condition = createSearchCondition(item);
            if (condition != null) {
                return new ConditionNode(condition);
            }
        }

        // 尝试匹配不带括号的检索项
        Matcher simpleItemMatcher = SIMPLE_ITEM_PATTERN.matcher(query);
        if (simpleItemMatcher.find()) {
            String keyword = simpleItemMatcher.group(1).trim();
            String fieldName = simpleItemMatcher.group(2).trim();

            SearchItem item = new SearchItem(keyword, fieldName, 0, query.length());
            SearchConditionDTO condition = createSearchCondition(item);
            if (condition != null) {
                return new ConditionNode(condition);
            }
        }

        return null;
    }

    /**
     * 直接构建ES查询（新方法）
     */
    public Query buildESQuery(String advancedQuery) {
        if (StrUtil.isBlank(advancedQuery)) {
            return null;
        }

        try {
            // 预处理：标准化空格和操作符
            String normalizedQuery = normalizeQuery(advancedQuery);

            // 使用新的递归解析方法
            QueryNode rootNode = parseExpression(normalizedQuery);

            if (rootNode == null) {
                return null;
            }

            // 将解析树转换为ES查询
            return convertNodeToESQuery(rootNode);

        } catch (Exception e) {
            log.error("构建ES查询失败: {}", advancedQuery, e);
            return null;
        }
    }

    /**
     * 将解析树节点转换为ES查询
     */
    private Query convertNodeToESQuery(QueryNode node) {
        if (node == null) {
            return null;
        }

        if (node instanceof ConditionNode) {
            ConditionNode conditionNode = (ConditionNode) node;
            return buildFieldQueryFromCondition(conditionNode.condition);
        } else if (node instanceof OperatorNode) {
            OperatorNode opNode = (OperatorNode) node;
            return buildBoolQueryFromOperator(opNode);
        }

        return null;
    }

    /**
     * 从条件构建字段查询
     */
    private Query buildFieldQueryFromCondition(SearchConditionDTO condition) {
        try {
            String fieldName = getESFieldName(condition.getField());
            String value = condition.getValue();

            if ("published_date_range".equals(condition.getField())) {
                // 处理日期范围查询
                return buildDateRangeQuery(condition);
            }

            // 根据ES mapping配置选择合适的查询方式
            switch (condition.getField()) {
                // author字段使用keyword_lower子字段进行terms查询
                case "author":
                    return QueryBuilders.terms(t -> t.field("author.keyword_lower").terms(tf -> tf.value(List.of(co.elastic.clients.elasticsearch._types.FieldValue.of(value.toLowerCase())))));

                // nested字段需要使用nested查询
                case "lastname":
                case "forename":
                    return QueryBuilders.nested(n -> n
                        .path("author_info")
                        .query(QueryBuilders.term(t -> t.field(fieldName).value(value.toLowerCase()))));

                // 其他keyword类型的数组字段，使用terms查询
                case "pub_type":
                case "meshUi":
                case "source":
                    return QueryBuilders.terms(t -> t.field(fieldName).terms(tf -> tf.value(List.of(co.elastic.clients.elasticsearch._types.FieldValue.of(value)))));

                // grant字段使用match查询（支持部分匹配）
                case "grant":
                    return QueryBuilders.match(m -> m.field(fieldName).query(value));

                // 这些字段在ES中是text类型，但在Java实体中是List<String>，使用match查询进行分词搜索
                case "affiliation":
                case "keywords":
                case "databank":
                    return QueryBuilders.match(m -> m.field(fieldName).query(value));

                // 文本字段 - 使用 match 查询（需要分词搜索）
                case "publisherName":
                case "journalName":
                case "title":
                case "articleAbstract":
                    return QueryBuilders.match(m -> m.field(fieldName).query(value));

                // 单值精确匹配字段 - 使用 term 查询
                case "jcr":
                case "issue":
                    return QueryBuilders.term(t -> t.field(fieldName).value(value));

                // 数值字段 - 使用 term 查询
                case "pmid":
                    // PMID 需要转换为数值
                    try {
                        long pmidVal = Long.parseLong(value);
                        return QueryBuilders.term(t -> t.field(fieldName).value(pmidVal));
                    } catch (NumberFormatException e) {
                        // 如果不是数字，使用 match 查询
                        return QueryBuilders.match(m -> m.field(fieldName).query(value));
                    }

                case "pmcid":
                    // PMCID 需要转换为数值
                    try {
                        long pmcidVal = Long.parseLong(value);
                        return QueryBuilders.term(t -> t.field(fieldName).value(pmcidVal));
                    } catch (NumberFormatException e) {
                        return QueryBuilders.match(m -> m.field(fieldName).query(value));
                    }

                case "doi":
                    // DOI 清理前缀后使用 term 查询
                    String cleanDoi = value.toLowerCase().startsWith("doi:") ? value.substring(4) : value;
                    return QueryBuilders.term(t -> t.field(fieldName).value(cleanDoi));

                case "impactFactor":
                    // 影响因子转换为数值
                    try {
                        float impactFactorVal = Float.parseFloat(value);
                        return QueryBuilders.term(t -> t.field(fieldName).value(impactFactorVal));
                    } catch (NumberFormatException e) {
                        return QueryBuilders.match(m -> m.field(fieldName).query(value));
                    }

                case "largeCategorySection":
                    // 中科院分区转换为数值
                    try {
                        int sectionVal = Integer.parseInt(value);
                        return QueryBuilders.term(t -> t.field(fieldName).value(sectionVal));
                    } catch (NumberFormatException e) {
                        return QueryBuilders.match(m -> m.field(fieldName).query(value));
                    }
                // 其他字段默认使用 term 查询
                default:
                    return QueryBuilders.term(t -> t.field(fieldName).value(value));
            }
        } catch (Exception e) {
            log.error("构建字段查询失败: field={}, value={}", condition.getField(), condition.getValue(), e);
            return null;
        }
    }

    /**
     * 构建日期范围查询
     */
    private Query buildDateRangeQuery(SearchConditionDTO condition) {
        try {
            String startDate = condition.getStartDate();
            String endDate = condition.getEndDate();

            // 如果开始和结束日期都为空，返回null
            if (StrUtil.isBlank(startDate) && StrUtil.isBlank(endDate)) {
                return null;
            }

            return RangeQuery.of(r -> r
                .number(n -> {
                    n.field("published_date");

                    // 如果有开始日期，设置gte
                    if (StrUtil.isNotBlank(startDate)) {
                        try {
                            Integer startDateInt = Integer.valueOf(startDate);
                            n.gte(Double.valueOf(startDateInt));
                        } catch (NumberFormatException e) {
                            log.warn("开始日期格式错误: {}", startDate);
                        }
                    }

                    // 如果有结束日期，设置lte
                    if (StrUtil.isNotBlank(endDate)) {
                        try {
                            Integer endDateInt = Integer.valueOf(endDate);
                            n.lte(Double.valueOf(endDateInt));
                        } catch (NumberFormatException e) {
                            log.warn("结束日期格式错误: {}", endDate);
                        }
                    }

                    return n;
                })
            )._toQuery();
        } catch (Exception e) {
            log.error("构建日期范围查询失败", e);
            return null;
        }
    }

    /**
     * 从操作符节点构建布尔查询
     */
    private Query buildBoolQueryFromOperator(OperatorNode opNode) {
        List<Query> childQueries = new ArrayList<>();

        for (QueryNode child : opNode.children) {
            Query childQuery = convertNodeToESQuery(child);
            if (childQuery != null) {
                childQueries.add(childQuery);
            }
        }

        if (childQueries.isEmpty()) {
            return null;
        }

        if (childQueries.size() == 1 && !"NOT".equals(opNode.operator)) {
            return childQueries.get(0);
        }

        // 构建布尔查询
        return QueryBuilders.bool(boolQuery -> {
            switch (opNode.operator.toUpperCase()) {
                case "AND":
                    boolQuery.must(childQueries);
                    break;
                case "OR":
                    boolQuery.should(childQueries);
                    boolQuery.minimumShouldMatch("1");
                    break;
                case "NOT":
                    if (childQueries.size() == 1) {
                        // 纯NOT查询：match_all AND NOT condition
                        boolQuery.must(QueryBuilders.matchAll().build()._toQuery());
                        boolQuery.mustNot(childQueries);
                    } else {
                        // 多个条件的NOT（理论上不应该出现）
                        boolQuery.mustNot(childQueries);
                    }
                    break;
                default:
                    // 默认使用AND
                    boolQuery.must(childQueries);
                    break;
            }
            return boolQuery;
        });
    }

    /**
     * 获取ES字段名
     */
    private String getESFieldName(String fieldName) {
        // 根据ES实体类PlospArticleEs的字段映射
        switch (fieldName) {
            case "title": return "title";
            case "author": return "author";
            case "pmid": return "pmid";
            case "pmcid": return "pmc_id";
            case "doi": return "doi";
            case "articleAbstract": return "abstract";
            case "keywords": return "keywords";
            case "meshUi": return "mesh_ui";
            case "affiliation": return "affiliation";
            case "pub_type": return "pub_type";
            case "publisherName": return "publisher_name";
            case "journalName": return "journal_name";
            case "volume": return "volume";
            case "issue": return "issue";
            case "page": return "page";
            case "language": return "language";
            case "grant": return "grant";
            case "databank": return "databank";
            case "mesh": return "mesh";
            case "source": return "source";
            case "pub_status": return "pub_status";
            case "impactFactor": return "impact_factor";
            case "jcr": return "jcr";
            case "largeCategorySection": return "large_category_section";
            case "forename": return "author_info.forename";
            case "lastname": return "author_info.lastname";
            default: return fieldName.toLowerCase();
        }
    }

}
