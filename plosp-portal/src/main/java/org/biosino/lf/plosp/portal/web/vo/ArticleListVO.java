package org.biosino.lf.plosp.portal.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.article.domain.AuthorInfo;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ArticleListVO implements Serializable {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    private Long pmid;

    private Long pmcId;

    private String doi;

    private List<String> author;

    private List<AuthorInfo> authorInfo;

    private List<String> affiliation;

    private String title;

    private String articleAbstract;

    private List<String> keywords;

    private List<String> pubType;

    private Long publisherId;

    private Long journalId;

    private List<String> meshUi;

    private String journalName;

    private String publisherName;

    private String volume;

    private String issue;

    private String page;

    private Integer publishedYear;

    private Integer publishedMonth;

    private Integer publishedDay;

    /**
     * 格式化的发布日期字符串 (yyyy-MM-dd)
     */
    private Integer publishedDate;

    private List<String> grant;

    private List<String> databank;

    private Float impactFactor;

    private String jcr;

    private Integer largeCategorySection;

    private List<String> language;

    private Integer year;

    private List<String> source;

    private String pubStatus;

    private List<String> mesh;

    private Boolean free;

    private Boolean hasPdf;

    private Date createTime;

    private Date updateTime;

}
