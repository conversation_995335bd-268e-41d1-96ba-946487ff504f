package org.biosino.lf.plosp.portal.web.controller.system;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.domain.entity.SysDictData;
import org.biosino.lf.pds.system.service.ISysDictTypeService;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 门户字典控制器
 * 为门户用户提供字典数据查询服务
 */
@Tag(name = "门户字典", description = "门户字典数据查询接口")
@RestController
@RequestMapping("/api/dict")
@RequiredArgsConstructor
@Slf4j
public class PortalDictController {

    private final ISysDictTypeService dictTypeService;

    /**
     * 根据字典类型查询字典数据
     */
    @Operation(summary = "获取字典数据", description = "根据字典类型获取对应的字典数据列表")
    @GetMapping("/data/type/{dictType}")
    public AjaxResult getDictData(@Parameter(description = "字典类型") @PathVariable String dictType) {
        List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
        if (data == null) {
            data = new ArrayList<>();
        }
        return AjaxResult.success(data);
    }

    /**
     * 批量获取多个字典类型的数据
     */
    @Operation(summary = "批量获取字典数据", description = "一次性获取多个字典类型的数据")
    @PostMapping("/data/batch")
    public AjaxResult getBatchDictData(@RequestBody List<String> dictTypes) {
        Map<String, List<SysDictData>> result = new HashMap<>();

        for (String dictType : dictTypes) {
            List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
            result.put(dictType, data != null ? data : new ArrayList<>());
        }

        return AjaxResult.success(result);
    }

    /**
     * 获取国家字典数据
     */
    @Operation(summary = "获取国家列表", description = "获取所有国家的字典数据")
    @GetMapping("/countries")
    public AjaxResult getCountries() {
        return getDictData("node_country");
    }

    /**
     * 获取机构字典数据
     */
    @Operation(summary = "获取机构列表", description = "获取所有机构的字典数据")
    @GetMapping("/organizations")
    public AjaxResult getOrganizations() {
        return getDictData("node_organization");
    }
}
