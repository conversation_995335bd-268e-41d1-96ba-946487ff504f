package org.biosino.lf.plosp.portal.web.controller.search;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.plosp.portal.es.service.PlospArticleService;
import org.biosino.lf.plosp.portal.web.dto.ArticleQueryDTO;
import org.biosino.lf.plosp.portal.web.enums.ScArcQueryItemEnum;
import org.biosino.lf.plosp.portal.web.vo.UnifiedSearchResultVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/es/search")
@RequiredArgsConstructor
public class EsSearchController {
    private final PlospArticleService plospArticleService;

    /**
     * 统一搜索接口 - 包含智能搜索、聚合和过滤功能
     */
    @PostMapping("/list")
    public AjaxResult list(@Validated @RequestBody ArticleQueryDTO articleQueryDTO){
        UnifiedSearchResultVO result = plospArticleService.unifiedSearch(articleQueryDTO);
        return AjaxResult.success(result);
    }

    /**
     * 筛选项下拉框
     */
    @GetMapping("/selectItem")
    public AjaxResult selectItem() {
        // 返回可搜索的字段列表
        return AjaxResult.success(ScArcQueryItemEnum.getSelectVOList());
    }

    /**
     * MeSH词汇自动完成
     */
    @GetMapping("/meshAutoComplete")
    public AjaxResult meshAutoComplete(@RequestParam String keyword) {
        try {
            List<Map<String, String>> suggestions = plospArticleService.getMeshAutoComplete(keyword);
            return AjaxResult.success(suggestions);
        } catch (Exception e) {
            return AjaxResult.error("获取MeSH建议失败: " + e.getMessage());
        }
    }

}
