package org.biosino.lf.plosp.portal.web.controller.user;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.R;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.plosp.portal.web.dto.UserDocHistoryDTO;
import org.biosino.lf.plosp.portal.web.service.IUserDocHistoryService;
import org.springframework.web.bind.annotation.*;

/**
 * 浏览历史controller类
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/user/history")
public class UserViewHistoryController extends BaseController {

    private final IUserDocHistoryService userDocHistoryService;

    /**
     * 获取当前用户的浏览历史
     */
    @GetMapping("/list")
    public TableDataInfo list(UserDocHistoryDTO userDocHistoryDTO) {
        startPage();
        return getDataTable(userDocHistoryService.listByUserId(userDocHistoryDTO));
    }

    /**
     * 新增浏览历史
     */
    @PostMapping("/add")
    public R<String> add(@RequestParam Long docId) {
        userDocHistoryService.add(docId);
        return R.ok();
    }

    /**
     * 删除浏览历史
     */
    @DeleteMapping("/delete/{ids}")
    public R<String> deleteByIds(@PathVariable("ids") Long[] ids) {
        userDocHistoryService.deleteByIds(ids);
        return R.ok();
    }

}
