package org.biosino.lf.plosp.portal.web.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 搜索条件DTO
 * <AUTHOR>
 */
@Data
public class SearchConditionDTO {
    /**
     * 搜索字段
     */
    @NotBlank(message = "搜索字段不能为空")
    private String field;

    /**
     * 搜索值
     */
    @NotBlank(message = "搜索值不能为空")
    private String value;

    /**
     * 逻辑操作符 (AND, OR, NOT)
     */
    private String operator;

    /**
     * 开始日期 (用于日期范围查询)
     */
    private String startDate;

    /**
     * 结束日期 (用于日期范围查询)
     */
    private String endDate;
}
