package org.biosino.lf.plosp.portal.web.controller.user;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.UserApiKey;
import org.biosino.lf.pds.article.service.IUserApiKeyService;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.plosp.portal.utils.PortalSecurityUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户API密钥管理控制器
 *
 * <AUTHOR>
 */
@Tag(name = "用户API密钥管理", description = "用户API密钥的增删改查操作")
@RestController
@RequestMapping("/api/user/apikeys")
@RequiredArgsConstructor
public class UserApiKeyController {

    private final IUserApiKeyService userApiKeyService;

    /**
     * 获取API密钥列表
     */
    @Operation(summary = "获取API密钥列表")
    @GetMapping
    public AjaxResult getApiKeys() {
        final Long userId = PortalSecurityUtils.getUserId();
        List<UserApiKey> apiKeys = userApiKeyService.getUserApiKeys(userId);
        // 脱敏处理
        apiKeys.forEach(key -> {
            String maskedKey = key.getApiKey();
            if (maskedKey != null && maskedKey.length() > 8) {
                key.setApiKey(maskedKey.substring(0, 4) + "****" + maskedKey.substring(maskedKey.length() - 4));
            }
        });
        return AjaxResult.success(apiKeys);
    }

    /**
     * 创建API密钥
     */
    @Operation(summary = "创建API密钥")
    @PostMapping
    public AjaxResult createApiKey(@Parameter(description = "密钥名称") @RequestParam String keyName) {
        final Long userId = PortalSecurityUtils.getUserId();
        try {
            UserApiKey apiKey = userApiKeyService.createApiKey(userId, keyName);
            return AjaxResult.success("API密钥创建成功", apiKey);
        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 删除API密钥
     */
    @Operation(summary = "删除API密钥")
    @DeleteMapping("/{keyId}")
    public AjaxResult deleteApiKey(@Parameter(description = "密钥ID") @PathVariable Long keyId) {
        final Long userId = PortalSecurityUtils.getUserId();
        // 验证权限
        UserApiKey existingKey = userApiKeyService.getById(keyId);
        if (existingKey == null || !existingKey.getUserId().equals(userId)) {
            return AjaxResult.error("API密钥不存在或无权限操作");
        }
        boolean success = userApiKeyService.removeById(keyId);
        return success ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
    }

    /**
     * 修改API密钥名称
     */
    @Operation(summary = "修改API密钥名称")
    @PutMapping("/{keyId}")
    public AjaxResult updateApiKeyName(@Parameter(description = "密钥ID") @PathVariable Long keyId,
                                       @Parameter(description = "新密钥名称") @RequestParam String keyName) {
        final Long userId = PortalSecurityUtils.getUserId();
        try {
            boolean success = userApiKeyService.updateApiKeyName(keyId, userId, keyName);
            return success ? AjaxResult.success("密钥名称修改成功") : AjaxResult.error("修改失败");
        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 获取完整API密钥
     */
    @Operation(summary = "获取完整API密钥")
    @GetMapping("/{keyId}/full")
    public AjaxResult getFullApiKey(@Parameter(description = "密钥ID") @PathVariable Long keyId) {
        final Long userId = PortalSecurityUtils.getUserId();
        try {
            String fullApiKey = userApiKeyService.getFullApiKey(keyId, userId);
            return AjaxResult.success("获取成功", fullApiKey);
        } catch (RuntimeException e) {
            return AjaxResult.error(e.getMessage());
        }
    }
}
