package org.biosino.lf.plosp.portal.es.mapper;

import org.biosino.lf.pds.article.domain.Article;
import org.biosino.lf.plosp.portal.es.entity.PlospArticleEs;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Article与PlospArticleEs之间的映射器
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Mapper(componentModel = "spring")
public interface ArticleEsMapper {

    ArticleEsMapper INSTANCE = Mappers.getMapper(ArticleEsMapper.class);

    /**
     * Article转换为PlospArticleEs
     */

    @Mapping(target = "publishedDate", source = ".", qualifiedByName = "combinePublishedDateAsInteger")
    @Mapping(target = "language", source = "language", qualifiedByName = "splitLanguage")
    @Mapping(target = "free", source = "free")
    @Mapping(target = "hasPdf", source = "hasPdf")
    @Mapping(target = "journalName", source = "journalName")
    @Mapping(target = "publisherName", source = "publisherName")
    @Mapping(target = "impactFactor", source = "impactFactor")
    @Mapping(target = "jcr", source = "jcr")
    @Mapping(target = "largeCategorySection", source = "largeCategorySection")
    @Mapping(target = "meshUi", ignore = true)
    @Mapping(target = "mesh", ignore = true)
    @Mapping(target = "titleVector", ignore = true)
    @Mapping(target = "abstractVectors", ignore = true)
    PlospArticleEs articleToEsArticle(Article article);

    /**
     * 批量转换 - 使用与单个转换相同的映射规则
     */
    @Mapping(target = "publishedDate", source = ".", qualifiedByName = "combinePublishedDateAsInteger")
    @Mapping(target = "language", source = "language", qualifiedByName = "splitLanguage")
    @Mapping(target = "free", source = "free")
    @Mapping(target = "hasPdf", source = "hasPdf")
    @Mapping(target = "journalName", source = "journalName")
    @Mapping(target = "publisherName", source = "publisherName")
    @Mapping(target = "impactFactor", source = "impactFactor")
    @Mapping(target = "jcr", source = "jcr")
    @Mapping(target = "largeCategorySection", source = "largeCategorySection")
    List<PlospArticleEs> articlesToEsArticles(List<Article> articles);

    /**
     * 组合年月日字段为8位整数日期格式 (YYYYMMDD)
     * 月份和日期默认为00
     */
    @Named("combinePublishedDateAsInteger")
    default Integer combinePublishedDateAsInteger(Article article) {
        Integer year = article.getPublishedYear();
        Integer month = article.getPublishedMonth();
        Integer day = article.getPublishedDay();

        if (year == null) {
            return null;
        }

        // 默认值：月份为00，日期为00
        int finalMonth = (month != null && month >= 1 && month <= 12) ? month : 0;
        int finalDay = (day != null && day >= 1 && day <= 31) ? day : 0;

        // 直接构建8位整数：YYYYMMDD，不进行日期有效性验证
        // 因为月份和日期可以为00
        return year * 10000 + finalMonth * 100 + finalDay;
    }

    @Named("splitLanguage")
    default List<String> splitLanguage(String language){
        if(language == null || language.trim().isEmpty()){
            return new ArrayList<>();
        }

        String[] parts = language.trim().split("\\s+");
        return Arrays.stream(parts).filter(part -> !part.trim().isEmpty()).collect(Collectors.toList());
    }

}
