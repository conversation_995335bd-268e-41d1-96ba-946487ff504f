package org.biosino.lf.plosp.portal.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FavoriteDocVO {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long folderId;
    private List<String> source;
    private String title;
    private List<String> authors;
    private String description;
    private String journal;
    private Integer year;
    private String volume;
    private String issue;
    private String page;
    private String doi;
    private Long pmid;
    private Date collectTime;
}
