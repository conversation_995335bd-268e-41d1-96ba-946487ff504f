package org.biosino.lf.plosp.portal.es.mapper;

import org.biosino.lf.plosp.portal.es.entity.PlospArticleEs;
import org.biosino.lf.plosp.portal.web.vo.ArticleListVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * PlospArticleEs与ArticleListVO之间的映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface PlospArticleVoMapper {

    PlospArticleVoMapper INSTANCE = Mappers.getMapper(PlospArticleVoMapper.class);

    /**
     * PlospArticleEs转换为ArticleListVO
     */
    @Mapping(target = "publishedDate", source = "publishedDate")
    @Mapping(target = "free", source = "free")
    @Mapping(target = "hasPdf", source = "hasPdf")
    @Mapping(target = "journalName", source = "journalName")
    @Mapping(target = "publisherName", source = "publisherName")
    @Mapping(target = "impactFactor", source = "impactFactor")
    @Mapping(target = "jcr", source = "jcr")
    @Mapping(target = "largeCategorySection", source = "largeCategorySection")
    @Mapping(target = "publishedYear", ignore = true)
    @Mapping(target = "publishedMonth", ignore = true)
    @Mapping(target = "publishedDay", ignore = true)
    ArticleListVO esArticleToVo(PlospArticleEs esArticle);

    /**
     * 批量转换
     */
    List<ArticleListVO> esArticlesToVos(List<PlospArticleEs> esArticles);


}
