package org.biosino.lf.plosp.portal.web.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ApiParamDTO {
    /**
     * 认证token
     */
    private String token;

    /**
     * 操作类型
     */
    private String type;

    /**
     * 增量更新的起始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    /**
     * 需要更新的文章ID列表
     */
    private List<Long> articleIds;

    /**
     * 增量更新时间范围（小时）
     */
    private Integer hours;
}
