package org.biosino.lf.plosp.portal.web.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.plosp.portal.web.service.HomeService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * 异步定时任务服务
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HomeCacheScheduler {
    private final HomeService homeService;

    /**
     * 每小时异步刷新首页缓存
     * 使用 @Async 确保异步执行，不阻塞主线程
     */
    @Async("homeCacheTaskExecutor")
    @Scheduled(cron = "0 0 * * * ?")
    public CompletableFuture<Void> refreshCacheHourly(){
        return CompletableFuture.runAsync(() -> {
            try {
                log.info("开始异步刷新首页缓存（每小时）");
                long startTime = System.currentTimeMillis();
                // 执行刷新逻辑
                homeService.refreshHomeCache();
                long endTime = System.currentTimeMillis();
                log.info("首页异步缓存刷新完成，耗时：{}ms",endTime - startTime);
            } catch (Exception e){
                log.error("首页异步缓存刷新失败",e);
            }
        });
    }


}
