package org.biosino.lf.plosp.portal.web.service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.biosino.lf.pds.common.core.domain.R;
import org.biosino.lf.pds.common.core.page.PageDomain;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.plosp.portal.dto.ArticleTransmitSubmitDTO;
import org.biosino.lf.pds.article.custbean.dto.TransmitListQueryDTO;

/**
 * 文献传递服务层
 *
 * <AUTHOR>
 */
public interface ArticleTransmitService {
    R<String> articleTransmit(ArticleTransmitSubmitDTO transmitDTO);

    TableDataInfo transmitList(TransmitListQueryDTO dto, long userId, PageDomain pageDomain);

    void downloadArticlePDF(Long docId, Long userId, HttpServletRequest request, HttpServletResponse response);
}
