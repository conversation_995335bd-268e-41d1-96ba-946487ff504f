package org.biosino.lf.plosp.portal.web.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 筛选选项VO - 包含选项值和对应的文档数量
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FilterOptionVO {
    
    /**
     * 选项值
     */
    private String value;
    
    /**
     * 该选项对应的文档数量
     */
    private Long count;
    
    /**
     * 显示标签（可选，用于国际化或友好显示）
     */
    private String label;
    
    public FilterOptionVO(String value, Long count) {
        this.value = value;
        this.count = count;
        this.label = value; // 默认使用value作为label
    }
}
