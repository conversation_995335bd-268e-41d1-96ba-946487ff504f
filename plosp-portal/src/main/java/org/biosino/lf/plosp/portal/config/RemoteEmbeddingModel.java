package org.biosino.lf.plosp.portal.config;

import org.biosino.lf.plosp.portal.es.service.RemoteEmbeddingService;

/**
 * 基于远程服务的向量化模型包装类
 * 简单包装，不实现复杂的 Spring AI 接口
 * <AUTHOR>
 */
public class RemoteEmbeddingModel {
    
    private final RemoteEmbeddingService remoteEmbeddingService;
    
    public RemoteEmbeddingModel(RemoteEmbeddingService remoteEmbeddingService) {
        this.remoteEmbeddingService = remoteEmbeddingService;
    }
    
    /**
     * 单个文本向量化，返回 float[]
     */
    public float[] embed(String text) {
        return remoteEmbeddingService.embed(text);
    }
}
