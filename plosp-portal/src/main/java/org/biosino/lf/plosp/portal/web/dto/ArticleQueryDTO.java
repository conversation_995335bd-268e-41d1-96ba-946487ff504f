package org.biosino.lf.plosp.portal.web.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.lf.pds.common.core.domain.BaseQuery;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ArticleQueryDTO extends BaseQuery {

    /**
     * 高级检索字符串，如：(cancer[Title]) AND (smith[Author])
     */
    private String query;

    private String sortKey;

    // ========== 以下字段来自 ArticleFilterDTO，用于过滤功能 ==========

    /**
     * 时间范围类型
     * recent_1year - 最近1年
     * recent_5years - 最近5年
     * recent_10years - 最近10年
     * custom - 自定义范围
     */
    private String timeRange;

    /**
     * 自定义开始日期（YYYYMMDD格式，当timeRange为custom时使用）
     */
    private Integer startDate;

    /**
     * 自定义结束日期（YYYYMMDD格式，当timeRange为custom时使用）
     */
    private Integer endDate;

    /**
     * 是否有摘要筛选
     * true - 只显示有摘要的文献
     */
    private Boolean hasAbstract;

    /**
     * 是否免费筛选
     * true - 只显示免费文献 (free=true)
     */
    private Boolean isFree;

    /**
     * 是否有全文PDF筛选
     * true - 只显示有PDF的文献 (hasPdf=true)
     */
    private Boolean hasPdf;

    /**
     * 是否有相关数据筛选
     * true - 只显示有相关数据的文献 (databank字段存在)
     */
    private Boolean hasDatabank;

    /**
     * 排序方式
     * latest - 按发布时间降序
     * relevance - 按相关性排序（默认）
     */
    private String sortBy = "relevance";

    /**
     * 文献类型筛选
     * 可传递多个pubtype值，如：["Journal Article", "Review", "Research Support"]
     */
    private List<String> pubtypes;

    /**
     * 语言筛选
     * 可传递多个语言值，如：["英语", "中文", "法语"]
     */
    private List<String> languages;

    /**
     * 基金支持筛选
     * 可选值：["有基金支持", "无基金支持"]
     * 可以选择一个或两个
     */
    private List<String> grantSupport;

    /**
     * 影响因子范围筛选
     * 可选值：["10以上", "5-10", "3-5", "3以下"]
     * 可以选择多个，使用OR逻辑
     */
    private List<String> impactFactorRanges;

    /**
     * JCR分区筛选
     * 可选值：["Q1", "Q2", "Q3", "Q4"]
     * 可以选择多个，使用OR逻辑
     */
    private List<String> jcrQuartiles;

    /**
     * 中科院分区筛选
     * 可选值：["1", "2", "3", "4"]
     * 可以选择多个，使用OR逻辑
     */
    private List<String> largeCategorySections;

    /**
     * 排除预印本
     * true - 排除source值为BioRxiv和MedRxiv的数据
     */
    private Boolean excludePreprints;

    /**
     * PubMed Central收录
     * true - 只显示source值包含PMC的数据
     */
    private Boolean pmcOnly;
}
