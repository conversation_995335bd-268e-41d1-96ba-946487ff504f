package org.biosino.lf.plosp.portal.web.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.PlospUser;
import org.biosino.lf.pds.article.service.IPlospUserService;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.StringUtils;
import org.biosino.lf.plosp.portal.domain.PortalLoginUser;
import org.springframework.context.annotation.Primary;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

/**
 * 门户用户详细信息服务
 * 使用电子邮件作为用户名进行门户身份验证的自定义UserDetails服务
 */
@Slf4j
@Service("portalUserDetailsService")
@Primary
@RequiredArgsConstructor
public class PortalUserDetailsService implements UserDetailsService {

    private final IPlospUserService userService;

    @Override
    public UserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        PlospUser user = userService.selectUserByEmail(email);

        if (StringUtils.isNull(user)) {
            log.info("Portal login user: {} does not exist.", email);
            throw new UsernameNotFoundException("用户不存在");
        }

        if (user.getStatus() != null && user.getStatus() == 0) {
            log.info("Portal login user: {} has been disabled.", email);
            throw new ServiceException("用户已被禁用");
        }

        return createLoginUser(user);
    }

    /**
     * 从PlospUser创建登录用户
     */
    public UserDetails createLoginUser(PlospUser plospUser) {
        // 门户用户具有基本权限
        Set<String> permissions = new HashSet<>();
        permissions.add("portal:user:view");
        permissions.add("portal:user:edit");

        return new PortalLoginUser(plospUser.getUserId(), plospUser, permissions);
    }


}
