package org.biosino.lf.plosp.portal.es.entity;

/**
 * 处理结果统计类
 * <AUTHOR>
 */
public class ProcessResult {
    private static int processedCount = 0;
    private static int successCount = 0;
    private static int failureCount = 0;

    public void incrementSuccess() {
        successCount++;
    }

    public void incrementFailure() {
        failureCount++;
    }

    public int getProcessedCount() {
        return processedCount;
    }

    public void setProcessedCount(int processedCount) {
        this.processedCount = processedCount;
    }

    public int getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(int successCount) {
        this.successCount = successCount;
    }

    public int getFailureCount() {
        return failureCount;
    }

    public void setFailureCount(int failureCount) {
        this.failureCount = failureCount;
    }
}
