package org.biosino.lf.plosp.portal.es.service;

import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregate;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregation;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsAggregate;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsBucket;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.plosp.portal.config.RemoteEmbeddingModel;
import org.biosino.lf.plosp.portal.es.entity.PlospArticleEs;
import org.biosino.lf.plosp.portal.es.mapper.PlospArticleVoMapper;
import org.biosino.lf.plosp.portal.es.parser.AdvancedSearchParser;
import org.biosino.lf.plosp.portal.web.dto.ArticleQueryDTO;
import org.biosino.lf.plosp.portal.web.vo.ArticleListVO;
import org.biosino.lf.plosp.portal.web.vo.FilterOptionVO;
import org.biosino.lf.plosp.portal.web.vo.KeywordSummaryVO;
import org.biosino.lf.plosp.portal.web.vo.UnifiedSearchResultVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchAggregation;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlospArticleService {

    private final ElasticsearchOperations elasticsearchOperations;
    private final RemoteEmbeddingModel remoteEmbeddingModel;
    private final PlospArticleVoMapper plospArticleVoMapper;
    private final EsService esService;
    private final AdvancedSearchParser advancedSearchParser;

    private static final int MAX_RESULT_WINDOW = 10000;

    /**
     * 统一搜索 - 包含智能搜索、聚合和过滤功能
     */
    public UnifiedSearchResultVO unifiedSearch(ArticleQueryDTO queryDTO) {
        // 检查分页深度是否超过ES限制
        Pageable pageable = queryDTO.getPageable();
        long from = pageable.getOffset();
        int size = pageable.getPageSize();

        if (from + size > MAX_RESULT_WINDOW) {
            throw new IllegalArgumentException(
                String.format("分页深度过大，无法获取超过第 %d 条的记录。", MAX_RESULT_WINDOW)
            );
        }

        try {
            // 1. 构建基础查询
            Query baseQuery = buildBaseQuery(queryDTO.getQuery());

            // 2. 构建过滤条件
            Query filterQuery = buildFilterQuery(queryDTO);

            // 3. 构建排序
            Sort sort = buildSortFromQueryDTO(queryDTO);

            // 4. 构建聚合
            Map<String, Aggregation> aggregations = buildFilterAggregations();

            // 5. 执行统一查询（同时获取搜索结果和聚合数据）
            NativeQueryBuilder queryBuilder = new NativeQueryBuilder()
                .withQuery(baseQuery)
                .withPageable(queryDTO.getPageable())
                .withSort(sort)
                .withTrackTotalHits(true);

            // 添加过滤条件
            if (filterQuery != null) {
                queryBuilder.withFilter(filterQuery);
            }

            // 直接添加聚合
            for (Map.Entry<String, Aggregation> entry : aggregations.entrySet()) {
                queryBuilder.withAggregation(entry.getKey(), entry.getValue());
            }

            NativeQuery searchQuery = queryBuilder.build();

            // 执行搜索
            SearchHits<PlospArticleEs> searchHits = elasticsearchOperations.search(searchQuery, PlospArticleEs.class);

            // 7. 处理搜索结果
            List<PlospArticleEs> esArticles = searchHits.getSearchHits().stream()
                .map(SearchHit::getContent)
                .toList();
            List<ArticleListVO> articles = plospArticleVoMapper.esArticlesToVos(esArticles);

            Page<ArticleListVO> page = new PageImpl<>(articles, queryDTO.getPageable(), searchHits.getTotalHits());
            TableDataInfo tableData = new TableDataInfo(page.getContent(), (int) page.getTotalElements());

            // 8. 处理聚合结果
            Map<String, List<FilterOptionVO>> filterOptions = extractAggregationResults(searchHits);

            return new UnifiedSearchResultVO(tableData, filterOptions);

        } catch (Exception e) {
            log.error("统一搜索失败", e);
            TableDataInfo emptyTableData = new TableDataInfo(List.of(), 0);
            Map<String, List<FilterOptionVO>> emptyFilterOptions = new HashMap<>();
            return new UnifiedSearchResultVO(emptyTableData, emptyFilterOptions);
        }
    }

    /**
     * 构建基础查询
     */
    private Query buildBaseQuery(String query) {
        // 处理空查询
        if (StrUtil.isBlank(query)) {
            return QueryBuilders.matchAll().build()._toQuery();
        } else {
            query = query.trim();
            // 判断查询类型
            boolean isAdvanced = isAdvancedSearchQuery(query);
            boolean hasAi = containsAiSearch(query);

            if (isAdvanced) {
                // 检查是否包含AI语义搜索
                if (hasAi) {
                    log.info("使用AI语义搜索");
                    // 提取AI搜索的关键词
                    String aiKeyword = extractAiKeyword(query);
                    return buildVectorQuery(aiKeyword);
                } else {
                    log.info("使用高级检索");
                    return buildAdvancedSearchQuery(query);
                }
            } else {
                log.info("使用智能全字段搜索");
                return buildUniversalQuery(query);
            }
        }
    }

    /**
     * 构建过滤条件
     */
    private Query buildFilterQuery(ArticleQueryDTO queryDTO) {
        List<Query> filterQueries = new ArrayList<>();

        // 添加时间范围过滤
        if (StrUtil.isNotBlank(queryDTO.getTimeRange()) ||
            queryDTO.getStartDate() != null ||
            queryDTO.getEndDate() != null) {

            Query timeRangeQuery;
            if (StrUtil.isNotBlank(queryDTO.getTimeRange())) {
                // 使用预设时间范围或自定义范围
                if ("custom".equals(queryDTO.getTimeRange())) {
                    timeRangeQuery = buildCustomDateRangeQuery(queryDTO.getStartDate(), queryDTO.getEndDate());
                } else {
                    int[] dateRange = convertTimeRangeToDateInt(queryDTO.getTimeRange());
                    if (dateRange != null) {
                        timeRangeQuery = QueryBuilders.range(r -> r
                            .number(n -> n
                                .field("published_date")
                                .gte((double) dateRange[0])
                                .lte((double) dateRange[1])
                            )
                        );
                    } else {
                        timeRangeQuery = null;
                    }
                }
            } else {
                // 直接使用自定义日期范围（没有指定timeRange）
                timeRangeQuery = buildCustomDateRangeQuery(queryDTO.getStartDate(), queryDTO.getEndDate());
            }

            if (timeRangeQuery != null) {
                filterQueries.add(timeRangeQuery);
            }
        }

        List<Query> conditions = new ArrayList<>();

        if (queryDTO.getHasAbstract() != null && queryDTO.getHasAbstract()) {
            conditions.add(QueryBuilders.exists(e -> e.field("abstract")));
        }
        if (queryDTO.getIsFree() != null && queryDTO.getIsFree()) {
            conditions.add(QueryBuilders.term(t -> t.field("free").value(true)));
        }
        if (queryDTO.getHasPdf() != null && queryDTO.getHasPdf()) {
            conditions.add(QueryBuilders.term(t -> t.field("hasPdf").value(true)));
        }

        if (!conditions.isEmpty()) {
            filterQueries.add(QueryBuilders.bool(b -> b.should(conditions).minimumShouldMatch("1")));
        }

        // 添加databank过滤
        if (queryDTO.getHasDatabank() != null && queryDTO.getHasDatabank()) {
            Query databankQuery = QueryBuilders.exists(e -> e.field("databank"));
            filterQueries.add(databankQuery);
        }

        // 添加文献类型过滤 - AND逻辑：必须同时包含所有选择的类型
        if (queryDTO.getPubtypes() != null && !queryDTO.getPubtypes().isEmpty()) {
            // 为每个pub_type创建单独的term查询，然后用should组合（OR逻辑）
            List<Query> pubtypeQueries = queryDTO.getPubtypes().stream()
                .map(pubtype -> QueryBuilders.term(t -> t
                    .field("pub_type")
                    .value(FieldValue.of(pubtype))
                ))
                .toList();

            if (pubtypeQueries.size() == 1) {
                // 只有一个类型，直接添加
                filterQueries.add(pubtypeQueries.get(0));
            } else {
                // 多个类型，使用bool查询的should子句（OR逻辑）
                Query pubtypeAndQuery = QueryBuilders.bool(b -> b.should(pubtypeQueries));
                filterQueries.add(pubtypeAndQuery);
            }
        }

        // 添加语言过滤 - 混合逻辑：单选用包含，多选用or
        if (queryDTO.getLanguages() != null && !queryDTO.getLanguages().isEmpty()) {
            if (queryDTO.getLanguages().size() == 1) {
                // 单选：包含这种语言即可
                Query languageQuery = QueryBuilders.term(t -> t
                    .field("language")
                    .value(queryDTO.getLanguages().get(0))
                );
                filterQueries.add(languageQuery);
            } else {
                // 多选：必须同时包含所有选择的语言（or逻辑）
                List<Query> languageQueries = queryDTO.getLanguages().stream()
                    .map(language -> QueryBuilders.term(t -> t
                        .field("language")
                        .value(language)
                    ))
                    .toList();

                Query languageAndQuery = QueryBuilders.bool(b -> b.should(languageQueries));
                filterQueries.add(languageAndQuery);
            }
        }

        // 添加基金支持过滤
        if (queryDTO.getGrantSupport() != null && !queryDTO.getGrantSupport().isEmpty()) {
            List<Query> grantQueries = new ArrayList<>();

            for (String grantType : queryDTO.getGrantSupport()) {
                if ("有基金支持".equals(grantType)) {
                    // 有基金的文献
                    grantQueries.add(QueryBuilders.exists(e -> e.field("grant")));
                } else if ("无基金支持".equals(grantType)) {
                    // 无基金的文献
                    grantQueries.add(QueryBuilders.bool(b -> b.mustNot(QueryBuilders.exists(e -> e.field("grant")))));
                }
            }

            if (!grantQueries.isEmpty()) {
                if (grantQueries.size() == 1) {
                    // 只选择了一种类型
                    filterQueries.add(grantQueries.get(0));
                } else {
                    // 选择了两种类型，使用OR逻辑（有基金 OR 无基金 = 所有文献）
                    Query grantOrQuery = QueryBuilders.bool(b -> b.should(grantQueries).minimumShouldMatch(String.valueOf(1)));
                    filterQueries.add(grantOrQuery);
                }
            }
        }

        // 添加影响因子范围过滤 - OR逻辑：选择多个范围时使用OR
        if (queryDTO.getImpactFactorRanges() != null && !queryDTO.getImpactFactorRanges().isEmpty()) {
            List<Query> impactFactorQueries = new ArrayList<>();

            for (String range : queryDTO.getImpactFactorRanges()) {
                Query rangeQuery = buildImpactFactorRangeQuery(range);
                if (rangeQuery != null) {
                    impactFactorQueries.add(rangeQuery);
                }
            }

            if (!impactFactorQueries.isEmpty()) {
                if (impactFactorQueries.size() == 1) {
                    // 只选择了一个范围
                    filterQueries.add(impactFactorQueries.get(0));
                } else {
                    // 选择了多个范围，使用OR逻辑
                    Query impactFactorOrQuery = QueryBuilders.bool(b -> b
                        .should(impactFactorQueries)
                        .minimumShouldMatch("1"));
                    filterQueries.add(impactFactorOrQuery);
                }
            }
        }

        // 添加JCR分区过滤 - OR逻辑：选择多个分区时使用OR
        if (queryDTO.getJcrQuartiles() != null && !queryDTO.getJcrQuartiles().isEmpty()) {
            List<Query> jcrQueries = queryDTO.getJcrQuartiles().stream()
                .map(jcr -> QueryBuilders.term(t -> t
                    .field("jcr")
                    .value(FieldValue.of(jcr))
                ))
                .toList();

            if (jcrQueries.size() == 1) {
                // 只选择了一个JCR分区
                filterQueries.add(jcrQueries.get(0));
            } else {
                // 选择了多个JCR分区，使用OR逻辑
                Query jcrOrQuery = QueryBuilders.bool(b -> b
                    .should(jcrQueries)
                    .minimumShouldMatch("1"));
                filterQueries.add(jcrOrQuery);
            }
        }

        // 添加中科院分区过滤 - OR逻辑：选择多个分区时使用OR
        if (queryDTO.getLargeCategorySections() != null && !queryDTO.getLargeCategorySections().isEmpty()) {
            List<Query> largeCategorySectionQueries = queryDTO.getLargeCategorySections().stream()
                .map(section -> {
                    try {
                        // 中科院分区是integer类型，需要转换
                        int sectionInt = Integer.parseInt(section);
                        return QueryBuilders.term(t -> t
                            .field("large_category_section")
                            .value(sectionInt)
                        );
                    } catch (NumberFormatException e) {
                        log.warn("中科院分区格式错误: {}", section);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .toList();

            if (!largeCategorySectionQueries.isEmpty()) {
                if (largeCategorySectionQueries.size() == 1) {
                    // 只选择了一个中科院分区
                    filterQueries.add(largeCategorySectionQueries.get(0));
                } else {
                    // 选择了多个中科院分区，使用OR逻辑
                    Query largeCategorySectionOrQuery = QueryBuilders.bool(b -> b
                        .should(largeCategorySectionQueries)
                        .minimumShouldMatch("1"));
                    filterQueries.add(largeCategorySectionOrQuery);
                }
            }
        }

        // 添加排除预印本过滤
        if (queryDTO.getExcludePreprints() != null && queryDTO.getExcludePreprints()) {
            // 排除source值包含BioRxiv和MedRxiv的数据
            List<Query> preprintQueries = List.of(
                QueryBuilders.term(t -> t.field("source").value(FieldValue.of("BioRxiv"))),
                QueryBuilders.term(t -> t.field("source").value(FieldValue.of("MedRxiv")))
            );

            Query excludePreprintsQuery = QueryBuilders.bool(b -> b
                .mustNot(preprintQueries)
            );
            filterQueries.add(excludePreprintsQuery);
            log.info("添加排除预印本过滤: 排除包含BioRxiv和MedRxiv的文献");
        }

        // 添加PubMed Central收录过滤
        if (queryDTO.getPmcOnly() != null && queryDTO.getPmcOnly()) {
            // 只显示source值包含PMC的数据
            Query pmcOnlyQuery = QueryBuilders.term(t -> t
                .field("source")
                .value(FieldValue.of("PMC"))
            );
            filterQueries.add(pmcOnlyQuery);
            log.info("添加PubMed Central收录过滤: 只显示包含PMC的文献");
        }

        // 如果有过滤条件，组合成一个 bool 查询
        if (!filterQueries.isEmpty()) {
            return QueryBuilders.bool(b -> b.filter(filterQueries));
        } else {
            return null;
        }
    }



    /**
     * 构建排序（统一方法）
     */
    private Sort buildSortFromQueryDTO(ArticleQueryDTO queryDTO) {
        if (StrUtil.isNotBlank(queryDTO.getSortBy())) {
            return buildSortForFilter(queryDTO.getSortBy());
        } else {
            return Sort.by(Sort.Order.desc("_score"));
        }
    }

    /**
     * 提取聚合结果
     */
    private Map<String, List<FilterOptionVO>> extractAggregationResults(SearchHits<PlospArticleEs> searchHits) {
        Map<String, List<FilterOptionVO>> filterOptions = new HashMap<>();

        if (searchHits.hasAggregations()) {
            AggregationsContainer<?> aggregationsContainer = searchHits.getAggregations();

            if (aggregationsContainer != null) {
                // 解析各个字段的聚合结果（带计数）
                filterOptions.put("language", extractFilterOptionsWithCount(aggregationsContainer, "language_agg"));
                List<FilterOptionVO> pubtypeOptions = extractFilterOptionsWithCount(aggregationsContainer, "pubtype_agg");
                filterOptions.put("pubtype", pubtypeOptions);

                // free字段聚合 - 只统计免费文档的数量
                try {
                    List<FilterOptionVO> freeOptions = extractSimpleFreeOptionsWithCount(aggregationsContainer);
                    filterOptions.put("free", freeOptions);
                } catch (Exception e) {
                    log.error("处理free聚合失败", e);
                    filterOptions.put("free", new ArrayList<>());
                }

                // grant字段聚合 - 获取有基金和无基金的数量统计
                try {
                    List<FilterOptionVO> grantOptions = extractSimpleGrantOptionsWithCount(aggregationsContainer);
                    filterOptions.put("grant", grantOptions);
                } catch (Exception e) {
                    log.error("处理grant聚合失败", e);
                    filterOptions.put("grant", new ArrayList<>());
                }

                // abstract字段聚合 - 获取有摘要的数量统计
                try {
                    List<FilterOptionVO> abstractOptions = extractSimpleAbstractOptionsWithCount(aggregationsContainer);
                    filterOptions.put("abstract", abstractOptions);
                } catch (Exception e) {
                    filterOptions.put("abstract", new ArrayList<>());
                }

                // hasPdf字段聚合 - 获取有PDF的数量统计
                try {
                    List<FilterOptionVO> hasPdfOptions = extractSimpleHasPdfOptionsWithCount(aggregationsContainer);
                    filterOptions.put("hasPdf", hasPdfOptions);
                } catch (Exception e) {
                    filterOptions.put("hasPdf", new ArrayList<>());
                }

                // databank字段聚合 - 获取有databank的数量统计
                try {
                    List<FilterOptionVO> databankOptions = extractSimpleDatabankOptionsWithCount(aggregationsContainer);
                    filterOptions.put("databank", databankOptions);
                } catch (Exception e) {
                    filterOptions.put("databank", new ArrayList<>());
                }

                // 影响因子范围聚合 - 获取各个范围的数量统计
                try {
                    List<FilterOptionVO> impactFactorOptions = extractImpactFactorRangeOptionsWithCount(aggregationsContainer);
                    filterOptions.put("impactFactor", impactFactorOptions);
                } catch (Exception e) {
                    filterOptions.put("impactFactor", new ArrayList<>());
                }

                // JCR分区聚合 - 获取JCR分区选项及数量
                try {
                    List<FilterOptionVO> jcrOptions = extractFilterOptionsWithCount(aggregationsContainer, "jcr_agg");
                    filterOptions.put("jcr", jcrOptions);
                } catch (Exception e) {
                    filterOptions.put("jcr", new ArrayList<>());
                }

                // 中科院分区聚合 - 获取中科院分区选项及数量
                try {
                    List<FilterOptionVO> largeCategorySectionOptions = extractFilterOptionsWithCount(aggregationsContainer, "large_category_section_agg");
                    filterOptions.put("largeCategorySection", largeCategorySectionOptions);
                } catch (Exception e) {
                    filterOptions.put("largeCategorySection", new ArrayList<>());
                }
            } else {
                log.warn("聚合容器为空");
            }
        } else {
            log.warn("没有找到聚合结果");
        }

        return filterOptions;
    }



    /**
     * 判断是否为高级检索查询格式
     */
    private boolean isAdvancedSearchQuery(String query) {
        // 使用AdvancedSearchParser的验证方法
        return advancedSearchParser.isValidAdvancedQuery(query);
    }

    /**
     * 检查查询中是否包含AI语义搜索
     */
    private boolean containsAiSearch(String query) {
        // 检查是否包含 [AI] 或 [ai] 字段标识符
        // 修改正则表达式以支持嵌套括号
        return query.matches(".*\\[(?i)ai\\].*");
    }

    /**
     * 从查询中提取AI搜索的关键词
     */
    private String extractAiKeyword(String query) {
        try {
            // 简化方法：直接查找 [AI] 并提取前面的内容
            int aiIndex = query.toLowerCase().indexOf("[ai]");
            if (aiIndex == -1) {
                return query;
            }

            // 从 [AI] 位置向前查找最近的左括号
            int leftParen = -1;
            for (int i = aiIndex - 1; i >= 0; i--) {
                if (query.charAt(i) == '(') {
                    leftParen = i;
                    break;
                }
            }

            if (leftParen != -1) {
                String keyword = query.substring(leftParen + 1, aiIndex).trim();
                log.info("提取到AI搜索关键词: {}", keyword);
                return keyword;
            }

            log.warn("未能从查询中提取AI关键词: {}", query);
            return query;

        } catch (Exception e) {
            log.error("提取AI关键词失败: {}", query, e);
            return query;
        }
    }

    /**
     * 构建向量查询（纯语义搜索）
     */
    private Query buildVectorQuery(String queryText) {
        try {
            log.info("构建向量查询，查询文本: {}", queryText);

            // 生成查询向量（归一化）
            final List<Float> queryVector = EsService.normalizeVectorList(remoteEmbeddingModel, queryText);

            // 标题向量KNN查询
            Query knnTitleQuery = QueryBuilders.knn(fn -> fn
                    .field("title_vector")
                    .queryVector(queryVector)
                    .k(100)
                    .numCandidates(500)
                    .similarity(0.5f));

            // 摘要向量KNN查询（nested结构）
            Query nestedAbstractQuery = QueryBuilders.nested(fn -> fn
                    .path("abstract_vectors")
                    .query(QueryBuilders.knn(knn -> knn
                            .field("abstract_vectors.vector")
                            .queryVector(queryVector)
                            .k(100)
                            .numCandidates(500)
                            .similarity(0.3f)))
                    .scoreMode(ChildScoreMode.Max));

            // 组合查询：标题和摘要向量搜索
            Query combinedQuery = QueryBuilders.bool(bool -> bool
                    .should(knnTitleQuery)
                    .should(nestedAbstractQuery)
                    .minimumShouldMatch("1"));

            log.debug("KNN向量查询构建完成，查询向量维度: {}", queryVector.size());
            return combinedQuery;

        } catch (Exception e) {
            log.error("构建向量查询失败", e);
            // 向量搜索失败时返回空结果，不降级到关键词搜索
            throw new RuntimeException("向量搜索失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建高级检索查询
     */
    private Query buildAdvancedSearchQuery(String advancedQuery) {
        try {
            // 使用解析方法直接构建ES查询
            Query esQuery = advancedSearchParser.buildESQuery(advancedQuery);

            if (esQuery == null) {
                return QueryBuilders.matchAll().build()._toQuery();
            }

            return esQuery;

        } catch (Exception e) {
            log.error("构建高级检索查询失败: {}", advancedQuery, e);
            return QueryBuilders.matchAll().build()._toQuery();
        }
    }

    /**
     * 构建智能全字段查询
     */
    private Query buildUniversalQuery(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return QueryBuilders.matchAll().build()._toQuery();
        }

        String trimmedKeyword = keyword.trim();

        // 智能识别输入类型并构建相应查询
        if (isPmidFormat(trimmedKeyword)) {
            return buildPmidQuery(trimmedKeyword);
        } else if (isDoiFormat(trimmedKeyword)) {
            return buildDoiQuery(trimmedKeyword);
        } else if (isMeshUiFormat(trimmedKeyword)) {
            return buildMeshUiQuery(trimmedKeyword);
        } else if (isImpactFactorFormat(trimmedKeyword)) {
            return buildImpactFactorQuery(trimmedKeyword);
        } else if (isJcrQuartileFormat(trimmedKeyword)) {
            return buildJcrQuartileQuery(trimmedKeyword);
        } else if (isLargeCategorySectionFormat(trimmedKeyword)) {
            return buildLargeCategorySectionQuery(trimmedKeyword);
        } else if (isNumericId(trimmedKeyword)) {
            return buildNumericIdQuery(trimmedKeyword);
        } else {
            return buildMultiFieldQuery(trimmedKeyword);
        }
    }

    /**
     * 判断是否为PMID格式（纯数字，通常7-8位）
     */
    private boolean isPmidFormat(String keyword) {
        return keyword.matches("^\\d{7,9}$");
    }

    /**
     * 判断是否为DOI格式
     */
    private boolean isDoiFormat(String keyword) {
        return keyword.matches("^10\\.\\d{4,}/.*") || keyword.toLowerCase().startsWith("doi:");
    }

    /**
     * 判断是否为MeSH UI格式（如D000001, C000001等）
     */
    private boolean isMeshUiFormat(String keyword) {
        return keyword.matches("^[A-Z]\\d{6}$");
    }

    /**
     * 判断是否为影响因子格式（小数，通常0-100之间）
     */
    private boolean isImpactFactorFormat(String keyword) {
        try {
            float value = Float.parseFloat(keyword);
            return value >= 0 && value <= 100;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断是否为JCR分区格式（Q1, Q2, Q3, Q4）
     */
    private boolean isJcrQuartileFormat(String keyword) {
        return keyword.matches("^[Qq][1-4]$");
    }

    /**
     * 判断是否为中科院分区格式（1, 2, 3, 4）
     */
    private boolean isLargeCategorySectionFormat(String keyword) {
        return keyword.matches("^[1-4]$");
    }

    /**
     * 判断是否为数字ID（可能是publisher_id, journal_id等）
     */
    private boolean isNumericId(String keyword) {
        return keyword.matches("^\\d{1,6}$");
    }

    /**
     * 构建PMID查询
     */
    private Query buildPmidQuery(String pmid) {
        try {
            long pmidVal = Long.parseLong(pmid);
            return QueryBuilders.term(fn -> fn.field("pmid").value(pmidVal));
        } catch (NumberFormatException e) {
            return buildMultiFieldQuery(pmid);
        }
    }

    /**
     * 构建DOI查询
     */
    private Query buildDoiQuery(String doi) {
        // 清理DOI前缀
        String cleanDoi = doi.toLowerCase().startsWith("doi:") ? doi.substring(4) : doi;
        return QueryBuilders.term(fn -> fn.field("doi").value(cleanDoi));
    }

    /**
     * 构建MeSH UI查询
     */
    private Query buildMeshUiQuery(String meshUi) {
        return QueryBuilders.term(fn -> fn.field("mesh_ui").value(meshUi));
    }

    /**
     * 构建影响因子查询
     */
    private Query buildImpactFactorQuery(String impactFactor) {
        try {
            float impactFactorVal = Float.parseFloat(impactFactor);
            return QueryBuilders.term(fn -> fn.field("impact_factor").value(impactFactorVal));
        } catch (NumberFormatException e) {
            return buildMultiFieldQuery(impactFactor);
        }
    }

    /**
     * 构建JCR分区查询
     */
    private Query buildJcrQuartileQuery(String jcrQuartile) {
        // 统一转换为大写格式（Q1, Q2, Q3, Q4）
        String normalizedJcr = jcrQuartile.toUpperCase();
        return QueryBuilders.term(fn -> fn.field("jcr").value(normalizedJcr));
    }

    /**
     * 构建中科院分区查询
     */
    private Query buildLargeCategorySectionQuery(String section) {
        try {
            int sectionVal = Integer.parseInt(section);
            return QueryBuilders.term(fn -> fn.field("large_category_section").value(sectionVal));
        } catch (NumberFormatException e) {
            return buildMultiFieldQuery(section);
        }
    }

    /**
     * 构建数字ID查询（搜索publisher_id, journal_id等）
     */
    private Query buildNumericIdQuery(String numericId) {
        try {
            long idVal = Long.parseLong(numericId);
            return QueryBuilders.bool(fn -> fn
                .should(QueryBuilders.term(t -> t.field("pmid").value(idVal)))
                .minimumShouldMatch("1")
            );
        } catch (NumberFormatException e) {
            return buildMultiFieldQuery(numericId);
        }
    }

    /**
     * 构建多字段查询（核心方法）
     */
    private Query buildMultiFieldQuery(String keyword) {
        // 使用正确的字段名，避免数值字段的match查询
        return QueryBuilders.bool(fn -> fn
            .should(QueryBuilders.match(m -> m.field("title").query(keyword).boost(5.0f)))
            .should(QueryBuilders.match(m -> m.field("author").query(keyword).boost(3.0f)))
            .should(QueryBuilders.match(m -> m.field("keywords").query(keyword).boost(3.0f)))
            .should(QueryBuilders.match(m -> m.field("abstract").query(keyword).boost(2.0f)))
            .should(QueryBuilders.match(m -> m.field("affiliation").query(keyword).boost(2.0f)))
            .should(QueryBuilders.match(m -> m.field("mesh_ui").query(keyword).boost(2.0f)))
            .should(QueryBuilders.match(m -> m.field("jcr").query(keyword).boost(1.5f)))
            .should(QueryBuilders.match(m -> m.field("language").query(keyword).boost(1.0f)))
            .should(QueryBuilders.match(m -> m.field("grant").query(keyword).boost(1.0f)))
            .should(QueryBuilders.match(m -> m.field("source").query(keyword).boost(1.0f)))
            .should(QueryBuilders.match(t -> t.field("publisher_name").query(keyword).boost(2.0f)))
            .should(QueryBuilders.match(t -> t.field("journal_name").query(keyword).boost(2.0f)))
            .minimumShouldMatch("1")
        );
    }

    /**
     * 构建筛选字段的聚合查询
     */
    private Map<String, Aggregation> buildFilterAggregations() {
        Map<String, Aggregation> aggregations = new HashMap<>();

        // 语言聚合
        aggregations.put("language_agg",
            Aggregation.of(a -> a
                .terms(t -> t
                    .field("language")
                    .size(200)
                    .minDocCount(1)
                )
            )
        );

        // 免费状态聚合 - 只统计免费文档的数量
        aggregations.put("free_agg",
            Aggregation.of(a -> a
                .filter(f -> f.term(t -> t.field("free").value(true)))
            )
        );

        // 文献类型聚合
        aggregations.put("pubtype_agg",
            Aggregation.of(a -> a
                .terms(t -> t
                    .field("pub_type")
                    .size(50)
                    .minDocCount(1)
                )
            )
        );

        // 基金聚合 - 统计有基金的数量
        aggregations.put("grant_exists_agg",
            Aggregation.of(a -> a
                .filter(f -> f.exists(e -> e.field("grant")))
            )
        );

        // 基金聚合 - 统计无基金的数量
        aggregations.put("grant_not_exists_agg",
            Aggregation.of(a -> a
                .filter(f -> f.bool(b -> b.mustNot(QueryBuilders.exists(e -> e.field("grant")))))
            )
        );

        // 摘要聚合 - 只统计有摘要的数量
        aggregations.put("abstract_exists_agg",
            Aggregation.of(a -> a
                .filter(f -> f.exists(e -> e.field("abstract")))
            )
        );

        // PDF聚合 - 只统计有PDF的数量
        aggregations.put("hasPdf_exists_agg",
            Aggregation.of(a -> a
                .filter(f -> f.term(t -> t.field("hasPdf").value(true)))
            )
        );

        // databank聚合 - 只统计有databank的数量
        aggregations.put("databank_exists_agg",
            Aggregation.of(a -> a
                .filter(f -> f.exists(e -> e.field("databank")))
            )
        );

        // 影响因子范围聚合 - 按照现有模式创建多个filter聚合
        // 10以上
        aggregations.put("impact_factor_10_above_agg",
            Aggregation.of(a -> a
                .filter(f -> f
                    .range(r -> r
                        .number(n -> n
                            .field("impact_factor")
                            .gte(10.0)
                        )
                    )
                )
            )
        );

        // 5-10
        aggregations.put("impact_factor_5_to_10_agg",
            Aggregation.of(a -> a
                .filter(f -> f
                    .range(r -> r
                        .number(n -> n
                            .field("impact_factor")
                            .gte(5.0)
                            .lt(10.0)
                        )
                    )
                )
            )
        );

        // 3-5
        aggregations.put("impact_factor_3_to_5_agg",
            Aggregation.of(a -> a
                .filter(f -> f
                    .range(r -> r
                        .number(n -> n
                            .field("impact_factor")
                            .gte(3.0)
                            .lt(5.0)
                        )
                    )
                )
            )
        );

        // 3以下
        aggregations.put("impact_factor_below_3_agg",
            Aggregation.of(a -> a
                .filter(f -> f
                    .range(r -> r
                        .number(n -> n
                            .field("impact_factor")
                            .lt(3.0)
                        )
                    )
                )
            )
        );

        // JCR分区聚合
        aggregations.put("jcr_agg",
            Aggregation.of(a -> a
                .terms(t -> t
                    .field("jcr")
                    .size(20) // JCR分区通常不会太多
                    .minDocCount(1)
                )
            )
        );

        // 中科院分区聚合
        aggregations.put("large_category_section_agg",
            Aggregation.of(a -> a
                .terms(t -> t
                    .field("large_category_section")
                    .size(10) // 中科院分区通常是1-4区
                    .minDocCount(1)
                )
            )
        );

        return aggregations;
    }

    /**
     * 从聚合容器中提取带计数的筛选选项
     */
    private List<FilterOptionVO> extractFilterOptionsWithCount(AggregationsContainer<?> aggregationsContainer, String aggName) {
        try {

            // 使用反射获取聚合结果
            java.lang.reflect.Method getAggregationMethod = aggregationsContainer.getClass().getMethod("get", String.class);
            Object aggregation = getAggregationMethod.invoke(aggregationsContainer, aggName);

            if (aggregation != null) {

                // 提取带计数的词条
                return extractFilterOptionsFromObject(aggregation);
            } else {
                log.warn("带计数的聚合结果为空: {}", aggName);
            }

        } catch (Exception e) {
            log.warn("提取带计数的聚合结果失败: {}", aggName, e);
        }
        return new ArrayList<>();
    }

    /**
     * 从聚合对象中提取带计数的筛选选项
     */
    private List<FilterOptionVO> extractFilterOptionsFromObject(Object aggregation) {
        try {
            // 检查是否是ElasticsearchAggregation类型
            if (aggregation instanceof ElasticsearchAggregation esAgg) {

                // 获取原生的ES聚合对象
                Aggregate nativeAgg = esAgg.aggregation().getAggregate();

                // 处理Terms聚合
                if (nativeAgg.isSterms()) {
                    List<FilterOptionVO> rawOptions = nativeAgg.sterms().buckets().array().stream()
                            .filter(bucket -> StrUtil.isNotBlank(bucket.key().stringValue())
                                    && !"null".equalsIgnoreCase(bucket.key().stringValue()))
                            .map(bucket -> {
                                return new FilterOptionVO(
                                    bucket.key().stringValue(),
                                    bucket.docCount()
                                );
                            })
                            .toList();

                    // pubtype现在是List<String>类型，ES已自动展开聚合，直接返回结果
                    return rawOptions.stream()
                            .sorted((a, b) -> Long.compare(b.getCount(), a.getCount()))
                            .toList();

                } else if (nativeAgg.isLterms()) {
                    List<FilterOptionVO> rawOptions = nativeAgg.lterms().buckets().array().stream()
                            .filter(bucket -> StrUtil.isNotBlank(String.valueOf(bucket.key()))
                                    && !"null".equalsIgnoreCase(String.valueOf(bucket.key())))
                            .map(bucket -> {
                                return new FilterOptionVO(
                                    String.valueOf(bucket.key()),
                                    bucket.docCount()
                                );
                            })
                            .toList();

                    // pubtype现在是List<String>类型，ES已自动展开聚合，直接返回结果
                    return rawOptions.stream()
                            .sorted((a, b) -> Long.compare(b.getCount(), a.getCount()))
                            .toList();

                } else if (nativeAgg.isFilters()) {
                    // 处理filters聚合（用于基金有无状态）
                    return nativeAgg.filters().buckets().keyed().entrySet().stream()
                            .filter(entry -> entry.getValue().docCount() > 0)
                            .map(entry -> new FilterOptionVO(
                                entry.getKey(),
                                entry.getValue().docCount()
                            ))
                            .sorted((a, b) -> Long.compare(b.getCount(), a.getCount()))
                            .toList();

                } else {
                    log.warn("不支持的聚合类型: {}", nativeAgg._kind());
                }
            } else {
                log.warn("未知的聚合对象类型: {}", aggregation.getClass().getName());
            }

        } catch (Exception e) {
            log.error("从聚合对象提取带计数选项失败", e);
        }

        return new ArrayList<>();
    }

    /**
     * 从简单的filter聚合中提取带计数的grant选项 - 统计有基金和无基金的数量
     */
    private List<FilterOptionVO> extractSimpleGrantOptionsWithCount(AggregationsContainer<?> aggregationsContainer) {
        List<FilterOptionVO> options = new ArrayList<>();

        try {
            java.lang.reflect.Method getMethod = aggregationsContainer.getClass().getMethod("get", String.class);

            // 检查有基金的聚合
            Object existsAgg = getMethod.invoke(aggregationsContainer, "grant_exists_agg");
            if (existsAgg instanceof ElasticsearchAggregation esAgg) {
                Aggregate nativeAgg = esAgg.aggregation().getAggregate();

                if (nativeAgg.isFilter()) {
                    long hasGrantCount = nativeAgg.filter().docCount();
                    options.add(new FilterOptionVO("有基金支持", hasGrantCount));
                }
            }

            // 检查无基金的聚合
            Object notExistsAgg = getMethod.invoke(aggregationsContainer, "grant_not_exists_agg");
            if (notExistsAgg instanceof ElasticsearchAggregation esAgg) {
                Aggregate nativeAgg = esAgg.aggregation().getAggregate();

                if (nativeAgg.isFilter()) {
                    long noGrantCount = nativeAgg.filter().docCount();
                    options.add(new FilterOptionVO("无基金支持", noGrantCount));
                }
            }

        } catch (Exception e) {
            log.error("提取带计数的简单grant选项失败", e);
        }

        return options.stream()
                .sorted((a, b) -> Long.compare(b.getCount(), a.getCount()))
                .toList();
    }

    /**
     * 从简单的filter聚合中提取带计数的free选项 - 只统计免费文档的数量
     */
    private List<FilterOptionVO> extractSimpleFreeOptionsWithCount(AggregationsContainer<?> aggregationsContainer) {
        List<FilterOptionVO> options = new ArrayList<>();

        try {
            java.lang.reflect.Method getMethod = aggregationsContainer.getClass().getMethod("get", String.class);

            // 检查免费文档的聚合
            Object freeAgg = getMethod.invoke(aggregationsContainer, "free_agg");
            if (freeAgg instanceof ElasticsearchAggregation esAgg) {
                Aggregate nativeAgg = esAgg.aggregation().getAggregate();

                if (nativeAgg.isFilter()) {
                    long freeCount = nativeAgg.filter().docCount();

                    // 总是添加true选项，即使数量为0
                    options.add(new FilterOptionVO("true", freeCount));
                }
            }

        } catch (Exception e) {
            log.error("提取带计数的简单free选项失败", e);
        }

        return options.stream()
                .sorted((a, b) -> Long.compare(b.getCount(), a.getCount()))
                .toList();
    }

    /**
     * 从简单的filter聚合中提取带计数的abstract选项 - 只统计有摘要的数量
     */
    private List<FilterOptionVO> extractSimpleAbstractOptionsWithCount(AggregationsContainer<?> aggregationsContainer) {
        List<FilterOptionVO> options = new ArrayList<>();

        try {
            java.lang.reflect.Method getMethod = aggregationsContainer.getClass().getMethod("get", String.class);

            // 检查有摘要的聚合
            Object abstractAgg = getMethod.invoke(aggregationsContainer, "abstract_exists_agg");
            if (abstractAgg instanceof ElasticsearchAggregation esAgg) {
                Aggregate nativeAgg = esAgg.aggregation().getAggregate();

                if (nativeAgg.isFilter()) {
                    long abstractCount = nativeAgg.filter().docCount();

                    // 总是添加true选项，即使数量为0
                    options.add(new FilterOptionVO("true", abstractCount));
                }
            }

        } catch (Exception e) {
            log.error("提取带计数的简单abstract选项失败", e);
        }

        return options.stream()
                .sorted((a, b) -> Long.compare(b.getCount(), a.getCount()))
                .toList();
    }

    /**
     * 从简单的filter聚合中提取带计数的hasPdf选项 - 只统计有PDF的数量
     */
    private List<FilterOptionVO> extractSimpleHasPdfOptionsWithCount(AggregationsContainer<?> aggregationsContainer) {
        List<FilterOptionVO> options = new ArrayList<>();

        try {
            java.lang.reflect.Method getMethod = aggregationsContainer.getClass().getMethod("get", String.class);

            // 检查有PDF的聚合
            Object hasPdfAgg = getMethod.invoke(aggregationsContainer, "hasPdf_exists_agg");
            if (hasPdfAgg instanceof ElasticsearchAggregation esAgg) {
                Aggregate nativeAgg = esAgg.aggregation().getAggregate();

                if (nativeAgg.isFilter()) {
                    long hasPdfCount = nativeAgg.filter().docCount();

                    // 总是添加true选项，即使数量为0
                    options.add(new FilterOptionVO("true", hasPdfCount));
                }
            }

        } catch (Exception e) {
            log.error("提取带计数的简单hasPdf选项失败", e);
            // 即使出错也要返回基本选项
            options.add(new FilterOptionVO("true", 0L));
        }

        return options.stream()
                .sorted((a, b) -> Long.compare(b.getCount(), a.getCount()))
                .toList();
    }

    /**
     * 从简单的filter聚合中提取带计数的databank选项 - 只统计有databank的数量
     */
    private List<FilterOptionVO> extractSimpleDatabankOptionsWithCount(AggregationsContainer<?> aggregationsContainer) {
        List<FilterOptionVO> options = new ArrayList<>();

        try {
            java.lang.reflect.Method getMethod = aggregationsContainer.getClass().getMethod("get", String.class);

            // 检查有databank的聚合
            Object databankAgg = getMethod.invoke(aggregationsContainer, "databank_exists_agg");
            if (databankAgg instanceof ElasticsearchAggregation esAgg) {
                Aggregate nativeAgg = esAgg.aggregation().getAggregate();

                if (nativeAgg.isFilter()) {
                    long databankCount = nativeAgg.filter().docCount();

                    // 总是添加true选项，即使数量为0
                    options.add(new FilterOptionVO("true", databankCount));
                }
            }

        } catch (Exception e) {
            log.error("提取带计数的简单databank选项失败", e);
            // 即使出错也要返回基本选项
            options.add(new FilterOptionVO("true", 0L));
        }

        return options.stream()
                .sorted((a, b) -> Long.compare(b.getCount(), a.getCount()))
                .toList();
    }

    /**
     * 从多个filter聚合中提取带计数的影响因子范围选项
     */
    private List<FilterOptionVO> extractImpactFactorRangeOptionsWithCount(AggregationsContainer<?> aggregationsContainer) {
        List<FilterOptionVO> options = new ArrayList<>();

        try {
            java.lang.reflect.Method getMethod = aggregationsContainer.getClass().getMethod("get", String.class);

            // 按照指定的顺序检查各个范围的聚合结果
            String[][] rangeConfigs = {
                {"10以上", "impact_factor_10_above_agg"},
                {"5-10", "impact_factor_5_to_10_agg"},
                {"3-5", "impact_factor_3_to_5_agg"},
                {"3以下", "impact_factor_below_3_agg"}
            };

            for (String[] config : rangeConfigs) {
                String rangeName = config[0];
                String aggName = config[1];

                Object rangeAgg = getMethod.invoke(aggregationsContainer, aggName);
                if (rangeAgg instanceof ElasticsearchAggregation esAgg) {
                    Aggregate nativeAgg = esAgg.aggregation().getAggregate();

                    if (nativeAgg.isFilter()) {
                        long count = nativeAgg.filter().docCount();
                        // 即使count为0也要添加，前端需要显示完整的筛选选项
                        options.add(new FilterOptionVO(rangeName, count));
                    }
                }
            }

        } catch (Exception e) {
            log.error("提取影响因子范围聚合结果失败", e);
        }

        return options;
    }

    /**
     * 构建自定义日期范围查询
     * 支持只提供开始日期、只提供结束日期、或两个都提供
     */
    private Query buildCustomDateRangeQuery(Integer startDate, Integer endDate) {
        // 如果开始和结束日期都为空，返回匹配所有的查询
        if (startDate == null && endDate == null) {
            log.warn("自定义时间范围：开始日期和结束日期都为空");
            return QueryBuilders.matchAll(m -> m).term()._toQuery();
        }

        return RangeQuery.of(r -> r
            .number(n -> {
                n.field("published_date");

                // 如果有开始日期，设置gte
                if (startDate != null) {
                    // 验证日期格式
                    if (isValidDateFormat(startDate)) {
                        n.gte(Double.valueOf(startDate));
                        log.info("自定义时间范围：开始日期 >= {}", startDate);
                    } else {
                        log.warn("开始日期格式错误: {}", startDate);
                    }
                }

                // 如果有结束日期，设置lte
                if (endDate != null) {
                    // 验证日期格式
                    if (isValidDateFormat(endDate)) {
                        n.lte(Double.valueOf(endDate));
                        log.info("自定义时间范围：结束日期 <= {}", endDate);
                    } else {
                        log.warn("结束日期格式错误: {}", endDate);
                    }
                }

                return n;
            })
        )._toQuery();
    }

    /**
     * 验证8位数字日期格式 (YYYYMMDD)
     */
    private boolean isValidDateFormat(Integer dateInt) {
        if (dateInt == null) {
            return false;
        }

        String dateStr = String.valueOf(dateInt);

        // 必须是8位数字
        if (dateStr.length() != 8) {
            return false;
        }

        try {
            int year = Integer.parseInt(dateStr.substring(0, 4));
            int month = Integer.parseInt(dateStr.substring(4, 6));
            int day = Integer.parseInt(dateStr.substring(6, 8));

            // 基本范围检查
            if (year < 1900 || year > 2100) {
                return false;
            }
            if (month < 1 || month > 12) {
                return false;
            }
            if (day < 1 || day > 31) {
                return false;
            }

            // 尝试创建LocalDate来验证日期的有效性
            LocalDate.of(year, month, day);
            return true;

        } catch (Exception e) {
            log.warn("日期格式验证失败: {}", dateInt, e);
            return false;
        }
    }

    /**
     * 将时间范围转换为8位数日期格式 (YYYYMMDD)
     */
    private int[] convertTimeRangeToDateInt(String timeRange) {
        LocalDate now = LocalDate.now();
        LocalDate startDate;

        switch (timeRange) {
            case "recent_1year":
                startDate = now.minusYears(1);
                break;
            case "recent_5years":
                startDate = now.minusYears(5);
                break;
            case "recent_10years":
                startDate = now.minusYears(10);
                break;
            default:
                // 不限制时间
                log.warn("未知的时间范围类型: {}", timeRange);
                return new int[]{19700101, 99999999};
        }

        // 转换为YYYYMMDD格式
        int startDateInt = startDate.getYear() * 10000 +
                          startDate.getMonthValue() * 100 +
                          startDate.getDayOfMonth();
        int endDateInt = now.getYear() * 10000 +
                        now.getMonthValue() * 100 +
                        now.getDayOfMonth();

        return new int[]{startDateInt, endDateInt};
    }

    /**
     * 构建排序（用于过滤接口）
     */
    private Sort buildSortForFilter(String sortBy) {
        if ("latest".equals(sortBy)) {
            return Sort.by(Sort.Order.desc("published_date"), Sort.Order.desc("_score"));
        } else {
            // 默认按相关性排序
            return Sort.by(Sort.Order.desc("_score"));
        }
    }

    /**
     * 构建影响因子范围查询
     *
     * @param range 影响因子范围："10以上", "5-10", "3-5", "3以下"
     * @return 对应的ES查询
     */
    private Query buildImpactFactorRangeQuery(String range) {
        try {
            switch (range) {
                case "10以上" -> {
                    return RangeQuery.of(r -> r
                        .number(n -> {
                            n.field("impact_factor");
                            n.gte(10.0);
                            return n;
                        })
                    )._toQuery();
                }
                case "5-10" -> {
                    return RangeQuery.of(r -> r
                        .number(n -> {
                            n.field("impact_factor");
                            n.gte(5.0);
                            n.lt(10.0);
                            return n;
                        })
                    )._toQuery();
                }
                case "3-5" -> {
                    return RangeQuery.of(r -> r
                        .number(n -> {
                            n.field("impact_factor");
                            n.gte(3.0);
                            n.lt(5.0);
                            return n;
                        })
                    )._toQuery();
                }
                case "3以下" -> {
                    return RangeQuery.of(r -> r
                        .number(n -> {
                            n.field("impact_factor");
                            n.lt(3.0);
                            return n;
                        })
                    )._toQuery();
                }
                default -> {
                    return null;
                }
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * MeSH词汇自动完成
     * 委托给EsService处理，使用优化的搜索策略
     */
    public List<Map<String, String>> getMeshAutoComplete(String keyword) {
        try {
            return esService.searchMeshByName(keyword);
        } catch (Exception e) {
            log.error("MeSH自动完成搜索失败", e);
            return new ArrayList<>();
        }
    }

    public List<KeywordSummaryVO> getTopKeywords() {
        // 构建查询 + 聚合
        Query query = NativeQuery.builder()
                .withQuery(MatchAllQuery())
                .withAggregation("top_keywords",
                        terms("top_keywords")
                                .field("keywords.keyword") // 注意字段类型
                                .size(5))
                .build();

        // 执行查询
        SearchHits<PlospArticleEs> hits = elasticsearchTemplate.search(query, PlospArticleEs.class);

        // 解析聚合结果
        StringTermsAggregate terms = hits.getAggregations()
                .get("top_keywords")
                .aggregation()
                .getAggregate()
                .sterms();

        Map<String, Integer> result = new HashMap<>();
        for (StringTermsBucket bucket : terms.buckets().array()) {
            result.put(bucket.key().stringValue(), (int) bucket.docCount());
        }

        return result;
    }

}
