package org.biosino.lf.plosp.portal.web.vo;

import lombok.Data;
import org.springframework.data.domain.Page;

import java.io.Serializable;
import java.util.List;

/**
 * 文章搜索结果VO，包含搜索结果和聚合统计信息
 * 
 * <AUTHOR>
 */
@Data
public class ArticleSearchResultVO implements Serializable {

    /**
     * 搜索结果
     */
    private Page<ArticleListVO> articles;

    /**
     * 聚合统计信息
     */
    private AggregationInfo aggregations;

    @Data
    public static class AggregationInfo {

        /**
         * 发布时间范围统计
         */
        private DateRangeAggregation dateRange;

        /**
         * 可用文献统计
         */
        private AvailabilityAggregation availability;

        /**
         * 文献类型统计
         */
        private List<TypeCount> pubTypes;

        /**
         * 文章属性统计
         */
        private List<TypeCount> articleAttributes;

        /**
         * 语言统计
         */
        private List<TypeCount> languages;

        /**
         * 基金支持统计
         */
        private List<TypeCount> grants;
    }

    @Data
    public static class DateRangeAggregation {
        private Long lastYear;      // 最近1年
        private Long lastFiveYears; // 最近5年
        private Long lastTenYears;  // 最近10年
        private Long total;         // 总数
    }

    @Data
    public static class AvailabilityAggregation {
        private Long withAbstract;  // 摘要
        private Long freeFullText;  // 免费全文
        private Long hasPdf;        // 拥有全文
        private Long total;         // 总数
    }

    @Data
    public static class TypeCount {
        private String name;        // 类型名称
        private String value;       // 类型值
        private Long count;         // 数量
    }
}
