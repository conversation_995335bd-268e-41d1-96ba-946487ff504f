package org.biosino.lf.plosp.portal.web.service;

import org.biosino.lf.plosp.portal.web.dto.UserDocHistoryDTO;
import org.biosino.lf.plosp.portal.web.vo.UserDocHistoryVO;

import java.util.List;

/**
 * 浏览历史service层接口
 *
 * <AUTHOR>
 */
public interface IUserDocHistoryService {
    /**
     * 获取浏览历史记录
     */
    List<UserDocHistoryVO> listByUserId(UserDocHistoryDTO userDocHistoryDTO);

    /**
     * 新增浏览历史记录
     */
    void add(Long docId);

    /**
     * 删除浏览历史记录
     */
    void deleteByIds(Long[] ids);
}
