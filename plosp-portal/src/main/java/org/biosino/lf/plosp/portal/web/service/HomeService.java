package org.biosino.lf.plosp.portal.web.service;

import org.biosino.lf.pds.article.dto.JournalSummaryDTO;
import org.biosino.lf.plosp.portal.web.vo.KeywordSummaryVO;
import org.biosino.lf.plosp.portal.web.vo.ArticleSummaryVO;
import org.biosino.lf.plosp.portal.web.vo.HomeStatsVO;

import java.text.ParseException;
import java.util.List;

/**
 * 首页服务接口
 *
 * <AUTHOR>
 */
public interface HomeService {
    /**
     * 获取首页统计数据
     */
    HomeStatsVO getHomeStats();

    /**
     * 获取最受欢迎的5篇文献
     */
    List<ArticleSummaryVO> getPopularArticles() throws ParseException;

    /**
     * 获取最新的5篇文献失败
     */
    List<ArticleSummaryVO> getLastArticles() throws ParseException;

    /**
     * 获取最受欢迎的期刊
     */
    List<JournalSummaryDTO> getPopularJournals();

    /**
     * 获取文献高频关键词TOP 5
     */
    List<KeywordSummaryVO> getTopKeywords();

    /**
     * 异步刷新首页缓存
     */
    void refreshHomeCache();
}
