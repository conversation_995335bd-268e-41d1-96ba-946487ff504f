package org.biosino.lf.plosp.portal.web.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UserDocHistoryVO {
    /**
     * 主键id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 文献id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long docId;
    /**
     * pmid
     */
    private Long pmid;
    /**
     * doi
     */
    private String doi;

    /**
     * 文献标题
     */
    private String title;

    /**
     * 作者
     */
    private List<String> author;

    /**
     * 期刊标题
     */
    private String journalTitle;

    /**
     * 发表年
     */
    private Integer year;
    /**
     * 卷号
     */
    private String volume;

    /**
     * 期号
     */
    private String issue;

    /**
     * 页码
     */
    private String page;
    /**
     * 期刊Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long journalId;

    /**
     * 创建时间
     */
    private Date createTime;
}
