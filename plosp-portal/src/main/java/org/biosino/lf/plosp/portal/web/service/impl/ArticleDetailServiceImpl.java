package org.biosino.lf.plosp.portal.web.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.mapper.*;
import org.biosino.lf.pds.article.service.*;
import org.biosino.lf.plosp.portal.web.service.ArticleDetailService;
import org.biosino.lf.plosp.portal.web.vo.*;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleDetailServiceImpl implements ArticleDetailService {
    private final IArticleService articleService;
    private final JournalService journalService;
    private final IArticleGrantService articleGrantService;
    private final IGrantService grantService;
    private final IArticleDatabankService articleDatabankService;
    private final IArticleMeshService articleMeshService;
    private final IMeshService meshService;
    private final IPubTypeService pubTypeService;
    private final IArticlePubTypeService articlePubTypeService;
    private final TbDdsIfYearMapper ifYearMapper;
    private final TbDdsZkySectionMapper zkySectionMapper;
    private final OpenAiChatModel chatModel;
    private final ArticleInterpretationMapper articleInterpretationMapper;
    private final ReferenceMapper referenceMapper;
    private final ArticleMapper articleMapper;
    private final JournalMapper journalMapper;

    @Value("${spring.ai.openai.chat.options.model}")
    private String model;

    @Override
    public ArticleDetailVO getArticleDetail(String id) {
        Article article = findArticleById(id);
        if (article == null) {
            throw new RuntimeException("文章不存在");
        }

        ArticleDetailVO detailVO = new ArticleDetailVO();
        BeanUtils.copyProperties(article, detailVO);

        // 设置基本信息
        detailVO.setHitNum(article.getHitNum() != null ? article.getHitNum().intValue() : 0);
        detailVO.setDownload(article.getDownload() != null ? article.getDownload().intValue() : 0);

        // 设置来源信息
        detailVO.setSource(article.getSource());

        // 设置期刊信息
        detailVO.setJournalName(parseJournal(article));

        // 设置期刊后的内容
        detailVO.setJournalContent(parseJournalContent(article));

        // 设置作者信息
        detailVO.setAuthors(parseAuthors(article));

        // 设置机构信息
        detailVO.setAffiliation(parseAffiliations(article));

        // 设置关键词
        detailVO.setKeywords(article.getKeywords());

        // 设置发表月份
        detailVO.setPublishedMonth(parsePublishedMonth(article.getPublishedMonth()));

        // 设置文献分类
        detailVO.setPubTypes(parsePubTypes(article.getId()));

        // 设置基金资助
        detailVO.setGrants(parseGrants(article.getId()));

        // 设置关联数据
        detailVO.setDatabanks(parseDatabanks(article.getId()));

        // 设置MeSH主题词
        detailVO.setMeshTerms(parseMeshTerms(article.getId()));

        // 设置影响因子和JCR分区
        setImpactFactorInfo(detailVO, article.getJournalId());

        // 设置中科院分区
        setZkySectionInfo(detailVO, article.getJournalId());

        // 增加访问量
        incrementHitCount(article);

        return detailVO;
    }

    @Override
    public String getOrCreateInterpretation(Long docId) {
        // 1. 先查询数据库是否已有成功解读记录
        LambdaQueryWrapper<ArticleInterpretation> queryWrapper = Wrappers.<ArticleInterpretation>lambdaQuery()
                .eq(ArticleInterpretation::getDocId, docId)
                .eq(ArticleInterpretation::getStatus, 1)
                .orderByDesc(ArticleInterpretation::getCreateTime)
                .last("LIMIT 1");

        ArticleInterpretation existing = articleInterpretationMapper.selectOne(queryWrapper);
        if (existing != null) {
            return existing.getResult();
        }

        // 2. 获取论文摘要内容
        String abstractContent = getAbstractByDocId(docId);
        if (abstractContent == null || abstractContent.trim().isEmpty()) {
            return "无法获取论文摘要内容";
        }

        // 3. 调用DeepSeek-v3解读
        long startTime = System.currentTimeMillis();
        String result = callDeepSeekInterpretation(docId, abstractContent, startTime);

        return result;
    }

    @Override
    public ReferenceResult getReferences(Long docId, Integer limit) {
        LambdaQueryWrapper<Reference> wrapper = Wrappers.<Reference>lambdaQuery()
                .eq(Reference::getDocId, docId)
                .orderByAsc(Reference::getId);

        if (limit != null && limit > 0) {
            wrapper.last("LIMIT " + limit);
        }

        List<Reference> references = referenceMapper.selectList(wrapper);
        // 获取所有的pmid
        Set<Long> pmids = references.stream()
                .filter(ref -> StrUtil.isNotBlank(ref.getPmid()))
                .map(ref -> {
                    try {
                        return Long.parseLong(ref.getPmid());
                    } catch (NumberFormatException e) {
                        log.error(String.valueOf(e));
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 一次性查询所有相关的article数据和journal数据
        Map<Long, Article> articleMap = new HashMap<>();
        Map<Long, Journal> journalMap = new HashMap<>();

        if (!pmids.isEmpty()) {
            List<Article> articles = articleMapper.selectList(
                    Wrappers.<Article>lambdaQuery().in(Article::getPmid, pmids)
            );
            articleMap = articles.stream()
                    .collect(Collectors.toMap(Article::getPmid, article -> article));

            // 批量查询journal数据
            Set<Long> journalIds = articles.stream()
                    .map(Article::getJournalId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (!journalIds.isEmpty()) {
                List<Journal> journals = journalMapper.selectByIds(journalIds);
                journalMap = journals.stream()
                        .collect(Collectors.toMap(Journal::getId, journal -> journal));
            }
        }

        // 转换为VO，使用缓存的数据
        Map<Long, Article> finalArticleMap = articleMap;
        Map<Long, Journal> finalJournalMap = journalMap;
        List<ReferenceVO> referenceVO = references.stream()
                .map(ref -> convertToVO(ref, finalArticleMap, finalJournalMap))
                .collect(Collectors.toList());

        Long count = getReferenceCount(docId);
        ReferenceResult result = new ReferenceResult();
        result.setReferences(referenceVO);
        result.setCount(count);
        return result;
    }

    private Article findArticleById(String id) {
        LambdaQueryWrapper<Article> queryWrapper = Wrappers.<Article>lambdaQuery();

        if (id.toUpperCase().startsWith("PMC")) {
            // PMC ID格式：PMC + 数字
            String pmcIdStr = id.substring(3);
            if (StrUtil.isNumeric(pmcIdStr)) {
                Long pmcId = Long.parseLong(pmcIdStr);
                queryWrapper.eq(Article::getPmcId, pmcId);
            } else {
                return null;
            }
        } else if (id.matches("\\d+")) {
            // 纯数字，判断是自定义ID还是PMID
            Long numId = Long.parseLong(id);
            if (numId >= 800000000000L && numId < 802000000000L) {
                // 自定义ID范围
                queryWrapper.eq(Article::getCustomId, numId);
            } else if (numId.toString().length() > 18) {
                // 主键ID
                queryWrapper.eq(Article::getId, numId);
            } else {
                // PMID
                queryWrapper.eq(Article::getPmid, numId);
            }
        } else {
            // 无法识别的ID格式
            return null;
        }

        return articleMapper.findOne(queryWrapper);
    }

    private String parseJournal(Article article) {
        if (article.getJournalId() != null) {
            Journal journal = journalService.getById(article.getJournalId());
            return journal != null ? journal.getTitle() : null;
        }

        return null;
    }

    private String parseJournalContent(Article article) {
        // 格式为：year published_month;volume(issue):page
        StringBuilder content = new StringBuilder();

        if (article.getPublishedYear() != null) {
            content.append(article.getPublishedYear()).append(" ");
        }

        String monthStr = parsePublishedMonth(article.getPublishedMonth());
        if (StrUtil.isNotBlank(monthStr)) {
            content.append(monthStr).append(";");
        }

        if (StrUtil.isNotBlank(article.getVolume())) {
            content.append(article.getVolume());
        }

        if (StrUtil.isNotBlank(article.getIssue())) {
            content.append("(").append(article.getIssue()).append(")");
        }

        if (StrUtil.isNotBlank(article.getPage())) {
            content.append(":").append(article.getPage());
        }

        return content.toString();
    }

    private List<AuthorVO> parseAuthors(Article article) {
        // 优先使用authorInfo字段，如果为空则使用author字段
        if (article.getAuthorInfo() != null && !article.getAuthorInfo().isEmpty()) {
            // 构建机构到编号的映射
            Map<String, Integer> orgToIndexMap = new HashMap<>();
            int orgIndex = 1;

            // 收集所有不重复的机构
            for (AuthorInfo authorInfo : article.getAuthorInfo()) {
                if (authorInfo.getOrganizations() != null) {
                    for (String org : authorInfo.getOrganizations()) {
                        if (!orgToIndexMap.containsKey(org)) {
                            orgToIndexMap.put(org, orgIndex++);
                        }
                    }
                }
            }

            return article.getAuthorInfo().stream()
                    .map(authorInfo -> {
                        AuthorVO authorVO = new AuthorVO();

                        // 合并forename和lastname为完整姓名
                        String fullName = buildFullName(authorInfo.getForename(), authorInfo.getLastname());
                        authorVO.setName(fullName);

                        // 根据机构设置index（取第一个机构的编号）
                        if (authorInfo.getOrganizations() != null && !authorInfo.getOrganizations().isEmpty()) {
                            String firstOrg = authorInfo.getOrganizations().get(0);
                            authorVO.setIndex(orgToIndexMap.get(firstOrg));
                        }

                        authorVO.setIsCorresponding("yes".equalsIgnoreCase(authorInfo.getAuthorCorrespond()));

                        return authorVO;
                    })
                    .collect(Collectors.toList());
        }

        // 如果authorInfo为空，使用author字段（无机构信息）
        if (article.getAuthor() != null && !article.getAuthor().isEmpty()) {
            return article.getAuthor().stream()
                    .map(authorName -> {
                        AuthorVO authorVO = new AuthorVO();
                        authorVO.setName(authorName);
                        return authorVO;
                    })
                    .collect(Collectors.toList());
        }

        return null;
    }

    private List<String> parseAffiliations(Article article) {
        // 优先从authorInfo中获取机构信息
        if (article.getAuthorInfo() != null && !article.getAuthorInfo().isEmpty()) {
            return article.getAuthorInfo().stream()
                    .filter(authorInfo -> authorInfo.getOrganizations() != null)
                    .flatMap(authorInfo -> authorInfo.getOrganizations().stream())
                    .distinct()
                    .collect(Collectors.toList());
        }

        // 如果authorInfo为空，使用affiliation字段
        if (article.getAffiliation() != null && !article.getAffiliation().isEmpty()) {
            return article.getAffiliation();
        }

        return null;
    }

    private String parsePublishedMonth(Integer publishedMonth) {
        if (publishedMonth == null) {
            return null;
        }

        // 将数字月份转换为英文简称
        String[] months = {"Jan", "Feb", "Mar", "Apr", "May", "Jun",
                "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};

        if (publishedMonth >= 1 && publishedMonth <= 12) {
            return months[publishedMonth - 1];
        }

        return publishedMonth.toString();
    }

    private List<String> parsePubTypes(Long articleId) {
        List<ArticlePubType> articlePubTypes = articlePubTypeService.findByDocId(articleId);
        if (articlePubTypes == null || articlePubTypes.isEmpty()) {
            return null;
        }

        List<Long> pubTypeIds = articlePubTypes.stream()
                .map(ArticlePubType::getPubtypeId)
                .collect(Collectors.toList());

        List<PubType> pubTypes = pubTypeService.listByIds(pubTypeIds);
        return pubTypes.stream()
                .map(PubType::getPubType)
                .collect(Collectors.toList());
    }

    private List<GrantVO> parseGrants(Long articleId) {
        List<ArticleGrant> articleGrants = articleGrantService.findByDocId(articleId);
        if (articleGrants == null || articleGrants.isEmpty()) {
            return null;
        }

        List<Long> grantIds = articleGrants.stream()
                .map(ArticleGrant::getGrantId)
                .collect(Collectors.toList());

        List<Grant> grants = grantService.listByIds(grantIds);
        return grants.stream()
                .map(grant -> {
                    GrantVO grantVO = new GrantVO();
                    BeanUtils.copyProperties(grant, grantVO);
                    return grantVO;
                })
                .collect(Collectors.toList());
    }

    private List<DatabankVO> parseDatabanks(Long articleId) {
        List<ArticleDatabank> articleDatabanks = articleDatabankService.findByDocId(articleId);
        if (articleDatabanks == null || articleDatabanks.isEmpty()) {
            return null;
        }

        return articleDatabanks.stream()
                .map(databank -> {
                    DatabankVO databankVO = new DatabankVO();
                    databankVO.setName(databank.getName());
                    databankVO.setValue(databank.getValue());
                    return databankVO;
                })
                .sorted(Comparator.comparing(DatabankVO::getName))
                .collect(Collectors.toList());
    }

    private List<MeshTermVO> parseMeshTerms(Long articleId) {
        List<ArticleMesh> articleMeshes = articleMeshService.findByDocId(articleId);
        if (articleMeshes == null || articleMeshes.isEmpty()) {
            return null;
        }

        // 收集所有的descriptorId和qualifierId
        Set<String> allMeshIds = new HashSet<>();
        for (ArticleMesh articleMesh : articleMeshes) {
            if (StrUtil.isNotBlank(articleMesh.getDescriptorId())) {
                allMeshIds.add(articleMesh.getDescriptorId());
            }
            if (StrUtil.isNotBlank(articleMesh.getQualifierId1())) {
                allMeshIds.add(articleMesh.getQualifierId1());
                if (StrUtil.isNotBlank(articleMesh.getQualifierId2())) {
                    allMeshIds.add(articleMesh.getQualifierId2());
                    if (StrUtil.isNotBlank(articleMesh.getQualifierId3())) {
                        allMeshIds.add(articleMesh.getQualifierId3());
                    }
                }
            }
        }

        // 通过meshUi批量查询tb_dds_mesh表
        LambdaQueryWrapper<TbDdsMesh> queryWrapper = Wrappers.<TbDdsMesh>lambdaQuery()
                .in(TbDdsMesh::getMeshUi, allMeshIds);
        List<TbDdsMesh> meshes = meshService.list(queryWrapper);

        // 构建meshUi到meshName的映射
        Map<String, String> meshUiToNameMap = meshes.stream()
                .collect(Collectors.toMap(TbDdsMesh::getMeshUi, TbDdsMesh::getMeshName));

        List<MeshTermVO> result = new ArrayList<>();

        for (ArticleMesh articleMesh : articleMeshes) {
            String descriptorName = meshUiToNameMap.get(articleMesh.getDescriptorId());

            if (StrUtil.isNotBlank(articleMesh.getQualifierId1())) {
                // 有qualifierId1，为每个qualifierId创建一个对象
                MeshTermVO meshTermVO1 = new MeshTermVO();
                meshTermVO1.setDescriptorName(descriptorName);
                meshTermVO1.setQualifierName(meshUiToNameMap.get(articleMesh.getQualifierId1()));
                result.add(meshTermVO1);

                if (StrUtil.isNotBlank(articleMesh.getQualifierId2())) {
                    MeshTermVO meshTermVO2 = new MeshTermVO();
                    meshTermVO2.setDescriptorName(descriptorName);
                    meshTermVO2.setQualifierName(meshUiToNameMap.get(articleMesh.getQualifierId2()));
                    result.add(meshTermVO2);

                    if (StrUtil.isNotBlank(articleMesh.getQualifierId3())) {
                        MeshTermVO meshTermVO3 = new MeshTermVO();
                        meshTermVO3.setDescriptorName(descriptorName);
                        meshTermVO3.setQualifierName(meshUiToNameMap.get(articleMesh.getQualifierId3()));
                        result.add(meshTermVO3);
                    }
                }
            } else {
                // 没有qualifierId1，只显示descriptorName
                MeshTermVO meshTermVO = new MeshTermVO();
                meshTermVO.setDescriptorName(descriptorName);
                result.add(meshTermVO);
            }
        }

        return result;
    }

    /**
     * 设置影响因子和JCR分区信息
     */
    private void setImpactFactorInfo(ArticleDetailVO detailVO, Long journalId) {
        if (journalId == null) {
            return;
        }

        // 查询最新年份的影响因子信息
        LambdaQueryWrapper<TbDdsIfYear> queryWrapper = Wrappers.<TbDdsIfYear>lambdaQuery()
                .eq(TbDdsIfYear::getJournalId, journalId)
                .orderByDesc(TbDdsIfYear::getYear)
                .last("LIMIT 1");

        TbDdsIfYear ifYear = ifYearMapper.selectOne(queryWrapper);
        if (ifYear != null) {
            // 设置影响因子
            if (StrUtil.isNotBlank(ifYear.getImpactFactor())) {
                detailVO.setImpactFactor(Double.parseDouble(ifYear.getImpactFactor()));
            }
            // 设置JCR分区
            detailVO.setJcrQuartile(ifYear.getJcrQuartile());
        }
    }

    /**
     * 设置中科院分区信息
     */
    private void setZkySectionInfo(ArticleDetailVO detailVO, Long journalId) {
        if (journalId == null) {
            return;
        }

        // 查询最新年份的中科院分区信息
        LambdaQueryWrapper<TbDdsZkySection> queryWrapper = Wrappers.<TbDdsZkySection>lambdaQuery()
                .select(TbDdsZkySection::getLargeCategorySection)
                .eq(TbDdsZkySection::getJournalId, journalId)
                .orderByDesc(TbDdsZkySection::getYear)
                .last("LIMIT 1");

        TbDdsZkySection zkySection = zkySectionMapper.selectOne(queryWrapper);
        if (zkySection != null) {
            detailVO.setZkySections(zkySection.getLargeCategorySection());
        }
    }

    private void incrementHitCount(Article article) {
        article.setHitNum(article.getHitNum() + 1);
        articleService.updateById(article);
    }

    /**
     * 构建完整姓名
     */
    private String buildFullName(String forename, String lastname) {
        StringBuilder fullName = new StringBuilder();

        if (StrUtil.isNotBlank(forename)) {
            fullName.append(forename);
        }

        if (StrUtil.isNotBlank(lastname)) {
            if (!fullName.isEmpty()) {
                fullName.append(" ");
            }
            fullName.append(lastname);
        }
        return fullName.toString().trim();
    }

    /**
     * 调用DeepSeek API进行解读
     */
    private String callDeepSeekInterpretation(Long docId, String abstractContent, long startTime) {
        try {
            String promptText = buildInterpretationPrompt(abstractContent);
            Prompt prompt = new Prompt(promptText);

            ChatResponse response = chatModel.call(prompt);
            String content = response.getResult().getOutput().getText();

            // 获取token使用情况
            Integer tokensInput = null;
            Integer tokensOutput = null;

            if (response.getMetadata() != null && response.getMetadata().getUsage() != null) {
                var usage = response.getMetadata().getUsage();
                tokensInput = Math.toIntExact(usage.getPromptTokens());
                tokensOutput = Math.toIntExact(usage.getCompletionTokens());
            }

            // 保存成功结果到数据库
            long endTime = System.currentTimeMillis();
            saveInterpretation(docId, promptText, content, true, (int) (endTime - startTime),
                    tokensInput, tokensOutput);

            return content;

        } catch (Exception e) {
            log.error("调用DeepSeek API失败", e);
            String errorMsg = "解读生成失败：" + e.getMessage();

            // 保存失败结果到数据库
            long endTime = System.currentTimeMillis();
            saveInterpretation(docId, buildInterpretationPrompt(abstractContent), errorMsg, false,
                    (int) (endTime - startTime), null, null);

            return errorMsg;
        }
    }

    /**
     * 构建解读提示词
     */
    private String buildInterpretationPrompt(String abstractContent) {
        try {
            // 从resources读取提示词模板
            String promptTemplate = loadPromptTemplate();
            return promptTemplate + abstractContent;
        } catch (Exception e) {
            return getDefaultPrompt() + abstractContent;
        }
    }

    /**
     * 从resources加载提示词模板
     */
    private String loadPromptTemplate() throws IOException {
        ClassPathResource resource = new ClassPathResource("prompts/article-interpretation.txt");
        // 将InputStream标记为final，确保不被修改
        final InputStream inputStream = resource.getInputStream();
        // Charset标记为final，确保不被修改
        final Charset charset = StandardCharsets.UTF_8;
        // 使用try-with-resources确保InputStream自动关闭
        try (inputStream) {
            // 使用最终的常量变量
            return new String(inputStream.readAllBytes(), charset);
        }
    }

    /**
     * 默认提示词
     */
    private String getDefaultPrompt() {
        return "请对以下论文摘要进行解读和总结：\n";
    }

    /**
     * 根据docId获取论文摘要
     */
    private String getAbstractByDocId(Long docId) {
        Article article = articleService.getById(docId);
        return article != null ? article.getArticleAbstract() : null;
    }

    /**
     * 保存解读结果到数据库
     */
    private void saveInterpretation(Long docId, String prompt, String interpretation, boolean success, int latencyMs,
                                    Integer tokensInput, Integer tokensOutput) {
        ArticleInterpretation record = new ArticleInterpretation();
        record.setDocId(docId);
        record.setModelName(model);
        record.setModelVersion("v3");
        record.setPrompt(prompt);
        record.setResult(interpretation);
        record.setStatus(success ? 1 : 0);
        record.setLatencyMs(latencyMs);
        record.setTokensInput(tokensInput);
        record.setTokensOutput(tokensOutput);
        record.setCreateTime(LocalDateTime.now());

        articleInterpretationMapper.insert(record);
    }

    public Long getReferenceCount(Long docId) {
        return referenceMapper.selectCount(Wrappers.<Reference>lambdaQuery()
                .eq(Reference::getDocId, docId));
    }

    @Async
    public void updateReferenceCitations(Reference ref) {
        try {
            LambdaUpdateWrapper<Reference> queryWrapper = new LambdaUpdateWrapper<>();
            queryWrapper.eq(Reference::getId, ref.getId())
                    .set(Reference::getCitation, ref.getCitation())
                    .set(Reference::getPmid, ref.getPmid())
                    .set(Reference::getPmcid, ref.getPmcid())
                    .set(Reference::getDoi, ref.getDoi());

            referenceMapper.update(queryWrapper);
            log.info("批量更新引用格式完成: ref={}", ref);
        } catch (Exception e) {
            log.error("批量更新引用格式异常: ref={}", ref, e);
        }
    }

    private ReferenceVO convertToVO(Reference ref, Map<Long, Article> articleMap, Map<Long, Journal> journalMap) {
        Article article = new Article();
        if (ref.getPmid() != null) {
            article = articleMap.get(Long.valueOf(ref.getPmid()));
        }
        ReferenceVO vo = new ReferenceVO();
        vo.setId(ref.getId());
        vo.setDocId(ref.getDocId());

        // 构建citation
        String citation = buildCitationFromReference(ref, articleMap, journalMap);
        vo.setCitation(citation);

        vo.setPmid(ref.getPmid());

        // 设置pmcid - 过滤掉字符串"null"
        String pmcid = "null".equals(ref.getPmcid()) ? null : ref.getPmcid();
        if (StrUtil.isBlank(pmcid) && StrUtil.isNotBlank(ref.getPmid())) {
            pmcid = String.valueOf(article.getPmcId());
            // 过滤掉字符串"null"
            pmcid = "null".equals(pmcid) ? null : pmcid;
        }
        if (StrUtil.isNotBlank(pmcid)) {
            vo.setPmcid(pmcid);
            ref.setPmcid(pmcid);
        }

        // 设置doi - 过滤掉字符串"null"
        String doi = "null".equals(ref.getDoi()) ? null : ref.getDoi();
        if (StrUtil.isBlank(doi) && StrUtil.isNotBlank(ref.getPmid())) {
            doi = article.getDoi();
            // 过滤掉字符串"null"
            doi = "null".equals(doi) ? null : doi;
        }
        if (StrUtil.isNotBlank(doi)) {
            vo.setDoi(doi);
            ref.setDoi(doi);
        }

        // 判断各种链接是否可用
        vo.setHasPlospLink(checkPlospLink(ref));
        vo.setHasPubmedLink(StrUtil.isNotBlank(ref.getPmid()));
        vo.setHasPmcLink(StrUtil.isNotBlank(pmcid));
        vo.setHasDoiLink(StrUtil.isNotBlank(doi));

        return vo;
    }

    private String buildCitationFromReference(Reference ref, Map<Long, Article> articleMap, Map<Long, Journal> journalMap) {
        // 如果有PMID，尝试从article表查找匹配的文章
        if (StrUtil.isNotBlank(ref.getPmid())) {
            try {
                // 将PMID字符串转换为Long进行查询
                Long pmidLong = Long.parseLong(ref.getPmid());
                Article article = articleMap.get(pmidLong);

                if (article != null && StrUtil.isNotBlank(article.getTitle())) {
                    // 检查默认citation是否包含article的title
                    if (StrUtil.isNotBlank(ref.getCitation()) &&
                            ref.getCitation().contains(article.getTitle())) {
                        // title是citation的子串，使用默认citation
                        return ref.getCitation();
                    } else {
                        // title不是citation的子串或没有默认citation，生成新的citation
                        String newCitation = generateCitation(article, journalMap);
                        ref.setCitation(newCitation);
                        updateReferenceCitations(ref);
                        return newCitation;
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("PMID格式错误: {}", ref.getPmid());
            }
        }

        // 如果没有PMID或未找到匹配文章，返回原始citation
        return StrUtil.isNotBlank(ref.getCitation()) ? ref.getCitation() : "";
    }

    /**
     * 生成citation
     */
    private String generateCitation(Article article, Map<Long, Journal> journalMap) {
        StringBuilder citation = new StringBuilder();

        // 作者 (格式: Lastname, F., Lastname, F., and Lastname, F.)
        String authors = formatAuthorsForCitation(article);
        if (StrUtil.isNotBlank(authors)) {
            citation.append(authors).append(". ");
        }

        // 年份
        if (article.getYear() != null && article.getYear() > 0) {
            citation.append(article.getYear()).append(". ");
        }

        // 标题
        if (StrUtil.isNotBlank(article.getTitle())) {
            citation.append(article.getTitle()).append(". ");
        }

        // 期刊缩写
        String journalAbbr = getJournalAbbreviation(article.getJournalId(), journalMap);
        if (StrUtil.isNotBlank(journalAbbr)) {
            citation.append(journalAbbr).append(" ");
        }

        // 卷:页码 (格式: 5:319-330)
        if (StrUtil.isNotBlank(article.getVolume())) {
            citation.append(article.getVolume());
            if (StrUtil.isNotBlank(article.getPage())) {
                citation.append(":").append(article.getPage());
            }
            citation.append(".");
        }
        String ans = citation.toString().trim();
        return HtmlUtil.cleanHtmlTag(ans);
    }

    private String formatAuthorsForCitation(Article article) {
        if (article.getAuthorInfo() != null && !article.getAuthorInfo().isEmpty()) {
            List<String> formattedAuthors = article.getAuthorInfo().stream()
                    .map(this::formatAuthorForCitation)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());

            if (!formattedAuthors.isEmpty()) {
                return String.join(", ", formattedAuthors);
            }
        }

        if (article.getAuthor() != null && !article.getAuthor().isEmpty()) {
            return String.join(", ", article.getAuthor());
        }

        return "";
    }

    private String formatAuthorForCitation(AuthorInfo authorInfo) {
        if (StrUtil.isNotBlank(authorInfo.getLastname())) {
            StringBuilder formatted = new StringBuilder(authorInfo.getLastname());

            if (StrUtil.isNotBlank(authorInfo.getForename())) {
                String[] names = authorInfo.getForename().trim().split("\\s+");
                StringBuilder initials = new StringBuilder();
                for (String name : names) {
                    if (StrUtil.isNotBlank(name)) {
                        initials.append(name.charAt(0));
                    }
                }
                if (!initials.isEmpty()) {
                    formatted.append(" ").append(initials);
                }
            }

            return formatted.toString();
        }

        return buildFullName(authorInfo.getForename(), authorInfo.getLastname());
    }

    private String getJournalAbbreviation(Long journalId, Map<Long, Journal> journalMap) {
        if (journalId != null) {
            Journal journal = journalMap.get(journalId);
            if (journal != null) {
                return StrUtil.isNotBlank(journal.getIsoabbreviation()) ?
                        journal.getIsoabbreviation() : journal.getTitle();
            }
        }
        return "";
    }

    private Boolean checkPlospLink(Reference ref) {
        if (ref.getPmid() != null || ref.getPmcid() != null || ref.getDoi() != null) {
            return true;
        }
        return false;
    }
}
