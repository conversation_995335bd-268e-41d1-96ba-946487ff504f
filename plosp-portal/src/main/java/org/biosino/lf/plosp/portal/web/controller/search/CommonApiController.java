package org.biosino.lf.plosp.portal.web.controller.search;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.plosp.portal.es.service.EsService;
import org.biosino.lf.plosp.portal.es.service.EsScheduledService;
import org.biosino.lf.plosp.portal.utils.ThreadPoolUtil;
import org.biosino.lf.plosp.portal.web.dto.ApiParamDTO;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/es")
@RequiredArgsConstructor
public class CommonApiController {

    private final EsService esService;
    private final EsScheduledService esScheduledService;
    private static final Map<String, Object> taskRunningMap = new ConcurrentHashMap<>();
    private static final String INCREMENTAL_UPDATE_KEY = "incrementalUpdate";
    private static final String UPDATE_BY_IDS_KEY = "updateByIds";
    private static final String PROCESS_BASIC_DATA_KEY = "processBasicData";
    private static final String GENERATE_VECTORS_KEY = "generateVectors";
    private static final String PROCESS_MESH_KEY = "processMesh";

    @Value("${app.es-token:3c8661430cdd56d329af2914b93bdc07tzrG6kLxvMOV}")
    private String esToken;

    /**
     * token校验
     */
    private void checkToken(String token){
        if (token == null || !esToken.equals(token)) {
            throw new ServiceException("权限不足");
        }
    }

    /**
     * 前端可以选择执行：全量更新、向量更新、mesh更新 中的任意组合
     */
    @PostMapping("/dataProcess")
    public AjaxResult dataProcess(@RequestBody final ApiParamDTO dto) {
        String type = dto.getType();

        if (type == null || type.trim().isEmpty()) {
            throw new ServiceException("操作类型(type)不能为空");
        }

        return switch (type.toLowerCase()) {
            case "basic" -> {
                executeTask(dto.getToken(), PROCESS_BASIC_DATA_KEY, () -> {
                    esService.recreateIndex();
                    esService.fullRefresh();
                });
                yield AjaxResult.success("基础数据处理任务已启动，正在后台执行中...");
            }

            case "vectors" -> {
                executeTask(dto.getToken(), GENERATE_VECTORS_KEY, esService::generateVectorsForExistingData);
                yield AjaxResult.success("向量生成任务已启动，正在后台执行中...");
            }

            case "mesh" -> {
                executeTask(dto.getToken(), PROCESS_MESH_KEY, esService::processMeshForExistingData);
                yield AjaxResult.success("MeSH数据处理任务已启动，正在后台执行中...");
            }

            default -> throw new ServiceException("不支持的操作类型: " + type +
                ". 支持的类型: basic, vectors, mesh");
        };
    }

    /**
     * 查询任务运行状态
     */
    @GetMapping("/taskStatus")
    public AjaxResult getTaskStatus(@RequestParam(required = false) String token) {
        checkToken(token);

        Map<String, Object> status = new ConcurrentHashMap<>();
        status.put("processBasicData", taskRunningMap.containsKey(PROCESS_BASIC_DATA_KEY));
        status.put("processMesh", taskRunningMap.containsKey(PROCESS_MESH_KEY));
        status.put("generateVectors", taskRunningMap.containsKey(GENERATE_VECTORS_KEY));
        status.put("incrementalUpdate", taskRunningMap.containsKey(INCREMENTAL_UPDATE_KEY));
        status.put("updateByIds", taskRunningMap.containsKey(UPDATE_BY_IDS_KEY));
        status.put("runningTaskCount", taskRunningMap.size());
        status.put("allRunningTasks", taskRunningMap.keySet());

        return AjaxResult.success(status);
    }

    /**
     * ES增量更新
     */
    @PostMapping("/incrementalUpdate")
    public AjaxResult incrementalUpdate(@RequestBody final ApiParamDTO dto) {

        LocalDateTime lastUpdateTime = dto.getLastUpdateTime();

        // 如果没有指定时间，默认使用1小时前
        if (lastUpdateTime == null) {
            lastUpdateTime = LocalDateTime.now().minusHours(1);
        }

        final LocalDateTime finalLastUpdateTime = lastUpdateTime;
        executeTask(dto.getToken(), INCREMENTAL_UPDATE_KEY,
                   () -> esService.incrementalUpdate(finalLastUpdateTime));

        return AjaxResult.success("ES增量更新任务已启动，更新时间: " + lastUpdateTime + "，正在后台执行中...");
    }

    /**
     * 根据ID列表更新ES
     */
    @PostMapping("/updateByIds")
    public AjaxResult updateByIds(@RequestBody final ApiParamDTO dto) {

        List<Long> articleIds = dto.getArticleIds();

        if (articleIds == null || articleIds.isEmpty()) {
            throw new ServiceException("文章ID列表不能为空");
        }

        executeTask(dto.getToken(), UPDATE_BY_IDS_KEY,
                   () -> esService.updateByIds(articleIds));

        return AjaxResult.success("ES指定ID更新任务已启动，更新数量: " + articleIds.size() + "，正在后台执行中...");
    }



    /**
     * 校验token并执行任务
     */
    private void executeTask(final String token, final String taskKey, final Runnable task) {
        // 校验token
        checkToken(token);

        // 检查是否有任何任务正在运行
        if (!taskRunningMap.isEmpty()) {
            throw new ServiceException("系统正忙，有其他任务正在运行中，请稍后再试。");
        }

        // 执行任务
        ThreadPoolUtil.getExecutor().execute(() -> {
            try {
                taskRunningMap.put(taskKey, LocalDateTime.now());
                log.info("开始执行任务: {}", taskKey);
                task.run();
                log.info("任务执行完成: {}", taskKey);
            } catch (Exception e) {
                log.error("任务执行失败: {}", taskKey, e);
            } finally {
                taskRunningMap.remove(taskKey);
                log.info("任务清理完成: {}", taskKey);
            }
        });
    }

    /**
     * 手动触发增量更新
     */
    @PostMapping("/triggerIncrementalUpdate")
    public AjaxResult triggerIncrementalUpdate(@RequestBody final ApiParamDTO dto) {
        checkToken(dto.getToken());

        if (esScheduledService == null) {
            return AjaxResult.error("定时任务服务未启用，请检查配置 es.scheduled.enabled");
        }

        Integer hours = dto.getHours() != null ? dto.getHours() : 24;

        try {
            esScheduledService.triggerIncrementalUpdate(hours);
            return AjaxResult.success("手动增量更新任务已启动，时间范围: " + hours + "小时");
        } catch (Exception e) {
            log.error("手动触发增量更新失败", e);
            return AjaxResult.error("手动增量更新启动失败: " + e.getMessage());
        }
    }


}
