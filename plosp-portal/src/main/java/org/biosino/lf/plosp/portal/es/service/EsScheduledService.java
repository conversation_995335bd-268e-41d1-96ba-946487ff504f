package org.biosino.lf.plosp.portal.es.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * ES定时任务服务
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "es.scheduled.enabled", havingValue = "true", matchIfMissing = false)
public class EsScheduledService {

    private final EsService esService;

    @Value("${es.scheduled.incremental-hours:24}")
    private int incrementalHours;

    /**
     * 定时增量更新ES索引
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "${es.scheduled.cron:0 0 2 * * ?}")
    public void scheduledIncrementalUpdate() {
        try {
            log.warn("开始执行定时ES增量更新任务，增量时间范围: {}小时", incrementalHours);
            
            // 计算上次更新时间
            LocalDateTime lastUpdateTime = LocalDateTime.now().minusHours(incrementalHours);
            
            // 执行增量更新
            esService.incrementalUpdate(lastUpdateTime);
            
            log.warn("定时ES增量更新任务执行完成");
            
        } catch (Exception e) {
            log.error("定时ES增量更新任务执行失败", e);
            // 可以在这里添加告警通知
            sendAlertNotification("ES增量更新失败", e.getMessage());
        }
    }

    /**
     * 手动触发增量更新
     */
    @Async
    public void triggerIncrementalUpdate(int hours) {
        try {
            log.warn("手动触发ES增量更新，时间范围: {}小时", hours);
            LocalDateTime lastUpdateTime = LocalDateTime.now().minusHours(hours);
            esService.incrementalUpdate(lastUpdateTime);
            log.warn("手动ES增量更新完成");
        } catch (Exception e) {
            log.error("手动ES增量更新失败", e);
        }
    }

    /**
     * 发送告警通知
     */
    private void sendAlertNotification(String title, String message) {
        // TODO: 实现告警通知逻辑（邮件、钉钉等）
        log.error("告警: {} - {}", title, message);
    }
}
