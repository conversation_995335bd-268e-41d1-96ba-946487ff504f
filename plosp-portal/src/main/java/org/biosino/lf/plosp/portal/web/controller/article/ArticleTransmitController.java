package org.biosino.lf.plosp.portal.web.controller.article;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.custbean.dto.TransmitListQueryDTO;
import org.biosino.lf.pds.common.core.controller.GlobalBaseController;
import org.biosino.lf.pds.common.core.domain.R;
import org.biosino.lf.pds.common.core.page.PageDomain;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.pds.common.core.page.TableSupport;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.plosp.portal.dto.ArticleTransmitSubmitDTO;
import org.biosino.lf.plosp.portal.utils.PortalSecurityUtils;
import org.biosino.lf.plosp.portal.web.service.ArticleTransmitService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 文献传递控制层
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/transmit")
public class ArticleTransmitController extends GlobalBaseController {
    private final ArticleTransmitService articleTransmitService;

    /**
     * 文献传递
     */
    @PostMapping("/articleTransmit")
    public R<String> articleTransmit(@Validated @RequestBody final ArticleTransmitSubmitDTO transmitDTO) {
        return articleTransmitService.articleTransmit(transmitDTO);
    }

    /**
     * 我的文献传递列表
     */
    @GetMapping("/transmitList")
    public TableDataInfo transmitList(final TransmitListQueryDTO dto) {
        final Long userId = PortalSecurityUtils.getUserId();
        if (userId == null) {
            throw new ServiceException("用户未登录");
        }

        final PageDomain pageDomain = TableSupport.buildPageRequest();
        return articleTransmitService.transmitList(dto, userId, pageDomain);
    }

    /**
     * 下载文章PDF
     */
    @PostMapping("/downloadArticlePDF")
    public void downloadArticlePDF(final Long docId, final HttpServletRequest request, final HttpServletResponse response) {
        final Long userId = PortalSecurityUtils.getUserId();
        articleTransmitService.downloadArticlePDF(docId, userId, request, response);
    }

}
