package org.biosino.lf.plosp.portal.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.biosino.lf.pds.article.domain.TbFavoriteDoc;
import org.biosino.lf.plosp.portal.web.vo.FavoriteDocVO;
import org.biosino.lf.plosp.portal.web.dto.FavoriteDocDto;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FavoriteDocMapper extends BaseMapper<TbFavoriteDoc> {

    List<FavoriteDocVO> selectFavoriteDocsWithDetail(FavoriteDocDto favoriteDocDto);
}
