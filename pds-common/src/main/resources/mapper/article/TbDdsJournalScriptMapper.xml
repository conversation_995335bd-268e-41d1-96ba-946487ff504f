<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.TbDdsJournalScriptMapper">

    <resultMap id="ScriptDataResult" type="org.biosino.lf.pds.article.domain.TbDdsJournalScript">
        <id property="id" column="id"/>
        <result property="fileId" column="file_id"/>
        <result property="scriptName" column="script_name"/>
        <result property="scriptMd5" column="script_md5"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="lastSuccessTime" column="last_success_time"/>
        <result property="type" column="type"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
    </resultMap>

    <sql id="selectScriptVo">
        select s.id,
               s.file_id,
               s.script_name,
               s.script_md5,
               s.type,
               s.create_time,
               s.update_time,
               s.last_success_time,
               s.status,
               s.remark
        from tb_dds_journal_script s
    </sql>

    <select id="selectScriptList" parameterType="org.biosino.lf.pds.article.custbean.dto.ScriptDTO"
            resultMap="ScriptDataResult">
        <include refid="selectScriptVo"/>
        <where>
            <if test="scriptName != null and scriptName != ''">
                AND s.script_name like concat('%', #{scriptName}, '%')
            </if>
            <if test="scriptType != null and scriptType.size() > 0">
                AND s.type @> array[
                <foreach collection="scriptType" item="type" separator=",">
                    #{type}
                </foreach>
                ]::varchar[]
            </if>
            <if test="status != null and status != ''">
                AND s.status = #{status}
            </if>
            <if test="params != null and params.beginTime != null and params.beginTime != ''">
                AND s.create_time::date &gt;= to_date(#{params.beginTime}, 'yyyy-MM-dd')
            </if>
            <if test="params != null and params.endTime != null and params.endTime != ''">
                AND s.create_time::date &lt;= to_date(#{params.endTime}, 'yyyy-MM-dd')
            </if>
        </where>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by s.create_time DESC
    </select>

</mapper>
