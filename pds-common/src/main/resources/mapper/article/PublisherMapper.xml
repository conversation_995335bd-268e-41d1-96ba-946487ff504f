<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.PublisherMapper">

    <resultMap id="PublisherResult" type="org.biosino.lf.pds.article.domain.Publisher">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="ioc" column="ioc"/>
        <result property="alias" column="alias"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
        <result property="sourceType" column="source_type"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="journalCount" column="journal_count"/>
    </resultMap>

    <sql id="selectSql">
        select p.id,
               p.name,
               p.ioc,
               p.alias,
               p.source_type,
               p.status,
               p.create_time,
               p.update_time,
               (select count(*) from tb_dds_journal j where j.publisher_id = p.id) as journal_count
        from tb_dds_publisher p
    </sql>

    <!-- 查询出版社列表 -->
    <select id="selectPublisherList" parameterType="org.biosino.lf.pds.article.dto.PublisherQueryDTO"
            resultMap="PublisherResult">
        <include refid="selectSql"/>
        <where>
            <if test="nameMultiple != null and nameMultiple != ''">
                and name in
                <foreach collection="nameMultiple" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
            </if>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            <if test="ioc != null and ioc != ''">
                and ioc like concat('%', #{ioc}, '%')
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="sourceType != null and sourceType != ''">
                and source_type = #{sourceType}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                AND to_char(update_time,'yyyy-MM-dd')::date &gt;= to_date(#{beginTime},'yyyy-MM-dd')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                AND to_char(update_time,'yyyy-MM-dd')::date &lt;= to_date(#{endTime},'yyyy-MM-dd')
            </if>
        </where>
    </select>

    <!-- 出版社名称和别名唯一性验证查询 -->
    <select id="findConflictingPublishersByName" resultMap="PublisherResult">
        SELECT p.id, p.name, p.ioc, p.alias
        FROM tb_dds_publisher p
        WHERE (p.name = #{name}
               OR p.alias @> ARRAY[#{name}]::text[])
        <if test="excludeIds != null and excludeIds.size() > 0">
            AND p.id NOT IN
            <foreach collection="excludeIds" item="excludeId" open="(" separator="," close=")">
                #{excludeId}
            </foreach>
        </if>
        LIMIT 1
    </select>

    <select id="findConflictingPublishersByAlias" resultMap="PublisherResult">
        SELECT p.id, p.name, p.ioc, p.alias
        FROM tb_dds_publisher p
        WHERE (p.name = #{alias}
               OR p.alias @> ARRAY[#{alias}]::text[])
        <if test="excludeIds != null and excludeIds.size() > 0">
            AND p.id NOT IN
            <foreach collection="excludeIds" item="excludeId" open="(" separator="," close=")">
                #{excludeId}
            </foreach>
        </if>
        LIMIT 1
    </select>


</mapper>
