<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.ArticleAttachmentUploadMapper">

    <update id="updateDocIdBatch">
        UPDATE tb_dds_article_attachment_upload
        SET doc_id = #{targetDocId}
        WHERE doc_id = #{sourceDocId}
    </update>

    <resultMap id="ArticleAttachmentUploadResult" type="org.biosino.lf.pds.article.domain.ArticleAttachmentUpload">
        <id property="id" column="id"/>
        <result property="docId" column="doc_id"/>
        <result property="title" column="title"/>
        <result property="attachmentId" column="attachment_id"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="auditor" column="auditor"/>
        <result property="auditTime" column="audit_time"/>
        <result property="status" column="status"/>
        <result property="reason" column="reason"/>
        <result property="creatorName" column="creator_name"/>
        <result property="auditorName" column="auditor_name"/>
        <result property="attachmentName" column="attachment_name"/>
    </resultMap>

    <sql id="selectSql">
        select id,
               doc_id,
               title,
               attachment_id,
               creator,
               create_time,
               auditor,
               audit_time,
               status,
               reason
        from tb_dds_article_attachment_upload
    </sql>

    <!-- 关联查询列表 -->
    <select id="selectUploadList" parameterType="org.biosino.lf.pds.article.dto.ArticleAttachmentUploadQueryDTO"
            resultMap="ArticleAttachmentUploadResult">
        select a.id, a.doc_id, a.title, a.attachment_id, a.creator, a.create_time,
        a.auditor, a.audit_time, a.status, a.reason,
        c.user_name as creator_name,
        au.user_name as auditor_name,
        f.file_name as attachment_name
        from tb_dds_article_attachment_upload a
        left join tb_user c on a.creator = c.user_id
        left join sys_user au on a.auditor = au.user_id
        left join tb_dds_file f on a.attachment_id = f.id
        <where>
            <if test="title != null and title != ''">
                and a.title like concat('%', #{title}, '%')
            </if>
            <if test="creatorName != null and creatorName != ''">
                and c.user_name like concat('%', #{creatorName}, '%')
            </if>
            <if test="auditorName != null and auditorName != ''">
                and au.user_name like concat('%', #{auditorName}, '%')
            </if>
            <if test="status != null">
                and a.status = #{status}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                AND to_char(a.create_time,'yyyy-MM-dd')::date &gt;= to_date(#{beginTime},'yyyy-MM-dd')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                AND to_char(a.create_time,'yyyy-MM-dd')::date &lt;= to_date(#{endTime},'yyyy-MM-dd')
            </if>
        </where>
        order by a.create_time desc
    </select>

</mapper>
