<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.TbDdsTaskPaperMapper">

    <resultMap id="BaseExtResultMap" type="org.biosino.lf.pds.article.domain.TbDdsTaskPaper">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="doc_id" jdbcType="BIGINT" property="docId"/>
        <result column="journal_id" jdbcType="BIGINT" property="journalId"/>
<!--        <result column="publisher_id" jdbcType="BIGINT" property="publisherId"/>-->
        <result column="doi" jdbcType="VARCHAR" property="doi"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="time_execute" jdbcType="TIMESTAMP" property="timeExecute"/>
        <result column="time_complete" jdbcType="TIMESTAMP" property="timeComplete"/>
        <result column="batch_retry_times" jdbcType="INTEGER" property="batchRetryTimes"/>
        <result column="journal_retry_times" jdbcType="INTEGER" property="journalRetryTimes"/>
        <result column="school_retry_times" jdbcType="INTEGER" property="schoolRetryTimes"/>

        <result column="pmid" property="pmid"/>
        <result column="title" property="title"/>
        <result column="pmc_id" property="pmcId"/>
        <result column="author" property="author"
                typeHandler="org.biosino.lf.pds.article.config.StringListArrayTypeHandler"/>
    </resultMap>


    <select id="getNextExecuteTaskPaperId" resultType="java.lang.Long"
            parameterType="org.biosino.lf.pds.article.custbean.dto.TaskPaperQueryDTO">
        select p.id from tb_dds_task_paper p
        inner join tb_dds_task t on (p.task_id = t.id and t.status = #{taskStatus} and #{siteType}=ANY(t.support_site_type))
        where p.status = #{paperStatus}
        and p.id not in (
        select s.paper_id from tb_dds_task_schedule s where s.paper_id = p.id and s.site_id = #{siteId}
        )
        and (CASE WHEN (t.test_flag=1 AND t.site_id IS NOT NULL) THEN t.site_id = #{siteId} ELSE true END)
        <if test="startPmid != null and endPmid != null">
            and p.doc_id IN (SELECT art.id FROM tb_dds_article art
            WHERE art.pmid BETWEEN #{startPmid} AND #{endPmid})
        </if>

        <choose>
            <when test="siteType eq typeOfBatch">
                <!--批次节点-->
                and t.test_flag = 0
            </when>
            <otherwise>
                <!--源刊、高校节点-->
                and p.journal_id in (
                select j.journal_id from tb_dds_scriptlabel_journal j inner join tb_dds_journal jou on j.journal_id =
                jou.id where j.scriptlabel_id = #{scriptlabelId}
                <if test="activeScriptIdCol != null and activeScriptIdCol.size() > 0">
                    and jou.script_id not in
                    <foreach collection="activeScriptIdCol" item="scriptId" open="(" separator="," close=")">
                        #{scriptId}
                    </foreach>
                </if>
                )
            </otherwise>
        </choose>
        order by t.priority desc, p.create_time asc, p.id asc
        limit 1
    </select>

    <select id="getNextExecuteTaskPaperIdByScriptlableId" resultType="java.lang.Long"
            parameterType="org.biosino.lf.pds.article.custbean.dto.TaskPaperQueryDTO">
        select p.id
        from tb_dds_task_paper p
        inner join tb_dds_task t on (t.download_mode = #{downloadMode} and p.task_id = t.id and t.status =
        #{taskStatus} and #{siteType}=ANY(t.support_site_type))
        inner join tb_dds_task_schedule ts on (p.id = ts.paper_id and ts.status = #{scheduleStatus})
        where p.status = #{paperStatus}
        and ts.site_id in (select id from tb_dds_site where scriptlabel_id = #{scriptlabelId} and id != #{siteId})
        and (CASE WHEN (t.test_flag=1 AND t.site_id IS NOT NULL) THEN t.site_id = #{siteId} ELSE true END)

        <choose>
            <when test="siteType eq typeOfBatch">
                <!--批次节点-->
                and t.test_flag = 0
            </when>
            <otherwise>
                <!--源刊、高校节点-->
                and p.journal_id in (
                select j.journal_id from tb_dds_scriptlabel_journal j inner join tb_dds_journal jou on j.journal_id =
                jou.id where j.scriptlabel_id = #{scriptlabelId}
                <if test="activeScriptIdCol != null and activeScriptIdCol.size() > 0">
                    and jou.script_id not in
                    <foreach collection="activeScriptIdCol" item="scriptId" open="(" separator="," close=")">
                        #{scriptId}
                    </foreach>
                </if>
                )
            </otherwise>
        </choose>
        order by t.priority desc, p.create_time asc, p.id asc
        LIMIT 1
    </select>

    <update id="updateNotCompleteTaskPagerStatusByDocId">
        update tb_dds_task_paper set status=#{changeToStatus}, update_time=now()
        <if test="completeFlag">
            ,time_complete=now()
        </if>
        where doc_id = #{docId} and status IN
        <foreach collection="notCompleteStatus" item="statusVal" open="(" separator="," close=")">
            #{statusVal}
        </foreach>
    </update>

    <select id="statisticsTaskPaperNumByStatus" resultType="org.biosino.lf.pds.article.custbean.vo.StatInfoVO">
        select status, count(*) num
        from tb_dds_task_paper
        where task_id = #{taskId}
        group by status;
    </select>

    <select id="searchTaskPaper" resultMap="BaseExtResultMap"
            parameterType="org.biosino.lf.pds.article.custbean.dto.ArticleViewQueryDTO">
        SELECT p.*, a.pmid,a.title,a.pmc_id,a.author FROM tb_dds_task_paper p INNER JOIN tb_dds_article a ON p.doc_id =
        a.id
        <where>
            <if test="taskId != null and taskId != ''">
                AND p.task_id = #{taskId}
            </if>
            <if test="searchLiteratureId != null and searchLiteratureId != ''">
                AND concat('', p.doc_id) LIKE concat('%', #{searchLiteratureId}, '%')
            </if>
            <if test="pmid != null and pmid != ''">
                AND a.pmid = #{pmid}
            </if>
            <if test="searchStatus != null and searchStatus != ''">
                AND p.status = #{searchStatus}
            </if>

            <if test="statusList != null and statusList.size() > 0">
                AND p.status IN
                <foreach collection="statusList" item="statusVal" open="(" separator="," close=")">
                    #{statusVal}
                </foreach>
            </if>
        </where>
        ORDER BY p.create_time DESC
        <if test="custLimit != null">
            LIMIT #{custLimit}
        </if>
        <if test="custOffset != null">
            OFFSET #{custOffset}
        </if>
    </select>

    <update id="updateDocIdBatch">
        UPDATE tb_dds_task_paper
        SET doc_id = #{targetDocId}
        WHERE doc_id = #{sourceDocId}
    </update>

    <update id="updateAllFieldsForArticleMerge">
        UPDATE tb_dds_task_paper
        SET doc_id = #{targetDocId},
            journal_id = #{targetJournalId},
            doi = #{targetDoi}
        WHERE doc_id = #{sourceDocId}
    </update>

    <update id="updateJournalIdBatch">
        UPDATE tb_dds_task_paper
        SET journal_id = #{targetJournalId}
        WHERE journal_id IN
        <foreach collection="sourceJournalIds" item="sourceJournalId" open="(" separator="," close=")">
            #{sourceJournalId}
        </foreach>
    </update>

</mapper>
