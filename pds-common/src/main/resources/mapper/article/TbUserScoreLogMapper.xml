<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.TbUserScoreLogMapper">
    <resultMap id="BaseResultMap" type="org.biosino.lf.pds.article.domain.TbUserScoreLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="doc_id" jdbcType="BIGINT" property="docId"/>
        <result column="score" jdbcType="INTEGER" property="score"/>
        <result column="comment" jdbcType="VARCHAR" property="comment"/>
    </resultMap>

    <select id="sumUserScore" resultType="java.lang.Long">
        SELECT COALESCE(SUM(sl.score),0) FROM tb_user_score_log sl WHERE sl.user_id = #{userId}
    </select>

    <update id="updateDocIdBatch">
        UPDATE tb_user_score_log
        SET doc_id = #{targetDocId}
        WHERE doc_id = #{sourceDocId}
    </update>

</mapper>