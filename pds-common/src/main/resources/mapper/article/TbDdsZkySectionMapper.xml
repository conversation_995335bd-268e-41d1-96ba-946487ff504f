<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.TbDdsZkySectionMapper">

    <resultMap id="TbDdsZkySectionResult" type="org.biosino.lf.pds.article.domain.TbDdsZkySection">
        <id property="id" column="id"/>
        <result property="journalId" column="journal_id"/>
        <result property="top" column="top"/>
        <result property="year" column="year"/>
        <result property="largeCategory" column="large_category"/>
        <result property="largeCategorySection" column="large_category_section"/>
        <result property="subclass1" column="subclass_1"/>
        <result property="subclass1Section" column="subclass_1_section"/>
        <result property="subclass2" column="subclass_2"/>
        <result property="subclass2Section" column="subclass_2_section"/>
        <result property="subclass3" column="subclass_3"/>
        <result property="subclass3Section" column="subclass_3_section"/>
        <result property="subclass4" column="subclass_4"/>
        <result property="subclass4Section" column="subclass_4_section"/>
        <result property="subclass5" column="subclass_5"/>
        <result property="subclass5Section" column="subclass_5_section"/>
        <result property="subclass6" column="subclass_6"/>
        <result property="subclass6Section" column="subclass_6_section"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTbDdsZkySectionVo">
        select id,
               journal_id,
               top,
               year,
               large_category,
               large_category_section,
               subclass_1,
               subclass_1_section,
               subclass_2,
               subclass_2_section,
               subclass_3,
               subclass_3_section,
               subclass_4,
               subclass_4_section,
               subclass_5,
               subclass_5_section,
               subclass_6,
               subclass_6_section,
               create_time,
               update_time
        from tb_dds_zky_section
    </sql>

    <update id="updateJournalIdBatch">
        update tb_dds_zky_section
        set journal_id = #{targetJournalId}
        where journal_id in
        <foreach collection="sourceJournalIds" item="sourceId" open="(" separator="," close=")">
            #{sourceId}
        </foreach>
    </update>

</mapper>
