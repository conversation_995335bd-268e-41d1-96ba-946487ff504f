<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.ArticleAuthorMapper">

    <resultMap id="ArticleAuthorResult" type="org.biosino.lf.pds.article.domain.ArticleAuthor">
        <id property="id" column="id"/>
        <result property="docId" column="doc_id"/>
        <result property="authorId" column="author_id"/>
        <result property="organizationId" column="organization_id"
                typeHandler="org.biosino.lf.pds.article.config.LongListArrayTypeHandler"/>
        <result property="authorOrder" column="author_order"/>
        <result property="authorFirst" column="author_first"/>
        <result property="authorCorrespond" column="author_correspond"/>
    </resultMap>

    <sql id="selectSql">
        select a.id, a.doc_id, a.author_id, a.organization_id, a.author_order, a.author_first, a.author_correspond
        from tb_dds_article_author a
    </sql>

    <select id="selectByOrgIdIn" resultMap="ArticleAuthorResult">
        <include refid="selectSql"/>
        where a.organization_id &amp;&amp;
        <foreach item="id" index="index" collection="orgIds" open="ARRAY[" separator="," close="]">
            #{id}
        </foreach>
    </select>
    <select id="findByDocId" resultType="org.biosino.lf.pds.article.domain.ArticleAuthor">
        <include refid="selectSql"/>
        where doc_id = #{docId}
    </select>

</mapper>
