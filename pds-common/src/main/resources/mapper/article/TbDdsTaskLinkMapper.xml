<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.TbDdsTaskLinkMapper">

    <delete id="deleteByTaskId">
        delete
        from tb_dds_task_link
        where paper_id in (select id from tb_dds_task_paper where task_id = #{taskId})
    </delete>

</mapper>