<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.ArticleOtherIdMapper">


    <select id="getArticleIdByDoi" resultType="java.lang.Long">
        SELECT doc_id FROM tb_dds_article_otherid WHERE "source" = 'doi' AND "other_id" = #{doi} LIMIT 1
    </select>

    <select id="getArticleDoiByDocId" resultType="java.lang.String">
        SELECT other_id FROM tb_dds_article_otherid WHERE "doc_id" = #{doc_id} AND "source" = 'doi' LIMIT 1
    </select>

</mapper>
