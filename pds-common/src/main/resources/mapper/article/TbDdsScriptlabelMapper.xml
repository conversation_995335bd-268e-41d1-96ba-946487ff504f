<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.TbDdsScriptlabelMapper">
    <resultMap type="org.biosino.lf.pds.article.custbean.vo.TbDdsScriptlabelVO" id="TbDdsScriptlabelVOResult">
        <result property="labelId" column="id"/>
        <result property="labelName" column="name"/>
        <result property="labelType" column="type"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="nodeCount" column="node_count"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap type="org.biosino.lf.pds.article.custbean.vo.ScriptVO" id="ScriptVOResult">
        <result property="scriptId" column="id"/>
        <result property="scriptName" column="script_name"/>
        <result property="scriptFileId" column="script_file_id"/>
        <result property="scriptMd5" column="script_md5"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="uploadTime" column="upload_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="lastSuccessTime" column="last_success_time"/>
        <result property="sort" column="sort"/>
    </resultMap>

    <select id="selectTbDdsScriptlabelList" resultMap="TbDdsScriptlabelVOResult">
        SELECT
        l.id,
        l.name,
        l.type,
        l.status,
        l.remark,
        l.creator,
        l.create_time,
        l.update_time,
        COUNT(s.id) AS node_count
        FROM
        tb_dds_scriptlabel l
        LEFT JOIN
        tb_dds_site s ON l.id = s.scriptlabel_id
        <where>
            <if test="labelName != null and labelName != ''">
                AND l.name like concat('%', #{labelName}, '%')
            </if>
            <if test="labelType != null and labelType != ''">
                AND l.type = #{labelType}
            </if>
            <if test="labelTypes != null and labelTypes.size() > 0">
                AND l.type IN
                <foreach collection="labelTypes" item="labelVal" open="(" separator="," close=")">
                    #{labelVal}
                </foreach>
            </if>
            <if test="status != null and status != ''">
                AND l.status = #{status}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND l.create_time::date &gt;= to_date(#{params.beginTime}, 'yyyy-MM-dd')
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND l.create_time::date &lt;= to_date(#{params.endTime}, 'yyyy-MM-dd')
            </if>
        </where>
        ${params.dataScope}
        GROUP BY l.id
        ORDER BY l.create_time DESC
    </select>

    <select id="findScriptListOfLabel" resultMap="ScriptVOResult">
        SELECT DISTINCT js.id,
               js.script_name,
               js.script_md5,
               js.status,
               js.remark,
               js.file_id AS script_file_id,
               js.last_success_time,
               ls.sort
        FROM tb_dds_scriptlabel_script ls
                 INNER JOIN tb_dds_journal_script js ON ls.script_id = js.id
        WHERE ls.scriptlabel_id = #{scriptlabelId}
        ORDER BY ls.sort ASC
    </select>

</mapper>