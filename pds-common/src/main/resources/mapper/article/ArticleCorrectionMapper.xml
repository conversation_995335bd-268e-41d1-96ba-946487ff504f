<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.ArticleCorrectionMapper">

    <update id="updateDocIdBatch">
        UPDATE tb_dds_article_correction
        SET doc_id = #{targetDocId}
        WHERE doc_id = #{sourceDocId}
    </update>

    <resultMap id="ArticleDefectResult" type="org.biosino.lf.pds.article.domain.ArticleCorrection">
        <id property="id" column="id"/>
        <result property="docId" column="doc_id"/>
        <result property="title" column="title"/>
        <result property="correctionType" column="correction_type"/>
        <result property="content" column="content"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="auditor" column="auditor"/>
        <result property="auditTime" column="audit_time"/>
        <result property="status" column="status"/>
        <result property="reason" column="reason"/>
        <result property="creatorName" column="creator_name"/>
        <result property="auditorName" column="auditor_name"/>
    </resultMap>

    <sql id="selectSql">
        select id,
               doc_id,
               title,
               correction_type,
               content,
               creator,
               create_time,
               auditor,
               audit_time,
               status,
               reason
        from tb_dds_article_correction
    </sql>

    <!-- 关联查询列表 -->
    <select id="selectDefectList" parameterType="org.biosino.lf.pds.article.dto.ArticleCorrectionQueryDTO"
            resultMap="ArticleDefectResult">
        select d.id, d.doc_id, d.title, d.correction_type, d.content,
        d.creator, d.create_time, d.auditor, d.audit_time, d.status, d.reason,
        s.user_name as creator_name,
        p.user_name as auditor_name
        from tb_dds_article_correction d
        left join tb_user s on d.creator = s.user_id
        left join sys_user p on d.auditor = p.user_id
        <where>
            <if test="title != null and title != ''">
                and d.title like concat('%', #{title}, '%')
            </if>
            <if test="correctionType != null and correctionType != ''">
                and d.correction_type = #{defectType}
            </if>
            <if test="status != null">
                and d.status = #{status}
            </if>
            <if test="creatorName != null and creatorName != ''">
                and s.user_name like concat('%', #{creatorName}, '%')
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                AND to_char(d.create_time,'yyyy-MM-dd')::date &gt;= to_date(#{beginTime},'yyyy-MM-dd')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                AND to_char(d.create_time,'yyyy-MM-dd')::date &lt;= to_date(#{endTime},'yyyy-MM-dd')
            </if>
        </where>
        order by d.create_time desc
    </select>

</mapper>
