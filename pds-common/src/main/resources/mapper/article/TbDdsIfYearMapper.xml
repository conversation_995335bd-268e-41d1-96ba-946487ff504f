<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.TbDdsIfYearMapper">

    <resultMap id="TbDdsIfYearResult" type="org.biosino.lf.pds.article.domain.TbDdsIfYear">
        <id property="id" column="id"/>
        <result property="journalId" column="journal_id"/>
        <result property="year" column="year"/>
        <result property="impactFactor" column="impact_factor"/>
        <result property="jcrQuartile" column="jcr_quartile"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectTbDdsIfYearVo">
        select id,
               journal_id,
               year,
               impact_factor,
               jcr_quartile,
               create_time,
               update_time
        from tb_dds_if_year
    </sql>

    <update id="updateJournalIdBatch">
        update tb_dds_if_year
        set journal_id = #{targetJournalId}
        where journal_id in
        <foreach collection="sourceJournalIds" item="sourceId" open="(" separator="," close=")">
            #{sourceId}
        </foreach>
    </update>

</mapper>
