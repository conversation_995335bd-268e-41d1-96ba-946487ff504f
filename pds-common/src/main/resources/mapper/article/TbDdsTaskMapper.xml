<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.biosino.lf.pds.article.mapper.TbDdsTaskMapper">

    <select id="findCreatorBySource" resultType="java.lang.Long">
        SELECT creator AS longValue
        FROM tb_dds_task
        WHERE source = #{source}
        GROUP BY creator
    </select>

    <select id="findTaskIdByCreator" resultType="java.lang.String">
        SELECT id AS stringValue
        FROM tb_dds_task
        WHERE source = #{source}
          AND creator = #{creator}
    </select>

</mapper>
