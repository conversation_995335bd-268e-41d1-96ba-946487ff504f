package org.biosino.lf.pds.common.utils.db;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;

import java.util.List;

/**
 * mybatis plus工具类
 *
 * <AUTHOR>
 */
public class MpPlusUtil {

    /**
     * 查询单个数据，使用limit1限制返回数量
     */
    public static <T> T findOne(BaseMapper<T> mapper, LambdaQueryWrapper<T> queryWrapper) {
        final List<T> list = mapper.selectList(PageDTO.of(1, 1), queryWrapper);
        T data = null;
        if (CollUtil.isNotEmpty(list)) {
            data = list.get(0);
        }
        return data;
    }

}
