package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.biosino.lf.pds.article.config.StringListArrayTypeHandler;
import org.biosino.lf.pds.common.enums.task.TaskStatusEnum;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@TableName(value = "tb_dds_task", autoResultMap = true)
public class TbDdsTask implements Serializable {
    public static final int RETRY_NUM = 3;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    private String name;

    private String description;

    /**
     * 任务输入文件ID，对应tb_dds_file表的id
     */
    private Long fileId;

    private Integer total = 0;

    private Long creator;

    private Date createTime;

    /**
     * create("待分配"), assigning("分配中"), assign_error("分配失败"), assigned("分配完成"), complete("完成"), paused("已暂停");
     *
     * @see TaskStatusEnum
     */
    private String status;

    /**
     * 任务来源，
     * PDS：系统内置
     * PLOSP：PLOSP
     *
     * @see org.biosino.lf.pds.common.enums.task.TaskSourceEnum
     */
    private String source;

    /**
     * 优先级，
     * 自定义（取值范围0~99）：系统管理员
     * 高（数值3）：系统管理员、高级用户
     * 普通（底层数值2）：系统管理员、高级用户、普通用户
     * 系统任务（底层数值1）：系统预留，系统中的任务空闲时，定期把系统中没有PDF的文献下载PDF	门户的文献传递（数值100）：最高优先级，此类请求需要快速响应，且每次文献请求只有一篇。
     *
     * @see org.biosino.lf.pds.common.enums.task.TaskPriorityEnum
     */
    private Short priority;

    /**
     * 重试时间间隔，单位秒
     */
    private Integer retryInterval;

    /**
     * 是否测试任务，仅限高级用户、管理员
     */
    private Short testFlag;

    /**
     * 支持的节点类型，批次1、源刊2、高校3(多选，使用数组类型)
     * 字典类型:script_type
     */
    @TableField(value = "support_site_type", typeHandler = StringListArrayTypeHandler.class)
    private List<String> supportSiteType;

    /**
     * 下载模式：速度优先、完整度优先(高级用户、管理员)
     * 字典类型:task_download_mode
     *
     * @see org.biosino.lf.pds.common.enums.task.TaskDownloadModeEnum
     */
    private String downloadMode;

    /**
     * 测试模式下，指定的节点ID
     */
    private Integer siteId;

    public void setSupportSiteType(List<String> supportSiteType) {
        this.supportSiteType = supportSiteType != null ? supportSiteType.stream().distinct().toList() : null;
    }

}
