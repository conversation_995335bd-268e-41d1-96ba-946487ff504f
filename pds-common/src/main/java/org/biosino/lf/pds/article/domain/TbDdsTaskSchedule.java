package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@TableName("tb_dds_task_schedule")
public class TbDdsTaskSchedule implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long paperId;

    private Integer siteId;

    /**
     * 分配时间，tb_dds_task_paper的创建时间create_time
     */
    private Date timeAssigned;

    private Date timeExecute;

    /**
     * @see org.biosino.lf.pds.common.enums.task.TaskPaperScheduleStatusEnum
     */
    private String status;

    @TableField(exist = false)
    private String siteName;

    @TableField(exist = false)
    private String siteType;

}
