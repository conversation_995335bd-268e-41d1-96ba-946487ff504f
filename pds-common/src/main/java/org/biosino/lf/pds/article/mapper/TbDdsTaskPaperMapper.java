package org.biosino.lf.pds.article.mapper;

import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.custbean.dto.ArticleViewQueryDTO;
import org.biosino.lf.pds.article.custbean.dto.TaskPaperQueryDTO;
import org.biosino.lf.pds.article.custbean.vo.StatInfoVO;
import org.biosino.lf.pds.article.domain.TbDdsTaskPaper;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface TbDdsTaskPaperMapper extends CommonMapper<TbDdsTaskPaper> {
    /**
     * 获取 下一个任务
     */
    Long getNextExecuteTaskPaperId(TaskPaperQueryDTO dto);

    /**
     * 重试时，获取当前节点标签其他节点的未完成的任务
     */
    Long getNextExecuteTaskPaperIdByScriptlableId(TaskPaperQueryDTO dto);

    void updateNotCompleteTaskPagerStatusByDocId(@Param("docId") Long docId, @Param("completeFlag") boolean completeFlag,
                                                 @Param("changeToStatus") String changeToStatus,
                                                 @Param("notCompleteStatus") Set<String> notCompleteStatus);

    /**
     * 统计任务下的文献传递状态数量
     */
    List<StatInfoVO> statisticsTaskPaperNumByStatus(@Param("taskId") String taskId);

    List<TbDdsTaskPaper> searchTaskPaper(ArticleViewQueryDTO queryDto);

    /**
     * 批量更新任务论文表中的期刊ID（用于期刊合并）
     * 将所有匹配源期刊ID的记录更新为目标期刊ID
     *
     * @param targetJournalId  目标期刊ID
     * @param sourceJournalIds 源期刊ID列表
     */
    void updateJournalIdBatch(Long targetJournalId, List<Long> sourceJournalIds);

    /**
     * 批量更新任务论文表中的doc_id字段（用于文章合并）
     * 将所有匹配源doc_id的记录更新为目标doc_id
     *
     * @param targetDocId 目标文档ID
     * @param sourceDocId 源文档ID
     * @return 更新的记录数
     */
    int updateDocIdBatch(@Param("targetDocId") Long targetDocId, @Param("sourceDocId") Long sourceDocId);

    /**
     * 批量更新任务论文表中的所有相关字段（用于文章合并）
     * 将所有匹配源doc_id的记录更新为目标文章的相关信息
     * 数据全部以目标文章数据为准，如果是null则置空，不为空则更新
     *
     * @param targetDocId 目标文档ID
     * @param sourceDocId 源文档ID
     * @param targetJournalId 目标期刊ID
     * @param targetDoi 目标DOI
     * @return 更新的记录数
     */
    int updateAllFieldsForArticleMerge(@Param("targetDocId") Long targetDocId,
                                       @Param("sourceDocId") Long sourceDocId,
                                       @Param("targetJournalId") Long targetJournalId,
                                       @Param("targetDoi") String targetDoi);

}
