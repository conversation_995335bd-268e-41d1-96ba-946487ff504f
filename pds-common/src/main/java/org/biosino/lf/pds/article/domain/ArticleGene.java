package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 文章基因关联表
 */
@Data
@TableName(value = "tb_dds_article_gene", autoResultMap = true)
public class ArticleGene {
    /**
     * PubMed ID
     */
    @TableField("doc_id")
    private Long docId;

    /**
     * 基因ID
     */
    @TableField("gene_id")
    private Long geneId;
}
