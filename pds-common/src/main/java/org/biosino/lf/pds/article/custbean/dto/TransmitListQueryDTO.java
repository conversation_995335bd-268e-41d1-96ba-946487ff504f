package org.biosino.lf.pds.article.custbean.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.biosino.lf.pds.common.core.domain.BaseEntity;
import org.biosino.lf.pds.common.core.page.PageDomain;

/**
 * 文献传递列表查询DTO
 *
 * <AUTHOR>
 */
@Data
public class TransmitListQueryDTO extends BaseEntity {
    @NotBlank(message = "任务令牌不能为空")
    private String apiToken;

    /**
     * 根据PMID/PMCID/DOI/标题筛选，四项筛选添加之间的逻辑为或关系
     */
    private String articleTitle;
    private String status;

    private String beginTime;
    private String endTime;

    private PageDomain pageDomainVal;
    private long userId;
}
