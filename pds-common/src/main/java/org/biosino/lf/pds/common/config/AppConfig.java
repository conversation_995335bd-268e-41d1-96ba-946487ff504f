package org.biosino.lf.pds.common.config;

import org.apache.commons.lang3.StringUtils;
import org.biosino.lf.pds.common.constant.Constants;
import org.biosino.lf.pds.common.enums.DirectoryEnum;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * 读取项目相关配置
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "app")
public class AppConfig {
    private static final String TASK_DIR = "/taskDir";
    private static final String PDS_RESULT = "/pdsResult";

    /**
     * 项目名称
     */
    private String name;

    /**
     * 版本
     */
    private String version;

    /**
     * 版权年份
     */
    private String copyrightYear;

    /**
     * 上传路径
     */
    private static String profile;

    /**
     * 获取地址开关
     */
    private static boolean addressEnabled;

    /**
     * 验证码类型
     */
    private static String captchaType;

    /**
     * 数据文件目录
     */
    private static String dataHome;

    /**
     * 前端配置的VITE_APP_BASE_API请求前缀
     */
    private static String viteAppBaseApi;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCopyrightYear() {
        return copyrightYear;
    }

    public void setCopyrightYear(String copyrightYear) {
        this.copyrightYear = copyrightYear;
    }

    public static String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        AppConfig.profile = profile;
    }

    public static boolean isAddressEnabled() {
        return addressEnabled;
    }

    public void setAddressEnabled(boolean addressEnabled) {
        AppConfig.addressEnabled = addressEnabled;
    }

    public static String getCaptchaType() {
        return captchaType;
    }

    public void setCaptchaType(String captchaType) {
        AppConfig.captchaType = captchaType;
    }

    public static String getViteAppBaseApi() {
        return viteAppBaseApi;
    }

    public void setViteAppBaseApi(String viteAppBaseApi) {
        AppConfig.viteAppBaseApi = viteAppBaseApi;
    }

    /**
     * 获取导入上传路径
     */
    public static String getImportPath() {
        return getProfile() + "/import";
    }

    /**
     * 获取头像上传路径
     */
    public static String getAvatarPath() {
        return getProfile() + "/avatar";
    }

    /**
     * 获取下载路径
     */
    public static String getDownloadPath() {
        return getProfile() + "/download/";
    }

    /**
     * 获取上传路径
     */
    public static String getUploadPath() {
        return getProfile() + "/upload";
    }

    /**
     * 获取PDS文献传递相关文件所在目录（目录中的文件可直接下载）
     */
    public static File getTaskDir() {
        final File dir = new File(getProfile() + TASK_DIR);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }

    public static String getTaskDirPreUrl() {
        return getViteAppBaseApi() + Constants.RESOURCE_PREFIX + TASK_DIR;
    }

    public static File getPdsResultDir() {
        final File dir = new File(getProfile() + PDS_RESULT);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }

    public static String getPdsResultrPreUrl() {
        return getViteAppBaseApi() + Constants.RESOURCE_PREFIX + PDS_RESULT;
    }

    public static String getDataHome() {
        return dataHome;
    }

    public void setDataHome(String dataHome) {
        AppConfig.dataHome = dataHome;
    }


    public static File initDataHome(DirectoryEnum directoryEnum) {
        String homeStr = getDataHome();
        if (StringUtils.isBlank(homeStr)) {
            homeStr = System.getProperty("user.home") + File.separator + "lfs";
        }
        File homeDir = new File(homeStr);
        if (!homeDir.exists() || !homeDir.isDirectory()) {
            homeDir.mkdirs();
        }
        if (directoryEnum == null) {
            return homeDir;
        }
        File dir = new File(homeDir, directoryEnum.name());
        if (!dir.exists() || !dir.isDirectory()) {
            dir.mkdirs();
        }
        return dir;
    }

}
