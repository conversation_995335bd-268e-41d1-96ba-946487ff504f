package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.biosino.lf.pds.article.config.StringListArrayTypeHandler;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@TableName(value = "tb_dds_journal_script", autoResultMap = true)
public class TbDdsJournalScript implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 脚本文件id，对应tb_dds_file表id
     */
    private Long fileId;

    private String scriptName;

    /**
     * 脚本md5
     */
    private String scriptMd5;

    private Long creator;

    private Date createTime;

    private Date updateTime;

    /**
     * 批次、源刊、高校(多选)
     */
    @TableField(value = "type", typeHandler = StringListArrayTypeHandler.class)
    private List<String> type;

    /**
     * 脚本最后一次运行成功时间
     */
    private Date lastSuccessTime;

    /**
     * 脚本状态, 状态（0-正常，1-停用）
     * 字典类型：sys_normal_disable
     * @see org.biosino.lf.pds.common.enums.StatusEnums
     */
    private String status;

    private String remark;

}
