package org.biosino.lf.pds.article.dto;

import cn.hutool.db.DaoTemplate;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class JournalUpdateDTO {
    /**
     * 期刊id
     */
    private Long id;
    /**
     * 期刊名称
     */
    private String title;

    /**
     * 期刊简称
     */
    private String isoabbreviation;

    /**
     * 出版社id
     */
    private Long publisherId;
    /**
     * 出版社名称
     */
    private String publisherName;

    /**
     * issn print
     */
    private String issnPrint;

    /**
     * issn electronic
     */
    private String issnElectronic;

    /**
     * unique nlm id
     */
    private String uniqueNlmId;

    /**
     * 历史issn
     */
    private List<String> issnHistory;

    /**
     * 历史unique nlm id
     */
    private List<String> uniqueHistory;

    /**
     * MedlineTA
     */
    private String medlineTa;

    /**
     * 来源
     */
    private List<String> source;

    /**
     * 更新时间
     */
    private Date updateTime;
}
