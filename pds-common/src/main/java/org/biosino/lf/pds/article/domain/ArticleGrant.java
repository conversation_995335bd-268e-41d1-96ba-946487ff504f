package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 文章基金关联表
 */
@Data
@TableName(value = "tb_dds_article_grant", autoResultMap = true)
public class ArticleGrant {
    /**
     * PubMed ID
     */
    @TableField("doc_id")
    private Long docId;

    /**
     * 基金ID
     */
    @TableField("grant_id")
    private Long grantId;
}
