package org.biosino.lf.pds.article.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.domain.ArticleOtherId;

/**
 * 文章其他ID关联表 Mapper 接口
 */
@Mapper
public interface ArticleOtherIdMapper extends CommonMapper<ArticleOtherId> {

    Long getArticleIdByDoi(@Param("doi") String doi);

    String getArticleDoiByDocId(@Param("doc_id") Long docId);

}
