package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户收藏文件夹
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "tb_favorite_doc", autoResultMap = true)
public class TbFavoriteDoc implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 用户id
     */
    @TableField("user_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 文献id
     */
    @TableField("doc_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long docId;

    /**
     * 文件夹id
     */
    @TableField("folder_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long folderId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
}
