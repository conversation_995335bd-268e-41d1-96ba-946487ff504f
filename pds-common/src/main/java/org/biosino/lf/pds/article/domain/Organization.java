package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 机构信息表
 */
@Data
@TableName(value = "deleted_tb_dds_organization", autoResultMap = true)
public class Organization {
    /**
     * 主键ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 机构名称
     */
    @TableField("name")
    private String name;

    /**
     * 机构描述
     */
    @TableField("description")
    private String description;
}
