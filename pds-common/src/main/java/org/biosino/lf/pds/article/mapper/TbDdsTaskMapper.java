package org.biosino.lf.pds.article.mapper;

import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.domain.TbDdsTask;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface TbDdsTaskMapper extends CommonMapper<TbDdsTask> {

    List<Long> findCreatorBySource(@Param("source") String source);

    List<String> findTaskIdByCreator(@Param("creator") Long creator, @Param("source") String source);

}
