package org.biosino.lf.pds.article.domain.statistics;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * <AUTHOR> @date 2025/8/29
 */
@Data
@TableName(value = "statistics_article", autoResultMap = true)
public class StatisticsArticle {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 年
     */
    @TableField("year")
    private Integer year;

    /**
     * 月
     */
    @TableField("month")
    private Integer month;

    /**
     * 统计本月文献总数
     */
    @TableField("total")
    private Long total = 0L;

    /**
     * 统计本月新增文献数量
     */
    @TableField("total_growth")
    private Long totalGrowth = 0L;
}
