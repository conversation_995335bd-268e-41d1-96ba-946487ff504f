package org.biosino.lf.pds.article.custbean.dto;

import jakarta.validation.constraints.*;
import lombok.Data;
import org.biosino.lf.pds.common.enums.task.TaskDownloadModeEnum;
import org.biosino.lf.pds.common.validator.ValidEnum;

import java.io.Serializable;
import java.util.List;

/**
 * 文献传递任务发布DTO
 *
 * <AUTHOR>
 * @date 2025/6/23
 */
@Data
public class TaskPublishDTO implements Serializable {

    /**
     * 任务描述
     */
    @NotBlank(message = "任务描述不能为空")
    @Size(max = 300, message = "任务描述长度不能超过300个字符")
    private String taskDesc;

    private String taskName;

    /**
     * 优先级
     */
    @NotNull(message = "优先级不能为空")
    @Min(value = 1, message = "优先级最小为1")
    @Max(value = 100, message = "优先级最大为100")
    private Integer priority;

    /**
     * 节点类型列表
     */
    @NotEmpty(message = "至少选择一种节点类型")
    private List<String> nodeTypes;

    /**
     * 是否测试任务 0-否 1-是
     */
    @Min(value = 0, message = "测试任务标志值不正确")
    @Max(value = 1, message = "测试任务标志值不正确")
    private Integer testFlag;

    /**
     * 下载模式：speed-速度优先 complete-完整度优先
     */
    // @NotBlank(message = "请选择下载模式")
    @ValidEnum(enumClass = TaskDownloadModeEnum.class, allowNull = false, message = "下载模式值不支持")
    private String downloadMode;

    /**
     * 重试间隔（秒）
     */
//    @NotNull(message = "重试间隔不能为空")
//    @Min(value = 5, message = "重试间隔最小为5秒")
//    @Max(value = 120, message = "重试间隔最大为120秒")
    private Integer retryInterval = 5;

    /**
     * 文献标识符数据
     * 每行包含三列：PMID, PMCID, DOI
     */
    @NotEmpty(message = "请添加至少一条文献标识符数据")
    @Size(max = 5000, message = "文献标识符数据不能超过5000条")
    private List<List<String>> literatureData;

    // @ValidEnum(enumClass = TaskSourceEnum.class, message = "任务来源错误")
    private String taskSourceFlag;

    private Long taskUserId;

    /**
     * 指定节点ID
     * 格式：节点id_节点类型
     */
    private String siteId;
}
