package org.biosino.lf.pds.common.enums;

import lombok.Getter;
import org.springframework.core.env.Environment;

import java.util.Optional;

/**
 * 应用名称枚举
 */
@Getter
public enum AppNameEnum {
    PDS, PLOSP;


    public static Optional<AppNameEnum> getCurrAppName(Environment environment) {
        if (environment == null) {
            return Optional.empty();
        }
        final String appName = environment.getProperty("app.name");
        for (AppNameEnum appNameEnum : values()) {
            if (appNameEnum.name().equalsIgnoreCase(appName)) {
                return Optional.of(appNameEnum);
            }
        }
        return Optional.empty();
    }

}
