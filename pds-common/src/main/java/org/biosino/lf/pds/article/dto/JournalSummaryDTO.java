package org.biosino.lf.pds.article.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 期刊摘要VO
 *
 * <AUTHOR> @date 2025/8/28
 */
@Data
public class JournalSummaryDTO {

    /**
     * 期刊id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 期刊标题
     */
    private String title;

    /**
     * 点击量
     */
    private String hitNum;

    /**
     * 影响因子, 来自tb_dds_if_year 表
     */
    private String impactFactor;

    /**
     * 期刊分类，来自tb_dds_zky_section 表
     */
    private String category;
}
