package org.biosino.lf.pds.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

public class MonthUtil {

    private static Map<String, Integer> monthMap = new LinkedHashMap<String, Integer>();

    static {
        monthMap.put("jan", 1);
        monthMap.put("feb", 2);
        monthMap.put("mar", 3);
        monthMap.put("apr", 4);
        monthMap.put("may", 5);
        monthMap.put("jun", 6);
        monthMap.put("jul", 7);
        monthMap.put("aug", 8);
        monthMap.put("sep", 9);
        monthMap.put("oct", 10);
        monthMap.put("nov", 11);
        monthMap.put("dec", 12);
    }

    public static Integer getMonth(String mon) {
        if (StringUtils.isBlank(mon)) {
            return null;
        }
        try {
            return Integer.parseInt(mon.trim());
        } catch (Exception e) {
            return monthMap.get(mon.toLowerCase());
        }
    }
}
