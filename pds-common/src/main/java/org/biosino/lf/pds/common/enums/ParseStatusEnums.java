package org.biosino.lf.pds.common.enums;

/**
 * 解析状态
 *
 * <AUTHOR>
 */
public enum ParseStatusEnums {
    Pending(1, "待入库"), Complete(2, "已入库"), Fail(0, "失败"), Processing(3, "处理中");

    private final Integer code;
    private final String info;

    ParseStatusEnums(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
