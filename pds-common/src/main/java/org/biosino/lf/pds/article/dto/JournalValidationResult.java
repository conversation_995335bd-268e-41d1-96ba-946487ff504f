package org.biosino.lf.pds.article.dto;

import lombok.Data;
import org.biosino.lf.pds.article.domain.Journal;

/**
 * 期刊验证结果DTO
 *
 * <AUTHOR>
 */
@Data
public class JournalValidationResult {
    
    /**
     * 是否验证通过
     */
    private boolean valid;
    
    /**
     * 冲突的字段名称
     */
    private String conflictField;
    
    /**
     * 冲突的值
     */
    private String conflictValue;
    
    /**
     * 冲突的期刊记录
     */
    private Journal conflictJournal;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 创建验证通过的结果
     */
    public static JournalValidationResult valid() {
        JournalValidationResult result = new JournalValidationResult();
        result.setValid(true);
        return result;
    }
    
    /**
     * 创建验证失败的结果
     */
    public static JournalValidationResult invalid(String conflictField, String conflictValue, 
                                                  Journal conflictJournal, String errorMessage) {
        JournalValidationResult result = new JournalValidationResult();
        result.setValid(false);
        result.setConflictField(conflictField);
        result.setConflictValue(conflictValue);
        result.setConflictJournal(conflictJournal);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
