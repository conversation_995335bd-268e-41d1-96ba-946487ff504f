package org.biosino.lf.pds.common.utils.uuid;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;

/**
 * ID生成器工具类
 *
 * <AUTHOR>
 */
public class IdUtils {
    private static Snowflake snowflake;

    /**
     * 获取随机UUID
     *
     * @return 随机UUID
     */
    public static String randomUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线
     *
     * @return 简化的UUID，去掉了横线
     */
    public static String simpleUUID() {
        return UUID.randomUUID().toString(true);
    }

    /**
     * 获取随机UUID，使用性能更好的ThreadLocalRandom生成UUID
     *
     * @return 随机UUID
     */
    public static String fastUUID() {
        return UUID.fastUUID().toString();
    }

    /**
     * 简化的UUID，去掉了横线，使用性能更好的ThreadLocalRandom生成UUID
     *
     * @return 简化的UUID，去掉了横线
     */
    public static String fastSimpleUUID() {
        return UUID.fastUUID().toString(true);
    }


    /**
     * 雪花算法id生成器
     */
    private static synchronized Snowflake snowflakeInstance() {
        if (snowflake == null) {
            snowflake = new Snowflake(DateUtil.parse("2024-11-01", DatePattern.NORM_DATE_FORMAT).toJdkDate(), 0, 1, false);
        }
        return snowflake;
    }

    /**
     * 获取雪花算法id
     */
    public static long getSnowflakeNextId() {
        return snowflakeInstance().nextId();
    }

    public static String getSnowflakeNextIdStr() {
        return String.valueOf(getSnowflakeNextId());
    }
}
