package org.biosino.lf.pds.article.custbean.dto;

import lombok.Data;
import org.biosino.lf.pds.common.annotation.Excel;
import org.biosino.lf.pds.common.annotation.Excel.ColumnType;

import java.util.Date;

/**
 * 节点监控导出DTO
 *
 * <AUTHOR>
 */
@Data
public class NodeMonitorExportDTO {

    /**
     * 节点ID
     */
    @Excel(name = "节点ID", sort = 1, cellType = ColumnType.NUMERIC)
    private Integer id;

    /**
     * 节点名称
     */
    @Excel(name = "节点名称", sort = 2, width = 20)
    private String siteName;

    /**
     * 节点类型
     */
    @Excel(name = "节点类型", sort = 3, readConverterExp = "1=批次,2=源刊,3=高校")
    private String siteType;

    /**
     * 节点分组
     */
    /*@Excel(name = "节点分组", sort = 4, width = 15)
    private String siteGroup;*/

    @Excel(name = "脚本标签", sort = 5, width = 20)
    private String labelName;

    /**
     * 节点状态
     */
    @Excel(name = "节点状态", sort = 6, readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 节点IP
     */
    @Excel(name = "节点IP", sort = 7, width = 15)
    private String ip;

    /**
     * 最后握手时间
     */
    @Excel(name = "最后握手时间", sort = 8, width = 20, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastHandshakeTime;

    @Excel(name = "运行状态", sort = 9, width = 15)
    private String heartbeatSignal;

    /**
     * CPU逻辑核心数
     */
    @Excel(name = "CPU逻辑核心数", sort = 10, cellType = ColumnType.NUMERIC)
    private Integer cpuLogicalCount;

    /**
     * CPU物理核心数
     */
    @Excel(name = "CPU物理核心数", sort = 11, cellType = ColumnType.NUMERIC)
    private Integer cpuPhysicalCount;

    /**
     * CPU当前频率(MHz)
     */
    @Excel(name = "CPU当前频率(MHz)", sort = 12, cellType = ColumnType.NUMERIC, scale = 2)
    private Double cpuFreqCurrent;

    /**
     * 内存总量(GB)
     */
    @Excel(name = "内存总量(GB)", sort = 13, cellType = ColumnType.NUMERIC, scale = 2)
    private Double memoryTotal;

    /**
     * 内存已用(GB)
     */
    @Excel(name = "内存已用(GB)", sort = 14, cellType = ColumnType.NUMERIC, scale = 2)
    private Double memoryUsed;

    /**
     * 内存使用率(%)
     */
    @Excel(name = "内存使用率(%)", sort = 15, cellType = ColumnType.NUMERIC, scale = 2, suffix = "%")
    private Double memoryPercent;

    /**
     * 磁盘总量(GB)
     */
    @Excel(name = "磁盘总量(GB)", sort = 16, cellType = ColumnType.NUMERIC, scale = 2)
    private Double diskTotal;

    /**
     * 磁盘已用(GB)
     */
    @Excel(name = "磁盘已用(GB)", sort = 17, cellType = ColumnType.NUMERIC, scale = 2)
    private Double diskUsed;

    /**
     * 磁盘使用率(%)
     */
    @Excel(name = "磁盘使用率(%)", sort = 18, cellType = ColumnType.NUMERIC, scale = 2, suffix = "%")
    private Double diskPercent;

    /**
     * 执行中任务数
     */
    @Excel(name = "执行中任务数", sort = 19, cellType = ColumnType.NUMERIC)
    private Integer executingTasks;

    /**
     * 成功任务数
     */
    @Excel(name = "成功任务数", sort = 20, cellType = ColumnType.NUMERIC)
    private Integer successTasks;

    /**
     * 失败任务数
     */
    @Excel(name = "失败任务数", sort = 21, cellType = ColumnType.NUMERIC)
    private Integer failedTasks;

    /**
     * 忽略任务数
     */
//    @Excel(name = "忽略任务数", sort = 20, cellType = ColumnType.NUMERIC)
//    private Integer ignoreTasks;

    /**
     * 任务总数
     */
    @Excel(name = "任务总数", sort = 22, cellType = ColumnType.NUMERIC)
    private Integer totalTasks;

    /**
     * 线程数量
     */
//    @Excel(name = "线程数量", sort = 22, cellType = ColumnType.NUMERIC)
//    private Integer taskThreadNum;

    /**
     * 获取任务间隔时间(秒)
     */
    @Excel(name = "获取任务间隔时间(秒)", sort = 23, cellType = ColumnType.NUMERIC)
    private Integer obtainTaskInterval;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", sort = 24, width = 20, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
