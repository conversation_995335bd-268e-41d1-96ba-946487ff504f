package org.biosino.lf.pds.article.dto;

import lombok.Data;
import org.biosino.lf.pds.common.core.domain.BaseQuery;

/**
 * 文章附件上传信息查询参数
 */
@Data
public class ArticleAttachmentUploadQueryDTO extends BaseQuery {

    /**
     * 文献标题
     */
    private String title;

    /**
     * 上传人
     */
    private String creatorName;


    /**
     * 审核人
     */
    private String auditorName;


    /**
     * 状态（0-待审核，1-已接受，2-已驳回）
     */
    private Integer status;

}
