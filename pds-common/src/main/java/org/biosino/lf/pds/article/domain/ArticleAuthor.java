package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.article.config.LongListArrayTypeHandler;

import java.util.List;
import java.util.Set;

/**
 * 文章作者关联表
 */
@Data
@TableName(value = "deleted_tb_dds_article_author", autoResultMap = true)
public class ArticleAuthor {
    /**
     * 主键ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 文档ID
     */
    @TableField("doc_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long docId;

    /**
     * 作者ID
     */
    @TableField("author_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long authorId;

    /**
     * 机构ID
     */
    @TableField(value = "organization_id", typeHandler = LongListArrayTypeHandler.class)
    private List<Long> organizationId;

    /**
     * 作者顺序
     */
    @TableField("author_order")
    private Integer authorOrder;

    /**
     * 是否第一作者（yes/no）
     */
    @TableField("author_first")
    private String authorFirst;

    /**
     * 是否通讯作者（yes/no）
     */
    @TableField("author_correspond")
    private String authorCorrespond;

    @TableField(exist = false)
    private Author author;

    @TableField(exist = false)
    private Set<Organization> organizations;
}
