package org.biosino.lf.pds.common.enums.task;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 任务来源
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Getter
public enum TaskSourceEnum {
    PDS, PLOSP;

    public static List<String> allNames() {
        TaskSourceEnum[] values = values();
        List<String> list = new ArrayList<>();
        for (TaskSourceEnum value : values) {
            list.add(value.name());
        }
        return list;
    }
}
