package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户API密钥实体类
 *
 * <AUTHOR>
 * @date 2025/08/04
 */
@Data
@TableName(value = "tb_user_api_key", autoResultMap = true)
public class UserApiKey implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * API密钥名称
     */
    @TableField("key_name")
    private String keyName;

    /**
     * API密钥
     */
    @TableField("api_key")
    private String apiKey;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
}
