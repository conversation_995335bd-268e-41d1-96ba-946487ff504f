package org.biosino.lf.pds.article.custbean.vo;

import lombok.Data;

import java.util.Date;

/**
 * 脚本标签VO
 *
 * <AUTHOR>
 */
@Data
public class TbDdsScriptlabelVO {
    /**
     * 标签ID
     */
    private Integer labelId;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签类型
     */
    private String labelType;

    /**
     * 标签类型名称
     private String labelTypeStr;
     */

    /**
     * 状态
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关联节点数量
     */
    private Integer nodeCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
