package org.biosino.lf.pds.common.enums.task;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum ArticleAttachmentSourceEnum {

    pmc("由后台程序从pmc获得"), pubmed("由后台程序从pubmed获得"), sci_hub("由后台程序从sci-hub获得"),
    success_man("后台用户上传"), success_plosp("PLOSP用户上传"),
    site_download("由站点下载");

    public final String description;

    ArticleAttachmentSourceEnum(String description) {
        this.description = description;
    }

    public static boolean contains(String name) {
        if (StringUtils.isBlank(name))
            return false;
        for (ArticleAttachmentSourceEnum de : values()) {
            if (de.name().equalsIgnoreCase(name))
                return true;
        }
        return false;
    }
}
