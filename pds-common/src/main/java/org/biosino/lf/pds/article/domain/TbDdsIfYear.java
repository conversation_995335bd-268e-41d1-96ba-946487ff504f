package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 影响因子、JCR分区年度表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "tb_dds_if_year", autoResultMap = true)
public class TbDdsIfYear {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 期刊ID
     */
    @TableField("journal_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long journalId;

    /**
     * 年份
     */
    @TableField("year")
    private String year;

    /**
     * 影响因子
     */
    @TableField("impact_factor")
    private String impactFactor;

    /**
     * JCR分区
     */
    @TableField("jcr_quartile")
    private String jcrQuartile;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
}
