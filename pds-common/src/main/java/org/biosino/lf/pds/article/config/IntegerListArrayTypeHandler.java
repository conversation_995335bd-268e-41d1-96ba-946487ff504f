package org.biosino.lf.pds.article.config;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;
import java.util.List;

public class IntegerListArrayTypeHandler extends BaseTypeHandler<List<Integer>> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, List<Integer> integers, JdbcType jdbcType) throws SQLException {
        if (integers != null) {
            Array array = preparedStatement.getConnection().createArrayOf(JdbcType.INTEGER.name(), integers.toArray(new Integer[0]));
            preparedStatement.setArray(i, array);
        }
    }

    @Override
    public List<Integer> getNullableResult(ResultSet resultSet, String s) throws SQLException {
        Array array = resultSet.getArray(s);
        if (array == null) {
            return null;
        }
        Integer[] result = (Integer[]) array.getArray();
        array.free();
        return List.of(result);
    }

    @Override
    public List<Integer> getNullableResult(ResultSet resultSet, int i) throws SQLException {
        Array array = resultSet.getArray(i);
        if (array == null) {
            return null;
        }
        Integer[] result = (Integer[]) array.getArray();
        array.free();
        return List.of(result);
    }

    @Override
    public List<Integer> getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        Array array = callableStatement.getArray(i);
        if (array == null) {
            return null;
        }
        Integer[] result = (Integer[]) array.getArray();
        array.free();
        return List.of(result);
    }
}

