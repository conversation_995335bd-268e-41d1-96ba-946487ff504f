package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * MeSH主题词表
 * <AUTHOR>
 */
@Data
@TableName(value = "tb_dds_mesh", autoResultMap = true)
public class TbDdsMesh {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * MeSH唯一标识符
     */
    @TableField("mesh_ui")
    private String meshUi;

    /**
     * MeSH名称
     */
    @TableField("mesh_name")
    private String meshName;

    /**
     * MeSH类型
     */
    @TableField("mesh_type")
    private String meshType;

    /**
     * 树编号
     */
    @TableField("tree_numbers")
    private String treeNumbers;

    /**
     * 修订日期
     */
    @TableField("date_revised")
    private String dateRevised;

    /**
     * 术语信息
     */
    @TableField("terms")
    private String terms;

    /**
     * 父级信息
     */
    @TableField("parent_info")
    private String parentInfo;

    /**
     * 子级信息
     */
    @TableField("child_info")
    private String childInfo;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
}
