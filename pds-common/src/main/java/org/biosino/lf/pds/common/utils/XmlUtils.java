package org.biosino.lf.pds.common.utils;

import org.xml.sax.InputSource;
import org.xml.sax.helpers.DefaultHandler;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.StringReader;

public class XmlUtils {
    // 线程安全地缓存 SAXParser
    private static final ThreadLocal<SAXParser> threadLocalParser = ThreadLocal.withInitial(() -> {
        try {
            SAXParserFactory factory = SAXParserFactory.newInstance();
            factory.setNamespaceAware(true);
            return factory.newSAXParser();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create SAXParser", e);
        }
    });

    /**
     * 校验 XML 是否结构合法（well-formed）
     */
    public static boolean isWellFormedXML(String xmlContent) {
        try {
            SAXParser parser = threadLocalParser.get();
            InputSource source = new InputSource(new StringReader(xmlContent));
            // 仅验证结构，不处理内容
            parser.parse(source, new DefaultHandler());
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
