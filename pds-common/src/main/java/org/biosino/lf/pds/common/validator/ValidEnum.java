package org.biosino.lf.pds.common.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 枚举name取值范围验证
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EnumValidator.class)
@Documented
public @interface ValidEnum {
    Class<? extends Enum<?>> enumClass();

    String message() default "Must be a valid enumeration value";

    Class<?>[] groups() default {};

    // 忽略大小写的配置，默认为false
    boolean ignoreCase() default false;

    // 是否允许null值，默认为true，表示若值为null则默认校验通过
    boolean allowNull() default true;

    Class<? extends Payload>[] payload() default {};

}
