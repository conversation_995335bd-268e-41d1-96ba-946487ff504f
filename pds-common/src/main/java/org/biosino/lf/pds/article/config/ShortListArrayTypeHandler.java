package org.biosino.lf.pds.article.config;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;
import java.util.List;

public class ShortListArrayTypeHandler extends BaseTypeHandler<List<Short>> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, List<Short> integers, JdbcType jdbcType) throws SQLException {
        if (integers != null) {
            Array array = preparedStatement.getConnection().createArrayOf(JdbcType.SMALLINT.name(), integers.toArray(new Short[0]));
            preparedStatement.setArray(i, array);
        }
    }

    @Override
    public List<Short> getNullableResult(ResultSet resultSet, String s) throws SQLException {
        Array array = resultSet.getArray(s);
        if (array == null) {
            return null;
        }
        Short[] result = (Short[]) array.getArray();
        array.free();
        return List.of(result);
    }

    @Override
    public List<Short> getNullableResult(ResultSet resultSet, int i) throws SQLException {
        Array array = resultSet.getArray(i);
        if (array == null) {
            return null;
        }
        Short[] result = (Short[]) array.getArray();
        array.free();
        return List.of(result);
    }

    @Override
    public List<Short> getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        Array array = callableStatement.getArray(i);
        if (array == null) {
            return null;
        }
        Short[] result = (Short[]) array.getArray();
        array.free();
        return List.of(result);
    }
}

