package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文章解读表
 * <AUTHOR>
 */
@Data
@TableName(value = "tb_dds_article_interpretation", autoResultMap = true)
public class ArticleInterpretation {
    /**
     * 雪花算法生成的ID
     */
    @TableField("id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 关联文章ID
     */
    @TableField("doc_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long docId;

    /**
     * 模型名称
     */
    @TableField("model_name")
    private String modelName;

    /**
     * 模型版本
     */
    @TableField("model_version")
    private String modelVersion;

    /**
     * 输入的提示词
     */
    private String prompt;

    /**
     * 模型输出结果
     */
    private String result;

    /**
     * 执行状态 0=失败, 1=成功
     */
    private Integer status;

    /**
     * 输入token数
     */
    @TableField("tokens_input")
    private Integer tokensInput;

    /**
     * 输出token数
     */
    @TableField("tokens_output")
    private Integer tokensOutput;

    /**
     * 耗时 (毫秒)
     */
    @TableField("latency_ms")
    private Integer latencyMs;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
}
