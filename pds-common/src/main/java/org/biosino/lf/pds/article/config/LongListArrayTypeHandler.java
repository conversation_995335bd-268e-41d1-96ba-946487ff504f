package org.biosino.lf.pds.article.config;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;
import java.util.Arrays;
import java.util.List;

public class LongListArrayTypeHandler extends BaseTypeHandler<List<Long>> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, List<Long> strings, JdbcType jdbcType) throws SQLException {
        if (strings != null) {
            Array array = preparedStatement.getConnection().createArrayOf(JdbcType.BIGINT.name(), strings.toArray(new Long[0]));
            preparedStatement.setArray(i, array);
        }
    }

    @Override
    public List<Long> getNullableResult(ResultSet resultSet, String s) throws SQLException {
        Array array = resultSet.getArray(s);
        if (array == null) {
            return null;
        }
        Long[] result = (Long[]) array.getArray();
        array.free();
        return Arrays.asList(result);
    }

    @Override
    public List<Long> getNullableResult(ResultSet resultSet, int i) throws SQLException {
        Array array = resultSet.getArray(i);
        if (array == null) {
            return null;
        }
        Long[] result = (Long[]) array.getArray();
        array.free();
        return List.of(result);
    }

    @Override
    public List<Long> getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        Array array = callableStatement.getArray(i);
        if (array == null) {
            return null;
        }
        Long[] result = (Long[]) array.getArray();
        array.free();
        return List.of(result);
    }
}

