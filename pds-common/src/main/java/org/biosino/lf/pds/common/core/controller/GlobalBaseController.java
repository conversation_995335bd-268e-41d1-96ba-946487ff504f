package org.biosino.lf.pds.common.core.controller;

import cn.hutool.core.util.StrUtil;
import org.biosino.lf.pds.common.utils.DateUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.beans.PropertyEditorSupport;
import java.util.Date;

/**
 * 公共基础控制层
 *
 * <AUTHOR>
 */
public class GlobalBaseController {
    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // 设置request params Collection最大长度，默认是256，要放在下面两个前面
        binder.setAutoGrowCollectionLimit(5000);
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });

        // 字符串 类型转换
        binder.registerCustomEditor(String.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) throws IllegalArgumentException {
                setValue(StrUtil.trimToNull(text));
            }
        });

    }
}
