package org.biosino.lf.pds.article.custbean.vo;

import lombok.Data;
import org.biosino.lf.pds.common.core.page.TableDataInfo;

import java.util.Date;
import java.util.List;

/**
 * 任务跟踪VO
 *
 * <AUTHOR>
 */
@Data
public class TbDdsTaskVO {
    private TableDataInfo tableDataInfo;
    private String downloadingTotal;
    private String waitingTotal;
    private String currUserEmail;

    @Data
    public static class TaskItem {
        private String taskId;
        private String taskName;
        // 文献总数
        private Integer totalCount;
        // 状态
        private String status;
        private String statusStr;

        // 测试模式下，指定的节点ID
        private Integer siteId;

        //任务列表数量统计
        private Integer stockCount = 0;//存量库
        private Integer downloadedCount = 0;//已下载,节点自动下载
        private Integer failedCount = 0;//下载失败
        private Integer pendingCount = 0;//待下载
        private Integer manUploadCount = 0;//手工上传
        private Integer notExistCount = 0;//库中不存在
        private Date updateTime;

        private Date createTime;
        private Short priority;
        private String creator;
        private Long creatorId;
        private boolean testFlag;
        private List<String> nodeTypes;

        public Integer getNotExistCount() {
            notExistCount = totalCount - stockCount - downloadedCount - manUploadCount - failedCount - pendingCount;
            return Math.max(notExistCount, 0);
        }

        // 为数字类型字段添加安全的set方法，null值自动设置为0
        public void setTotalCount(Integer totalCount) {
            this.totalCount = totalCount == null ? 0 : totalCount;
        }

        public void setSiteId(Integer siteId) {
            this.siteId = siteId == null ? 0 : siteId;
        }

        public void setStockCount(Integer stockCount) {
            this.stockCount = stockCount == null ? 0 : stockCount;
        }

        public void setDownloadedCount(Integer downloadedCount) {
            this.downloadedCount = downloadedCount == null ? 0 : downloadedCount;
        }

        public void setFailedCount(Integer failedCount) {
            this.failedCount = failedCount == null ? 0 : failedCount;
        }

        public void setPendingCount(Integer pendingCount) {
            this.pendingCount = pendingCount == null ? 0 : pendingCount;
        }

        public void setManUploadCount(Integer manUploadCount) {
            this.manUploadCount = manUploadCount == null ? 0 : manUploadCount;
        }

        public void setNotExistCount(Integer notExistCount) {
            this.notExistCount = notExistCount == null ? 0 : notExistCount;
        }
    }

}
