package org.biosino.lf.pds.article.custbean.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.biosino.lf.pds.common.core.domain.BaseEntity;
import org.biosino.lf.pds.common.enums.StatusEnums;

import java.util.List;

/**
 * 脚本信息
 *
 * <AUTHOR>
 */
@Data
public class ScriptDTO extends BaseEntity {
    /**
     * 脚本ID
     */
    private Integer id;

    /**
     * 脚本名称
     */
    @NotBlank(message = "脚本名称不能为空")
    private String scriptName;

    /**
     * 脚本类型（批次、源刊、高校等）
     */
    @NotEmpty(message = "脚本类型不能为空")
    private List<String> scriptType;

    /**
     * 临时文件名（上传后返回的文件名）
     */
    private String tempFileName;

    /**
     * 脚本文件ID（编辑时使用，如果不为空则表示文件未修改）
     */
    private String scriptFileId;

    /**
     * 状态（0-正常，1-停用）
     * 字典类型：sys_normal_disable
     */
//    @NotBlank(message = "状态不能为空")
    private String status = StatusEnums.ENABLE.getCode().toString();

    /**
     * 备注
     */
    private String remark;
}
