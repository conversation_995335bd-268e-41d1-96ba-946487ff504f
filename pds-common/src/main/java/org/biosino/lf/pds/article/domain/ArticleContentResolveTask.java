package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/27
 */
@Data
@TableName(value = "tb_dds_article_content_resolve_task", autoResultMap = true)
public class ArticleContentResolveTask {

    /**
     * 文献id
     */
    @TableId(value = "doc_id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long docId;

    /**
     * 实体解析状态
     */
    @TableField("entity_parse_status")
    private Integer entityParseStatus;

    /**
     * 实体解析错误状态
     */
    @TableField("entity_parse_error_msg")
    private String entityParseErrorMsg;

    /**
     * 实体解析开始时间
     */
    @TableField("entity_parse_start_time")
    private Date entityParseStartTime;

    /**
     * 实体解析日志
     */
    @TableField("entity_parse_end_time")
    private Date entityParseEndTime;

    /**
     * 实体解析结果
     */
    @TableField("entity_parse_result")
    private String entityParseResult;

    /**
     * 实体解析日志
     */
    @TableField("entity_parse_log")
    private String entityParseLog;


    /**
     * 全文识别状态
     */
    @TableField("fulltext_parse_status")
    private Integer fulltextParseStatus;

    /**
     * 全文识别错误消息
     */
    @TableField("fulltext_parse_error_msg")
    private String fulltextParseErrorMsg;

    /**
     * 全文识别开始时间
     */
    @TableField("fulltext_parse_start_time")
    private Date fulltextParseStartTime;

    /**
     * 全文识别结束时间
     */
    @TableField("fulltext_parse_end_time")
    private Date fulltextParseEndTime;

    /**
     * 全文识别日志
     */
    @TableField("fulltext_parse_log")
    private String fulltextParseLog;

    /**
     * 这篇文章的摘要
     */
    @TableField(exist = false)
    private String articleAbstract;

    /**
     * 这篇文章对应pdf文件的路径
     */
    @TableField(exist = false)
    private String filePath;
}
