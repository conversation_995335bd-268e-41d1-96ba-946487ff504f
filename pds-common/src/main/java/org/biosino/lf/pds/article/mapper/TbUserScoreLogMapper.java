package org.biosino.lf.pds.article.mapper;

import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.domain.TbUserScoreLog;

/**
 * <AUTHOR>
 */
public interface TbUserScoreLogMapper extends CommonMapper<TbUserScoreLog> {

    /**
     * 根据用户ID查询用户总积分
     */
    Long sumUserScore(@Param("userId") Long userId);

    /**
     * 批量更新用户收藏表中的doc_id字段（用于文章合并）
     * 将所有匹配源doc_id的记录更新为目标doc_id
     *
     * @param targetDocId 目标文档ID
     * @param sourceDocId 源文档ID
     * @return 更新的记录数
     */
    int updateDocIdBatch(@Param("targetDocId") Long targetDocId, @Param("sourceDocId") Long sourceDocId);

}
