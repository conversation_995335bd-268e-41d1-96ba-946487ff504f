package org.biosino.lf.pds.common.enums.task;

import lombok.Getter;

/**
 * 任务优先级
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Getter
public enum TaskPriorityEnum {
    sys(1, "系统预留"),
    common(2, "普通"),
    adv(3, "高"),
    custom(0, "自定义（0~99）"),
    plosp(100, "PLOSP文献传递");

    private final short code;
    private final String text;

    TaskPriorityEnum(int code, String text) {
        this.code = (short) code;
        this.text = text;
    }
}
