package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 基因信息表
 */
@Data
@TableName(value = "tb_dds_gene", autoResultMap = true)
public class Gene {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 基因符号
     */
    @TableField("gene_symbol")
    private String geneSymbol;
}
