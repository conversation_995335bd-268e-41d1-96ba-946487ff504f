package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 参考文献表
 */
@Data
@TableName(value = "tb_dds_reference", autoResultMap = true)
public class Reference {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 文档ID
     */
    @TableField("doc_id")
    private Long docId;

    /**
     * 引用信息
     */
    @TableField("citation")
    private String citation;

    /**
     * PubMed ID
     */
    @TableField("pmid")
    private String pmid;

    /**
     * PMC ID
     */
    @TableField("pmcid")
    private String pmcid;

    /**
     * DOI
     */
    @TableField("doi")
    private String doi;
}
