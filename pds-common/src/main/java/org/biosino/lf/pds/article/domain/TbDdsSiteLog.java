package org.biosino.lf.pds.article.domain;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 站点日志
 *
 * <AUTHOR>
 */
@Data
@TableName("tb_dds_site_log")
public class TbDdsSiteLog implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    private Integer siteId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long fileId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long fileSize;

    private Date createTime;

    @TableField(exist = false)
    private String fileSizeStr;

    public void setFileSize(Long fileSize) {
        setFileSizeStr(fileSize == null ? "0" : FileUtil.readableFileSize(fileSize));
        this.fileSize = fileSize;
    }

}