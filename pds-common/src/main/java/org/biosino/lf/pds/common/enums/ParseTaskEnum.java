package org.biosino.lf.pds.common.enums;


/**
 * 解析任务状态
 */
public enum ParseTaskEnum {
    error(-1, "错误"),
    ready(1, "准备"),
    queuing(2, "排队中"),
    running(3, "运行中"),
    success(4, "成功");
    private final Integer code;
    private final String info;

    ParseTaskEnum(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

    public Integer getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
