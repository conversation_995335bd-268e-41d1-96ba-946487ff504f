package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 文献解析的元数据表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "tb_dds_article_parse", autoResultMap = true)
public class ArticleParse {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 文献ID
     */
    @TableField("doc_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long docId;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * xml本地存储路径
     */
    @TableField("local_path")
    private String localPath;

    /**
     * 文件来源的原始ID
     */
    @TableField("source_id")
    private String sourceId;

    /**
     * 文件的来源，可能有：Pubmed、PMC、BioRxiv、MedRxiv
     */
    @TableField("source")
    private String source;

    /**
     * 解析的处理状态，1 未入库、2 已入库、0 失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 解析错误原因
     */
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 文件MD5值
     */
    @TableField("file_md5")
    private String fileMd5;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 文件版本
     */
    @TableField("version")
    private Integer version;

    /**
     * 文件后缀
     */
    @TableField("content_type")
    private String contentType;

}
