package org.biosino.lf.pds.article.custbean.vo;

import lombok.Data;
import org.biosino.lf.pds.article.custbean.dto.SystemInfoDTO;

import java.util.Date;

/**
 * 节点VO
 *
 * <AUTHOR>
 */
@Data
public class SiteVO {
    /**
     * 节点ID
     */
    private Integer id;

    /**
     * 节点名称
     */
    private String siteName;

    /**
     * 节点简写
     */
    private String siteAbbr;

    /**
     * 节点类型（批次1、源刊2、高校3）
     */
    private String siteType;

    /**
     * 节点状态（0-正常，1-停用）
     * 字典类型：sys_normal_disable
     */
    private String status;

    /**
     * 节点分组
     */
//    private String siteGroup;

    /**
     * 获取任务间隔时间（秒）
     */
    private Integer obtainTaskInterval;

    /**
     * 单位
     */
    private String unit;

    /**
     * 地址
     */
    private String address;

    /**
     * 脚本标签ID
     */
    private Integer scriptlabelId;

    /**
     * 脚本标签名称
     */
    private String labelName;

    /**
     * 创建者ID
     */
    private Long creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 线程数量
     */
    private Integer taskThreadNum;

    /**
     * 系统信息
     */
    private SystemInfoDTO systemInfoDTO;

    /**
     * 节点IP
     */
    private String ip;

    private Date lastHandshakeTime;

    /**
     * 心跳信号: 连接、断开
     */
    private String heartbeatSignal;

    /**
     * 节点统计信息
     */
    private Integer total;
    private Integer execute;
    private Integer failed;
    private Integer success;

    private Integer toRefreshLog;
}