package org.biosino.lf.pds.article.custbean.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TransmitListVO {
    private String articleId;
    private String title;
    private String status;
    private List<String> author;

    private String journal;
    private String year;

    private String volume;
    private String issue;
    private String page;
    private String pmid;
    private String doi;

    private String requestTime;

    private long paperId;
    private String taskId;

//    private Long id;
//    private String transmitId;
//    private String source;
//    private String description;

}
