package org.biosino.lf.pds.article.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.domain.ArticleCorrection;
import org.biosino.lf.pds.article.dto.ArticleCorrectionQueryDTO;

import java.util.List;

/**
 * 文献纠错信息 Mapper 接口
 */
@Mapper
public interface ArticleCorrectionMapper extends BaseMapper<ArticleCorrection> {

    /**
     * 查询纠错列表（关联用户表）
     *
     * @param queryDTO 查询条件
     * @return 纠错列表
     */
    List<ArticleCorrection> selectDefectList(ArticleCorrectionQueryDTO queryDTO);
    
    /**
     * 批量更新文献纠错表中的doc_id字段（用于文章合并）
     * 将所有匹配源doc_id的记录更新为目标doc_id
     *
     * @param targetDocId 目标文档ID
     * @param sourceDocId 源文档ID
     * @return 更新的记录数
     */
    int updateDocIdBatch(@Param("targetDocId") Long targetDocId, @Param("sourceDocId") Long sourceDocId);
}
