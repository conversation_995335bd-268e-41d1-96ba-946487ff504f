package org.biosino.lf.pds.article.dto;

import cn.hutool.core.date.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.lf.pds.common.core.domain.BaseQuery;

import java.util.Date;

/**
 * 文献纠错信息查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ArticleQueryDTO extends BaseQuery {

    /**
     * PMID
     */
    private Long pmid;

    /**
     * PMCID
     */
    private Long pmcId;

    /**
     * 自定义ID
     */
    private Long customId;

    /**
     * DOI
     */
    private String doi;

    /**
     * 文献标题
     */
    private String title;

    /**
     * 期刊名称
     */
    private String journalName;

    /**
     * 卷号
     */
    private String volume;

    private Integer yearStart;

    private Integer yearEnd;

    private Integer monthStart;

    private Integer monthEnd;

    private Integer dayStart;

    private Integer dayEnd;


    @Override
    public void setBeginTime(Date beginTime) {
        super.setBeginTime(beginTime);
        if (beginTime != null) {
            int year = DateUtil.year(beginTime);
            int month = DateUtil.month(beginTime);
            int day = DateUtil.dayOfMonth(beginTime);
            this.setYearStart(year);
            this.setMonthStart(month);
            this.setDayStart(day);
        }
    }

    @Override
    public void setEndTime(Date endTime) {
        super.setEndTime(endTime);
        if (endTime != null) {
            int year = DateUtil.year(endTime);
            int month = DateUtil.month(endTime);
            int day = DateUtil.dayOfMonth(endTime);
            this.setYearEnd(year);
            this.setMonthEnd(month);
            this.setDayEnd(day);
        }
    }
}
