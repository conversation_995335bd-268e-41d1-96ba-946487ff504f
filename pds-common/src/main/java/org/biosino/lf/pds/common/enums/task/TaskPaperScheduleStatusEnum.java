package org.biosino.lf.pds.common.enums.task;

import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
public enum TaskPaperScheduleStatusEnum {

    executing("执行中"), success("成功"), failed("失败"), ignore("忽略");

    private final String description;

    TaskPaperScheduleStatusEnum(String description) {
        this.description = description;
    }

    public static List<TaskPaperScheduleStatusEnum> getNotCompleteTaskPaperScheduleStatus() {
        List<TaskPaperScheduleStatusEnum> list = new ArrayList<>();
        list.add(TaskPaperScheduleStatusEnum.executing);
        return list;
    }

    public static Map<String, String> toMap() {
        Map<String, String> map = new LinkedHashMap<>();
        for (TaskPaperScheduleStatusEnum e : values()) {
            map.put(e.name(), e.getDescription());
        }
        return map;
    }
}
