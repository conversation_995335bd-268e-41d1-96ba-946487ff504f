package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "tb_user_score_log")
public class TbUserScoreLog {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Integer userId;

    /**
     * @see org.biosino.lf.pds.common.enums.task.ScoreSourceEnum
     */
    private String source;

    private Date createTime;

    private Long docId;

    private Integer score;

    private String comment;
}