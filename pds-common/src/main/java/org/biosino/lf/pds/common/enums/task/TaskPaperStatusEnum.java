package org.biosino.lf.pds.common.enums.task;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Getter
public enum TaskPaperStatusEnum {

    waiting("等待站点执行"), executing("正在执行"), failed("执行失败"),
    success_exist("存量库"), success_auto("自动下载成功"), success_man("手动上传成功");

    private final String description;

    TaskPaperStatusEnum(String description) {
        this.description = description;
    }

    public static List<TaskPaperStatusEnum> getNotCompleteTaskpaperStatus() {
        List<TaskPaperStatusEnum> list = new ArrayList<>();
        list.add(TaskPaperStatusEnum.waiting);
        list.add(TaskPaperStatusEnum.executing);
        return list;
    }

    public static List<TaskPaperStatusEnum> getCompleteTaskpaperStatus() {
        List<TaskPaperStatusEnum> list = new ArrayList<>();
        list.add(TaskPaperStatusEnum.failed);
        list.add(TaskPaperStatusEnum.success_exist);
        list.add(TaskPaperStatusEnum.success_auto);
        list.add(TaskPaperStatusEnum.success_man);
        return list;
    }

    public static List<TaskPaperStatusEnum> getSuccessTaskPaperStatus() {
        List<TaskPaperStatusEnum> list = new ArrayList<>();
        list.add(TaskPaperStatusEnum.success_exist);
        list.add(TaskPaperStatusEnum.success_auto);
        list.add(TaskPaperStatusEnum.success_man);
        return list;
    }

    public static List<TaskPaperStatusEnum> getNotStartTaskPaperStatus() {
        List<TaskPaperStatusEnum> list = new ArrayList<>();
        list.add(TaskPaperStatusEnum.waiting);
        return list;
    }

    public static List<String> getNames() {
        List<String> l = new ArrayList<>();
        for (TaskPaperStatusEnum e : values()) {
            l.add(e.name());
        }
        return l;
    }

    public static String getDescription(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }
        return TaskPaperStatusEnum.valueOf(name).getDescription();
    }

    public static Map<String, String> toMap() {
        Map<String, String> map = new LinkedHashMap<>();
        for (TaskPaperStatusEnum e : values()) {
            map.put(e.name(), e.getDescription());
        }
        return map;
    }
}
