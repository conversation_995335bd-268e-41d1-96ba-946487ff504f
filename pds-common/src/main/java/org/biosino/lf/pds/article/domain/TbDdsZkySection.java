package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 中科院分区表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "tb_dds_zky_section", autoResultMap = true)
public class TbDdsZkySection {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 期刊ID
     */
    @TableField("journal_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long journalId;

    /**
     * 顶级分类
     */
    @TableField("top")
    private String top;

    /**
     * 年份
     */
    @TableField("year")
    private String year;

    /**
     * 大类
     */
    @TableField("large_category")
    private String largeCategory;

    /**
     * 大类分区
     */
    @TableField("large_category_section")
    private Integer largeCategorySection;

    /**
     * 子类1
     */
    @TableField("subclass_1")
    private String subclass1;

    /**
     * 子类1分区
     */
    @TableField("subclass_1_section")
    private Integer subclass1Section;

    /**
     * 子类2
     */
    @TableField("subclass_2")
    private String subclass2;

    /**
     * 子类2分区
     */
    @TableField("subclass_2_section")
    private Integer subclass2Section;

    /**
     * 子类3
     */
    @TableField("subclass_3")
    private String subclass3;

    /**
     * 子类3分区
     */
    @TableField("subclass_3_section")
    private Integer subclass3Section;

    /**
     * 子类4
     */
    @TableField("subclass_4")
    private String subclass4;

    /**
     * 子类4分区
     */
    @TableField("subclass_4_section")
    private Integer subclass4Section;

    /**
     * 子类5
     */
    @TableField("subclass_5")
    private String subclass5;

    /**
     * 子类5分区
     */
    @TableField("subclass_5_section")
    private Integer subclass5Section;

    /**
     * 子类6
     */
    @TableField("subclass_6")
    private String subclass6;

    /**
     * 子类6分区
     */
    @TableField("subclass_6_section")
    private Integer subclass6Section;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
}
