package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户积分变动记录实体
 */
@Data
@TableName(value = "tb_user_points_record", autoResultMap = true)
public class PlospUserPointsRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    @TableField(exist = false)
    private String username;

    /**
     * 变动积分（正数增加，负数减少）
     */
    @TableField("points")
    private Integer points;

    /**
     * 变动类型（download_pdf:下载文献, upload_literature:上传文献, admin_gift:管理员赠送, admin_deduct:管理员扣减, register_reward:注册奖励）
     */
    @TableField("type")
    private String type;

    /**
     * 变动原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 变动时间
     */
    @TableField("change_time")
    private Date changeTime;

    /**
     * 操作人ID
     */
    @TableField("operator_id")
    private Long operatorId;

    @TableField(exist = false)
    private String operatorName;

    /**
     * 相关文献ID
     */
    @TableField("literature_id")
    private Long literatureId;

    /**
     * 相关文献标题
     */
    @TableField("literature_title")
    private String literatureTitle;
}
