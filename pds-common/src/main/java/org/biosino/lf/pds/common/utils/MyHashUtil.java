package org.biosino.lf.pds.common.utils;

import cn.hutool.core.io.FileUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.biosino.lf.pds.common.exception.ServiceException;

import java.io.File;
import java.io.InputStream;

/**
 * 哈希工具类
 *
 * <AUTHOR>
 */
public class MyHashUtil {

    public static String md5(File file) {
        if (file == null || !file.exists()) {
            throw new ServiceException("文件未指定或不存在，无法生成MD5: " + file);
        }
        //return SecureUtil.md5(file);
        InputStream inputStream = null;
        try {
            inputStream = FileUtil.getInputStream(file);
            return md5(inputStream, false);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    public static String md5(final InputStream inputStream) {
        return md5(inputStream, true);
    }

    public static String md5(final InputStream inputStream, final boolean close) {
        if (inputStream == null) {
            throw new ServiceException("输入流不能为空，无法生成MD5");
        }
        try {
            return DigestUtils.md5Hex(inputStream);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        } finally {
            if (close) {
                IOUtils.closeQuietly(inputStream);
            }
        }
    }

}
