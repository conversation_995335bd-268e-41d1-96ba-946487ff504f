package org.biosino.lf.pds.article.dto;

import lombok.Data;
import org.biosino.lf.pds.common.core.domain.BaseQuery;

/**
 * 前台用户查询DTO
 */
@Data
public class PlospUserQueryDTO extends BaseQuery {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;


    /**
     * 姓名
     */
    private String userName;

    /**
     * 组织机构
     */
    private String organization;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 用户类型
     */
    private String userType;

}
