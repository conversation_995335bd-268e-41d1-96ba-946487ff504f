package org.biosino.lf.pds.article.custbean.dto;

import lombok.Data;

import java.util.List;

/**
 * 任务详情查询DTO
 *
 * <AUTHOR>
 */
@Data
public class ArticleViewQueryDTO {
    private String taskId;
    private String searchLiteratureId;
    private String pmid;
    private String searchStatus;
    private Integer custLimit;
    private Integer custOffset;

    private List<String> statusList;
    private String downloadStatus;
}
