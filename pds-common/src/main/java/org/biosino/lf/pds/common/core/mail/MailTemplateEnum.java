package org.biosino.lf.pds.common.core.mail;

import lombok.Getter;

@Getter
public enum MailTemplateEnum {

    TASK_PDF_DOWNLOAD("PDS文献传递任务结果下载地址", "pds_task_pdf_down.ftlh"),
    SITE_ERROR("PDS文献传递节点异常报告", "site_error.ftlh"),
    ;

    private final String operatorName;
    private final String templateName;

    MailTemplateEnum(String operatorName, String templateName) {
        this.operatorName = operatorName;
        this.templateName = templateName;
    }


}
