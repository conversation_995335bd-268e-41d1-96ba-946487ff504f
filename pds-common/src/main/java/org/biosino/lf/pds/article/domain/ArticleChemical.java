package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 文章化学物质关联表
 */
@Data
@TableName(value = "tb_dds_article_chemical", autoResultMap = true)
public class ArticleChemical {
    /**
     * 文档ID
     */
    @TableField("doc_id")
    private Long docId;

    /**
     * 化学物质ID
     */
    @TableField("chemical_id")
    private Long chemicalId;
}
