package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 脚本标签
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@TableName("tb_dds_scriptlabel")
public class TbDdsScriptlabel implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String name;

    /**
     * 批次1、源刊2、高校3(单选)
     * <p>
     * 字典类型:script_type
     */
    private String type;

    /**
     * 0("正常"), 1("停用");
     * 字典类型:sys_normal_disable
     *
     * @see org.biosino.lf.pds.common.enums.StatusEnums
     */
    private String status;

    private String remark;

    private Long creator;

    private Date createTime;

    private Date updateTime;
}
