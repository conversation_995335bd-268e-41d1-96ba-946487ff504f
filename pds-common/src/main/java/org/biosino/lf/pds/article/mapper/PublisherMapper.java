package org.biosino.lf.pds.article.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.domain.Publisher;
import org.biosino.lf.pds.article.dto.PublisherQueryDTO;

import java.util.List;

/**
 * 出版社 Mapper 接口
 */
@Mapper
public interface PublisherMapper extends BaseMapper<Publisher> {

    /**
     * 查询出版社列表
     *
     * @param queryDTO 查询条件
     * @return 出版社列表
     */
    List<Publisher> selectPublisherList(PublisherQueryDTO queryDTO);

    /**
     * 查找与指定出版社名称冲突的出版社
     * @param name 出版社名称
     * @param excludeIds 要排除的出版社ID列表
     * @return 冲突的出版社列表
     */
    List<Publisher> findConflictingPublishersByName(@Param("name") String name, @Param("excludeIds") List<Long> excludeIds);

    /**
     * 查找与指定别名冲突的出版社
     * @param alias 别名
     * @param excludeIds 要排除的出版社ID列表
     * @return 冲突的出版社列表
     */
    List<Publisher> findConflictingPublishersByAlias(@Param("alias") String alias, @Param("excludeIds") List<Long> excludeIds);

}
