package org.biosino.lf.pds.article.dto;

import lombok.Data;
import org.biosino.lf.pds.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 文献信息
 *
 * <AUTHOR>
 */
@Data
public class ArticleParseDTO extends BaseEntity {

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件来源的原始ID
     */
    private String sourceId;

    /**
     * 文件的来源，可能有：Pubmed、PMC、BioRxiv、MedRxiv
     */
    private String source;

    /**
     * 解析的处理状态，1 未入库、2 已入库、0 失败
     */
    private Integer status;

    /**
     * 解析错误原因
     */
    private String errorMsg;

    /**
     * 更新时间
     */
    private Date updateTime;

}
