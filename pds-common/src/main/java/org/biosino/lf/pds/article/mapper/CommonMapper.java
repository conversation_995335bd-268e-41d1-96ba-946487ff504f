package org.biosino.lf.pds.article.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;

import java.util.List;

/**
 * 公共mapper
 *
 * <AUTHOR>
 */
public interface CommonMapper<T> extends BaseMapper<T> {

    /**
     * 查询单个数据
     * 使用limit 1仅取查询的第一条数据
     *
     * @return 单个数据()
     */
    default T findOne(LambdaQueryWrapper<T> queryWrapper) {
        final List<T> list = selectList(PageDTO.of(1, 1), queryWrapper);
        T data = null;
        if (CollUtil.isNotEmpty(list)) {
            data = list.get(0);
        }
        return data;
    }

}
