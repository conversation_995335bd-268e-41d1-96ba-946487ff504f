package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 前台用户实体类
 *
 * <AUTHOR> @date 2025/6/24
 */
@Data
@TableName(value = "tb_user", autoResultMap = true)
public class PlospUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "user_id", type = IdType.AUTO)
    private Long userId;

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    @TableField("email")
    private String email;

    /**
     * 密码
     */
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    @TableField("password")
    private String password;

    /**
     * 名
     */
    @NotBlank(message = "名字不能为空")
    @Size(max = 50, message = "名字长度不能超过50个字符")
    @TableField("first_name")
    private String firstName;

    /**
     * 姓
     */
    @NotBlank(message = "姓氏不能为空")
    @Size(max = 50, message = "姓氏长度不能超过50个字符")
    @TableField("last_name")
    private String lastName;

    @TableField("user_name")
    private String userName;

    /**
     * 组织机构
     */
    @NotBlank(message = "组织机构不能为空")
    @TableField("organization")
    private String organization;

    /**
     * 部门
     */
    @Size(max = 100, message = "部门长度不能超过100个字符")
    @TableField("department")
    private String department;

    /**
     * PI名称(与piname字段重复，保留原表结构)
     */
    @Size(max = 100, message = "PI名称长度不能超过100个字符")
    @TableField("pi_name")
    private String piName;

    /**
     * 职称
     */
    @Size(max = 50, message = "职称长度不能超过50个字符")
    @TableField("title")
    private String title;

    /**
     * 电话
     */
    @Pattern(regexp = "^$|^[0-9\\+\\-\\(\\)\\s]{5,20}$", message = "电话号码格式不正确")
    @Size(max = 20, message = "电话长度不能超过20个字符")
    @TableField("phone")
    private String phone;

    /**
     * 国家/地区
     */
    @NotBlank(message = "国家/地区不能为空")
    @Size(max = 50, message = "国家/地区长度不能超过50个字符")
    @TableField("country_region")
    private String countryRegion;

    /**
     * 州/省
     */
    @Size(max = 50, message = "州/省长度不能超过50个字符")
    @TableField("state_province")
    private String stateProvince;

    /**
     * 城市
     */
    @Size(max = 50, message = "城市长度不能超过50个字符")
    @TableField("city")
    private String city;

    /**
     * 状态 (1-正常 0-禁用)
     */
    @TableField("status")
    private Integer status;

    /**
     * 积分
     */
    @TableField("points")
    private Integer points;

    /**
     * 用户类型
     */
    @TableField("user_type")
    private String userType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    @TableLogic
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 用户头像（二进制数据）
     */
    @TableField("avatar")
    private byte[] avatar;
}
