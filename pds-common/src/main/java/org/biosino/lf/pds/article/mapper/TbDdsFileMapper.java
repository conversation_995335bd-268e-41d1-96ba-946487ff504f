package org.biosino.lf.pds.article.mapper;

import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.domain.TbDdsFile;

/**
 * <p>
 * 存放文件metadata元数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface TbDdsFileMapper extends CommonMapper<TbDdsFile> {

    /**
     * 批量更新任务论文表中的doc_id字段（用于文章合并）
     * 将所有匹配源doc_id的记录更新为目标doc_id
     *
     * @param targetDocId 目标文档ID
     * @param sourceDocId 源文档ID
     * @return 更新的记录数
     */
    int updateDocIdBatch(@Param("targetDocId") Long targetDocId, @Param("sourceDocId") Long sourceDocId);

}
