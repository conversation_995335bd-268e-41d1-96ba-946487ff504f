package org.biosino.lf.pds.article.custbean.dto.api;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashSet;

/**
 * plosp文献传递接口DTO
 *
 * <AUTHOR>
 */
@Data
public class ArticleTransmitDTO implements Serializable {
    @NotBlank(message = "任务名称不能为空")
    private String name;

    @NotBlank(message = "任务描述不能为空")
    private String description;

    @NotNull(message = "用户id不能为空")
    private Long userId;

    @NotNull(message = "任务优先级不能为空")
    private Integer priority;

    @NotNull(message = "文章ID不能为空")
    @Size(min = 1, message = "文章ID不能为空")
    private LinkedHashSet<Long> ids;

    @NotBlank(message = "任务令牌不能为空")
    private String apiToken;
}
