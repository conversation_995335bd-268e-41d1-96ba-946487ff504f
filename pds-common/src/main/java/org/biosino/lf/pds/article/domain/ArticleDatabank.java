package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 文章数据库关联表
 */
@Data
@TableName(value = "tb_dds_article_databank", autoResultMap = true)
public class ArticleDatabank {
    /**
     * ID
     */
    @TableField("id")
    private Long id;

    /**
     * 文档ID
     */
    @TableField("doc_id")
    private Long docId;

    /**
     * 数据库名称
     */
    @TableField("name")
    private String name;

    /**
     * 数据库值
     */
    @TableField("value")
    private String value;
}
