package org.biosino.lf.pds.common.enums;

import cn.hutool.core.util.StrUtil;

/**
 * 文献来源类型
 *
 * <AUTHOR>
 */
public enum SourceTypeEnums {
    PubMed, PMC, bioRxiv, medRxiv, Custom;

    /**
     * 判断是否存在该日期类型状态
     */
    public static SourceTypeEnums getByName(String type) {
        if (StrUtil.isBlank(type)) {
            return null;
        }
        final SourceTypeEnums[] values = SourceTypeEnums.values();
        for (SourceTypeEnums value : values) {
            if (value.name().equalsIgnoreCase(type)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 处理用户输入的来源值，如果在枚举中存在（忽略大小写，去除两端空格），
     * 则返回枚举中规范的书写方式，否则返回用户填写的值
     *
     * @param userInput 用户输入的来源值
     * @return 处理后的来源值
     */
    public static String processUserInput(String userInput) {
        if (StrUtil.isBlank(userInput)) {
            return userInput;
        }

        // 去除两端空格
        String trimmedInput = userInput.trim();

        // 查找匹配的枚举值（忽略大小写）
        SourceTypeEnums matchedEnum = getByName(trimmedInput);

        // 如果找到匹配的枚举，返回枚举的规范名称，否则返回用户输入的值
        return matchedEnum != null ? matchedEnum.name() : trimmedInput;
    }
}
