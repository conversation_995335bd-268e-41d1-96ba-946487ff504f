package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 作者信息表
 */
@Data
@TableName(value = "deleted_tb_dds_author", autoResultMap = true)
public class Author {
    /**
     * 主键ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 名字
     */
    @TableField("forename")
    private String forename;

    /**
     * 姓氏
     */
    @TableField("lastname")
    private String lastname;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 类型（personal / organization）
     */
    @TableField("type")
    private String type;

    @TableField(exist = false)
    private List<Organization> organizations;
}
