package org.biosino.lf.pds.article.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ErrorMsgVO {
    private Integer row;
    private String column;
    private String value;
    private String message;

    public static ErrorMsgVO errMsg(Integer rowNum, String columnName, String value, String message) {
        if (rowNum == null) {
            return new ErrorMsgVO(null, columnName, value, message);
        }
        return new ErrorMsgVO(rowNum + 1, columnName, value, message);
    }


    public static ErrorMsgVO errMsg(String message) {
        return new ErrorMsgVO(null, null, null, message);
    }
}
