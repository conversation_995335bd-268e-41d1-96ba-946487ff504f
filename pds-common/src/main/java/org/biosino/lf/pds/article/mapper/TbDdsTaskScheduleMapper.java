package org.biosino.lf.pds.article.mapper;

import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.custbean.vo.StatInfoVO;
import org.biosino.lf.pds.article.domain.TbDdsTaskSchedule;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface TbDdsTaskScheduleMapper extends CommonMapper<TbDdsTaskSchedule> {

    void updateNotCompleteTaskScheduleStatusByDocId(@Param("docId") Long docId, @Param("changeToStatus") String changeToStatus
            , @Param("notCompleteStatus") Set<String> notCompleteStatus);

    List<TbDdsTaskSchedule> getTaskScheduleByPaperId(@Param("paperId") Long paperId);

    void deleteByTaskId(@Param("taskId") String taskId);

    List<StatInfoVO> countGroupByStatusOfSite(@Param("siteId") Integer siteId);

    List<StatInfoVO> countGroupByStatusOfSites(@Param("siteIds") Collection<Integer> siteIds);
}
