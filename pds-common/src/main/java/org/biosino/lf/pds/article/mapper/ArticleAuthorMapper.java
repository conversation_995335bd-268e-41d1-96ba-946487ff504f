package org.biosino.lf.pds.article.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.domain.ArticleAuthor;

import java.util.Collection;
import java.util.List;

/**
 * 文章作者关联表 Mapper 接口
 */
@Mapper
public interface ArticleAuthorMapper extends BaseMapper<ArticleAuthor> {

    List<ArticleAuthor> selectByOrgIdIn(@Param("orgIds") Collection<Long> orgIds);

    List<ArticleAuthor> findByDocId(@Param("docId") Long docId);
}
