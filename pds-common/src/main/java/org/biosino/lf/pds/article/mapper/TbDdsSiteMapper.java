package org.biosino.lf.pds.article.mapper;

import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.custbean.dto.SiteDTO;
import org.biosino.lf.pds.article.custbean.vo.SiteVO;
import org.biosino.lf.pds.article.domain.TbDdsSite;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface TbDdsSiteMapper extends CommonMapper<TbDdsSite> {

    /**
     * 查询节点列表
     *
     * @param site 节点信息
     * @return 节点列表
     */
    List<SiteVO> selectTbDdsSiteList(SiteDTO site);

    /**
     * 根据ID获取节点VO
     *
     * @param id 节点ID
     * @return 节点VO
     */
    SiteVO getSiteVOById(Integer id);

    /**
     * 查询可重试的节点ID
     */
    List<Integer> findReTrySiteIds(@Param("status") String status, @Param("aliveHandshakeTime") String aliveHandshakeTime,
                                   @Param("paperId") Long paperId, @Param("supportSiteType") List<String> supportSiteType,
                                   @Param("batchType") String batchType);

    List<Integer> searchCanAutoAssignSiteIds(@Param("status") String status, @Param("aliveHandshakeTime") String aliveHandshakeTime,
                                             @Param("siteType") String siteType);
}
