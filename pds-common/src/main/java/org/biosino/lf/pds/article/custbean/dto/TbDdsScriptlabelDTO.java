package org.biosino.lf.pds.article.custbean.dto;

import lombok.Data;
import org.biosino.lf.pds.common.core.domain.BaseEntity;
import org.biosino.lf.pds.common.enums.StatusEnums;

import java.util.List;

/**
 * 脚本便签DTO
 *
 * <AUTHOR>
 */
@Data
public class TbDdsScriptlabelDTO extends BaseEntity {
    private Integer labelId;
    private String labelName;
    private String labelType;
    private List<String> labelTypes;

    private String status = StatusEnums.ENABLE.getCode().toString();
    private String remark;
}
