package org.biosino.lf.pds.article.mapper.statistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.biosino.lf.pds.article.domain.statistics.StatisticsArticlePublished;

@Mapper
public interface StatisticsArticlePublishedMapper extends BaseMapper<StatisticsArticlePublished> {
    @Delete("DELETE FROM statistics_article_published WHERE year = #{year} AND month = #{month}")
    void deleteByYearAndMonth(int year, int month);
}
