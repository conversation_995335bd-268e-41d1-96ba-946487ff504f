package org.biosino.lf.pds.common.enums;

import cn.hutool.core.util.StrUtil;

/**
 * 文献日期类型状态
 *
 * <AUTHOR>
 */
public enum DateTypeEnums {
    received, revised, accepted, epub, ppub;

    /**
     * 判断是否存在该日期类型状态
     */
    public static boolean existByType(String type) {
        if (StrUtil.isBlank(type)) {
            return false;
        }
        final DateTypeEnums[] values = DateTypeEnums.values();
        for (DateTypeEnums value : values) {
            if (value.name().equals(type)) {
                return true;
            }
        }
        return false;
    }
}
