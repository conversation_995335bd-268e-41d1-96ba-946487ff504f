package org.biosino.lf.pds.common.enums.task;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * plosp积分枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ScoreSourceEnum {
    sign("签到"), register("注册"), comment("评论/回复"), interpretation_received("解读被接收"),//公共规则
    download_article_pdf("下载文献全文"), system("系统赠送"), reward("悬赏"), manager("管理员赠送");//根据具体情况变化

    private final String cnName;

    ScoreSourceEnum(String cnName) {
        this.cnName = cnName;
    }

    public static Map<String, String> toMap() {
        Map<String, String> map = new LinkedHashMap<>();
        for (ScoreSourceEnum e : values()) {
            map.put(e.name(), e.getCnName());
        }
        return map;
    }
}
