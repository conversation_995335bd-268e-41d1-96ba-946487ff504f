package org.biosino.lf.pds.article.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.biosino.lf.pds.article.custbean.dto.TbDdsScriptlabelDTO;
import org.biosino.lf.pds.article.custbean.vo.ScriptVO;
import org.biosino.lf.pds.article.custbean.vo.TbDdsScriptlabelVO;
import org.biosino.lf.pds.article.domain.TbDdsScriptlabel;

import java.util.List;

/**
 * <p>
 * 脚本标签 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface TbDdsScriptlabelMapper extends CommonMapper<TbDdsScriptlabel> {

    /**
     * 查询脚本标签列表
     *
     * @param dto 查询参数
     * @return 脚本标签列表
     */
    List<TbDdsScriptlabelVO> selectTbDdsScriptlabelList(TbDdsScriptlabelDTO dto);

    /**
     * 查询脚本标签关联的脚本列表
     *
     * @param scriptlabelId 脚本标签ID
     * @return 脚本列表
     */
    List<ScriptVO> findScriptListOfLabel(Integer scriptlabelId);
}
