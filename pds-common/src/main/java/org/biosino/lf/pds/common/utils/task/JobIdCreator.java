package org.biosino.lf.pds.common.utils.task;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.time.FastDateFormat;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

public class JobIdCreator {

    // private static SimpleDateFormat sf = new SimpleDateFormat("yy-MM-dd-HH-mm-ss");
    private final static FastDateFormat sf = FastDateFormat.getInstance("yy-MM-dd-HH-mm-ss", null, null);

    private static final Map<Integer, String> map = new LinkedHashMap<Integer, String>();

    static {
        map.put(0, "0");
        for (int i = 1; i <= 26; i++)
            map.put(i, String.valueOf((char) (i + 64)));
        for (int j = 27; j <= 35; j++)
            map.put(j, String.valueOf((j - 26)));
        for (int k = 36; k <= 60; k++)
            map.put(k, String.valueOf((char) (k + 61)));

    }

    public static String generateCode() {
        return generateCode("D");
    }

    public static String generateCode(String prefix) {
        String curDate = sf.format(new Date());
        String[] curDateArray = curDate.split("-");
        String year = curDateArray[0];
        String month = map.get(Integer.valueOf(curDateArray[1]));
        String day = map.get(Integer.valueOf(curDateArray[2]));
        String hour = map.get(Integer.valueOf(curDateArray[3]));
        String min = map.get(Integer.valueOf(curDateArray[4]));
        String sec = map.get(Integer.valueOf(curDateArray[5]));
        return prefix + year + month + day + hour + min + sec + RandomStringUtils.random(8, true, true);
    }

    public synchronized static String randomString() {
        return RandomStringUtils.random(16, true, true);
    }

    public static void main(String[] args) {
        for (Integer key : map.keySet()) {
            System.out.println(key + "\t" + map.get(key));
        }
        System.out.println(RandomStringUtils.random(8, true, true));
        System.out.println(randomString());
        System.out.println(randomString());
    }
}
