package org.biosino.lf.pds.common.utils;

import cn.hutool.core.util.CharsetUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.archivers.zip.Zip64Mode;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.compress.archivers.zip.ZipFile;
import org.apache.commons.compress.compressors.CompressorException;
import org.apache.commons.compress.compressors.CompressorInputStream;
import org.apache.commons.compress.compressors.CompressorStreamFactory;
import org.apache.commons.compress.compressors.bzip2.BZip2CompressorInputStream;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.biosino.lf.pds.common.exception.ServiceException;

import java.io.*;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.zip.GZIPInputStream;

/**
 * 压缩工具类
 * <p>
 * 安全说明：
 * 1. 所有解压方法都包含路径穿越(Zip Slip)防护，防止恶意压缩包中的文件被解压到目标目录以外的位置
 * 2. 解压前会检查文件路径是否在目标目录内，若不在则抛出异常中止解压过程
 */
@Slf4j
public class CompressUtil {

    /**
     * 判断是否为支持的压缩文件格式
     *
     * @param fileName 文件名
     * @return 是否支持
     */
    public static boolean isSupported(String fileName) {
        String upperName = fileName.toUpperCase();
        return upperName.endsWith(".ZIP") || upperName.endsWith(".TAR") || upperName.endsWith(".TAR.BZ2") || upperName.endsWith(".BZ2") || upperName.endsWith(".TAR.GZ") || upperName.endsWith(".GZ") || upperName.endsWith(".XZ");
    }

    /**
     * 解压ZIP文件
     *
     * @param srcFile 源ZIP文件
     * @param destDir 目标目录
     * @throws ServiceException 解压异常
     */
    private static void unZip(File srcFile, File destDir) throws ServiceException {
//        unZip(srcFile, destDir, "GBK");
        unZip(srcFile, destDir, CharsetUtil.systemCharset());
    }

    /**
     * 解压ZIP文件
     *
     * @param srcFile 源ZIP文件
     * @param destDir 目标目录
     * @param charset 编码方式
     * @throws ServiceException 解压异常
     */
    private static void unZip(File srcFile, File destDir, Charset charset) throws ServiceException {
        if (srcFile == null || !srcFile.exists()) {
            throw new IllegalArgumentException("请指定压缩文件");
        }
        if (!destDir.exists()) {
            destDir.mkdirs();
        }

        try (ZipFile zipFile = ZipFile.builder()
                .setFile(srcFile)
                .setCharset(charset)
                .get()) {
            Enumeration<ZipArchiveEntry> entries = zipFile.getEntries();
            ZipArchiveEntry entry;
            while (entries.hasMoreElements()) {
                entry = entries.nextElement();
                String entryName = entry.getName();

                // 防止路径穿越攻击(Zip Slip)
                File file = new File(destDir, entryName);
                String canonicalDestDirPath = destDir.getCanonicalPath();
                String canonicalFilePath = file.getCanonicalPath();

                if (!canonicalFilePath.startsWith(canonicalDestDirPath + File.separator) &&
                        !canonicalFilePath.equals(canonicalDestDirPath)) {
                    throw new ServiceException("路径穿越攻击检测: " + entryName);
                }

                if (entry.isDirectory()) {
                    // 对于目录也需要检查路径安全性
                    if (!file.exists()) {
                        file.mkdirs();
                    }
                    continue;
                }

                if (!file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }

                try (InputStream is = zipFile.getInputStream(entry);
                     OutputStream os = new FileOutputStream(file)) {
                    IOUtils.copy(is, os);
                }
            }
        } catch (Exception e) {
            throw new ServiceException("decompress failed. " + e.getMessage());
        }
    }

    /**
     * 解压TAR文件
     *
     * @param inputStream TAR文件输入流
     * @param destDir     目标目录
     * @throws ServiceException 解压异常
     */
    private static void unTar(InputStream inputStream, File destDir) throws ServiceException {
        try (TarArchiveInputStream is = new TarArchiveInputStream(inputStream)) {
            if (!destDir.exists()) {
                destDir.mkdirs();
            }

            TarArchiveEntry entry;
            while ((entry = is.getNextEntry()) != null) {
                String entryName = entry.getName();

                // 防止路径穿越攻击(Zip Slip)
                File file = new File(destDir, entryName);
                String canonicalDestDirPath = destDir.getCanonicalPath();
                String canonicalFilePath = file.getCanonicalPath();

                if (!canonicalFilePath.startsWith(canonicalDestDirPath + File.separator) &&
                        !canonicalFilePath.equals(canonicalDestDirPath)) {
                    throw new ServiceException("路径穿越攻击检测: " + entryName);
                }

                if (entry.isDirectory()) {
                    // 对于目录也需要检查路径安全性
                    if (!file.exists()) {
                        file.mkdirs();
                    }
                    continue;
                }

                if (!file.getParentFile().exists()) {
                    file.getParentFile().mkdirs();
                }

                try (OutputStream os = new FileOutputStream(file)) {
                    IOUtils.copy(is, os);
                }
            }
        } catch (Exception e) {
            throw new ServiceException("decompress failed. " + e.getMessage());
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    /**
     * 解压TAR文件
     *
     * @param srcFile 源TAR文件
     * @param destDir 目标目录
     * @throws FileNotFoundException 文件未找到异常
     * @throws ServiceException      解压异常
     */
    private static void unTar(File srcFile, File destDir) throws FileNotFoundException, ServiceException {
        unTar(new BufferedInputStream(new FileInputStream(srcFile)), destDir);
    }

    /**
     * 解压BZ2格式的TAR文件
     *
     * @param srcFile 源TAR.BZ2文件
     * @param destDir 目标目录
     * @throws IOException      IO异常
     * @throws ServiceException 解压异常
     */
    private static void unTarBZIP2(File srcFile, File destDir) throws IOException, ServiceException {
        unTar(new BZip2CompressorInputStream(new FileInputStream(srcFile)), destDir);
    }

    /**
     * 解压GZ格式的TAR文件
     *
     * @param srcFile 源TAR.GZ文件
     * @param destDir 目标目录
     * @throws IOException      IO异常
     * @throws ServiceException 解压异常
     */
    private static void unTarGZ(File srcFile, File destDir) throws IOException, ServiceException {
        unTar(new GZIPInputStream(new FileInputStream(srcFile)), destDir);
    }

    /**
     * 通过压缩器解压文件
     *
     * @param compressionAlgorithm 压缩算法
     * @param srcFile              源压缩文件
     * @param destDir              目标目录
     * @throws IOException         IO异常
     * @throws CompressorException 压缩器异常
     */
    private static void decompressByCompressor(String compressionAlgorithm, File srcFile, File destDir) throws IOException, CompressorException {
        if (!destDir.exists()) {
            destDir.mkdirs();
        }

        String baseName = FilenameUtils.getBaseName(srcFile.toString());

        // 防止路径穿越攻击(Zip Slip)
        File destFile = new File(destDir, baseName);
        String canonicalDestDirPath = destDir.getCanonicalPath();
        String canonicalFilePath = destFile.getCanonicalPath();

        if (!canonicalFilePath.startsWith(canonicalDestDirPath + File.separator) &&
                !canonicalFilePath.equals(canonicalDestDirPath)) {
            throw new IOException("路径穿越攻击检测: " + baseName);
        }

        // 确保父目录存在
        if (!destFile.getParentFile().exists()) {
            destFile.getParentFile().mkdirs();
        }

        try (InputStream is = new FileInputStream(srcFile);
             CompressorInputStream in = new CompressorStreamFactory().createCompressorInputStream(compressionAlgorithm, is);
             OutputStream os = new BufferedOutputStream(new FileOutputStream(destFile))) {
            IOUtils.copy(in, os);
        }
    }


    /**
     * 解压文件
     *
     * @param srcFile 源压缩文件
     * @param destDir 目标目录
     * @throws ServiceException 解压异常
     */
    public static void decompress(File srcFile, File destDir) throws ServiceException {
        decompress(srcFile, destDir, null);
    }

    /**
     * 解压文件
     *
     * @param srcFile  源压缩文件
     * @param destDir  目标目录
     * @param filename 指定文件名，若为null则使用srcFile的文件名
     * @throws ServiceException 解压异常
     */
    public static void decompress(File srcFile, File destDir, String filename) throws ServiceException {
        if (!srcFile.isFile()) {
            throw new ServiceException("FileNotFound");
        }
        filename = StringUtils.isNotBlank(filename) ? filename : srcFile.getName();
        if (!isSupported(filename)) {
            return;
        }
        String upperName = filename.toUpperCase();
        try {
            if (upperName.endsWith(".ZIP")) {
                unZip(srcFile, destDir);
            } else if (upperName.endsWith(".TAR.BZ2")) {
                unTarBZIP2(srcFile, destDir);
            } else if (upperName.endsWith(".TAR.GZ")) {
                unTarGZ(srcFile, destDir);
            } else if (upperName.endsWith(".TAR")) {
                unTar(srcFile, destDir);
            } else if (upperName.endsWith(".BZ2")) {
                decompressByCompressor(CompressorStreamFactory.BZIP2, srcFile, destDir);
            } else if (upperName.endsWith(".GZ")) {
                decompressByCompressor(CompressorStreamFactory.GZIP, srcFile, destDir);
            } else if (upperName.endsWith(".XZ")) {
                decompressByCompressor(CompressorStreamFactory.XZ, srcFile, destDir);
            }
        } catch (Exception e) {
            log.warn("解压文件出现异常", e);
            throw new ServiceException("decompress failed. " + e.getMessage());
        }
    }


    /**
     * 压缩多个文件为ZIP文件
     *
     * @param files   源文件数组
     * @param zipFile 目标ZIP文件
     * @throws ServiceException 压缩异常
     */
    public static void zip(File[] files, File zipFile) throws ServiceException {
        if (files == null || files.length == 0) {
            return;
        }
        if (zipFile == null) {
            throw new ServiceException("文件为空");
        }
        if (!zipFile.getParentFile().exists()) {
            zipFile.getParentFile().mkdirs();
        }

        try (ZipArchiveOutputStream zaos = new ZipArchiveOutputStream(zipFile)) {
            zaos.setUseZip64(Zip64Mode.AsNeeded);

            for (File file : files) {
                if (file == null) {
                    continue;
                }
                ZipArchiveEntry zipArchiveEntry = new ZipArchiveEntry(file, file.getName());
                zaos.putArchiveEntry(zipArchiveEntry);

                if (file.isFile()) {
                    try (InputStream is = new FileInputStream(file)) {
                        byte[] buffer = new byte[1024 * 5];
                        int len;
                        while ((len = is.read(buffer)) != -1) {
                            zaos.write(buffer, 0, len);
                        }
                    }
                }
                zaos.closeArchiveEntry();
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 压缩文件或目录
     *
     * @param srcFile 文件或目录
     * @param zipFile 目标zip文件
     * @throws IOException      IO异常
     * @throws ServiceException 服务异常
     */
    public static void zip(File srcFile, File zipFile) throws IOException, ServiceException {
        if (srcFile == null || !srcFile.exists()) {
            throw new IllegalArgumentException("请指定要压缩的文件");
        }
        if (!zipFile.getParentFile().exists()) {
            zipFile.getParentFile().mkdirs();
        }
        if (!srcFile.isDirectory()) {
            zip(new File[]{srcFile}, zipFile);
            return;
        }

        try (ZipArchiveOutputStream out = new ZipArchiveOutputStream(new BufferedOutputStream(new FileOutputStream(zipFile)))) {
            out.setUseZip64(Zip64Mode.AsNeeded);
            packFiles(out, srcFile, "");
        }
    }

    /**
     * 打包文件到ZIP输出流
     *
     * @param out      ZIP输出流
     * @param dir      要打包的目录
     * @param pathName ZIP内的路径名
     * @throws IOException IO异常
     */
    private static void packFiles(ZipArchiveOutputStream out, File dir, String pathName) throws IOException {
        File[] files = dir.listFiles();
        if (files == null || files.length < 1) {
            return;
        }
        for (File file : files) {
            if (file.isDirectory()) {
                // 创建目录条目
                ZipArchiveEntry entry = new ZipArchiveEntry(pathName + file.getName() + "/");
                out.putArchiveEntry(entry);
                out.closeArchiveEntry();
                // 递归处理子目录
                packFiles(out, file, pathName + file.getName() + "/");
            } else {
                // 处理文件
                ZipArchiveEntry entry = new ZipArchiveEntry(pathName + file.getName());
                out.putArchiveEntry(entry);
                try (InputStream is = new BufferedInputStream(new FileInputStream(file))) {
                    IOUtils.copy(is, out);
                }
                out.closeArchiveEntry();
            }
        }
    }

    /*public static void main(String[] args) {
        try {
//            decompress(new File("C:\\Users\\<USER>\\Desktop\\29875158.zip"), new File("C:\\Users\\<USER>\\Desktop"));
            //        zipFiles(new File[]{new File("D:\\gtris\\report\\20170425\\111.pdf"), new File("D:\\gtris\\report\\20170425\\222.pdf")}, new File("D:\\gtris\\report\\20170425\\rs.zip"));
            //            zip(new File("C:\\Users\\<USER>\\Desktop\\catalina.out"), new File("C:\\Users\\<USER>\\Desktop\\catalina1.zip"));
            zip(new File("D:\\solr.log"), new File("D:\\rs.zip"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/
}