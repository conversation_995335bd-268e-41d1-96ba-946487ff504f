package org.biosino.lf.pds.article.mapper.statistics;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.biosino.lf.pds.article.domain.statistics.StatisticsArticle;
import org.biosino.lf.pds.article.mapper.CommonMapper;

@Mapper
public interface StatisticsArticleMapper extends CommonMapper<StatisticsArticle> {
    @Delete("DELETE FROM statistics_article WHERE year = #{year} AND month = #{month}")
    void deleteByYearAndMonth(int year, int month);
}
