package org.biosino.lf.pds.article.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.domain.TbDdsIfYear;

import java.util.List;

/**
 * 影响因子年度表 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface TbDdsIfYearMapper extends CommonMapper<TbDdsIfYear> {

    /**
     * 批量更新影响因子年度表中的期刊ID（用于期刊合并）
     * 将所有匹配源期刊ID的记录更新为目标期刊ID
     *
     * @param targetJournalId  目标期刊ID
     * @param sourceJournalIds 源期刊ID列表
     */
    void updateJournalIdBatch(@Param("targetJournalId") Long targetJournalId,
                              @Param("sourceJournalIds") List<Long> sourceJournalIds);
}
