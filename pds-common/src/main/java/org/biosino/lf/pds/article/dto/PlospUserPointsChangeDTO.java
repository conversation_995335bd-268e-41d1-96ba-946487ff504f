package org.biosino.lf.pds.article.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 用户积分变更请求DTO
 */
@Data
public class PlospUserPointsChangeDTO {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 变动积分
     */
    @NotNull(message = "变动积分不能为空")
    private Integer points;

    /**
     * 变动原因
     */
    @NotBlank(message = "变动原因不能为空")
    @Size(max = 200, message = "变动原因长度不能超过200个字符")
    private String reason;
}
