package org.biosino.lf.pds.article.config;

import com.alibaba.fastjson2.TypeReference;
import org.biosino.lf.pds.article.domain.ArticleAuthor;

import java.util.List;


public class ArticleAuthorListTypeHandler extends ListTypeHandler<ArticleAuthor> {

    @Override
    protected TypeReference<List<ArticleAuthor>> specificType() {
        return new TypeReference<List<ArticleAuthor>>() {
        };
    }

}
