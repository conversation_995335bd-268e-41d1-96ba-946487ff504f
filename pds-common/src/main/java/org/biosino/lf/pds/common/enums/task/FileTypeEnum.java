package org.biosino.lf.pds.common.enums.task;

import lombok.Getter;
import org.biosino.lf.pds.common.exception.ServiceException;

/**
 * 文件类型
 *
 * <AUTHOR>
 */
@Getter
public enum FileTypeEnum {
    PDF,
    HTML,
    IMG,
    SUPP,
    XML, JSON, SCRIPT,
    TASK,
    LOG,
    OTHER;

    /**
     * 根据文件类型字符串获取枚举
     */
    public static FileTypeEnum getFileTypeEnum(String fileType) {
        try {
            return FileTypeEnum.valueOf(fileType.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new ServiceException("未识别的文件类型");
        }
    }

}
