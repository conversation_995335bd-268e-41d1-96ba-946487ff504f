package org.biosino.lf.pds.article.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@TableName("tb_dds_task_link")
public class TbDdsTaskLink implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long paperId;

    private String type;

    private Integer isFree;

    private String linkUrl;

    private Integer linkNo;

    private Date downStart;

    private Date downEnd;

    private Long size;

    private Date uploadStart;

    private Date uploadEnd;

    private String uploadPath;

    private String status;

    private Long articleAttachId;

    private Date updateTime;
}
