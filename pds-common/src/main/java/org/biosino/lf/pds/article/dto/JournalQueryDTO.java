package org.biosino.lf.pds.article.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.biosino.lf.pds.article.config.StringListArrayTypeHandler;
import org.biosino.lf.pds.common.core.domain.BaseQuery;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/1
 */
@Data
public class JournalQueryDTO extends BaseQuery {
    /**
     * 期刊名称（多个查询）
     */
    private List<String> titleMultiple;

    /**
     * 期刊名称
     */
    private String title;

    /**
     * 期刊简称
     */
    private String isoabbreviation;

    /**
     * 出版社名称
     */
    private String publisherName;

    /**
     * issn_print
     */
    private String issnPrint;

    /**
     * issn_electronic
     */
    private String issnElectronic;

    /**
     * Unique NLM ID
     */
    private String uniqueNlmId;

    /**
     * 来源
     */
    private String source;

    /**
     * 状态
     */
    private Integer status;
}















