<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.biosino.lf.pds.system.mapper.PlospUserMapper">

    <select id="findAllSeq" resultType="org.biosino.lf.pds.article.custbean.dto.SeqInfoDTO">
        SELECT seqn.nspname AS seq_schema,
               seqc.relname AS sequence_name,
               tabn.nspname AS table_schema,
               tabc.relname AS table_name,
               a.attname    AS column_name
        FROM pg_class seqc
                 JOIN pg_namespace seqn ON seqn.oid = seqc.relnamespace
                 JOIN pg_depend d ON d.objid = seqc.oid AND d.classid = 'pg_class'::regclass
        JOIN pg_class tabc
        ON tabc.oid = d.refobjid
            JOIN pg_namespace tabn ON tabn.oid = tabc.relnamespace
            LEFT JOIN pg_attribute a ON a.attrelid = tabc.oid AND a.attnum = d.refobjsubid
        WHERE seqc.relkind = 'S'
          AND seqn.nspname = #{schema}
          AND tabn.nspname = #{schema}
          AND d.deptype IN ('a'
            , 'n'
            , 'i')
        ORDER BY seqn.nspname, seqc.relname
    </select>

    <select id="getMaxValue" resultType="java.lang.Long">
        SELECT max(${columnName})
        FROM ${tableSchema}.${tableName}
    </select>

    <update id="setSeqValue">
        SELECT setval(#{sequenceName}, #{value}, true)
    </update>

</mapper>
