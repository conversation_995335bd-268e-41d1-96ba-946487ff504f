package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.dto.JournalMergeDTO;
import org.biosino.lf.pds.article.dto.JournalQueryDTO;
import org.biosino.lf.pds.article.dto.JournalUpdateDTO;

import java.util.List;

/**
 * 期刊服务接口
 */
public interface IJournalService extends IService<Journal> {

    /**
     * 查询期刊列表
     */
    List<Journal> selectJournalList(JournalQueryDTO queryDTO);

    /**
     * 根据ID获取期刊详情
     */
    Journal selectJournalById(Long id);

    /**
     * 根据期刊名称批量查询期刊列表
     */
    List<Journal> findByTitleIn(List<String> journalNames);

    /**
     *修改期刊
     */
    Journal updateJournal(JournalUpdateDTO journalUpdateDTO);

    /**
     * 修改期刊状态
     */
    boolean updateJournalStatus(Long id, Long status);

    /**
     * 合并期刊
     */
    boolean mergeJournals(JournalMergeDTO journalMergeDTO);

}
