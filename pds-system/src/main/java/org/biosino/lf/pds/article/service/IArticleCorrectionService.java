package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticleCorrection;
import org.biosino.lf.pds.article.dto.ArticleCorrectionQueryDTO;

import java.util.List;

/**
 * 文献纠错信息服务接口
 */
public interface IArticleCorrectionService extends IService<ArticleCorrection> {

    /**
     * 查询纠错列表
     *
     * @param queryDTO 查询条件
     * @return 纠错列表
     */
    List<ArticleCorrection> selectDefectList(ArticleCorrectionQueryDTO queryDTO);

    /**
     * 接受纠错
     *
     * @param id 纠错ID
     */
    void accept(Long id);

    /**
     * 驳回纠错
     *
     * @param id     纠错ID
     * @param reason 驳回原因
     */
    void reject(Long id, String reason);

    void removeByDocId(Long id);

    /**
     * 批量更新doc_id字段（用于文章合并）
     * 将所有匹配源doc_id的记录更新为目标doc_id
     *
     * @param targetDocId 目标文档ID
     * @param sourceDocId 源文档ID
     * @return 更新的记录数
     */
    int updateDocIdBatch(Long targetDocId, Long sourceDocId);

}
