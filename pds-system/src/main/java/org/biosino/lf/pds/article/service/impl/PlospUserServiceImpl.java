package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.PlospUser;
import org.biosino.lf.pds.article.dto.PlospUserQueryDTO;
import org.biosino.lf.pds.article.service.IPlospUserService;
import org.biosino.lf.pds.common.core.mail.MailService;
import org.biosino.lf.pds.common.core.redis.RedisCache;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.SecurityUtils;
import org.biosino.lf.pds.common.utils.StringUtils;
import org.biosino.lf.pds.system.mapper.PlospUserMapper;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 前台用户服务实现类
 */
@Service
@RequiredArgsConstructor
public class PlospUserServiceImpl extends ServiceImpl<PlospUserMapper, PlospUser> implements IPlospUserService {

    private final MailService mailService;
    private final RedisCache redisCache;

    // 验证码缓存前缀
    private static final String REGISTER_CODE_KEY = "register:code:";
    // 验证码有效期（分钟）
    private static final Integer CODE_EXPIRE_MINUTES = 5;

    @Override
    public List<PlospUser> selectUserList(PlospUserQueryDTO queryDTO) {
        // 查询条件
        Wrapper<PlospUser> qw = Wrappers.<PlospUser>lambdaQuery()
                .like(StrUtil.isNotBlank(queryDTO.getEmail()), PlospUser::getEmail, queryDTO.getEmail())
                .like(StrUtil.isNotBlank(queryDTO.getUserName()), PlospUser::getUserName, queryDTO.getUserName())
                .like(StrUtil.isNotBlank(queryDTO.getOrganization()), PlospUser::getOrganization, queryDTO.getOrganization())
                .eq(StrUtil.isNotBlank(queryDTO.getUserType()), PlospUser::getUserType, queryDTO.getUserType())
                .eq(queryDTO.getStatus() != null, PlospUser::getStatus, queryDTO.getStatus())
                .ge(queryDTO.getBeginTime() != null, PlospUser::getCreateTime, queryDTO.getBeginTime() == null ? null : DateUtil.beginOfDay(queryDTO.getBeginTime()))
                .le(queryDTO.getEndTime() != null, PlospUser::getCreateTime, queryDTO.getEndTime() == null ? null : DateUtil.beginOfDay(queryDTO.getEndTime()));

        return this.list(qw);
    }

    @Override
    public PlospUser selectUserByEmail(String email) {
        if (StrUtil.isBlank(email)) {
            return null;
        }
        return this.getOne(Wrappers.<PlospUser>lambdaQuery().eq(PlospUser::getEmail, email));
    }


    @Override
    public boolean addUser(PlospUser user) {
        if (!checkEmailUnique(user.getUserId(), user.getEmail())) {
            throw new ServiceException("新增用户'" + user.getEmail() + "'失败，邮箱已存在");
        }

        // 设置默认值
        user.setCreateTime(new Date());
        if (user.getPoints() == null) {
            user.setPoints(0);
        }
        if (user.getStatus() == null) {
            user.setStatus(0); // 默认启用状态
        }
        user.setUserName(user.getFirstName() + " " + user.getLastName());

        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));

        return this.save(user);
    }

    @Override
    public boolean updateUser(PlospUser user) {
        if (!checkEmailUnique(user.getUserId(), user.getEmail())) {
            throw new ServiceException("修改用户'" + user.getEmail() + "'失败，邮箱已存在");
        }
        PlospUser plospUser = getOptById(user.getUserId()).orElseThrow(() -> new ServiceException("用户不存在"));
        user.setUserName(user.getFirstName() + " " + user.getLastName());

        // 拷贝属性时排除null
        BeanUtil.copyProperties(user, plospUser, CopyOptions.create().setIgnoreNullValue(true));

        plospUser.setUpdateTime(new Date());

        // 如果密码不为空且不是加密过的，则加密密码
        if (StringUtils.isNotEmpty(user.getPassword())) {
            plospUser.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        }

        return this.updateById(plospUser);
    }

    @Override
    public boolean deleteUserByIds(Long[] ids) {
        return this.removeByIds(Arrays.asList(ids));
    }

    @Override
    public boolean changeStatus(Long id, Integer status) {
        PlospUser existingUser = this.getOptById(id).orElseThrow(() -> new ServiceException("用户不存在"));
        existingUser.setStatus(status);
        existingUser.setUpdateTime(new Date());

        return this.updateById(existingUser);
    }

    @Override
    public boolean checkEmailUnique(Long id, String email) {
        return !this.exists(Wrappers.<PlospUser>lambdaQuery().eq(PlospUser::getEmail, email).ne(id != null, PlospUser::getUserId, id));
    }

    @Override
    public void sendRegisterCode(String email) {
        // 1. 检查邮箱是否已注册
        if (!checkEmailUnique(null, email)) {
            throw new ServiceException("该邮箱已注册，请直接登录");
        }

        // 2. 生成6位随机数字验证码
        String code = RandomUtil.randomNumbers(6);

        // 3. 将验证码保存到Redis，有效期5分钟
        String cacheKey = REGISTER_CODE_KEY + email;
        redisCache.setCacheObject(cacheKey, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);

        // 4. 发送验证码邮件
        String subject = "【PLOSP系统】注册验证码";
        // 构建HTML格式的邮件内容
        String htmlContent = "<div style='background-color:#f7f7f7;padding:20px;'>" +
                "<div style='max-width:600px;margin:0 auto;background-color:#fff;padding:20px;border-radius:5px;box-shadow:0 0 10px rgba(0,0,0,0.1);'>" +
                "<h2 style='color:#333;text-align:center;'>PLOSP系统注册验证码</h2>" +
                "<p style='color:#666;'>您好，</p>" +
                "<p style='color:#666;'>您正在注册PLOSP系统账号，验证码为：</p>" +
                "<p style='font-size:24px;font-weight:bold;color:#007bff;text-align:center;margin:20px 0;letter-spacing:5px;'>" + code + "</p>" +
                "<p style='color:#666;'>验证码有效期为5分钟，请勿将验证码泄露给他人。</p>" +
                "<p style='color:#666;'>如非本人操作，请忽略此邮件。</p>" +
                "<p style='color:#999;font-size:12px;text-align:center;margin-top:30px;'>本邮件由系统自动发送，请勿回复</p>" +
                "</div></div>";

        try {
            mailService.sendHtmlMail(email, subject, htmlContent);
        } catch (Exception e) {
            // 发送失败时，删除缓存的验证码
            redisCache.deleteObject(cacheKey);
            throw new ServiceException("验证码发送失败，请稍后重试");
        }
    }

    @Override
    public void registerUser(PlospUser user, String code) {
        // 1. 检查验证码是否正确
        String cacheKey = REGISTER_CODE_KEY + user.getEmail();
        String cacheCode = redisCache.getCacheObject(cacheKey);

        if (cacheCode == null) {
            throw new ServiceException("验证码已过期，请重新获取");
        }

        if (!cacheCode.equals(code)) {
            throw new ServiceException("验证码错误，请重新输入");
        }

        // 2. 验证码正确，删除缓存的验证码
        redisCache.deleteObject(cacheKey);

        // 3. 检查邮箱是否已注册（二次检查，防止在获取验证码后有人抢注）
        if (!checkEmailUnique(null, user.getEmail())) {
            throw new ServiceException("该邮箱已注册，请直接登录");
        }

        // 4. 设置用户默认值
        user.setCreateTime(new Date());
        user.setPoints(0);
        user.setStatus(0); // 默认启用状态
        user.setUserName(user.getFirstName() + " " + user.getLastName());

        // 5. 加密密码
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));

        // 6. 保存用户
        if (!this.save(user)) {
            throw new ServiceException("注册失败，请稍后重试");
        }

        // 7. 可以在此处添加发送欢迎邮件的代码
        try {
            String subject = "【PLOSP系统】注册成功";
            String welcomeContent = "<div style='background-color:#f7f7f7;padding:20px;'>" +
                    "<div style='max-width:600px;margin:0 auto;background-color:#fff;padding:20px;border-radius:5px;box-shadow:0 0 10px rgba(0,0,0,0.1);'>" +
                    "<h2 style='color:#333;text-align:center;'>欢迎加入PLOSP系统</h2>" +
                    "<p style='color:#666;'>尊敬的 " + user.getUserName() + "，</p>" +
                    "<p style='color:#666;'>恭喜您成功注册PLOSP系统账号！感谢您的使用。</p>" +
                    "<p style='color:#666;'>您现在可以登录系统，享受我们提供的各种服务。</p>" +
                    "<div style='margin:30px 0;text-align:center;'>" +
                    "<a href='#' style='display:inline-block;background-color:#007bff;color:#fff;text-decoration:none;padding:10px 20px;border-radius:5px;'>立即登录</a>" +
                    "</div>" +
                    "<p style='color:#999;font-size:12px;text-align:center;margin-top:30px;'>本邮件由系统自动发送，请勿回复</p>" +
                    "</div></div>";
            mailService.sendHtmlMail(user.getEmail(), subject, welcomeContent);
        } catch (Exception e) {
            // 发送欢迎邮件失败不影响注册流程，只需记录日志
            // 这里省略日志记录
        }
    }

    @Override
    public boolean resetUserPwd(Long userId, String newPassword) {
        // 1. 检查用户是否存在
        PlospUser user = this.getOptById(userId).orElseThrow(() -> new ServiceException("用户不存在"));

        // 2. 新密码不能为空
        if (StringUtils.isEmpty(newPassword)) {
            throw new ServiceException("新密码不能为空");
        }

        // 3. 加密密码
        String encryptedPwd = SecurityUtils.encryptPassword(newPassword);

        // 4. 更新用户密码
        user.setPassword(encryptedPwd);
        user.setUpdateTime(new Date());

        // 5. 保存修改
        return this.updateById(user);
    }
}
