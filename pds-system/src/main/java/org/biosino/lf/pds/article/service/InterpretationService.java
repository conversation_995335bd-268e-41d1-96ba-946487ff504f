package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticleInterpretation;

/**
 * 文献解读信息服务接口
 */
public interface InterpretationService extends IService<ArticleInterpretation> {

    void removeByDocId(Long id);

    /**
     * 批量更新doc_id字段（用于文章合并）
     * 将所有匹配源doc_id的记录更新为目标doc_id
     *
     * @param targetDocId 目标文档ID
     * @param sourceDocId 源文档ID
     * @return 更新的记录数
     */
    int updateDocIdBatch(Long targetDocId, Long sourceDocId);

}
