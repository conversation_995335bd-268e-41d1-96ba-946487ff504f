package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.Author;

import java.util.Collection;
import java.util.List;

/**
 * 作者信息表 服务接口
 */
public interface IAuthorService extends IService<Author> {
    /**
     * 根据作者ID列表查询作者信息
     *
     * @param ids 作者ID列表
     * @return 作者信息列表
     */
    List<Author> findByIds(Collection<Long> ids);

}
