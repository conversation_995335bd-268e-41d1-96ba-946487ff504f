package org.biosino.lf.pds.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.biosino.lf.pds.article.custbean.dto.SeqInfoDTO;
import org.biosino.lf.pds.article.domain.PlospUser;

import java.util.List;

/**
 * 前台用户数据访问层
 */
public interface PlospUserMapper extends BaseMapper<PlospUser> {
    List<SeqInfoDTO> findAllSeq(@Param("schema") String schema);

    Long getMaxValue(@Param("columnName") String columnName,
                     @Param("tableSchema") String tableSchema,
                     @Param("tableName") String tableName);

    void setSeqValue(@Param("sequenceName") String sequenceName,
                     @Param("value") Long value);
}
