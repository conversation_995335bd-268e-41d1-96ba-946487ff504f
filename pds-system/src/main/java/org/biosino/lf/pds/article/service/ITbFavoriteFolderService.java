package org.biosino.lf.pds.article.service;


import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.TbFavoriteFolder;

import java.util.List;

/**
 * 收藏夹service层
 * <AUTHOR>
 */
public interface ITbFavoriteFolderService extends IService<TbFavoriteFolder> {
    /**
     * 获取文件夹
     */
    List<TbFavoriteFolder> listByUserId(Long userId);

    /**
     * 创建文件夹
     */
    void createFolder(Long userId,String folderName);

    /**
     * 更新文件夹名称
     */
    void updateFolder(Long id,String folderName);

    /**
     * 删除收藏夹以及文献
     */
    void deleteFolderWithDoc(Long id);
}
