package org.biosino.lf.pds.article.service;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.TbDdsTaskLog;
import org.biosino.lf.pds.article.mapper.TbDdsTaskLogMapper;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Date;

/**
 * 基础公共服务层
 *
 * <AUTHOR>
 */
public interface CommonService<T> extends IService<T> {

    /**
     * 获取异常信息
     */
    default String getErrMessage(Throwable t) {
        StringWriter stringWriter = new StringWriter();
        PrintWriter pw = null;
        try {
            pw = new PrintWriter(stringWriter, true);
            t.printStackTrace(pw);
            return stringWriter.getBuffer().toString();
        } finally {
            IoUtil.close(pw);
        }
    }

    default void appendTaskMessage(final TbDdsTaskLogMapper tbDdsTaskLogMapper, String taskId, String msg) {
        if (StrUtil.isBlank(taskId) || StrUtil.isBlank(msg)) {
            return;
        }

        final TbDdsTaskLog log = new TbDdsTaskLog();
        log.setTaskId(taskId);
        log.setMessage(msg);
        log.setCreateTime(new Date());
        tbDdsTaskLogMapper.insert(log);
    }
}
