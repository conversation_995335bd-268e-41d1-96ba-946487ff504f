package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.dto.JournalMergeDTO;
import org.biosino.lf.pds.article.dto.JournalUpdateDTO;
import org.biosino.lf.pds.article.dto.JournalValidationResult;
import org.biosino.lf.pds.article.mapper.JournalMapper;
import org.biosino.lf.pds.article.service.JournalValidationService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 期刊验证服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class JournalValidationServiceImpl implements JournalValidationService {

    private final JournalMapper journalMapper;

    @Override
    public JournalValidationResult validateJournalUpdate(JournalUpdateDTO journalUpdateDTO) {
        List<Long> excludeIds = new ArrayList<>();
        if (journalUpdateDTO.getId() != null) {
            excludeIds.add(journalUpdateDTO.getId());
        }

        // 验证 issnPrint
        if (StrUtil.isNotBlank(journalUpdateDTO.getIssnPrint())) {
            JournalValidationResult result = validateSingleIssnValue("issnPrint",
                journalUpdateDTO.getIssnPrint(), excludeIds);
            if (!result.isValid()) {
                return result;
            }
        }

        // 验证 issnElectronic
        if (StrUtil.isNotBlank(journalUpdateDTO.getIssnElectronic())) {
            JournalValidationResult result = validateSingleIssnValue("issnElectronic",
                journalUpdateDTO.getIssnElectronic(), excludeIds);
            if (!result.isValid()) {
                return result;
            }
        }

        // 验证 uniqueNlmId
        if (StrUtil.isNotBlank(journalUpdateDTO.getUniqueNlmId())) {
            JournalValidationResult result = validateSingleIssnValue("uniqueNlmId",
                journalUpdateDTO.getUniqueNlmId(), excludeIds);
            if (!result.isValid()) {
                return result;
            }
        }

        // 验证 issnHistory
        if (CollUtil.isNotEmpty(journalUpdateDTO.getIssnHistory())) {
            for (String issnValue : journalUpdateDTO.getIssnHistory()) {
                if (StrUtil.isNotBlank(issnValue)) {
                    JournalValidationResult result = validateSingleIssnValue("issnHistory",
                        issnValue, excludeIds);
                    if (!result.isValid()) {
                        return result;
                    }
                }
            }
        }

        // 验证 uniqueHistory
        if (CollUtil.isNotEmpty(journalUpdateDTO.getUniqueHistory())) {
            for (String uniqueValue : journalUpdateDTO.getUniqueHistory()) {
                if (StrUtil.isNotBlank(uniqueValue)) {
                    JournalValidationResult result = validateSingleIssnValue("uniqueHistory",
                        uniqueValue, excludeIds);
                    if (!result.isValid()) {
                        return result;
                    }
                }
            }
        }

        return JournalValidationResult.valid();
    }

    @Override
    public JournalValidationResult validateJournalMerge(JournalMergeDTO journalMergeDTO) {
        List<Long> excludeIds = new ArrayList<>();

        // 排除目标期刊和源期刊
        if (journalMergeDTO.getTargetId() != null) {
            excludeIds.add(journalMergeDTO.getTargetId());
        }
        if (CollUtil.isNotEmpty(journalMergeDTO.getSourceIds())) {
            excludeIds.addAll(journalMergeDTO.getSourceIds());
        }

        // 验证 issnPrint
        if (StrUtil.isNotBlank(journalMergeDTO.getIssnPrint())) {
            JournalValidationResult result = validateSingleIssnValue("issnPrint",
                journalMergeDTO.getIssnPrint(), excludeIds);
            if (!result.isValid()) {
                return result;
            }
        }

        // 验证 issnElectronic
        if (StrUtil.isNotBlank(journalMergeDTO.getIssnElectronic())) {
            JournalValidationResult result = validateSingleIssnValue("issnElectronic",
                journalMergeDTO.getIssnElectronic(), excludeIds);
            if (!result.isValid()) {
                return result;
            }
        }

        // 验证 uniqueNlmId
        if (StrUtil.isNotBlank(journalMergeDTO.getUniqueNlmId())) {
            JournalValidationResult result = validateSingleIssnValue("uniqueNlmId",
                journalMergeDTO.getUniqueNlmId(), excludeIds);
            if (!result.isValid()) {
                return result;
            }
        }

        // 验证 issnHistory
        if (CollUtil.isNotEmpty(journalMergeDTO.getIssnHistory())) {
            for (String issnValue : journalMergeDTO.getIssnHistory()) {
                if (StrUtil.isNotBlank(issnValue)) {
                    JournalValidationResult result = validateSingleIssnValue("issnHistory",
                        issnValue, excludeIds);
                    if (!result.isValid()) {
                        return result;
                    }
                }
            }
        }

        // 验证 uniqueHistory
        if (CollUtil.isNotEmpty(journalMergeDTO.getUniqueHistory())) {
            for (String uniqueValue : journalMergeDTO.getUniqueHistory()) {
                if (StrUtil.isNotBlank(uniqueValue)) {
                    JournalValidationResult result = validateSingleIssnValue("uniqueHistory",
                        uniqueValue, excludeIds);
                    if (!result.isValid()) {
                        return result;
                    }
                }
            }
        }

        return JournalValidationResult.valid();
    }

    @Override
    public JournalValidationResult validateSingleIssnValue(String fieldName, String value, List<Long> excludeIds) {
        if (StrUtil.isBlank(value)) {
            return JournalValidationResult.valid();
        }

        List<Journal> conflictJournals;

        // 根据字段名称选择相应的查询方法
        switch (fieldName) {
            case "issnPrint":
                conflictJournals = journalMapper.findConflictingJournalsByIssnPrint(value, excludeIds);
                break;
            case "issnElectronic":
                conflictJournals = journalMapper.findConflictingJournalsByIssnElectronic(value, excludeIds);
                break;
            case "uniqueNlmId":
                conflictJournals = journalMapper.findConflictingJournalsByUniqueNlmId(value, excludeIds);
                break;
            case "issnHistory":
                conflictJournals = journalMapper.findConflictingJournalsByIssnHistory(value, excludeIds);
                break;
            case "uniqueHistory":
                conflictJournals = journalMapper.findConflictingJournalsByUniqueHistory(value, excludeIds);
                break;
            default:
                return JournalValidationResult.valid();
        }

        if (CollUtil.isNotEmpty(conflictJournals)) {
            Journal conflictJournal = conflictJournals.get(0);
            String errorMessage = String.format("字段 '%s' 的值 '%s' 已存在于期刊记录中 (ID: %d, 标题: %s)",
                getFieldDisplayName(fieldName), value, conflictJournal.getId(), conflictJournal.getTitle());

            return JournalValidationResult.invalid(fieldName, value, conflictJournal, errorMessage);
        }

        return JournalValidationResult.valid();
    }

    /**
     * 获取字段的显示名称
     */
    private String getFieldDisplayName(String fieldName) {
        switch (fieldName) {
            case "issnPrint":
                return "ISSN Print";
            case "issnElectronic":
                return "ISSN Electronic";
            case "uniqueNlmId":
                return "Unique NLM ID";
            case "issnHistory":
                return "ISSN History";
            case "uniqueHistory":
                return "Unique History";
            default:
                return fieldName;
        }
    }
}
