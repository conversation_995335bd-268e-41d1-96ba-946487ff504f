package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.PlospUser;
import org.biosino.lf.pds.article.domain.PlospUserPointsRecord;
import org.biosino.lf.pds.article.dto.PlospUserPointsChangeDTO;
import org.biosino.lf.pds.article.dto.PlospUserPointsRecordQueryDTO;
import org.biosino.lf.pds.article.service.IPlospUserPointsRecordService;
import org.biosino.lf.pds.article.service.IPlospUserService;
import org.biosino.lf.pds.common.core.domain.entity.SysUser;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.SecurityUtils;
import org.biosino.lf.pds.common.utils.StringUtils;
import org.biosino.lf.pds.system.mapper.TbUserPointsRecordMapper;
import org.biosino.lf.pds.system.service.ISysUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户积分记录服务实现类
 */
@Service
@RequiredArgsConstructor
public class PlospUserPointsRecordServiceImpl extends ServiceImpl<TbUserPointsRecordMapper, PlospUserPointsRecord> implements IPlospUserPointsRecordService {

    private final IPlospUserService userService;
    private final ISysUserService sysUserService;

    @Override
    public List<PlospUserPointsRecord> selectPointsRecordList(PlospUserPointsRecordQueryDTO queryDTO) {
        Wrapper<PlospUserPointsRecord> qw = Wrappers.<PlospUserPointsRecord>lambdaQuery()
                .eq(queryDTO.getUserId() != null, PlospUserPointsRecord::getUserId, queryDTO.getUserId())
                .eq(StringUtils.isNotEmpty(queryDTO.getType()), PlospUserPointsRecord::getType, queryDTO.getType())
                .ge(queryDTO.getBeginTime() != null, PlospUserPointsRecord::getChangeTime, queryDTO.getBeginTime() == null ? null : DateUtil.beginOfDay(queryDTO.getBeginTime()))
                .le(queryDTO.getEndTime() != null, PlospUserPointsRecord::getChangeTime, queryDTO.getEndTime() == null ? null : DateUtil.endOfDay(queryDTO.getEndTime()));
        List<PlospUserPointsRecord> list = this.list(qw);
        Set<Long> userIds = list.stream().map(PlospUserPointsRecord::getUserId).collect(Collectors.toSet());
        Set<Long> sysUserIds = list.stream().map(PlospUserPointsRecord::getOperatorId).collect(Collectors.toSet());
        Map<Long, PlospUser> userIdToUserMap = userService.listByIds(userIds).stream().collect(Collectors.toMap(PlospUser::getUserId, user -> user));
        Map<Long, SysUser> sysUserIdToSysUserMap = sysUserService.listByIds(sysUserIds).stream().collect(Collectors.toMap(SysUser::getUserId, user -> user));

        list.forEach(record -> {
            if (userIdToUserMap.containsKey(record.getUserId())) {
                record.setUsername(userIdToUserMap.get(record.getUserId()).getUserName());
            }
            if (sysUserIdToSysUserMap.containsKey(record.getOperatorId())) {
                record.setOperatorName(sysUserIdToSysUserMap.get(record.getOperatorId()).getUserName());
            }
        });

        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPointsRecord(PlospUserPointsChangeDTO changeDTO) {
        // 获取用户信息
        PlospUser user = userService.getById(changeDTO.getUserId());
        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        Integer pointsChange = changeDTO.getPoints();

        // 更新用户积分 - 使用update语句直接更新
        boolean updateResult = userService.update(new LambdaUpdateWrapper<PlospUser>()
                .eq(PlospUser::getUserId, changeDTO.getUserId())
                .setSql("points = CASE WHEN (points + " + pointsChange + ") < 0 THEN 0 ELSE (points + " + pointsChange + ") END"));

        if (!updateResult) {
            throw new ServiceException("更新用户积分失败");
        }

        // 创建积分记录
        PlospUserPointsRecord record = new PlospUserPointsRecord();
        record.setUserId(user.getUserId());
        record.setPoints(pointsChange);
        record.setType(pointsChange > 0 ? "admin_gift" : "admin_deduct");
        record.setReason(changeDTO.getReason());
        record.setChangeTime(new Date());
        record.setOperatorId(SecurityUtils.getUserId());

        // 保存积分记录
        return this.save(record);
    }
}
