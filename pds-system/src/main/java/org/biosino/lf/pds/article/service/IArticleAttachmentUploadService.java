package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticleAttachmentUpload;
import org.biosino.lf.pds.article.dto.ArticleAttachmentUploadQueryDTO;

import java.util.List;

/**
 * 文章附件上传信息服务接口
 */
public interface IArticleAttachmentUploadService extends IService<ArticleAttachmentUpload> {

    List<ArticleAttachmentUpload> selectUploadList(ArticleAttachmentUploadQueryDTO queryDTO);

    void accept(Long id);

    void reject(Long id, String reason);

    void removeByDocId(Long id);

    /**
     * 批量更新doc_id字段（用于文章合并）
     * 将所有匹配源doc_id的记录更新为目标doc_id
     *
     * @param targetDocId 目标文档ID
     * @param sourceDocId 源文档ID
     * @return 更新的记录数
     */
    int updateDocIdBatch(Long targetDocId, Long sourceDocId);
}
