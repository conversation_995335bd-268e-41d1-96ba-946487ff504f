package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.biosino.lf.pds.article.domain.Article;
import org.biosino.lf.pds.article.domain.TbDdsFile;
import org.biosino.lf.pds.article.dto.ArticleImportDTO;
import org.biosino.lf.pds.article.dto.ArticleQueryDTO;
import org.biosino.lf.pds.article.dto.ArticleUpdateDTO;
import org.biosino.lf.pds.article.dto.ArticleUploadAttachmentDTO;
import org.biosino.lf.pds.article.vo.ErrorMsgVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

/**
 * 文献服务接口
 *
 * <AUTHOR>
 */
public interface IArticleService extends IService<Article> {

    /**
     * 查询文献列表
     *
     * @param queryDTO 查询参数
     * @return 文献列表
     */
    List<Article> selectArticleList(ArticleQueryDTO queryDTO);

    /**
     * 获取文献详情
     *
     * @param id 文献ID
     * @return 文献详情
     */
    Article selectArticleById(Long id);

    void updateArticle(ArticleUpdateDTO articleUpdateDTO);

    File getAttachmentZipByIds(List<Long> pmids);

    /**
     * 批量上传文献附件
     *
     * @param file 上传的ZIP文件
     * @return 上传结果信息
     */
    List<ErrorMsgVO> batchUploadArticleAttachment(MultipartFile file);

    /**
     * 导入文献题录
     *
     * @param importList 导入的文献数据列表
     * @return 导入结果信息
     */
    List<ErrorMsgVO> importArticles(List<ArticleImportDTO> importList);

    List<TbDdsFile> uploadAttachment(ArticleUploadAttachmentDTO articleUploadAttachmentDTO);

    /**
     * 获取指定范围内的最大customId
     *
     * @param minValue 最小值（包含）
     * @param maxValue 最大值（不包含）
     * @return 最大customId值，如果没有找到则返回null
     */
    Long getMaxCustomIdInRange(Long minValue, Long maxValue);

    /**
     * 批量更新文献的期刊ID（用于期刊合并）
     *
     * @param targetJournalId  目标期刊ID
     * @param sourceJournalIds 源期刊ID列表
     */
    void updateJournalIdForMerge(@NotNull Long targetJournalId, @NotNull @NotEmpty List<Long> sourceJournalIds);

    /**
     * 获取总下载量
     *
     * @return 总下载量
     */
    Long getTotalDownload();
}
