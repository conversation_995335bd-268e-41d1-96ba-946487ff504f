package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.ArticleInterpretation;
import org.biosino.lf.pds.article.mapper.ArticleCorrectionMapper;
import org.biosino.lf.pds.article.mapper.ArticleInterpretationMapper;
import org.biosino.lf.pds.article.service.InterpretationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 文献纠错信息服务实现类
 */
@Service
@RequiredArgsConstructor
public class InterpretationServiceImpl extends ServiceImpl<ArticleInterpretationMapper, ArticleInterpretation> implements InterpretationService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByDocId(Long id) {
        LambdaQueryWrapper<ArticleInterpretation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ArticleInterpretation::getDocId, id);
        this.remove(queryWrapper);
    }

    @Override
    public int updateDocIdBatch(Long targetDocId, Long sourceDocId) {
        return baseMapper.updateDocIdBatch(targetDocId, sourceDocId);
    }

}
