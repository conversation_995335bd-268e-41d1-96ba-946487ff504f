package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.UserApiKey;
import org.biosino.lf.pds.article.mapper.UserApiKeyMapper;
import org.biosino.lf.pds.article.service.IUserApiKeyService;
import org.biosino.lf.pds.common.utils.uuid.IdUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户API密钥服务实现类
 *
 * <AUTHOR>
 * @date 2025/08/04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserApiKeyServiceImpl extends ServiceImpl<UserApiKeyMapper, UserApiKey> implements IUserApiKeyService {


    @Override
    public List<UserApiKey> getUserApiKeys(Long userId) {
        LambdaQueryWrapper<UserApiKey> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserApiKey::getUserId, userId)
                .orderByDesc(UserApiKey::getCreateTime);
        return this.list(wrapper);
    }

    @Override
    public UserApiKey createApiKey(Long userId, String keyName) {
        // 检查名称是否重复
        LambdaQueryWrapper<UserApiKey> nameWrapper = new LambdaQueryWrapper<>();
        nameWrapper.eq(UserApiKey::getUserId, userId)
                .eq(UserApiKey::getKeyName, keyName);
        if (this.count(nameWrapper) > 0) {
            throw new RuntimeException("密钥名称已存在");
        }
        // 生成API密钥
        String apiKey = generateApiKey();
        // 创建实体
        UserApiKey userApiKey = new UserApiKey();
        userApiKey.setUserId(userId);
        userApiKey.setKeyName(keyName);
        userApiKey.setApiKey(apiKey);
        // 保存到数据库
        boolean success = this.save(userApiKey);
        if (!success) {
            throw new RuntimeException("创建API密钥失败");
        }
        log.info("用户[{}]创建API密钥成功，密钥名称: {}", userId, keyName);
        return userApiKey;
    }

    @Override
    public boolean updateApiKeyName(Long keyId, Long userId, String newKeyName) {
        // 验证权限
        UserApiKey existingKey = this.getById(keyId);
        if (existingKey == null || !existingKey.getUserId().equals(userId)) {
            throw new RuntimeException("API密钥不存在或无权限操作");
        }
        // 检查新名称是否重复
        LambdaQueryWrapper<UserApiKey> nameWrapper = new LambdaQueryWrapper<>();
        nameWrapper.eq(UserApiKey::getUserId, userId)
                .eq(UserApiKey::getKeyName, newKeyName)
                .ne(UserApiKey::getId, keyId);
        if (this.count(nameWrapper) > 0) {
            throw new RuntimeException("密钥名称已存在");
        }
        // 更新名称
        existingKey.setKeyName(newKeyName);
        boolean success = this.updateById(existingKey);
        if (success) {
            log.info("用户[{}]修改API密钥名称成功，密钥ID: {}, 新名称: {}", userId, keyId, newKeyName);
        }
        return success;
    }

    @Override
    public String getFullApiKey(Long keyId, Long userId) {
        UserApiKey userApiKey = this.getById(keyId);
        if (userApiKey == null || !userApiKey.getUserId().equals(userId)) {
            throw new RuntimeException("API密钥不存在或无权限操作");
        }
        log.info("用户[{}]获取完整API密钥，密钥ID: {}", userId, keyId);
        return userApiKey.getApiKey();
    }

    /**
     * 生成API密钥 - 使用UUID
     */
    private String generateApiKey() {
        return IdUtils.fastSimpleUUID();
    }
}
