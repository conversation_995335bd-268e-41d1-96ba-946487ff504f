package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.UserApiKey;

import java.util.List;

/**
 * 用户API密钥服务接口
 *
 * <AUTHOR>
 * @date 2025/08/04
 */
public interface IUserApiKeyService extends IService<UserApiKey> {

    /**
     * 获取用户的API密钥列表
     */
    List<UserApiKey> getUserApiKeys(Long userId);

    /**
     * 创建API密钥
     */
    UserApiKey createApiKey(Long userId, String keyName);

    /**
     * 修改API密钥名称
     */
    boolean updateApiKeyName(Long keyId, Long userId, String newKeyName);

    /**
     * 获取完整的API密钥（用于复制）
     */
    String getFullApiKey(Long keyId, Long userId);
}
