package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.TbFavoriteDoc;
import org.biosino.lf.pds.article.mapper.TbFavoriteDocMapper;
import org.biosino.lf.pds.article.service.ITbFavoriteDocService;
import org.springframework.stereotype.Service;

/**
 * 用户收藏夹service实现类
 * <AUTHOR>
 */
@Service
public class TbFavoriteDocServiceImpl extends ServiceImpl<TbFavoriteDocMapper, TbFavoriteDoc> implements ITbFavoriteDocService {
    
    @Override
    public int updateDocIdBatch(Long targetDocId, Long sourceDocId) {
        return baseMapper.updateDocIdBatch(targetDocId, sourceDocId);
    }

}
