package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.ArticleAttachmentUpload;
import org.biosino.lf.pds.article.domain.ArticleCorrection;
import org.biosino.lf.pds.article.dto.ArticleCorrectionQueryDTO;
import org.biosino.lf.pds.article.mapper.ArticleCorrectionMapper;
import org.biosino.lf.pds.article.service.IArticleCorrectionService;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 文献纠错信息服务实现类
 */
@Service
@RequiredArgsConstructor
public class ArticleCorrectionServiceImpl extends ServiceImpl<ArticleCorrectionMapper, ArticleCorrection> implements IArticleCorrectionService {

    @Override
    public List<ArticleCorrection> selectDefectList(ArticleCorrectionQueryDTO queryDTO) {
        return this.baseMapper.selectDefectList(queryDTO);
    }

    @Override
    public void accept(Long id) {
        ArticleCorrection defect = this.getById(id);
        if (defect == null) {
            throw new ServiceException("纠错记录不存在");
        }

        // 修改状态为已接受
        defect.setStatus(1);
        defect.setAuditor(SecurityUtils.getUserId());
        defect.setAuditTime(new Date());

        this.updateById(defect);
    }

    @Override
    public void reject(Long id, String reason) {
        ArticleCorrection defect = this.getById(id);
        if (defect == null) {
            throw new ServiceException("纠错记录不存在");
        }

        // 修改状态为已驳回
        defect.setStatus(-1);
        defect.setAuditor(SecurityUtils.getUserId());
        defect.setAuditTime(new Date());
        defect.setReason(reason);

        this.updateById(defect);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByDocId(Long id) {
        LambdaQueryWrapper<ArticleCorrection> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ArticleCorrection::getDocId, id);
        this.remove(queryWrapper);
    }

    @Override
    public int updateDocIdBatch(Long targetDocId, Long sourceDocId) {
        return baseMapper.updateDocIdBatch(targetDocId, sourceDocId);
    }
}
