package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticleAuthor;

import java.util.List;

/**
 * 文章作者关联表 服务接口
 */
public interface IArticleAuthorService extends IService<ArticleAuthor> {
    /**
     * 根据文档ID查询文章作者关联信息
     *
     * @param docId 文档ID
     * @return 文章作者关联信息集合
     */
    List<ArticleAuthor> findByDocId(Long docId);

    void removeByDocId(Long docId);
}
