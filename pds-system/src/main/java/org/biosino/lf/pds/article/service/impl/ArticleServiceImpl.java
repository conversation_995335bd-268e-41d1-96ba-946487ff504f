package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.custbean.dto.FileUploadDTO;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.dto.*;
import org.biosino.lf.pds.article.mapper.ArticleMapper;
import org.biosino.lf.pds.article.service.IArticleService;
import org.biosino.lf.pds.article.service.IJournalService;
import org.biosino.lf.pds.article.service.ITbDdsFileService;
import org.biosino.lf.pds.article.vo.ErrorMsgVO;
import org.biosino.lf.pds.common.constant.DirConstants;
import org.biosino.lf.pds.common.enums.JournalSourceTypeEnums;
import org.biosino.lf.pds.common.enums.SourceTypeEnums;
import org.biosino.lf.pds.common.enums.StatusEnums;
import org.biosino.lf.pds.common.enums.task.ArticleAttachmentSourceEnum;
import org.biosino.lf.pds.common.enums.task.FileTypeEnum;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.file.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文献服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> implements IArticleService {

    private final ITbDdsFileService fileService;
    private final IJournalService journalService;

    /**
     * 查询文献列表
     *
     * @param queryDTO 查询参数
     * @return 文献列表
     */
    @Override
    public List<Article> selectArticleList(ArticleQueryDTO queryDTO) {
        List<Article> articles = baseMapper.selectArticleList(queryDTO);
        articles.forEach(x -> {
            List<TbDdsFile> pdfList = fileService.findByDocIdAndType(x.getId(), FileTypeEnum.PDF.name());
            if (CollUtil.isNotEmpty(pdfList)) {
                x.setExistPdf(true);
            }
        });
        return articles;
    }

    public Article getArticleByPmid(Long pmid) {
        if (pmid == null) {
            return null;
        }
        return this.getOne(Wrappers.lambdaQuery(Article.class).eq(Article::getPmid, pmid));
    }

    public Article getArticleByPmcId(Long pmcId) {
        if (pmcId == null) {
            return null;
        }
        return this.getOne(Wrappers.lambdaQuery(Article.class).eq(Article::getPmcId, pmcId));
    }

    public Article getArticleByCustomId(Long customId) {
        if (customId == null) {
            return null;
        }
        return this.getOne(Wrappers.lambdaQuery(Article.class).eq(Article::getCustomId, customId));
    }

    /**
     * 获取文献详情
     *
     * @param id 文献ID
     * @return 文献详情
     */
    @Override
    public Article selectArticleById(Long id) {
        Article article = baseMapper.selectArticleById(id);

        List<TbDdsFile> pdfList = fileService.findByDocIdAndType(article.getId(), FileTypeEnum.PDF.name());
        if (CollUtil.isNotEmpty(pdfList)) {
            article.setPdfFile(pdfList.get(0));
        }
        List<TbDdsFile> suppList = fileService.findByDocIdAndType(article.getId(), FileTypeEnum.SUPP.name());

        if (CollUtil.isNotEmpty(suppList)) {
            article.setSuppFiles(suppList);
        }

        return article;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateArticle(ArticleUpdateDTO articleUpdateDTO) {
        Article article = selectArticleById(articleUpdateDTO.getId());

        // 拷贝Article基本信息
        BeanUtil.copyProperties(articleUpdateDTO, article);

        // 构建AuthorInfo列表
        List<AuthorDTO> authorList = articleUpdateDTO.getAuthorList();
        if (CollUtil.isNotEmpty(authorList)) {
            List<AuthorInfo> authorInfoList = new ArrayList<>();

            int authorOrder = 1;
            for (AuthorDTO authorDTO : authorList) {
                AuthorInfo authorInfo = new AuthorInfo();
                authorInfo.setForename(authorDTO.getForename());
                authorInfo.setLastname(authorDTO.getLastname());
                authorInfo.setEmail(authorDTO.getEmail());
                authorInfo.setType("person");
                authorInfo.setAuthorOrder(authorOrder++);
                authorInfo.setOrganizations(authorDTO.getOrganizations());

                authorInfoList.add(authorInfo);
            }

            // 设置作者信息到Article对象
            article.setAuthorInfo(authorInfoList);
        }

        if (CollUtil.isEmpty(article.getAuthor())) {
            article.setAuthor(null);
        }
        if (CollUtil.isEmpty(article.getAffiliation())) {
            article.setAffiliation(null);
        }
        if (CollUtil.isEmpty(article.getKeywords())) {
            article.setKeywords(null);
        }
        if (CollUtil.isEmpty(article.getOtherDate())) {
            article.setOtherDate(null);
        }

        // 更新文章信息
        this.updateById(article);
    }

    @Override
    public File getAttachmentZipByIds(List<Long> ids) {
        List<Article> articles = this.listByIds(ids);
        if (CollUtil.isEmpty(articles)) {
            throw new ServiceException("文献不存在");
        }

        Map<Long, Article> docIdToArticleMap = articles.stream().collect(Collectors.toMap(Article::getId, Function.identity()));

        List<TbDdsFile> ddsFileList = fileService.findByDocIdIn(ids);
        if (CollUtil.isEmpty(ddsFileList)) {
            throw new ServiceException("文献附件不存在");
        }

        // ddsFileList根据 docId分组
        Map<Long, List<TbDdsFile>> docIdToFileListMap = ddsFileList.stream().collect(Collectors.groupingBy(TbDdsFile::getDocId));

        File tempDir = FileUtils.getTempDir();
        docIdToFileListMap.forEach((docId, fileList) -> {
            // 先获取这个文献目录的名称
            Article article = docIdToArticleMap.get(docId);
            String dirName;
            if (article.getPmid() != null) {
                dirName = "PMID_" + article.getPmid();
            } else if (article.getPmcId() != null) {
                dirName = "PMCID_" + article.getPmcId();
            } else if (article.getCustomId() != null) {
                dirName = "CUSTOMID_" + article.getCustomId();
            } else {
                dirName = "DOC_" + docId;
            }

            for (TbDdsFile ddsFile : fileList) {
                // 开始拷贝文件
                File targetFile = FileUtil.file(tempDir, dirName, ddsFile.getType(), ddsFile.getFileName());
                TbDdsFileContent fileContent = fileService.findContentById(ddsFile.getId());
                if (ddsFile.getFilePath() != null) {
                    FileUtil.copy(FileUtil.file(DirConstants.DATA_HOME, ddsFile.getFilePath()), targetFile, true);
                } else if (fileContent != null && fileContent.getFileData() != null) {
                    FileUtil.writeBytes(fileContent.getFileData(), targetFile);
                }

            }
        });

        return ZipUtil.zip(tempDir);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ErrorMsgVO> batchUploadArticleAttachment(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("文件不存在");
        }

        // 获取文件后缀
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            throw new ServiceException("文件名不能为空");
        }

        // 创建临时文件
        File tempDir = FileUtils.getTempDir();
        File destFile = new File(tempDir, originalFilename);
        FileUtil.mkParentDirs(destFile);

        List<ErrorMsgVO> messages = new ArrayList<>();
        File unzipDir;
        try {
            // 保存上传的文件到临时目录
            file.transferTo(destFile);

            // 解压ZIP文件
            unzipDir = ZipUtil.unzip(destFile);

            // 查找实际的PMID目录，可能需要跳过一层包装目录
            File[] pmidDirs = findPmidDirectories(unzipDir);
            if (pmidDirs == null || pmidDirs.length == 0) {
                throw new ServiceException("目录格式错误或无数据，请确保ZIP文件中包含PMID_xxx、PMCID_xxx或CUSTOMID_xxx格式的目录");
            }

            // 定义支持的文件类型
            List<String> typeDefines = Arrays.asList("PDF", "SUPP");

            // 用于收集所有需要上传的文件
            Map<Long, Map<String, List<File>>> docIdToTypeFilesMap = new HashMap<>();

            // 第一步：校验所有文件
            for (File idDir : pmidDirs) {
                if (!idDir.isDirectory()) {
                    log.warn("文件不是目录 " + idDir.getAbsolutePath());
                    continue;
                }

                String dirName = idDir.getName().trim();
                Article article = null;
                String idValue;

                // 解析文件夹名称格式：PMID_xxx, PMCID_xxx, CUSTOMID_xxx
                if (dirName.startsWith("PMID_")) {
                    idValue = dirName.substring(5);
                    if (StrUtil.isNumeric(idValue)) {
                        Long pmid = Long.valueOf(idValue);
                        article = this.getArticleByPmid(pmid);
                    }
                } else if (dirName.startsWith("PMCID_")) {
                    idValue = dirName.substring(6);
                    if (StrUtil.isNumeric(idValue)) {
                        Long pmcId = Long.valueOf(idValue);
                        article = this.getArticleByPmcId(pmcId);
                    }
                } else if (dirName.startsWith("CUSTOMID_")) {
                    idValue = dirName.substring(9);
                    if (StrUtil.isNumeric(idValue)) {
                        Long customId = Long.valueOf(idValue);
                        article = this.getArticleByCustomId(customId);
                    }
                } else {
                    log.warn("文件夹名称格式不正确，应为 PMID_xxx、PMCID_xxx 或 CUSTOMID_xxx 格式: " + dirName);
                    continue;
                }

                if (article == null) {
                    messages.add(ErrorMsgVO.errMsg(StrUtil.format("没有找到文献：{}", dirName)));
                    continue;
                }

                File[] typeDirs = idDir.listFiles();
                if (ArrayUtil.isEmpty(typeDirs)) {
                    messages.add(ErrorMsgVO.errMsg(StrUtil.format("没有指定类型目录，跳过: {}", dirName)));
                    continue;
                }

                Map<String, List<File>> typeToFilesMap = new HashMap<>();
                for (File typeDir : typeDirs) {
                    String type = typeDir.getName().toUpperCase();
                    if (!typeDefines.contains(type)) {
                        messages.add(ErrorMsgVO.errMsg(StrUtil.format("{}，目录类型：{} 不正确", dirName, type)));
                        continue;
                    }
                    // 使用HuTool的FileUtil.loopFiles递归收集文件
                    List<File> files = FileUtil.loopFiles(typeDir);
                    typeToFilesMap.put(type, files);
                }

                // 校验每个文件
                for (Map.Entry<String, List<File>> entry : typeToFilesMap.entrySet()) {
                    String type = entry.getKey();
                    List<File> files = entry.getValue();
                    if (CollUtil.isEmpty(files)) {
                        continue;
                    }
                    for (File fileToUpload : files) {
                        // 计算MD5以检查重复
                        String md5 = DigestUtil.md5Hex(fileToUpload);
                        boolean exists = fileService.existsByDocIdAndMd5(article.getId(), md5);

                        if (exists) {
                            messages.add(ErrorMsgVO.errMsg(StrUtil.format("请勿上传重复文件, {}, type: {} ,file: {}", dirName, type, fileToUpload.getName())));
                        }
                    }
                }

                docIdToTypeFilesMap.put(article.getId(), typeToFilesMap);
            }
            if (CollUtil.isNotEmpty(messages)) {
                return messages;
            }

            // 遍历每个文献
            for (Map.Entry<Long, Map<String, List<File>>> docEntry : docIdToTypeFilesMap.entrySet()) {
                Long docId = docEntry.getKey();
                Map<String, List<File>> typeToFilesMap = docEntry.getValue();

                // 遍历每种类型的文件
                for (Map.Entry<String, List<File>> typeEntry : typeToFilesMap.entrySet()) {
                    String fileType = typeEntry.getKey();
                    List<File> files = typeEntry.getValue();

                    // 确定文件类型枚举
                    FileTypeEnum fileTypeEnum = FileTypeEnum.getFileTypeEnum(fileType.toUpperCase());
                    // 如果是pdf文件，还得先删除原来得pdf文件
                    if (FileTypeEnum.PDF.equals(fileTypeEnum)) {
                        fileService.deleteByDocIdAndFileType(docId, fileTypeEnum.name());
                    }

                    // 上传每个文件
                    for (File fileToUpload : files) {
                        String md5 = DigestUtil.md5Hex(fileToUpload);
                        FileUploadDTO uploadDTO = new FileUploadDTO(fileToUpload, fileTypeEnum, md5, fileToUpload.getName(), ArticleAttachmentSourceEnum.success_man.name(), docId, true);
                        fileService.upload(uploadDTO);
                    }
                }
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("处理ZIP文件失败: " + destFile.getAbsolutePath(), e);
            throw new ServiceException("上传失败：" + e.getMessage());
        }

        return null;
    }

    /**
     * 查找所有PMID格式的目录
     *
     * @param rootDir 根目录
     * @return PMID目录数组
     */
    private File[] findPmidDirectories(File rootDir) {
        List<File> pmidDirs = new ArrayList<>();
        findPmidDirectoriesRecursive(rootDir, pmidDirs);
        return pmidDirs.toArray(new File[0]);
    }

    /**
     * 递归查找PMID目录的具体实现
     *
     * @param dir      当前目录
     * @param pmidDirs 结果列表
     */
    private void findPmidDirectoriesRecursive(File dir, List<File> pmidDirs) {
        File[] files = dir.listFiles();
        if (files == null) {
            return;
        }

        for (File file : files) {
            if (file.isDirectory()) {
                // 如果是PMID格式的目录，添加到结果中
                if (isPmidFormatDirectory(file.getName())) {
                    pmidDirs.add(file);
                }
                // 无论是否是PMID格式目录，都继续递归查找子目录
                findPmidDirectoriesRecursive(file, pmidDirs);
            }
        }
    }

    /**
     * 检查目录名是否符合PMID格式
     *
     * @param dirName 目录名
     * @return 是否符合格式
     */
    private boolean isPmidFormatDirectory(String dirName) {
        if (StrUtil.isBlank(dirName)) {
            return false;
        }
        dirName = dirName.trim();
        return dirName.startsWith("PMID_") ||
                dirName.startsWith("PMCID_") ||
                dirName.startsWith("CUSTOMID_");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ErrorMsgVO> importArticles(List<ArticleImportDTO> importList) {
        if (importList == null || importList.isEmpty()) {
            throw new ServiceException("导入数据不能为空");
        }
        // 每一项都BeanUtil.trim;
        importList = importList.stream().map(BeanUtil::trimStrFields).toList();

        // 查询当前最大customId
        Long currentMaxCustomId = maxCustomIdRangeIn(800000000000L, 801000000000L);

        List<Long> pmids = importList.stream().map(ArticleImportDTO::getPmid).toList();
        List<Long> pmcIds = importList.stream().map(ArticleImportDTO::getPmcId).toList();
        List<Long> customIds = importList.stream().map(ArticleImportDTO::getCustomId).toList();
        List<String> dois = importList.stream().map(ArticleImportDTO::getDoi).toList();
        List<String> titles = importList.stream().map(ArticleImportDTO::getTitle).toList();
        List<String> journalNames = importList.stream().map(ArticleImportDTO::getJournalName).toList();

        Map<Long, Article> pmidToArticleMap = findByPmidIn(pmids).stream().collect(Collectors.toMap(Article::getPmid, x -> x, (x, y) -> y));
        Map<Long, Article> pmcIdToArticleMap = findByPmcIdIn(pmcIds).stream().collect(Collectors.toMap(Article::getPmcId, x -> x, (x, y) -> y));
        Map<Long, Article> customIdToArticleMap = findByCustomIdIn(customIds).stream().collect(Collectors.toMap(Article::getCustomId, x -> x, (x, y) -> y));
        Map<String, Article> doiToArticleMap = findByDoiIn(dois).stream().collect(Collectors.toMap(Article::getDoi, x -> x, (x, y) -> y));
        Map<String, Article> titleToArticleMap = findByTitleIn(titles).stream().collect(Collectors.toMap(Article::getTitle, x -> x, (x, y) -> y));
        Map<String, Journal> journalNameToJournalMap = journalService.findByTitleIn(journalNames).stream().collect(Collectors.toMap(Journal::getTitle, x -> x, (x, y) -> y));

        Map<String, Journal> saveJournalMap = new HashMap<>();

        List<ErrorMsgVO> result = new ArrayList<>();

        Set<Long> pmidRecordSet = new HashSet<>();
        Set<Long> pmcIdRecordSet = new HashSet<>();
        Set<Long> customIdRecordSet = new HashSet<>();
        Set<String> dioRecordSet = new HashSet<>();
        Set<String> titleRecordSet = new HashSet<>();

        // 校验
        for (int i = 0; i < importList.size(); i++) {
            ArticleImportDTO dtoItem = importList.get(i);

            // pmid、pmcid、doi 至少填一个
            Long pmid = dtoItem.getPmid();
            Long pmcId = dtoItem.getPmcId();
            String doi = dtoItem.getDoi();

            // customId 校验逻辑
            Long customId = dtoItem.getCustomId();

            // 如果没有PMID、PMC ID、DOI，则customId必填
            if (pmid == null && pmcId == null && StrUtil.isBlank(doi)) {
                if (customId == null) {
                    result.add(ErrorMsgVO.errMsg(i, "标识符", "", "PMID、PMC ID、DOI、自定义ID 至少需要填写一个"));
                }
            }

            // 如果提供了customId，则进行相关校验
            if (customId != null) {
                // customId 范围校验
                if (currentMaxCustomId != null && customId <= currentMaxCustomId) {
                    result.add(ErrorMsgVO.errMsg(i, "自定义ID", String.valueOf(customId),
                            "自定义ID必须大于当前最大值: " + currentMaxCustomId));
                }
                if (customId >= 801000000000L) {
                    result.add(ErrorMsgVO.errMsg(i, "自定义ID", String.valueOf(customId),
                            "自定义ID必须小于801000000000"));
                }
                // customId 重复校验
                if (customIdToArticleMap.containsKey(customId)) {
                    result.add(ErrorMsgVO.errMsg(i, "自定义ID", String.valueOf(customId), "文章已存在，请勿重复添加"));
                }
                if (customIdRecordSet.contains(customId)) {
                    result.add(ErrorMsgVO.errMsg(i, "自定义ID", String.valueOf(customId), "请勿指定重复的自定义ID"));
                }
                customIdRecordSet.add(customId);
            }

            if (pmid != null) {
                if (pmidToArticleMap.containsKey(pmid)) {
                    result.add(ErrorMsgVO.errMsg(i, "PMID", String.valueOf(pmid), "文章已存在，请勿重复添加"));
                }
                if (pmidRecordSet.contains(pmid)) {
                    result.add(ErrorMsgVO.errMsg(i, "PMID", String.valueOf(pmid), "请勿指定重复的 PMID"));
                }
                if (pmid >= 100000000L) {
                    result.add(ErrorMsgVO.errMsg(i, "PMID", String.valueOf(pmid),
                            "PMID格式不正确，超出正常数字范围"));
                }
                pmidRecordSet.add(pmid);
            }

            if (pmcId != null) {
                // PMC ID 基本校验：确保是正数
                if (pmcId <= 0) {
                    result.add(ErrorMsgVO.errMsg(i, "PMC ID", String.valueOf(pmcId), "PMC ID必须是正数"));
                }
                if (pmcIdToArticleMap.containsKey(pmcId)) {
                    result.add(ErrorMsgVO.errMsg(i, "PMC ID", String.valueOf(pmcId), "文章已存在，请勿重复添加"));
                }
                if (pmcIdRecordSet.contains(pmcId)) {
                    result.add(ErrorMsgVO.errMsg(i, "PMC ID", String.valueOf(pmcId), "请勿指定重复的 PMC ID"));
                }
                pmcIdRecordSet.add(pmcId);
            }

            if (StrUtil.isNotBlank(doi)) {
                // DOI 格式校验：必须以10.开头，符合DOI标准格式
                if (!doi.matches("^10\\.\\d{4,}/.*$")) {
                    result.add(ErrorMsgVO.errMsg(i, "DOI", doi, "DOI格式不正确，必须以10.开头并符合标准DOI格式"));
                }
                if (doiToArticleMap.containsKey(doi)) {
                    result.add(ErrorMsgVO.errMsg(i, "DOI", doi, "文章已存在，请勿重复添加"));
                }
                if (dioRecordSet.contains(doi)) {
                    result.add(ErrorMsgVO.errMsg(i, "DOI", doi, "请勿指定重复的 DOI"));
                }
                dioRecordSet.add(doi);
            }

            // 来源必填
            if (StrUtil.isBlank(dtoItem.getSource())) {
                result.add(ErrorMsgVO.errMsg(i, "来源", dtoItem.getSource(), "来源不能为空"));
            }

            String title = dtoItem.getTitle();
            if (title != null) {
                if (titleToArticleMap.containsKey(title)) {
                    result.add(ErrorMsgVO.errMsg(i, "标题", title, "文章已存在，请勿重复添加"));
                }
                if (titleRecordSet.contains(title)) {
                    result.add(ErrorMsgVO.errMsg(i, "标题", title, "请勿指定重复的标题"));
                }
                titleRecordSet.add(title);
            }


            // 作者必填
            if (StrUtil.isBlank(dtoItem.getAuthor())) {
                result.add(ErrorMsgVO.errMsg(i, "作者", dtoItem.getAuthor(), "作者不能为空"));
            }

            // 确保期刊填写正确
            if (StrUtil.isNotBlank(dtoItem.getJournalName())) {
                if (journalNameToJournalMap.containsKey(dtoItem.getJournalName())) {
                    dtoItem.setJournalId(journalNameToJournalMap.get(dtoItem.getJournalName()).getId());
                } else if (saveJournalMap.containsKey(dtoItem.getJournalName())) {
                    dtoItem.setJournalId(saveJournalMap.get(dtoItem.getJournalName()).getId());
                } else {
                    // 如果不存在就new一个
                    Journal journal = new Journal();
                    long journalId = IdUtil.getSnowflakeNextId();
                    journal.setId(journalId);
                    journal.setTitle(dtoItem.getJournalName());
                    journal.setStatus(StatusEnums.ENABLE.getCode());
                    journal.setSource(CollUtil.newArrayList(SourceTypeEnums.Custom.name()));
                    journal.setSourceType(JournalSourceTypeEnums.system.name());
                    journal.setCreateTime(new Date());
                    journal.setUpdateTime(new Date());
                    dtoItem.setJournalId(journalId);
                    saveJournalMap.put(dtoItem.getJournalName(), journal);
                }
            }

        }
        if (CollUtil.isNotEmpty(result)) {
            return result;
        }

        List<Article> saveList = new ArrayList<>();
        // 用于自动分配customId
        Long nextCustomId = currentMaxCustomId + 1;

        for (ArticleImportDTO importDTO : importList) {
            try {

                Article article = new Article();
                // 创建新的文献记录
                BeanUtil.copyProperties(importDTO, article);

                // 如果没有提供customId，但有PMID/PMC ID/DOI，则自动分配customId
                if (importDTO.getCustomId() == null &&
                    (importDTO.getPmid() != null || importDTO.getPmcId() != null || StrUtil.isNotBlank(importDTO.getDoi()))) {
                    article.setCustomId(nextCustomId++);
                }

                // 处理作者列表
                if (StrUtil.isNotBlank(importDTO.getAuthor())) {
                    String[] split = importDTO.getAuthor().split(";");
                    List<String> list = Arrays.stream(split).filter(StrUtil::isNotBlank).toList();
                    article.setAuthor(list.stream().map(String::trim).collect(Collectors.toList()));
                }

                // 处理机构列表
                if (StrUtil.isNotBlank(importDTO.getOrganization())) {
                    String[] split = importDTO.getOrganization().split(";");
                    List<String> list = Arrays.stream(split).filter(StrUtil::isNotBlank).toList();
                    article.setAffiliation(list.stream().map(String::trim).collect(Collectors.toList()));
                }

                // 处理关键词列表
                if (StrUtil.isNotBlank(importDTO.getKeyword())) {
                    String[] split = importDTO.getKeyword().split(";");
                    List<String> list = Arrays.stream(split).filter(StrUtil::isNotBlank).toList();
                    article.setKeywords(list.stream().map(String::trim).collect(Collectors.toList()));
                }

                // 处理来源列表
                if (StrUtil.isNotBlank(importDTO.getSource())) {
                    String[] split = importDTO.getSource().split(";");
                    List<String> list = Arrays.stream(split)
                            .filter(StrUtil::isNotBlank)
                            .map(SourceTypeEnums::processUserInput)
                            .collect(Collectors.toList());
                    article.setSource(list);
                }

                // 设置创建和更新时间
                Date now = new Date();
                article.setCreateTime(now);
                article.setUpdateTime(now);

                saveList.add(article);
            } catch (Exception e) {
                throw new ServiceException(e.getMessage());
            }
        }

        // 保存
        saveBatch(saveList);
        journalService.saveBatch(saveJournalMap.values());
        return null;
    }

    @Override
    public List<TbDdsFile> uploadAttachment(ArticleUploadAttachmentDTO dto) {
        getOptById(dto.getDocId()).orElseThrow(() -> new ServiceException("文献不存在"));

        FileTypeEnum fileTypeEnum = FileTypeEnum.getFileTypeEnum(dto.getFileType());

        // 如果上传的时PDF就先，删除原有
        final boolean isPdf = FileTypeEnum.PDF.equals(fileTypeEnum);
        if (isPdf) {
            fileService.deleteByDocIdAndFileType(dto.getDocId(), fileTypeEnum.name());
        }

        List<MultipartFile> multipartFiles = dto.getFile();

        ArrayList<TbDdsFile> result = new ArrayList<>();
        // 保存新的
        for (MultipartFile multipartFile : multipartFiles) {
            FileUploadDTO uploadDTO = new FileUploadDTO(multipartFile, fileTypeEnum, null, multipartFile.getOriginalFilename(),
                    ArticleAttachmentSourceEnum.success_man.name(), dto.getDocId(), isPdf);
            TbDdsFile tbDdsFile = fileService.upload(uploadDTO);
            result.add(tbDdsFile);
        }

        return result;
    }

    private List<Article> findByTitleIn(List<String> titles) {
        titles = titles.stream().filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(titles)) {
            return Collections.emptyList();
        }
        return this.list(Wrappers.<Article>lambdaQuery()
                .in(Article::getTitle, titles));
    }

    private List<Article> findByDoiIn(List<String> dois) {
        dois = dois.stream().filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(dois)) {
            return Collections.emptyList();
        }
        return this.list(Wrappers.<Article>lambdaQuery()
                .in(Article::getDoi, dois));
    }


    private List<Article> findByPmcIdIn(List<Long> pmcIds) {
        pmcIds = pmcIds.stream().filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(pmcIds)) {
            return Collections.emptyList();
        }
        return this.list(Wrappers.<Article>lambdaQuery()
                .in(Article::getPmcId, pmcIds));
    }

    private List<Article> findByPmidIn(List<Long> pmids) {
        pmids = pmids.stream().filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(pmids)) {
            return Collections.emptyList();
        }
        return this.list(Wrappers.<Article>lambdaQuery()
                .in(Article::getPmid, pmids));
    }

    private List<Article> findByCustomIdIn(List<Long> customIds) {
        customIds = customIds.stream().filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(customIds)) {
            return Collections.emptyList();
        }
        return this.list(Wrappers.<Article>lambdaQuery()
                .in(Article::getCustomId, customIds));
    }

    /**
     * 查询指定范围内的最大customId
     *
     * @param minValue 最小值（包含）
     * @param maxValue 最大值（不包含）
     * @return 最大customId值，如果没有找到则返回null
     */
    private Long maxCustomIdRangeIn(Long minValue, Long maxValue) {
        Article article = this.getOne(Wrappers.<Article>lambdaQuery()
                .ge(Article::getCustomId, minValue)
                .lt(Article::getCustomId, maxValue)
                .orderByDesc(Article::getCustomId)
                .last("LIMIT 1"));
        return article != null ? article.getCustomId() : minValue;
    }

    @Override
    public Long getMaxCustomIdInRange(Long minValue, Long maxValue) {
        return maxCustomIdRangeIn(minValue, maxValue);
    }

    /**
     * 批量更新文献的期刊ID（用于期刊合并）
     *
     * @param targetJournalId
     * @param sourceJournalIds
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateJournalIdForMerge(Long targetJournalId, List<Long> sourceJournalIds) {
        baseMapper.updateJournalIdBatch(targetJournalId, sourceJournalIds);
    }

    /**
     * 获取文献总下载量
     *
     * @return 总下载量
     */
    @Override
    public Long getTotalDownload() {
        return baseMapper.getTotalDownload();
    }
}
