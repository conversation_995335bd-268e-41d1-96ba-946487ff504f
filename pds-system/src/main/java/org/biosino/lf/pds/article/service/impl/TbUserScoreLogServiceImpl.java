package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.TbDdsFile;
import org.biosino.lf.pds.article.domain.TbUserScoreLog;
import org.biosino.lf.pds.article.domain.TbUserScoreRule;
import org.biosino.lf.pds.article.mapper.TbUserScoreLogMapper;
import org.biosino.lf.pds.article.mapper.TbUserScoreRuleMapper;
import org.biosino.lf.pds.article.service.ITbDdsFileService;
import org.biosino.lf.pds.article.service.ITbUserScoreLogService;
import org.biosino.lf.pds.common.enums.task.FileTypeEnum;
import org.biosino.lf.pds.common.enums.task.ScoreSourceEnum;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.DownloadUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * plosp积分
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbUserScoreLogServiceImpl extends ServiceImpl<TbUserScoreLogMapper, TbUserScoreLog> implements ITbUserScoreLogService {
    private final ITbDdsFileService tbDdsFileService;
    private final TbUserScoreLogMapper tbUserScoreLogMapper;
    private final TbUserScoreRuleMapper tbUserScoreRuleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void downloadArticlePDF(final Long docId, final Long userId, final ScoreSourceEnum scoreSourceEnum,
                                   final HttpServletRequest request, final HttpServletResponse response) {
        if (userId == null) {
            throw new ServiceException("用户未登录");
        }

        final List<TbDdsFile> ddsFileList = tbDdsFileService.findByDocIdsAndType(CollUtil.toList(docId), FileTypeEnum.PDF);
        if (CollUtil.isEmpty(ddsFileList)) {
            throw new ServiceException("文献PDF不存在");
        }
        final TbDdsFile dbAttachment = ddsFileList.stream().sorted(Comparator.comparing(TbDdsFile::getCreateTime).reversed()).findFirst()
                .orElseThrow(() -> new ServiceException("文献PDF不存在"));
        final File file = TbDdsFileServiceImpl.initDiskFile(dbAttachment.getFilePath());

        //已经下载过某文献，再次下载不扣分
        final TbUserScoreLog scoreLog = tbUserScoreLogMapper.findOne(Wrappers.lambdaQuery(TbUserScoreLog.class)
                .eq(TbUserScoreLog::getUserId, userId)
                .eq(TbUserScoreLog::getDocId, docId).eq(TbUserScoreLog::getSource, scoreSourceEnum)
                .select(TbUserScoreLog::getId));
        if (scoreLog == null) {
            //文献未下载过，则会扣分
            changeUserScoreLog(scoreSourceEnum, docId, userId);
        }

        try {
            if (FileUtil.exist(file) && file.isFile()) {
                DownloadUtils.download(request, response, file);
            } else {
                throw new ServiceException("文件不存在");
            }
        } catch (IOException e) {
            log.warn("文献下载文件失败：", e);
        }
    }

    /**
     * 根据ScoreSourceEnum添加积分日志（日志中score大于0表示增加积分，小于0则减少积分）
     */
    public void changeUserScoreLog(ScoreSourceEnum sourceEnum, Long docId, Long userId) {
        if (sourceEnum == null || userId == null) {
            return;
        }
        final TbUserScoreRule rule = tbUserScoreRuleMapper.findOne(Wrappers.lambdaQuery(TbUserScoreRule.class).eq(TbUserScoreRule::getName, sourceEnum.name()));
        if (rule == null) {
            log.error("未找到积分规则：" + sourceEnum.name());
            return;
        }
        final Long score = tbUserScoreLogMapper.sumUserScore(userId);
        if (score + rule.getScore() < 0) {
            throw new ServiceException("积分不足");
        }

        final TbUserScoreLog log = new TbUserScoreLog();
        log.setUserId(userId.intValue());
        log.setSource(sourceEnum.name());
        log.setCreateTime(new Date());
        log.setScore(rule.getScore());
        log.setComment(rule.getComment());
        if (docId != null && docId > 0) {
            log.setDocId(docId);
        }
        tbUserScoreLogMapper.insert(log);
    }

}
