package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.biosino.lf.pds.article.custbean.dto.FileUploadDTO;
import org.biosino.lf.pds.article.domain.TbDdsFile;
import org.biosino.lf.pds.article.domain.TbDdsFileContent;
import org.biosino.lf.pds.article.mapper.TbDdsFileContentMapper;
import org.biosino.lf.pds.article.mapper.TbDdsFileMapper;
import org.biosino.lf.pds.article.service.ITbDdsFileService;
import org.biosino.lf.pds.common.config.AppConfig;
import org.biosino.lf.pds.common.enums.DirectoryEnum;
import org.biosino.lf.pds.common.enums.task.FileTypeEnum;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.DownloadUtils;
import org.biosino.lf.pds.common.utils.MyHashUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * 文件处理
 * (处理tb_dds_file表和tb_dds_file_content表数据)
 *
 * <AUTHOR>
 * @see org.biosino.lf.pds.article.mapper.TbDdsFileMapper
 * @see org.biosino.lf.pds.article.mapper.TbDdsFileContentMapper
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbDdsFileServiceImpl extends ServiceImpl<TbDdsFileMapper, TbDdsFile> implements ITbDdsFileService {
    private final TbDdsFileMapper tbDdsFileMapper;
    private final TbDdsFileContentMapper tbDdsFileContentMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TbDdsFile upload(FileUploadDTO uploadDTO) {
        if (uploadDTO == null) {
            throw new ServiceException("上传参数不能为空");
        }

        // 处理File类型上传
        if (uploadDTO.getFile() != null) {
            return uploadFile(uploadDTO);
        }

        // 处理MultipartFile类型上传
        if (uploadDTO.getMultipartFile() != null) {
            return uploadMultipartFile(uploadDTO);
        }

        throw new ServiceException("必须提供文件或MultipartFile");
    }

    /**
     * 处理File类型上传
     */
    private TbDdsFile uploadFile(FileUploadDTO uploadDTO) {
        File file = uploadDTO.getFile();

        // 验证文件参数
        if (file == null || !file.exists() || !file.isFile()) {
            throw new ServiceException("文件不存在");
        }
        if (file.length() <= 0) {
            throw new ServiceException("文件不能为空");
        }

        try {
            // 获取文件名和字节数据
            final String fileName = file.getName();
            final long fileSize = file.length();

            // 使用公共方法处理上传
            return processFileUpload(
                    file,
                    null,
                    uploadDTO.getFileTypeEnum(),
                    uploadDTO.getFileMd5(),
                    uploadDTO.getOriginalFilename() != null ? uploadDTO.getOriginalFilename() : fileName,
                    fileName,
                    fileSize,
                    uploadDTO.getSource(),
                    uploadDTO.getDocId(),
                    uploadDTO.getCreateSiteId(),
                    uploadDTO.isCheckExist()
            );
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new ServiceException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 处理MultipartFile类型上传
     */
    private TbDdsFile uploadMultipartFile(FileUploadDTO uploadDTO) {
        MultipartFile file = uploadDTO.getMultipartFile();

        // 验证文件参数
        if (file == null || file.isEmpty()) {
            throw new ServiceException("文件不能为空");
        }

        try {
            // 获取文件字节数据和原始文件名
            String fileName = StrUtil.isNotBlank(uploadDTO.getOriginalFilename()) ?
                    uploadDTO.getOriginalFilename() : file.getOriginalFilename();

            // 使用公共方法处理上传
            return processFileUpload(
                    null,
                    file,
                    uploadDTO.getFileTypeEnum(),
                    uploadDTO.getFileMd5(),
                    fileName,
                    fileName,
                    file.getSize(),
                    uploadDTO.getSource(),
                    uploadDTO.getDocId(),
                    uploadDTO.getCreateSiteId(),
                    uploadDTO.isCheckExist()
            );
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new ServiceException("文件上传失败: " + e.getMessage());
        }
    }


    /**
     * 处理文件上传的公共方法
     *
     * @param fileTypeEnum    文件类型枚举
     * @param fileMd5         提供的文件MD5（可为空）
     * @param displayFileName 显示的文件名
     * @param fileNameForExt  用于提取扩展名的文件名
     * @param fileSize        文件大小
     * @param source          来源
     * @param docId           文档ID
     * @param createSiteId    小蚂蚁上传时的节点id
     * @return 保存后的文件对象
     * @throws IOException 如果文件处理出错
     */
    private TbDdsFile processFileUpload(
            final File file,
            final MultipartFile multipartFile,
            final FileTypeEnum fileTypeEnum,
            final String fileMd5,
            final String displayFileName,
            final String fileNameForExt,
            final long fileSize,
            final String source,
            final Long docId,
            final Integer createSiteId,
            final boolean checkExist
    ) throws IOException {
        // 1. 计算文件MD5
        final String md5;
        if (StrUtil.isNotBlank(fileMd5)) {
            md5 = fileMd5;
        } else {
            md5 = MyHashUtil.md5(initInputStream(file, multipartFile));
        }

        // 2. 获取文件扩展名
        final String fileExt = getFileExtension(fileNameForExt);
        final String typeName = fileTypeEnum.name();

        if (checkExist) {
            // 3. 检查文件md5是否已存在
            checkFileExists(md5, displayFileName, docId, fileTypeEnum);
        }

        // 4. 创建文件元数据对象
        final TbDdsFile ddsFile = createFileMetadata(
                displayFileName, fileExt, fileSize, md5, typeName, source, docId, createSiteId);

        // 5. 处理文件存储（PDF存磁盘，其他存数据库）
        final boolean isPdfFile = FileTypeEnum.PDF.equals(fileTypeEnum) || fileExt.toLowerCase().endsWith(".pdf");

        if (isPdfFile) {
            // 5.1 处理PDF文件
            handlePdfFile(ddsFile, initInputStream(file, multipartFile), fileExt);
        } else {
            // 5.2 非PDF文件不设置filePath
            ddsFile.setFilePath(null);
        }

        // 6. 保存文件元数据
        this.save(ddsFile);

        // 7. 如果是非PDF文件，保存文件内容到数据库
        if (!isPdfFile) {
            saveFileContent(ddsFile.getId(), initByteArray(file, multipartFile));
        }

        return ddsFile;
    }

    private InputStream initInputStream(File file, MultipartFile multipartFile) throws IOException {
        return file != null ? FileUtil.getInputStream(file) : multipartFile.getInputStream();
    }

    private byte[] initByteArray(File file, MultipartFile multipartFile) throws IOException {
        // return file != null ? FileUtil.readBytes(file) : multipartFile.getBytes();
        return file != null ? FileUtils.readFileToByteArray(file) : multipartFile.getBytes();
    }


    /**
     * 检查文件是否已存在
     */
    private void checkFileExists(String md5, String fileName, Long docId, final FileTypeEnum fileTypeEnum) {
        // 判断是否需要校验type
//        final boolean checkType = checkTypeOrNot(fileTypeEnum);
        final TbDdsFile existingFile = tbDdsFileMapper.findOne(Wrappers.lambdaQuery(TbDdsFile.class)
                .eq(TbDdsFile::getMd5, md5)
                .eq(TbDdsFile::getType, fileTypeEnum.name())
                .eq(docId != null, TbDdsFile::getDocId, docId)
                .select(TbDdsFile::getId)
        );

        if (existingFile != null) {
            log.error("文件已存在，文件名：{}，type：{}，MD5: {}，docId：{}", fileName, fileTypeEnum.name(), md5, docId);
            throw new ServiceException("文件已存在");
        }
    }

    private boolean checkTypeOrNot(FileTypeEnum fileTypeEnum) {
        switch (fileTypeEnum) {
            case SCRIPT -> {
                return true;
            }
        }
        return false;
    }

    /**
     * 创建文件元数据对象
     */
    private TbDdsFile createFileMetadata(
            String fileName,
            String contentType,
            long fileSize,
            String md5,
            String typeName,
            String source,
            Long docId, Integer createSiteId) {

        final TbDdsFile ddsFile = new TbDdsFile();
        ddsFile.setFileName(fileName);
        ddsFile.setContentType(contentType);
        ddsFile.setFileSize(fileSize);
        ddsFile.setMd5(md5);
        ddsFile.setCreateTime(new Date());
        ddsFile.setType(typeName);
        ddsFile.setSource(source);
        ddsFile.setDocId(docId);
        ddsFile.setCreateSiteId(createSiteId);

        return ddsFile;
    }

    /**
     * 处理PDF文件（存储到磁盘）
     */
    private void handlePdfFile(final TbDdsFile ddsFile, final InputStream inputStream, final String fileExt) {
        try {
            // 创建PDF存储目录，使用两层目录分散文件
            final String pathStr = randomStr(2) + "/" + randomStr(2) + "/" + IdUtil.fastSimpleUUID() + fileExt;
            final DirectoryEnum directoryEnum = DirectoryEnum.attachment;
            final String path = "PDF/" + pathStr;
            final File destFile = new File(AppConfig.initDataHome(directoryEnum), path);
            if (!destFile.getParentFile().exists()) {
                destFile.getParentFile().mkdirs();
            }
            // 此方法会自动创建目标文件所在的目录
            FileUtil.writeFromStream(inputStream, destFile, false);

            /*if (sourceFile != null) {
                // 复制文件到PDF目录
                Files.copy(sourceFile.toPath(), destFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            } else {
                // 写入文件到PDF目录
                //Files.write(pdfPath, fileBytes);
                sourceMultipartFile.transferTo(destFile);
            }*/

            // 设置文件路径
            ddsFile.setFilePath(directoryEnum + "/" + path);
        } finally {
            IoUtil.close(inputStream);
        }
    }

    private String randomStr(final int length) {
        return RandomUtil.randomString(RandomUtil.BASE_CHAR_NUMBER_LOWER, length);
    }

    /**
     * 保存文件内容到数据库
     */
    private void saveFileContent(Long fileId, byte[] fileBytes) {
        final TbDdsFileContent fileContent = new TbDdsFileContent();
        fileContent.setId(fileId);
        fileContent.setFileData(fileBytes);
        tbDdsFileContentMapper.insert(fileContent);
    }

    @Override
    public TbDdsFileContent findContentById(Long id) {
        if (id == null) {
            return null;
        }
        return tbDdsFileContentMapper.selectById(id);
    }

    @Override
    public boolean delById(Long id) {
        return delById(id, null);
    }

    @Override
    public boolean delById(Long id, TbDdsFile tbDdsFile) {
        if (id == null) {
            return false;
        }
        tbDdsFileContentMapper.deleteById(id);

        final TbDdsFile ddsFile;
        if (tbDdsFile != null) {
            ddsFile = tbDdsFile;
        } else {
            ddsFile = this.getById(id);
        }

        if (ddsFile != null) {
            final String filePath = ddsFile.getFilePath();
            final File destFile = initDiskFile(filePath);
            if (destFile != null && destFile.exists() && destFile.isFile()) {
                FileUtil.del(destFile);
            }
        }
        return this.removeById(id);
    }

    public static File initDiskFile(final String filePath) {
        if (StrUtil.isNotBlank(filePath)) {
            return new File(AppConfig.initDataHome(null), filePath);
        }
        return null;
    }

    /**
     * 判断文件是否存在（元数据和内容同时存在）
     */
    @Override
    public boolean existsById(final Long fileId) {
        if (fileId == null) {
            return false;
        }
        final TbDdsFile fileMeta = tbDdsFileMapper.findOne(Wrappers.lambdaQuery(TbDdsFile.class).eq(TbDdsFile::getId, fileId).select(TbDdsFile::getId));
        return fileMeta != null && existsContentById(fileId);
    }

    @Override
    public boolean existsContentById(Long fileId) {
        if (fileId == null) {
            return false;
        }
        final TbDdsFileContent fileContent = tbDdsFileContentMapper.findOne(Wrappers.lambdaQuery(TbDdsFileContent.class)
                .eq(TbDdsFileContent::getId, fileId).select(TbDdsFileContent::getId));
        return fileContent != null;
    }

    /**
     * 获取文件扩展名（包含.）
     *
     * @param fileName 文件名
     * @return 扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? "" : fileName.substring(dotIndex);
    }


    @Override
    public List<TbDdsFile> findByDocIdIn(Collection<Long> docIds) {
        if (CollUtil.isEmpty(docIds)) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.lambdaQuery(TbDdsFile.class).in(TbDdsFile::getDocId, docIds));
    }

    @Override
    public List<TbDdsFile> findByDocIdAndType(Long docId, String name) {
        if (docId == null || StrUtil.isBlank(name)) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.lambdaQuery(TbDdsFile.class)
                .eq(TbDdsFile::getDocId, docId)
                .eq(TbDdsFile::getType, name));
    }

    @Override
    public List<TbDdsFile> findByDocIdsAndType(Collection<Long> docIds, FileTypeEnum typeEnum) {
        if (CollUtil.isEmpty(docIds) || typeEnum == null) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.lambdaQuery(TbDdsFile.class)
                .in(TbDdsFile::getDocId, new HashSet<>(docIds))
                .eq(TbDdsFile::getType, typeEnum.name()));
    }

    @Override
    public void download(Long fileId, String fileName, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 1. 参数验证
            if (fileId == null) {
                throw new ServiceException("文件ID不能为空");
            }
            if (response == null) {
                throw new ServiceException("HttpServletResponse不能为空");
            }

            // 2. 查询文件元数据
            TbDdsFile ddsFile = this.getById(fileId);
            if (ddsFile == null) {
                throw new ServiceException("文件不存在");
            }

            // 3. 确定下载文件名
            String downloadFileName = StrUtil.isNotBlank(fileName) ? fileName : ddsFile.getFileName();
            if (StrUtil.isBlank(downloadFileName)) {
                downloadFileName = "download_file_" + fileId;
            }

            // 5. 处理文件下载
            if (StrUtil.isNotBlank(ddsFile.getFilePath())) {
                // 5.1 PDF文件从磁盘下载
                downloadFromDisk(ddsFile, downloadFileName, request, response);
            } else {
                // 5.2 其他文件从数据库下载
                downloadFromDatabase(fileId, downloadFileName, request, response);
            }

            log.info("文件下载成功：fileId={}, fileName={}", fileId, downloadFileName);

        } catch (ServiceException e) {
            log.error("文件下载失败：fileId={}, fileName={}, error={}", fileId, fileName, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("文件下载异常：fileId={}, fileName={}", fileId, fileName, e);
            throw new ServiceException("文件下载失败：" + e.getMessage());
        }
    }

    /**
     * 从磁盘下载文件（主要用于PDF文件）
     */
    private void downloadFromDisk(TbDdsFile ddsFile, String downloadFileName, HttpServletRequest request, HttpServletResponse response) {
        try {
            File diskFile = initDiskFile(ddsFile.getFilePath());
            if (diskFile == null || !diskFile.exists() || !diskFile.isFile()) {
                throw new ServiceException("磁盘文件不存在：" + ddsFile.getFilePath());
            }

            // 使用DownloadUtils下载文件
            DownloadUtils.download(request, response, diskFile, downloadFileName);

        } catch (Exception e) {
            log.error("从磁盘下载文件失败：filePath={}", ddsFile.getFilePath(), e);
            throw new ServiceException("下载文件失败：" + e.getMessage());
        }
    }

    /**
     * 从数据库下载文件（主要用于非PDF文件）
     */
    private void downloadFromDatabase(Long fileId, String downloadFileName, HttpServletRequest request, HttpServletResponse response) {
        try {
            // 查询文件内容
            TbDdsFileContent fileContent = findContentById(fileId);
            if (fileContent == null) {
                throw new ServiceException("文件内容不存在");
            }

            byte[] fileData = fileContent.getFileData();
            if (fileData == null || fileData.length == 0) {
                throw new ServiceException("文件内容为空");
            }

            // 使用DownloadUtils下载字节数组
            DownloadUtils.download(request, response, fileData, downloadFileName);

        } catch (Exception e) {
            log.error("从数据库下载文件失败：fileId={}", fileId, e);
            throw new ServiceException("下载文件失败：" + e.getMessage());
        }
    }


    @Override
    public void deleteByDocIdAndFileType(Long docId, String fileType) {
        if (docId == null || StrUtil.isBlank(fileType)) {
            return;
        }
        // 找到文件
        List<TbDdsFile> fileList = this.list(Wrappers.lambdaQuery(TbDdsFile.class)
                .eq(TbDdsFile::getDocId, docId)
                .eq(TbDdsFile::getType, fileType));
        if (CollUtil.isEmpty(fileList)) {
            return;
        }
        // 删除文件
        for (TbDdsFile ddsFile : fileList) {
            delById(ddsFile.getId(), ddsFile);
        }
    }

    @Override
    public boolean existsByDocIdAndMd5(Long id, String md5) {
        if (id == null || StrUtil.isBlank(md5)) {
            return false;
        }
        return this.count(Wrappers.lambdaQuery(TbDdsFile.class)
                .eq(TbDdsFile::getDocId, id)
                .eq(TbDdsFile::getMd5, md5)) > 0;
    }
}
