package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.biosino.lf.pds.article.domain.TbUserScoreLog;
import org.biosino.lf.pds.common.enums.task.ScoreSourceEnum;

/**
 * plosp积分
 *
 * <AUTHOR>
 */
public interface ITbUserScoreLogService extends IService<TbUserScoreLog> {

    void downloadArticlePDF(Long docId, Long userId, ScoreSourceEnum scoreSourceEnum, HttpServletRequest request, HttpServletResponse response);

}
