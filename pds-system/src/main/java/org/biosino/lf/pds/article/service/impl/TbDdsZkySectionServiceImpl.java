package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.TbDdsZkySection;
import org.biosino.lf.pds.article.mapper.TbDdsZkySectionMapper;
import org.biosino.lf.pds.article.service.ITbDdsZkySectionService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 中科院分区表 服务实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class TbDdsZkySectionServiceImpl extends ServiceImpl<TbDdsZkySectionMapper, TbDdsZkySection> implements ITbDdsZkySectionService {

}
