package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.PlospUserPointsRecord;
import org.biosino.lf.pds.article.dto.PlospUserPointsChangeDTO;
import org.biosino.lf.pds.article.dto.PlospUserPointsRecordQueryDTO;

import java.util.List;

/**
 * 用户积分记录服务接口
 */
public interface IPlospUserPointsRecordService extends IService<PlospUserPointsRecord> {

    /**
     * 查询用户积分记录列表
     *
     * @param queryDTO 查询参数
     * @return 积分记录列表
     */
    List<PlospUserPointsRecord> selectPointsRecordList(PlospUserPointsRecordQueryDTO queryDTO);

    /**
     * 新增积分变动记录
     *
     * @param changeDTO 积分变动信息
     * @return 结果
     */
    boolean addPointsRecord(PlospUserPointsChangeDTO changeDTO);
}
