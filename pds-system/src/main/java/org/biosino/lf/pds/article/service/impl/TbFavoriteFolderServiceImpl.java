package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.TbFavoriteDoc;
import org.biosino.lf.pds.article.domain.TbFavoriteFolder;
import org.biosino.lf.pds.article.mapper.TbFavoriteFolderMapper;
import org.biosino.lf.pds.article.service.ITbFavoriteDocService;
import org.biosino.lf.pds.article.service.ITbFavoriteFolderService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 收藏夹服务层实现类
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class TbFavoriteFolderServiceImpl extends ServiceImpl<TbFavoriteFolderMapper, TbFavoriteFolder> implements ITbFavoriteFolderService {

    private final ITbFavoriteDocService favoriteDocService;

    @Override
    public List<TbFavoriteFolder> listByUserId(Long userId) {
        if (userId == null) {
            throw new RuntimeException("参数不能为空");
        }
        LambdaQueryWrapper<TbFavoriteFolder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbFavoriteFolder::getUserId, userId)
                .orderByAsc(TbFavoriteFolder::getCreateTime);
        List<TbFavoriteFolder> list = this.list(queryWrapper);
        // 获取收藏夹中的文件数量
        for (TbFavoriteFolder folder : list) {
            LambdaQueryWrapper<TbFavoriteDoc> query = new LambdaQueryWrapper<>();
            query.eq(TbFavoriteDoc::getUserId, userId)
                    .eq(TbFavoriteDoc::getFolderId, folder.getId());
            long count = favoriteDocService.count(query);
            folder.setCount(count);
        }
        return list;
    }

    @Override
    public void createFolder(Long userId, String folderName) {
        if (userId == null) {
            throw new RuntimeException("参数不能为空");
        }
        if (StrUtil.isBlank(folderName)) {
            throw new RuntimeException("收藏夹名称不能为空");
        }
        if (isExist(userId, folderName)) {
            throw new RuntimeException("收藏夹已存在");
        }
        TbFavoriteFolder tbFavoriteFolder = new TbFavoriteFolder();
        tbFavoriteFolder.setFolderName(folderName);
        tbFavoriteFolder.setUserId(userId);
        this.save(tbFavoriteFolder);
    }

    @Override
    public void updateFolder(Long id, String folderName) {
        if (id == null) {
            throw new RuntimeException("参数不能为空");
        }
        if (folderName == null) {
            throw new RuntimeException("收藏夹名称不能为空");
        }
        TbFavoriteFolder folder = this.getById(id);
        if (folder.getStatus() == 1) {
            throw new RuntimeException("默认收藏夹，不可更新");
        }
        folder.setFolderName(folderName);
        this.updateById(folder);
    }

    @Override
    public void deleteFolderWithDoc(Long id) {
        if (id == null) {
            throw new RuntimeException("收藏夹id为空");
        }
        // 若为默认收藏夹，则不能删除
        TbFavoriteFolder folder = this.getById(id);
        if (folder.getStatus() == 1) {
            throw new RuntimeException("默认收藏夹，不可删除");
        }
        // 查询当前收藏夹中的所有文献
        LambdaQueryWrapper<TbFavoriteDoc> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(TbFavoriteDoc::getId)
                .eq(TbFavoriteDoc::getFolderId, id);
        List<TbFavoriteDoc> favoriteDocList = favoriteDocService.list(queryWrapper);
        List<Long> ids = favoriteDocList.stream().map(TbFavoriteDoc::getId).toList();
        favoriteDocService.removeBatchByIds(ids);
        this.removeById(id);
    }

    /**
     * 判断收藏夹是否已存在
     */
    private boolean isExist(Long userId, String folderName) {
        LambdaQueryWrapper<TbFavoriteFolder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TbFavoriteFolder::getFolderName, folderName)
                .eq(TbFavoriteFolder::getUserId, userId);
        long count = this.count(queryWrapper);
        return count > 0;
    }

}
