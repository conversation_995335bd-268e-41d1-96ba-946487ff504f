package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.Organization;
import org.biosino.lf.pds.article.mapper.OrganizationMapper;
import org.biosino.lf.pds.article.service.IArticleAuthorService;
import org.biosino.lf.pds.article.service.IOrganizationService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 机构信息表 服务实现类
 */
@Service
public class OrganizationServiceImpl extends ServiceImpl<OrganizationMapper, Organization> implements IOrganizationService {

    private final IArticleAuthorService articleAuthorService;

    public OrganizationServiceImpl(@Lazy IArticleAuthorService articleAuthorService) {
        this.articleAuthorService = articleAuthorService;
    }

    @Override
    public List<Organization> findByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return CollUtil.newArrayList();
        }
        return this.list(
                Wrappers.<Organization>lambdaQuery()
                        .in(Organization::getId, ids)
        );
    }


}
