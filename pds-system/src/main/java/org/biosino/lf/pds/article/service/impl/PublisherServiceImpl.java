package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.Publisher;
import org.biosino.lf.pds.article.dto.PublisherMergeDTO;
import org.biosino.lf.pds.article.dto.PublisherQueryDTO;
import org.biosino.lf.pds.article.dto.PublisherValidationResult;
import org.biosino.lf.pds.article.mapper.JournalMapper;
import org.biosino.lf.pds.article.mapper.PublisherMapper;
import org.biosino.lf.pds.article.mapper.TbDdsTaskPaperMapper;
import org.biosino.lf.pds.article.service.IPublisherService;
import org.biosino.lf.pds.article.service.PublisherValidationService;
import org.biosino.lf.pds.article.vo.ErrorMsgVO;
import org.biosino.lf.pds.common.exception.PublisherValidationException;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.Date;
import java.util.List;

/**
 * 出版社服务实现类
 */
@Service
@RequiredArgsConstructor
public class PublisherServiceImpl extends ServiceImpl<PublisherMapper, Publisher> implements IPublisherService {

    private final JournalMapper journalMapper;
    private final PublisherValidationService publisherValidationService;
    private final TbDdsTaskPaperMapper tbDdsTaskPaperMapper;

    @Override
    public List<Publisher> selectPublisherList(PublisherQueryDTO queryDTO) {
        return this.baseMapper.selectPublisherList(queryDTO);
    }

    @Override
    public Publisher selectPublisherById(Long id) {
        return this.getById(id);
    }

    @Override
    public boolean updatePublisher(Publisher publisher) {
        // 验证出版社名称和别名唯一性
        PublisherValidationResult validationResult = publisherValidationService.validatePublisherUpdate(publisher);
        if (!validationResult.isValid()) {
            throw new PublisherValidationException(validationResult);
        }

        Publisher existPublisher = this.getOptById(publisher.getId()).orElseThrow(() -> new ServiceException("出版社不存在"));

        BeanUtil.copyProperties(publisher, existPublisher);
        existPublisher.setUpdateTime(new Date());

        return this.updateById(existPublisher);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean mergePublishers(PublisherMergeDTO mergeDTO) {

        if (CollUtil.isEmpty(mergeDTO.getSourceIds()) || mergeDTO.getTargetId() == null) {
            throw new ServiceException("未选择需要合并的出版社");
        }

        // 验证出版社名称和别名唯一性
        PublisherValidationResult validationResult = publisherValidationService.validatePublisherMerge(mergeDTO);
        if (!validationResult.isValid()) {
            throw new PublisherValidationException(validationResult);
        }

        // 检查目标出版社是否存在
        Publisher targetPublisher = this.getOptById(mergeDTO.getTargetId()).orElseThrow(() -> new ServiceException("目标出版社不存在"));

        // 检查源出版社是否存在
        List<Publisher> sourcePublishers = this.listByIds(mergeDTO.getSourceIds());

        if (CollUtil.isEmpty(sourcePublishers)) {
            throw new ServiceException("部分源出版社不存在");
        }

        // 更新目标出版社信息
        BeanUtil.copyProperties(mergeDTO, targetPublisher);

        // 修改对应期刊的出版社id
        journalMapper.updatePublisherIdBatch(mergeDTO.getTargetId(), mergeDTO.getSourceIds());

        // 删除源出版社
        this.removeByIds(mergeDTO.getSourceIds());

        targetPublisher.setUpdateTime(new Date());
        return this.updateById(targetPublisher);
    }

    @Override
    public boolean updatePublisherStatus(Long id, Integer status) {
        return this.update(Wrappers.<Publisher>lambdaUpdate().eq(Publisher::getId, id).set(Publisher::getStatus, status));
    }

    @Override
    public List<ErrorMsgVO> validateAlias(List<String> alias, List<Long> excludeIds) {
        List<ErrorMsgVO> result = new ArrayList<>();

        if (CollUtil.isEmpty(alias)) {
            return result;
        }

        // 查询所有出版社的别名数据
        LambdaQueryWrapper<Publisher> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Publisher::getId, Publisher::getName, Publisher::getAlias);

        // 排除指定的ID
        if (CollUtil.isNotEmpty(excludeIds)) {
            queryWrapper.notIn(Publisher::getId, excludeIds);
        }

        List<Publisher> existingPublishers = this.list(queryWrapper);

        // 构建别名到出版社的映射，提高查找效率
        Map<String, String> aliasToPublisherMap = new HashMap<>();
        for (Publisher publisher : existingPublishers) {
            if (CollUtil.isNotEmpty(publisher.getAlias())) {
                for (String existingAlias : publisher.getAlias()) {
                    aliasToPublisherMap.put(existingAlias, publisher.getName());
                }
            }
            // 出版社名称也不能作为别名
            if (StrUtil.isNotBlank(publisher.getName())) {
                aliasToPublisherMap.put(publisher.getName(), publisher.getName());
            }
        }

        // 校验每个别名
        for (int i = 0; i < alias.size(); i++) {
            String aliasItem = alias.get(i);
            if (StrUtil.isBlank(aliasItem)) {
                continue;
            }

            // 检查是否与现有出版社的别名或名称重复
            String conflictPublisher = aliasToPublisherMap.get(aliasItem);
            if (StrUtil.isNotBlank(conflictPublisher)) {
                result.add(ErrorMsgVO.errMsg(i, "别名", aliasItem,
                    StrUtil.format("别名已存在于出版社：{}", conflictPublisher)));
            }
        }

        return result;
    }
}
