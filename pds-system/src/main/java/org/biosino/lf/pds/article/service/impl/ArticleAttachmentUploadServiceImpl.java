package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.ArticleAttachmentUpload;
import org.biosino.lf.pds.article.dto.ArticleAttachmentUploadQueryDTO;
import org.biosino.lf.pds.article.mapper.ArticleAttachmentUploadMapper;
import org.biosino.lf.pds.article.service.IArticleAttachmentUploadService;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 文章附件上传信息服务实现类
 */
@Service
@RequiredArgsConstructor
public class ArticleAttachmentUploadServiceImpl extends ServiceImpl<ArticleAttachmentUploadMapper, ArticleAttachmentUpload> implements IArticleAttachmentUploadService {

    @Override
    public List<ArticleAttachmentUpload> selectUploadList(ArticleAttachmentUploadQueryDTO queryDTO) {
        return this.baseMapper.selectUploadList(queryDTO);
    }


    @Override
    public void accept(Long id) {
        ArticleAttachmentUpload item = this.getOptById(id).orElseThrow(() -> new ServiceException("记录不存在"));
        item.setStatus(1);
        item.setAuditor(SecurityUtils.getUserId());
        item.setAuditTime(new Date());
        this.updateById(item);
    }

    @Override
    public void reject(Long id, String reason) {
        ArticleAttachmentUpload item = this.getOptById(id).orElseThrow(() -> new ServiceException("记录不存在"));
        item.setStatus(-1);
        item.setAuditor(SecurityUtils.getUserId());
        item.setAuditTime(new Date());
        item.setReason(reason);
        this.updateById(item);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByDocId(Long id) {
        LambdaQueryWrapper<ArticleAttachmentUpload> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ArticleAttachmentUpload::getDocId, id);
        this.remove(queryWrapper);
    }

    @Override
    public int updateDocIdBatch(Long targetDocId, Long sourceDocId) {
        return baseMapper.updateDocIdBatch(targetDocId, sourceDocId);
    }
}
