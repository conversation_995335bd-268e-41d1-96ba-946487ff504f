package org.biosino.lf.plosp.web.controller.article;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.PlospUser;
import org.biosino.lf.pds.article.dto.PlospUserQueryDTO;
import org.biosino.lf.pds.article.service.IPlospUserService;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 前台用户管理Controller
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/plospUser")
public class PlospUserController extends BaseController {

    private final IPlospUserService userService;

    /**
     * 查询用户列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PlospUserQueryDTO queryDTO) {
        startPage();
        List<PlospUser> list = userService.selectUserList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 获取用户详细信息
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(userService.getById(id));
    }

    /**
     * 新增用户
     */
    @PostMapping
    public AjaxResult add(@RequestBody @Validated PlospUser user) {
        userService.addUser(user);
        return AjaxResult.success();
    }

    /**
     * 修改用户
     */
    @PutMapping
    public AjaxResult edit(@RequestBody @Validated PlospUser user) {
        userService.updateUser(user);
        return AjaxResult.success();
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(userService.deleteUserByIds(ids));
    }

    /**
     * 修改用户状态
     */
    @RequestMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestParam Long id, @RequestParam Integer status) {
        userService.changeStatus(id, status);
        return AjaxResult.success();
    }

    /**
     * 发送注册验证码
     */
    @PostMapping("/sendRegisterCode")
    public AjaxResult sendRegisterCode(@RequestParam String email) {
        userService.sendRegisterCode(email);
        return AjaxResult.success("验证码发送成功");
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public AjaxResult register(@RequestBody PlospUser user, @RequestParam String code) {
        userService.registerUser(user, code);
        return AjaxResult.success("注册成功");
    }

    /**
     * 重置用户密码
     */
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody PlospUser user) {
        userService.resetUserPwd(user.getUserId(), user.getPassword());
        return AjaxResult.success();
    }
}
