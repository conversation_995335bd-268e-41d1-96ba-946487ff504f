package org.biosino.lf.plosp.web.controller.article;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.ArticleAttachmentUpload;
import org.biosino.lf.pds.article.dto.ArticleAttachmentUploadQueryDTO;
import org.biosino.lf.pds.article.service.IArticleAttachmentUploadService;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @date 2025/6/20
 */
@RestController
@RequestMapping("/article/attachmentUpload")
@RequiredArgsConstructor
public class ArticleAttachmentUploadController extends BaseController {

    private final IArticleAttachmentUploadService attachmentUploadService;

    @GetMapping("/list")
    public TableDataInfo list(ArticleAttachmentUploadQueryDTO queryDTO) {
        startPage();
        List<ArticleAttachmentUpload> list = attachmentUploadService.selectUploadList(queryDTO);
        return getDataTable(list);
    }

    @RequestMapping("/accept")
    public AjaxResult accept(Long id) {
        attachmentUploadService.accept(id);
        return success();
    }

    @RequestMapping("/reject")
    public AjaxResult reject(Long id, String reason) {
        attachmentUploadService.reject(id, reason);
        return success();
    }

}
