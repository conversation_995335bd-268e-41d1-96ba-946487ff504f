package org.biosino.lf.plosp.web.controller.article;

import lombok.RequiredArgsConstructor;
import org.aspectj.weaver.loadtime.Aj;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.dto.JournalMergeDTO;
import org.biosino.lf.pds.article.dto.JournalQueryDTO;
import org.biosino.lf.pds.article.dto.JournalUpdateDTO;
import org.biosino.lf.pds.article.service.IJournalService;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/1
 */
@RestController
@RequestMapping("/journal")
@RequiredArgsConstructor
public class JournalController extends BaseController {

    private final IJournalService journalService;

    /**
     * 查询期刊列表
     */
    @GetMapping("/list")
    public TableDataInfo list(JournalQueryDTO queryDTO) {
        startPage();
        List<Journal> list = journalService.selectJournalList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 获取期刊详情
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return success(journalService.selectJournalById(id));
    }

    /**
     * 修改期刊
     */
    @PostMapping("/updateJournal")
    public AjaxResult updateJournal(@RequestBody JournalUpdateDTO journalUpdateDTO){
        Journal journal = journalService.updateJournal(journalUpdateDTO);
        return AjaxResult.success(journal);
    }

    /**
     * 合并期刊
     */
    @PostMapping("/merge")
    public AjaxResult merge(@RequestBody @Validated JournalMergeDTO journalMergeDTO){
        journalService.mergeJournals(journalMergeDTO);
        return AjaxResult.success();
    }

    /**
     * 修改期刊状态
     */
    @PostMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestParam Long id, @RequestParam Long status){
        journalService.updateJournalStatus(id, status);
        return AjaxResult.success();
    }

}
