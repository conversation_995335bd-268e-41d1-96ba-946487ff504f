package org.biosino.lf.plosp.web.controller.article;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.Article;
import org.biosino.lf.pds.article.domain.TbDdsFile;
import org.biosino.lf.pds.article.dto.ArticleImportDTO;
import org.biosino.lf.pds.article.dto.ArticleQueryDTO;
import org.biosino.lf.pds.article.dto.ArticleUpdateDTO;
import org.biosino.lf.pds.article.dto.ArticleUploadAttachmentDTO;
import org.biosino.lf.pds.article.service.IArticleService;
import org.biosino.lf.pds.article.vo.ErrorMsgVO;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.pds.common.utils.DownloadUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 文献管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/article")
@RequiredArgsConstructor
public class ArticleController extends BaseController {

    private final IArticleService articleService;

    /**
     * 获取文献列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ArticleQueryDTO queryDTO) {
        startPage();
        List<Article> list = articleService.selectArticleList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 获取文献详情
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return success(articleService.selectArticleById(id));
    }

    /**
     * 更新文献
     */
    @PostMapping("/updateArticle")
    public AjaxResult updateArticle(@RequestBody ArticleUpdateDTO articleUpdateDTO) {
        articleService.updateArticle(articleUpdateDTO);
        return success();
    }

    /**
     * 批量下载附件
     */
    @PostMapping("/downloadArticleAttachmentByIds")
    public void downloadArticleAttachmentByIds(@RequestParam("ids") List<Long> ids, HttpServletRequest request, HttpServletResponse response) throws IOException {
        File file = articleService.getAttachmentZipByIds(ids);
        DownloadUtils.download(request, response, file);
    }

    /**
     * 批量上传文件
     */
    @PostMapping("/batchUploadArticleAttachment")
    public AjaxResult batchUploadArticleAttachment(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return AjaxResult.error("文件不存在");
        }

        List<ErrorMsgVO> result = articleService.batchUploadArticleAttachment(file);
        return AjaxResult.success(result);

    }

    /**
     * 导入文献题录
     */
    @PostMapping("/importArticles")
    public AjaxResult importArticles(@RequestBody List<ArticleImportDTO> importList) {
        List<ErrorMsgVO> result = articleService.importArticles(importList);
        return AjaxResult.success(result);

    }

    /**
     * 上传文献附件
     */
    @PostMapping("/uploadAttachment")
    public AjaxResult uploadAttachment(ArticleUploadAttachmentDTO articleUploadAttachmentDTO) {
        List<TbDdsFile> result = articleService.uploadAttachment(articleUploadAttachmentDTO);
        return AjaxResult.success(result);
    }

    /**
     * 获取指定范围内的最大customId
     */
    @GetMapping("/getMaxCustomId")
    public AjaxResult getMaxCustomId(@RequestParam Long minValue, @RequestParam Long maxValue) {
        Long maxCustomId = articleService.getMaxCustomIdInRange(minValue, maxValue);
        return AjaxResult.success(maxCustomId.toString());
    }

}
