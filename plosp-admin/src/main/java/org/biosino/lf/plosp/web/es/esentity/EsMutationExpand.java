package org.biosino.lf.plosp.web.es.esentity;

import java.io.Serializable;

/**
 * 变异检索es索引
 *
 * <AUTHOR>
 * @date 2024/8/22
 */
//@Data
//@IndexName(EsMutationExpand.ES_INDEX_NAME)
//@Settings(maxResultWindow = 50 * DEFAULT_MAX_RESULT_WINDOW)
public class EsMutationExpand implements Serializable {
    public static final String ES_INDEX_NAME = "es_tbics_mutation_expand";

//    @IndexId(type = IdType.CUSTOMIZE)
//    private String id;
//
//    /**
//     * 雪花算法id，用于searchAfter排序
//     */
//    @IndexField(fieldType = FieldType.LONG, strategy = FieldStrategy.IGNORED)
//    private Long sid;
//
//    /**
//     * 生物分析ID或者自定义变异id
//     */
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
//    private String vcfId;
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
//    private String no;
//
//    /**
//     * 系统:1，自定义2
//     * 存储枚举code值
//     *
//     * @see org.lf.biosino.tb.common.enums.SnpEffType
//     */
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
//    private String type;
//
//
//    /**
//     * 生物分析id
//     */
//    @IndexField(fieldType = FieldType.LONG, strategy = FieldStrategy.IGNORED)
//    private Long anaId;
//
//    /**
//     * 自定义导入数据id
//     */
//    @IndexField(fieldType = FieldType.LONG, strategy = FieldStrategy.IGNORED)
//    private Long pmuId;
//
//    /**
//     * 批次ID
//     */
//    @IndexField(fieldType = FieldType.LONG, strategy = FieldStrategy.IGNORED)
//    private Long batchId;
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
//    private String batchIdStr;
//
//    /**
//     * 诊疗数据对象ID
//     */
//    @IndexField(fieldType = FieldType.LONG, strategy = FieldStrategy.IGNORED)
//    private Long treatId;
//
//    /**
//     * 确诊日期
//     */
//    @IndexField(fieldType = FieldType.DATE, strategy = FieldStrategy.IGNORED)
//    private Date diagnoseDate;
//
//    /**
//     * 病原学ID
//     */
//    @IndexField(fieldType = FieldType.LONG, strategy = FieldStrategy.IGNORED)
//    private Long etiologyId;
//
//    /**
//     * 创建者
//     */
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
//    private String createBy;
//
//    /**
//     * 创建时间
//     */
//    @IndexField(fieldType = FieldType.DATE, strategy = FieldStrategy.IGNORED)
//    private Date createTime;
//
//    /**
//     * 更新者
//     */
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
//    private String updateBy;
//
//    /**
//     * 更新时间
//     */
//    @IndexField(fieldType = FieldType.DATE, strategy = FieldStrategy.IGNORED)
//    private Date updateTime;
//
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
//    private String gene;
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
//    private String mutation;
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
//    private String geneMutation;
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
//    private String geneAmino;
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = false)
//    private String genome;
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
//    private String genomePosition;
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = false)
//    private String ref;
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = false)
//    private String alt;
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = false)
//    private String aminoAcidChange;
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
//    private String coverage;
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
//    private String frs;
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED)
//    private String variantDepth;
//
//    // 变异清单字典数据
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = false)
//    private String variNo;
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
//    private String dictType;
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
//    private String drug;
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = false)
//    private String originalMutation;
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = true)
//    private String confidence;
//
//    @IndexField(fieldType = FieldType.KEYWORD, strategy = FieldStrategy.IGNORED, ignoreCase = false)
//    private String source;
//
//    // 来源名称
//    @IndexField(exist = false)
//    private String sourceName;
//    @IndexField(exist = false)
//    private String diagnoseDateStr;
//    @IndexField(exist = false)
//    private String file1Name;
//    @IndexField(exist = false)
//    private String file2Name;
//    @IndexField(exist = false)
//    private String comment;
//    /**
//     * 批次名称，es只存批次id，否则批次名称改变时同步数据量太大
//     */
//    @IndexField(exist = false)
//    private String batchName;

}
