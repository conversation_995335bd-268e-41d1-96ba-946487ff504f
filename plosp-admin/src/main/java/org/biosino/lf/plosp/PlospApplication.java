package org.biosino.lf.plosp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"org.biosino.lf.pds.**", "org.biosino.lf.plosp.**"})
public class PlospApplication {
    public static void main(String[] args) {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(PlospApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  PLOSP启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
