package org.biosino.lf.plosp.web.controller.article;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.PlospUserPointsRecord;
import org.biosino.lf.pds.article.dto.PlospUserPointsChangeDTO;
import org.biosino.lf.pds.article.dto.PlospUserPointsRecordQueryDTO;
import org.biosino.lf.pds.article.service.IPlospUserPointsRecordService;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户积分管理Controller
 */
@RestController
@RequestMapping("/system/userPoints")
@RequiredArgsConstructor
public class PlospUserPointsController extends BaseController {

    private final IPlospUserPointsRecordService userPointsRecordService;

    /**
     * 查询用户积分记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PlospUserPointsRecordQueryDTO queryDTO) {
        startPage();
        List<PlospUserPointsRecord> list = userPointsRecordService.selectPointsRecordList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 修改用户积分
     */
    @RequestMapping("/change")
    public AjaxResult changePoints(@Validated @RequestBody PlospUserPointsChangeDTO changeDTO) {
        userPointsRecordService.addPointsRecord(changeDTO);
        return AjaxResult.success();
    }
}
