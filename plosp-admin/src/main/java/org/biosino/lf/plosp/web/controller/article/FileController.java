package org.biosino.lf.plosp.web.controller.article;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.TbDdsFile;
import org.biosino.lf.pds.article.domain.TbDdsFileContent;
import org.biosino.lf.pds.article.service.ITbDdsFileService;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.enums.task.FileTypeEnum;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.DownloadUtils;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.rmi.ServerException;
import java.util.List;

import static org.biosino.lf.pds.article.service.impl.TbDdsFileServiceImpl.initDiskFile;

/**
 * <AUTHOR> Li
 * @date 2025/6/26
 */
@Slf4j
@RestController
@RequestMapping("/article/file")
@RequiredArgsConstructor
public class FileController {

    private final ITbDdsFileService fileService;

    @RequestMapping("/download/{id}")
    public void download(@PathVariable Long id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        TbDdsFile ddsFile = fileService.getOptById(id).orElseThrow(() -> new ServiceException("未找到文件记录"));
        downloadTbDdsFile(ddsFile, request, response);
    }

    private void downloadTbDdsFile(TbDdsFile ddsFile, HttpServletRequest request, HttpServletResponse response) throws IOException {
        if (ddsFile == null) {
            throw new ServiceException("未找到文件记录");
        }
        if (StrUtil.isNotBlank(ddsFile.getFilePath())) {
            final File destFile = initDiskFile(ddsFile.getFilePath());
            if (FileUtil.exist(destFile)) {
                DownloadUtils.download(request, response, destFile, ddsFile.getFileName());
            } else {
                throw new ServerException("文件未找到");
            }
        } else {
            TbDdsFileContent fileContent = fileService.findContentById(ddsFile.getId());
            if (fileContent != null && fileContent.getFileData() != null) {
                DownloadUtils.download(request, response, fileContent.getFileData(), ddsFile.getFileName());
            } else {
                throw new ServerException("文件未找到");
            }
        }
    }

    /**
     * 删除文献附件
     */
    @DeleteMapping("/deleteAttachment/{fileId}")
    public AjaxResult deleteAttachment(@PathVariable Long fileId) {
        // 检查文件是否存在
        // 删除文件
        if (!fileService.delById(fileId)) {
            throw new ServiceException("文件不存在");
        }
        return AjaxResult.success();
    }

    /**
     * download_pdf
     */
    @RequestMapping("/downloadPdf")
    public void downloadPdf(@RequestParam Long id, HttpServletRequest request, HttpServletResponse response) throws IOException {
        final List<TbDdsFile> pdfList = fileService.findByDocIdAndType(id, FileTypeEnum.PDF.name());
        if (CollUtil.isNotEmpty(pdfList)) {
            final TbDdsFile ddsFile = pdfList.get(0);
            downloadTbDdsFile(ddsFile, request, response);
        }
    }
}
