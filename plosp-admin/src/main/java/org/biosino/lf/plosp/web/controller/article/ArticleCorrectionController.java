package org.biosino.lf.plosp.web.controller.article;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.domain.ArticleCorrection;
import org.biosino.lf.pds.article.dto.ArticleCorrectionQueryDTO;
import org.biosino.lf.pds.article.service.IArticleCorrectionService;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 文献纠错信息控制器
 */
@RestController
@RequestMapping("/article/correction")
@RequiredArgsConstructor
public class ArticleCorrectionController extends BaseController {

    private final IArticleCorrectionService defectService;

    /**
     * 查询纠错列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ArticleCorrectionQueryDTO queryDTO) {
        startPage();
        List<ArticleCorrection> list = defectService.selectDefectList(queryDTO);
        return getDataTable(list);
    }

    /**
     * 接受纠错
     */
    @RequestMapping("/accept")
    public AjaxResult accept(Long id) {
        defectService.accept(id);
        return success();
    }

    /**
     * 驳回纠错
     */
    @RequestMapping("/reject")
    public AjaxResult reject(Long id, String reason) {
        defectService.reject(id, reason);
        return success();
    }
}
