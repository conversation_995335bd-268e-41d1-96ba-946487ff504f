<h1 align="center" style="margin: 30px 0 30px; font-weight: bold;">PDS文献传递系统</h1>

## 简介
PDS文献传递系统前端。

* Node.js版本 **20.18.3**+，
  使用[nvm windows](https://github.com/coreybutler/nvm-windows)安装和管理多个node版本
```
在终端中运行以下命令（需管理员权限），设置国内镜像源
nvm node_mirror https://npmmirror.com/mirrors/node/
nvm npm_mirror https://npmmirror.com/mirrors/npm/

查看可安装版本
nvm ls available

查看已安装版本
nvm ls

安装指定版本
nvm install 20.18.3

切换到指定版本
nvm use 20.18.3
```
* 本仓库为前端技术栈 [Vue3](https://v3.cn.vuejs.org) + [Element Plus](https://element-plus.org/zh-CN) + [Vite](https://cn.vitejs.dev)

## 前端运行

```
# 进入项目目录
# 安装依赖
yarn --registry=https://registry.npmmirror.com

# 启动服务
yarn dev

# 构建测试环境 yarn build:stage
# 构建生产环境 yarn build:prod
# 前端访问地址 http://localhost:80
```

## 测试账号

- admin/admin123  
