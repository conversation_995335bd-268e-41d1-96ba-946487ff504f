{"name": "pds-admin", "version": "2.0.0", "description": "PDS文献传递系统", "author": "pds", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview", "lint": "eslint --ext .vue,.js,.ts --fix src"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "13.3.0", "axios": "1.9.0", "clipboard": "2.0.11", "echarts": "5.6.0", "element-plus": "2.9.5", "file-saver": "2.0.5", "fuse.js": "6.6.2", "handsontable": "^15.3.0", "hyperformula": "^3.0.0", "js-beautify": "1.14.11", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "lodash": "^4.17.21", "nprogress": "0.2.0", "pinia": "2.3.1", "splitpanes": "3.1.8", "vue": "3.5.13", "vue-cropper": "1.1.1", "vue-router": "4.5.0", "vuedraggable": "4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.21.0", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-vue": "5.2.1", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^9.32.0", "globals": "^16.0.0", "prettier": "^3.5.2", "sass": "1.77.5", "terser": "^5.39.0", "unplugin-auto-import": "0.19.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.4.19", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}