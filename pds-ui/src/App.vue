<template>
  <router-view />
</template>

<script setup>
  import useSettingsStore from '@/store/modules/settings';
  import { handleThemeStyle } from '@/utils/theme';

  onMounted(() => {
    nextTick(() => {
      // 初始化主题样式
      handleThemeStyle(useSettingsStore().theme);
    });
  });
</script>
<style lang="scss">
  .warning-icon {
    margin-top: 7px;
    margin-left: 5px;
    color: #e6a23c;
    cursor: pointer;
    font-size: 16px;
  }
</style>
