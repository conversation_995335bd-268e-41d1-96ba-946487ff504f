<template>
  <div class="app-container">
    <div class="monitor-content">
      <!-- 左侧节点列表 -->
      <div class="node-list">
        <!-- 左侧搜索框 -->
        <div class="list-search">
          <el-input
            v-model="searchKeyword"
            placeholder="节点名称"
            prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="node-items-container">
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="5" animated />
          </div>
          <div v-else-if="filteredNodes.length === 0" class="empty-container">
            <el-empty description="暂无节点数据" />
          </div>
          <div
            v-for="node in filteredNodes"
            v-else
            :key="node.id"
            class="node-item"
            :class="{
              'is-active': currentNode && node.id === currentNode.id,
            }"
            @click="selectNode(node)"
          >
            <div style="display: flex; justify-content: space-between">
              <div class="node-name">
                {{ node.siteName }}
              </div>
              <!--<div style="margin-left: 6px">
                <dict-tag
                  :options="sys_normal_disable"
                  :value="currentNode.status"
                />
              </div>-->
              <div>
                <el-tag type="info" size="small" round effect="plain">
                  ID: {{ node.id }}
                </el-tag>
              </div>
            </div>

            <!--<div class="node-group">{{ node.siteGroup }}</div>-->
            <!--<div class="node-group">{{ node.labelName }}</div>-->
            <div class="node-bottom-info">
              <div class="node-type">
                <div>
                  <dict-tag :options="script_type" :value="node.siteType" />
                </div>
              </div>
              <div class="node-status">
                <span
                  class="status-dot"
                  :class="getStatusClass(node.heartbeatSignal)"
                ></span>
                <span class="status-text">{{ node.heartbeatSignal }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 导出按钮 -->
        <div class="export-section">
          <el-button
            type="primary"
            icon="Download"
            :loading="exportLoading"
            @click="handleExport"
          >
            导出所有节点数据
          </el-button>
        </div>
      </div>

      <!-- 右侧详细信息 -->
      <div v-if="currentNode" v-loading="nodeDetailLoading" class="node-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <div class="section-title">
            <span>基本信息</span>
            <el-button
              type="primary"
              size="small"
              icon="Document"
              @click="handleViewLogs(currentNode)"
            >
              查看日志
            </el-button>
          </div>
          <div class="basic-info">
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">节点名称</div>
                <div class="info-value node-title">
                  {{ currentNode.siteName }}
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">脚本标签</div>
                <div class="info-value">
                  {{ currentNode.labelName }}
                </div>
              </div>
              <!--<div class="info-item">
                <div class="info-label">分组</div>
                <div class="info-value node-group">
                  {{ currentNode.siteGroup }}
                </div>
              </div>-->
              <div class="info-item">
                <div class="info-label">节点类型</div>
                <div class="info-value">
                  <dict-tag
                    :options="script_type"
                    :value="currentNode.siteType"
                  />
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">节点状态</div>
                <div class="info-value">
                  <dict-tag
                    :options="sys_normal_disable"
                    :value="currentNode.status"
                  />
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">IP</div>
                <div class="info-value">{{ currentNode.ip }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">最近联系时间</div>
                <div class="info-value">
                  {{ formatDate(currentNode.lastHandshakeTime) }}
                  <span
                    class="status-badge"
                    :class="getStatusClass(currentNode.heartbeatSignal)"
                  >
                    {{ currentNode.heartbeatSignal }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 资源消耗 -->
        <div class="detail-section">
          <div class="section-title">资源消耗</div>
          <div class="resource-info">
            <div class="resource-item">
              <el-progress
                type="dashboard"
                :percentage="cpuInfo.usage"
                :color="getResourceColor(cpuInfo.usage)"
                :stroke-width="10"
              />
              <div class="resource-detail">
                <div class="usage-info">
                  <span class="usage-label">已用</span>
                  <span class="usage-value"
                    >{{ cpuInfo.used }} / {{ cpuInfo.total }} core</span
                  >
                </div>
              </div>
              <div class="resource-name">CPU 使用率</div>
            </div>

            <div class="resource-item">
              <el-progress
                type="dashboard"
                :percentage="memoryInfo.usage"
                :color="getResourceColor(memoryInfo.usage)"
                :stroke-width="10"
              />
              <div class="resource-detail">
                <div class="usage-info">
                  <span class="usage-label">已用</span>
                  <span class="usage-value"
                    >{{ memoryInfo.used }} / {{ memoryInfo.total }} GB</span
                  >
                </div>
              </div>
              <div class="resource-name">内存使用率</div>
            </div>

            <div class="resource-item">
              <el-progress
                type="dashboard"
                :percentage="diskInfo.usage"
                :color="getResourceColor(diskInfo.usage)"
                :stroke-width="10"
              />
              <div class="resource-detail">
                <div class="usage-info">
                  <span class="usage-label">已用</span>
                  <span class="usage-value"
                    >{{ diskInfo.used }} / {{ diskInfo.total }} GB</span
                  >
                </div>
              </div>
              <div class="resource-name">磁盘使用率</div>
            </div>
          </div>
        </div>

        <!-- 监控指标 -->
        <div class="detail-section" style="height: 34vh">
          <div class="section-title">
            <span>监控指标</span>
            <!--<el-select v-model="timeRange" class="time-range-selector" size="small">
              <el-option label="最近 1 小时" value="1" />
              <el-option label="最近 6 小时" value="6" />
              <el-option label="最近 12 小时" value="12" />
              <el-option label="最近 24 小时" value="24" />
            </el-select>-->
          </div>
          <div class="metrics-chart">
            <div class="metrics-header">
              <div class="metric-item" style="color: #e6a23c">
                <div class="metric-value">
                  {{ currentNode.total || 0 }}
                </div>
                <div class="metric-name">总数</div>
              </div>
              <div class="metric-item" style="color: #67c23a">
                <div class="metric-value">
                  {{ currentNode.success || 0 }}
                </div>
                <div class="metric-name">完成数</div>
              </div>
              <div class="metric-item" style="color: #f56c6c">
                <div class="metric-value">
                  {{ currentNode.failed || 0 }}
                </div>
                <div class="metric-name">失败数</div>
              </div>
              <div class="metric-item" style="color: #409eff">
                <div class="metric-value">
                  {{ currentNode.execute || 0 }}
                </div>
                <div class="metric-name">执行中</div>
              </div>
            </div>
            <!--<div ref="chartRef" class="chart-container"></div>-->
          </div>
        </div>
      </div>

      <!-- 未选择节点时的提示 -->
      <div v-else class="node-detail empty-detail">
        <el-empty description="请选择一个节点查看详细信息" />
      </div>
    </div>

    <!-- 站点日志弹窗 -->
    <el-dialog
      v-model="logDialogVisible"
      title="站点日志"
      width="800px"
      append-to-body
    >
      <div class="log-dialog-header">
        <div class="log-info">
          <span class="log-site-name">{{ currentLogSite?.siteName }}</span>
          <span class="log-site-id">ID: {{ currentLogSite?.id }}</span>
        </div>
        <el-button
          type="primary"
          icon="Refresh"
          :disabled="
            currentLogSite?.heartbeatSignal === '断开' ||
            currentLogSite?.toRefreshLog === 1
          "
          :title="initRefreshLogBtnTitle(currentLogSite)"
          :loading="refreshLogLoading"
          @click="handleRefreshLog"
        >
          {{
            currentLogSite?.toRefreshLog === 1 ? '日志刷新中...' : '刷新日志'
          }}
        </el-button>
      </div>

      <el-table
        v-loading="logTableLoading"
        :data="logList"
        style="width: 100%"
        empty-text="暂无日志数据，请点击 [刷新日志] 获取"
      >
        <el-table-column
          prop="currNum"
          label="序号"
          width="80"
          align="center"
        />
        <el-table-column prop="createTime" label="上传时间" align="center">
          <template #default="scope">
            {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="fileSizeStr"
          label="文件大小"
          width="120"
          align="center"
        />
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="Download"
              @click="handleDownloadLog(scope.row)"
            >
              下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="logDialogVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="NodeMonitor">
  import { computed, getCurrentInstance, onMounted, ref, watch } from 'vue';
  import {
    getNodeMonitorInfo,
    getSiteLogList,
    listSite,
    refreshSiteLog,
  } from '@/api/task/site';
  import { formatDate } from '@/utils/index.js';
  import { parseTime } from '@/utils/ruoyi.js';

  const { proxy } = getCurrentInstance();
  const { sys_normal_disable, script_type } = proxy.useDict(
    'sys_normal_disable',
    'script_type',
  );

  const searchKeyword = ref('');
  const currentNode = ref(null);
  const timeRange = ref('1');
  let chart = null;

  // 记录总节点数
  const totalNodes = ref(0);

  // 加载状态
  const loading = ref(false);
  const nodeDetailLoading = ref(false);
  const exportLoading = ref(false);

  // 节点数据
  const nodeList = ref([]);

  // 获取过滤后的节点列表
  const filteredNodes = ref([]);

  // 日志弹窗相关
  const logDialogVisible = ref(false);
  const currentLogSite = ref(null);
  const logList = ref([]);
  const logTableLoading = ref(false);
  const refreshLogLoading = ref(false);

  // 计算属性：CPU使用率和信息
  const cpuInfo = computed(() => {
    if (!currentNode.value?.systemInfoDTO?.cpu) {
      return { usage: 0, used: '0', total: '0' };
    }
    const cpu = currentNode.value.systemInfoDTO.cpu;
    return {
      usage:
        Math.round(
          ((cpu.logicalCount - (cpu.physicalCount || 0)) / cpu.logicalCount) *
            100,
        ) || 0,
      used: cpu.physicalCount?.toString() || '0',
      total: cpu.logicalCount?.toString() || '0',
    };
  });

  // 计算属性：内存使用率和信息
  const memoryInfo = computed(() => {
    if (!currentNode.value?.systemInfoDTO?.memory) {
      return { usage: 0, used: '0', total: '0' };
    }
    const memory = currentNode.value.systemInfoDTO.memory;
    return {
      usage: Math.round(memory.percent || 0),
      used: ((memory.used || 0) / (1024 * 1024 * 1024)).toFixed(1),
      total: ((memory.total || 0) / (1024 * 1024 * 1024)).toFixed(1),
    };
  });

  // 计算属性：磁盘使用率和信息
  const diskInfo = computed(() => {
    if (!currentNode.value?.systemInfoDTO?.disk) {
      return { usage: 0, used: '0', total: '0' };
    }
    const disk = currentNode.value.systemInfoDTO.disk;
    return {
      usage: Math.round(disk.percent || 0),
      used: ((disk.used || 0) / (1024 * 1024 * 1024)).toFixed(1),
      total: ((disk.total || 0) / (1024 * 1024 * 1024)).toFixed(1),
    };
  });

  // 获取节点列表
  const getNodeList = async () => {
    loading.value = true;
    try {
      const response = await listSite({ findHandshakeFlag: true });
      if (response.code === 200) {
        nodeList.value = response.rows || [];
        handleSearch(); // 处理搜索和过滤
      } else {
        console.error('获取节点列表失败:', response.msg);
        nodeList.value = [];
      }
    } catch (error) {
      console.error('获取节点列表异常:', error);
      nodeList.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 获取节点监控详情
  const getNodeDetail = async nodeId => {
    if (!nodeId) return;

    nodeDetailLoading.value = true;
    try {
      const response = await getNodeMonitorInfo(nodeId);
      if (response.code === 200) {
        // 更新当前节点的详细信息
        const nodeDetail = response.data;
        if (currentNode.value && currentNode.value.id === nodeId) {
          // 合并基本信息和详细信息
          currentNode.value = { ...currentNode.value, ...nodeDetail };
        }
      } else {
        console.error('获取节点详情失败:', response.msg);
      }
    } catch (error) {
      console.error('获取节点详情异常:', error);
    } finally {
      nodeDetailLoading.value = false;
    }
  };

  // 处理搜索和分组过滤
  const handleSearch = () => {
    let result = [...nodeList.value];

    // 根据关键字过滤
    if (searchKeyword.value) {
      result = result.filter(node =>
        node.siteName.toLowerCase().includes(searchKeyword.value.toLowerCase()),
      );
    }

    // 更新节点总数
    totalNodes.value = result.length;

    // 显示所有过滤后的节点
    filteredNodes.value = result;

    // 如果当前选中的节点不在过滤结果中，取消选中
    if (
      currentNode.value &&
      !result.some(node => node.id === currentNode.value.id)
    ) {
      currentNode.value = null;
    }

    // 如果未选中节点但有过滤结果，自动选择第一个
    if (!currentNode.value && filteredNodes.value.length > 0) {
      selectNode(filteredNodes.value[0]);
    }
  };

  // 选择节点
  const selectNode = async node => {
    currentNode.value = node;
    // 获取节点详细监控信息
    await getNodeDetail(node.id);
    // initChart();
  };

  // 导出Excel
  const handleExport = () => {
    exportLoading.value = true;
    proxy
      .download('/site/exportNodeMonitor', {}, '节点监控数据.xlsx')
      .finally(() => {
        exportLoading.value = false;
      });
  };

  // 获取状态类名
  const getStatusClass = status => {
    switch (status) {
      case '连接':
        return 'status-running';
      case '断开':
        return 'status-disconnected';
      case '异常':
        return 'status-error';
      default:
        return 'status-unknown';
    }
  };

  // 获取资源使用率颜色
  const getResourceColor = percentage => {
    if (percentage < 60) return '#67C23A';
    if (percentage < 80) return '#E6A23C';
    return '#F56C6C';
  };

  /** 查看日志操作 */
  function handleViewLogs(row) {
    currentLogSite.value = row;
    logDialogVisible.value = true;
    loadSiteLogList(row.id);
  }

  /** 加载站点日志列表 */
  function loadSiteLogList(siteId) {
    logTableLoading.value = true;
    getSiteLogList(siteId)
      .then(response => {
        if (response.code === 200 && response.data) {
          const dataArr = response.data.logList || [];
          currentLogSite.value.toRefreshLog = response.data.toRefreshLog;

          if (dataArr && dataArr.length > 0) {
            for (let i = 0; i < dataArr.length; i++) {
              dataArr[i]['currNum'] = i + 1;
            }
          }
          logList.value = dataArr;
        } else {
          proxy.$modal.msgError(response.msg || '获取日志列表失败');
          logList.value = [];
        }
      })
      .catch(() => {
        proxy.$modal.msgError('获取日志列表失败');
        logList.value = [];
      })
      .finally(() => {
        logTableLoading.value = false;
      });
  }

  /** 下载日志文件 */
  function handleDownloadLog(row) {
    const fileName = `site_${currentLogSite.value.id}_${parseTime(row.createTime, '{y}{m}{d}_{h}{i}{s}')}.log`;
    proxy.download(`/site/downloadLog/${row.id}`, {}, fileName);
    proxy.$modal.msgSuccess(`正在下载日志文件: ${fileName}`);
  }

  function initRefreshLogBtnTitle(item) {
    if (item?.heartbeatSignal === '断开') {
      return '节点已断开';
    } else if (item?.toRefreshLog === 1) {
      return '日志刷新中...';
    } else {
      return '';
    }
  }

  /** 刷新日志操作 */
  function handleRefreshLog() {
    if (!currentLogSite.value) return;

    refreshLogLoading.value = true;
    refreshSiteLog(currentLogSite.value.id)
      .then(response => {
        if (response.code === 200) {
          proxy.$modal.msgSuccess('日志刷新请求已发送，请稍后查看');
          // 更新当前节点的刷新状态
          currentLogSite.value.toRefreshLog = 1;
          // 同时更新节点列表中的状态
          const nodeIndex = nodeList.value.findIndex(
            node => node.id === currentLogSite.value.id,
          );
          if (nodeIndex !== -1) {
            nodeList.value[nodeIndex].toRefreshLog = 1;
          }
        } else {
          proxy.$modal.msgError(response.msg || '刷新日志失败');
        }
      })
      .catch(() => {
        proxy.$modal.msgError('刷新日志失败');
      })
      .finally(() => {
        refreshLogLoading.value = false;
      });
  }

  /** 格式化文件大小 */
  function formatFileSize(bytes) {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 图表引用
  const chartRef = ref(null);

  // 初始化图表
  /*const initChart = () => {
    if (!currentNode.value) return;

    // 确保DOM已渲染
    nextTick(() => {
      if (!chartRef.value) return;

      // 如果已存在图表实例，销毁它
      if (chart) {
        chart.dispose();
      }

      // 创建新图表
      chart = echarts.init(chartRef.value);

      // 模拟时间数据
      const hours = [];
      const now = new Date();
      for (let i = 24; i > 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 60 * 1000);
        hours.push(`${String(time.getHours()).padStart(2, '0')}:00`);
      }

      // 模拟任务数据
      const generateRandomData = baseValue => {
        return hours.map(
          () => Math.floor(Math.random() * baseValue * 0.5) + baseValue * 0.5,
        );
      };

      const totalData = generateRandomData(currentNode.value.totalTasks);
      const completedData = generateRandomData(
        currentNode.value.completedTasks,
      );
      const failedData = generateRandomData(currentNode.value.failedTasks);
      const runningData = generateRandomData(currentNode.value.runningTasks);

      // 设置图表选项
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          data: ['总数', '完成数', '失败数', '执行中'],
          bottom: 0,
          icon: 'rect',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 12,
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: hours,
          axisLabel: {
            fontSize: 10,
            interval: 2,
          },
          axisLine: {
            lineStyle: {
              color: '#ddd',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
            },
          },
        },
        series: [
          {
            name: '总数',
            type: 'line',
            smooth: true,
            data: totalData,
            itemStyle: {
              color: '#F56C6C',
            },
            lineStyle: {
              width: 2,
            },
            symbol: 'circle',
            symbolSize: 6,
          },
          {
            name: '完成数',
            type: 'line',
            smooth: true,
            data: completedData,
            itemStyle: {
              color: '#67C23A',
            },
            lineStyle: {
              width: 2,
            },
            symbol: 'circle',
            symbolSize: 6,
          },
          {
            name: '失败数',
            type: 'line',
            smooth: true,
            data: failedData,
            itemStyle: {
              color: '#E6A23C',
            },
            lineStyle: {
              width: 2,
            },
            symbol: 'circle',
            symbolSize: 6,
          },
          {
            name: '执行中',
            type: 'line',
            smooth: true,
            data: runningData,
            itemStyle: {
              color: '#409EFF',
            },
            lineStyle: {
              width: 2,
            },
            symbol: 'circle',
            symbolSize: 6,
          },
        ],
      };

      // 设置图表
      chart.setOption(option);

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        chart.resize();
      });
    });
  };*/

  // 监听搜索条件变化
  watch(searchKeyword, () => {
    handleSearch();
  });

  // 监听时间范围变化
  /*  watch(timeRange, () => {
    initChart();
  });*/

  // 页面加载完成
  onMounted(() => {
    // 初始化加载节点列表数据
    getNodeList();
  });
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 0;
    background-color: #f5f7fa;
  }

  .monitor-content {
    display: flex;
    height: calc(100vh - 50px);

    .node-list {
      width: 300px;
      background-color: #fff;
      border-right: 1px solid #eee;
      display: flex;
      flex-direction: column;
      height: 100%;

      .list-search {
        padding: 16px;
        border-bottom: 1px solid #eee;
        flex-shrink: 0;
      }

      .node-items-container {
        flex: 1;
        overflow-y: auto;

        .node-item {
          padding: 16px;
          border-bottom: 1px solid #f0f0f0;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            background-color: #f5f7fa;
          }

          &.is-active {
            background-color: #ecf5ff;
            border-left: 3px solid #409eff;
          }

          .node-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
            color: #303133;
          }

          .node-group {
            font-size: 12px;
            color: #409eff;
            margin-bottom: 8px;
          }

          .node-bottom-info {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .node-type {
              .type-badge {
                padding: 4px 10px;
                border-radius: 12px;
                font-size: 13px;
                color: #fff;

                &.node-type-1 {
                  background-color: #409eff;
                }

                &.node-type-2 {
                  background-color: #67c23a;
                }

                &.node-type-3 {
                  background-color: #e6a23c;
                }
              }
            }

            .node-status {
              display: flex;
              align-items: center;
              font-size: 12px;
              color: #909399;
              margin-left: 8px;

              .status-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-right: 6px;
              }

              .status-running {
                background-color: #67c23a;
              }

              .status-disconnected {
                background-color: #f56c6c;
              }

              .status-error {
                background-color: #e6a23c;
              }

              .status-unknown {
                background-color: #909399;
              }
            }
          }
        }
      }
    }

    .node-detail {
      flex: 1;
      padding: 16px;
      overflow-y: auto;

      &.empty-detail {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .detail-section {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        margin-bottom: 16px;
        overflow: hidden;

        .section-title {
          padding: 12px 16px;
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          border-bottom: 1px solid #f0f0f0;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .time-range-selector {
            width: 120px;
          }
        }

        .basic-info {
          padding: 16px;

          .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;

            .info-item {
              .info-label {
                font-size: 16px;
                color: #666;
                margin-bottom: 4px;
              }

              .info-value {
                font-size: 14px;
                color: #606266;

                &.node-title {
                  font-size: 16px;
                  font-weight: 500;
                  color: #f56c6c;
                }

                &.node-group {
                  font-size: 16px;
                  color: #409eff;
                }

                .status-badge {
                  display: inline-flex;
                  align-items: center;
                  padding: 4px 10px;
                  border-radius: 12px;
                  font-size: 13px;

                  &.status-running {
                    background-color: rgba(103, 194, 58, 0.1);
                    color: #67c23a;
                  }

                  &.status-disconnected {
                    background-color: rgba(245, 108, 108, 0.1);
                    color: #f56c6c;
                  }

                  &.status-error {
                    background-color: rgba(230, 162, 60, 0.1);
                    color: #e6a23c;
                  }

                  .status-dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    margin-right: 6px;
                  }
                }
              }
            }
          }
        }

        .resource-info {
          padding: 20px;
          display: flex;
          flex-wrap: wrap;

          .resource-item {
            flex: 1;
            min-width: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;

            .resource-detail {
              margin-top: 16px;
              text-align: center;

              .usage-info {
                margin-bottom: 4px;
                line-height: 1.2;

                .usage-label {
                  font-size: 12px;
                  color: #909399;
                  margin-right: 8px;
                }

                .usage-value {
                  font-size: 14px;
                  color: #606266;
                }
              }
            }

            .resource-name {
              margin-top: 10px;
              font-size: 14px;
              color: #606266;
            }
          }
        }

        .metrics-chart {
          padding: 0 16px 16px;
          height: calc(100% - 60px); /* 减去section-title的高度 */
          display: flex;
          align-items: center;

          .metrics-header {
            display: flex;
            width: 100%;

            .metric-item {
              flex: 1;
              text-align: center;

              .metric-value {
                font-size: 24px;
                font-weight: 600;
                margin-bottom: 4px;
              }

              .metric-name {
                font-size: 14px;
              }
            }
          }

          .chart-container {
            height: 280px;
            width: 100%;
          }
        }
      }
    }

    .loading-container,
    .empty-container {
      padding: 20px;
      text-align: center;
    }

    .export-section {
      padding: 16px;
      border-top: 1px solid #ebeef5;
      flex-shrink: 0;

      .el-button {
        width: 100%;
      }
    }
  }

  // 日志弹窗样式
  .log-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ebeef5;

    .log-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .log-site-name {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .log-site-id {
        font-size: 14px;
        color: #909399;
        background: #f5f7fa;
        padding: 2px 8px;
        border-radius: 4px;
      }
    }
  }
</style>
