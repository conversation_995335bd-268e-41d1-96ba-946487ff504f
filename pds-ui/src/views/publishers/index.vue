<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
      class="search-form"
    >
      <el-form-item label="出版社名称(批量)" prop="nameMultiple">
        <el-input
          v-model="queryParams.nameMultiple"
          type="textarea"
          placeholder="请输入多个出版社名称"
          :rows="1"
          wrap="off"
          clearable
          style="width: 200px"
          @keydown="handleKeyDown"
        />
      </el-form-item>
      <el-form-item label="出版社" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入出版社名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          class="search-input"
          placeholder="出版社状态"
          clearable
        >
          <el-option
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="更新时间">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['publishers:merge']"
          type="warning"
          plain
          icon="Sort"
          :disabled="multiple"
          @click="handleMerge"
          >批量合并</el-button
        >
      </el-col>
      <right-toolbar
        v-model:show-search="showSearch"
        @query-table="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="publisherList"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        prop="name"
        sortable
        label="出版社名称"
        min-width="320"
        resizable
      >
        <template #default="scope">
          <div style="text-align: left;">
            <el-tooltip
              placement="top"
              :content="scope.row.name"
              effect="light"
              :show-after="500"
              popper-class="publisher-white-tooltip"
            >
              <span class="text-ellipsis">{{ scope.row.name }}</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="出版社地址" resizable>
        <template #default="scope">
          <div style="text-align: left;">
            <el-tooltip
              placement="top"
              :content="scope.row.ioc"
              effect="light"
              :show-after="500"
              popper-class="publisher-white-tooltip"
            >
              <span class="text-ellipsis">{{ scope.row.ioc }}</span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="出版社别名"
        min-width="250"
        resizable
      >
        <template #default="scope">
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: flex-start;
            "
          >
            <!-- 如果只有一个别名，直接显示 -->
            <span
              v-if="
                !Array.isArray(scope.row.alias) || scope.row.alias.length <= 1
              "
              class="text-ellipsis"
            >
              {{
                Array.isArray(scope.row.alias) && scope.row.alias.length > 0
                  ? scope.row.alias[0]
                  : scope.row.alias || ''
              }}
            </span>

            <!-- 如果有多个别名，显示第一个别名和可点击图标 -->
            <template v-else>
              <el-tooltip
                placement="top"
                :content="scope.row.alias[0]"
                effect="light"
                :show-after="500"
                popper-class="publisher-white-tooltip"
              >
                <span class="text-ellipsis">{{ scope.row.alias[0] }}</span>
              </el-tooltip>
              <el-icon
                class="more-icon"
                title="查看所有别名"
                @click="showAliasDialog(scope.row)"
              >
                <Fold />
              </el-icon>
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="期刊数量"
        align="center"
        prop="journalCount"
        width="100"
        resizable
      >
        <template #default="scope">
          {{ scope.row.journalCount || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" width="180" resizable>
        <template #default="scope">
          {{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        align="center"
        prop="status"
        width="80"
        resizable
      >
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="160"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['publishers:edit']"
            type="primary"
            link
            icon="Edit"
            @click="handleUpdate(scope.row.id)"
            >编辑</el-button
          >
          <el-button
            v-hasPermi="['publishers:status']"
            :type="scope.row.status === 1 ? 'success' : 'danger'"
            link
            :icon="scope.row.status === 1 ? 'CircleCheck' : 'CircleClose'"
            @click="handleStatusChange(scope.row)"
          >
            {{ scope.row.status === 1 ? '启用' : '停用' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改出版社对话框 -->
    <el-dialog v-model="open" :title="title" width="600px" append-to-body>
      <el-form
        ref="publisherFormRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="出版社名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入出版社名称" />
        </el-form-item>
        <el-form-item label="出版社地址" prop="ioc">
          <el-input v-model="form.ioc" placeholder="请输入出版社地址" />
        </el-form-item>
        <el-form-item label="出版社别名" prop="aliasStr">
          <el-input
            v-model="form.aliasStr"
            type="textarea"
            placeholder="请输入出版社别名，每行一个"
            :rows="10"
            wrap="off"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :value="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 出版社合并对话框 -->
    <el-dialog
      v-model="mergeOpen"
      title="出版社合并"
      width="600px"
      append-to-body
      :close-on-click-modal="!mergeLoading"
    >
      <div v-if="selectedPublishers.length < 2" class="merge-warning">
        请至少选择两个出版社进行合并操作
      </div>
      <el-form
        v-else
        ref="mergeFormRef"
        v-loading="mergeLoading"
        :model="mergeData"
        label-width="120px"
        element-loading-text="正在合并出版社，请稍候..."
      >
        <el-form-item label="出版社名称" prop="name">
          <el-autocomplete
            v-model="mergeData.name"
            :fetch-suggestions="queryPublisherNames"
            placeholder="请输入或选择合并后的出版社名称"
            clearable
            style="width: 100%"
            :disabled="mergeLoading"
          >
            <template #default="{ item }">
              <div>{{ item.value }}</div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="出版社地址" prop="ioc">
          <el-autocomplete
            v-model="mergeData.ioc"
            :fetch-suggestions="queryPublisherAddresses"
            placeholder="请输入或选择合并后的出版社地址"
            clearable
            style="width: 100%"
            :disabled="mergeLoading"
          >
            <template #default="{ item }">
              <div>{{ item.value }}</div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="出版社别名" prop="aliasStr">
          <el-input
            v-model="mergeData.aliasStr"
            type="textarea"
            placeholder="请输入出版社别名，每行一个"
            :rows="10"
            wrap="off"
            :disabled="mergeLoading"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            :loading="mergeLoading"
            :disabled="selectedPublishers.length < 2 || mergeLoading"
            @click="submitMerge"
          >
            {{ mergeLoading ? '合并中...' : '确 定' }}
          </el-button>
          <el-button :disabled="mergeLoading" @click="cancelMerge"
            >取 消</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 别名查看弹窗 -->
    <el-dialog
      v-model="aliasDialogOpen"
      title="出版社别名列表"
      width="800px"
      append-to-body
    >
      <div class="alias-dialog-content">
        <div class="publisher-info">
          <strong>出版社名称：</strong>{{ currentPublisher.name }}
        </div>
        <div class="alias-table-container">
          <el-table
            :data="aliasTableData"
            border
            style="width: 100%"
            max-height="400"
          >
            <el-table-column
              type="index"
              label="序号"
              width="60"
              align="center"
            />
            <el-table-column
              label="别名"
              prop="alias"
              align="left"
              show-overflow-tooltip
            />
          </el-table>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="aliasDialogOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 出版社验证错误弹窗 -->
    <el-dialog
      v-model="validationErrorDialog.visible"
      title="出版社字段冲突详情"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="validation-error-content">
        <el-alert
          title="检测到出版社字段冲突"
          type="error"
          :closable="false"
          show-icon
          class="mb-16"
        >
        </el-alert>

        <el-table
          :data="validationErrorDialog.conflictData"
          border
          style="width: 100%"
          class="conflict-table"
        >
          <el-table-column
            prop="field"
            label="冲突字段"
            width="80"
            align="center"
          >
            <template #default="scope">
              <el-tag :type="getPublisherFieldTagType(scope.row.field)">
                {{ getPublisherFieldDisplayName(scope.row.field) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="value"
            label="冲突值"
            width="200"
            align="center"
          >
            <template #default="scope">
              <el-text type="danger" class="conflict-value">
                {{ scope.row.value }}
              </el-text>
            </template>
          </el-table-column>
          <el-table-column
            prop="conflictPublisherId"
            label="冲突出版社ID"
            width="120"
            align="center"
          >
            <template #default="scope">
              <el-link
                type="primary"
                @click="handleUpdate(scope.row.conflictPublisherId)"
              >
                {{ scope.row.conflictPublisherId }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column
            prop="conflictPublisherName"
            label="冲突出版社名称"
            min-width="200"
          >
            <template #default="scope">
              <el-popover
                placement="top"
                :width="300"
                trigger="hover"
                :content="scope.row.conflictPublisherName"
              >
                <template #reference>
                  <span class="text-ellipsis">{{
                    scope.row.conflictPublisherName
                  }}</span>
                </template>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button
                type="primary"
                size="small"
                @click="handleUpdate(scope.row.conflictPublisherId)"
              >
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="validation-tips">
          <h4>解决建议：</h4>
          <ul>
            <li>检查输入的出版社名称或别名是否正确</li>
            <li>确认是否需要修改冲突出版社的信息</li>
            <li>如果是合并操作，请先处理冲突的出版社记录</li>
            <li>联系管理员协助处理复杂的冲突情况</li>
          </ul>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="success"
            icon="Download"
            @click="exportPublisherConflictData"
          >
            导出Excel
          </el-button>
          <el-button
            type="primary"
            @click="validationErrorDialog.visible = false"
          >
            我知道了
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 错误信息弹窗 -->
    <result-log
      v-if="resultDialogOpen"
      :log-data="resultDialogData"
      curr-exp-type="Publisher"
    >
    </result-log>
  </div>
</template>

<script setup>
  import {
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import { Fold } from '@element-plus/icons-vue';
  import Pagination from '@/components/Pagination';
  import RightToolbar from '@/components/RightToolbar';
  import ResultLog from '@/components/ResultLog/index.vue';
  import { parseTime } from '@/utils/ruoyi';

  import {
    changePublisherStatus,
    getPublisher,
    listPublisher,
    mergePublisher,
    updatePublisher,
  } from '@/api/article/publisher';
  import * as XLSX from 'xlsx';

  const { proxy } = getCurrentInstance();

  // 字典数据
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable');

  // 显示搜索条件
  const showSearch = ref(true);
  // 选中数组
  const ids = ref([]);
  // 选中的出版社对象
  const selectedPublishers = ref([]);
  // 非单个禁用
  const single = ref(true);
  // 非多个禁用
  const multiple = ref(true);
  // 遮罩层
  const loading = ref(false);
  // 总条数
  const total = ref(0);
  // 出版社表格数据
  const publisherList = ref([]);
  // 弹出层标题
  const title = ref('');
  // 是否显示弹出层
  const open = ref(false);
  // 是否显示合并弹出层
  const mergeOpen = ref(false);
  // 日期范围
  const dateRange = ref([]);
  // 错误信息弹窗
  const resultDialogOpen = ref(false);
  const resultDialogData = ref([]);
  // 别名查看弹窗
  const aliasDialogOpen = ref(false);
  const currentPublisher = ref({});
  const aliasTableData = ref([]);

  // 出版社验证错误弹窗
  const validationErrorDialog = ref({
    visible: false,
    errorMessage: '',
    conflictData: [],
  });

  // 数据定义
  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      nameMultiple: '', // 多个出版社名称查询
      name: undefined,
      status: undefined,
      orderByColumn: undefined, // 排序字段
      isAsc: undefined, // 排序方向
    },
    form: {
      id: undefined,
      name: undefined,
      ioc: undefined,
      status: '1',
      alias: [],
      aliasStr: undefined,
    },
    mergeData: {
      name: undefined,
      ioc: undefined,
      alias: [],
      aliasStr: undefined,
    },
    mergeLoading: false, // 添加合并加载状态
  });

  const { queryParams, form, mergeData, mergeLoading } = toRefs(data);

  // 监听合并表单别名字符串变化，切割为数组
  watch(
    () => mergeData.value.aliasStr,
    newVal => {
      if (newVal) {
        mergeData.value.alias = newVal
          .split('\n')
          .filter(item => item.trim() !== '');
      } else {
        mergeData.value.alias = [];
      }
    },
  );

  // 监听编辑表单别名字符串变化，切割为数组
  watch(
    () => form.value.aliasStr,
    newVal => {
      if (newVal) {
        form.value.alias = newVal
          .split('\n')
          .filter(item => item.trim() !== '');
      } else {
        form.value.alias = [];
      }
    },
  );

  // 表单校验
  const rules = reactive({
    name: [{ required: true, message: '出版社名称不能为空', trigger: 'blur' }],
  });

  // 出版社名称建议
  function queryPublisherNames(queryString, cb) {
    const uniqueNames = [
      ...new Set(
        selectedPublishers.value
          .filter(publisher => publisher.name) // 过滤掉空名称
          .map(publisher => publisher.name),
      ),
    ]; // 去重

    const suggestions = uniqueNames.map(name => ({
      value: name,
    }));

    const results = queryString
      ? suggestions.filter(item =>
          item.value.toLowerCase().includes(queryString.toLowerCase()),
        )
      : suggestions;

    cb(results);
  }

  // 出版社地址建议
  function queryPublisherAddresses(queryString, cb) {
    const uniqueAddresses = [
      ...new Set(
        selectedPublishers.value
          .filter(publisher => publisher.ioc) // 过滤掉空地址
          .map(publisher => publisher.ioc),
      ),
    ]; // 去重

    const suggestions = uniqueAddresses.map(address => ({
      value: address,
    }));

    const results = queryString
      ? suggestions.filter(item =>
          item.value.toLowerCase().includes(queryString.toLowerCase()),
        )
      : suggestions;

    cb(results);
  }

  /** 查询出版社列表 */
  function getList() {
    loading.value = true;

    // 处理多个出版社名称查询
    const params = { ...queryParams.value };
    if (params.nameMultiple) {
      // 按行分割，去除每行的首尾空白，过滤掉空行
      const lines = params.nameMultiple.split('\n').map(item => item.trim());
      const filteredLines = lines.filter(item => item !== '');
      params.nameMultiple = filteredLines;
    }

    listPublisher(proxy.addDateRange(params, dateRange.value))
      .then(response => {
        publisherList.value = response.rows;
        total.value = response.total;
        loading.value = false;
      })
      .catch(() => {
        loading.value = false;
      });
  }

  /** 取消按钮 */
  function cancel() {
    open.value = false;
    resetForm();
  }

  /** 取消合并按钮 */
  function cancelMerge() {
    mergeOpen.value = false;
    mergeData.value.name = undefined;
    mergeData.value.ioc = undefined;
    mergeData.value.alias = [];
    mergeData.value.aliasStr = undefined;
  }

  /** 表单重置 */
  function resetForm() {
    proxy.$refs.publisherFormRef?.resetFields();
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = [];
    proxy.$refs.queryForm?.resetFields();
    queryParams.value.pageNum = 1;
    handleQuery();
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    selectedPublishers.value = selection;
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
  }

  // 处理键盘事件
  function handleKeyDown(event) {
    // 如果是 Enter 键且没有按 Shift，则触发搜索
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault(); // 阻止默认的换行行为
      handleQuery();
    }
    // 如果是 Shift+Enter，则允许默认的换行行为
  }

  /** 显示别名查看弹窗 */
  function showAliasDialog(row) {
    currentPublisher.value = row;

    // 构建别名表格数据
    if (Array.isArray(row.alias) && row.alias.length > 0) {
      aliasTableData.value = row.alias.map(alias => ({
        alias: alias,
      }));
    } else {
      aliasTableData.value = [];
    }

    aliasDialogOpen.value = true;
  }

  /** 修改按钮操作 */
  function handleUpdate(publisherId) {
    resetForm();
    getPublisher(publisherId).then(response => {
      Object.assign(form.value, response.data);
      // 确保status是字符串类型
      if (form.value.status !== undefined) {
        form.value.status = String(form.value.status);
      }
      // 将alias数组转换为字符串
      if (form.value.alias && Array.isArray(form.value.alias)) {
        form.value.aliasStr = form.value.alias.join('\n');
      }
      open.value = true;
      title.value = '修改出版社';
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs.publisherFormRef?.validate(valid => {
      if (valid) {
        updatePublisher(form.value)
          .then(response => {
            let data = response.data;
            if (data && Array.isArray(data) && data.length > 0) {
              // 有错误信息，展示错误弹窗
              resultDialogData.value = data;
              resultDialogOpen.value = true;
            } else {
              // 修改成功
              proxy.$modal.msgSuccess('修改成功');
              open.value = false;
              getList();
            }
          })
          .catch(error => {
            console.error('修改出版社失败:', error);

            // 检查是否是出版社验证错误
            if (isPublisherValidationError(error)) {
              showValidationErrorDialog(error);
            } else {
              proxy.$modal.msgError('修改失败');
            }
          });
      }
    });
  }

  /** 合并按钮操作 */
  function handleMerge() {
    if (ids.value.length < 2) {
      proxy.$modal.msgWarning('请至少选择两个出版社进行合并操作');
      return;
    }
    mergeOpen.value = true;
    mergeData.value.name = undefined;
    mergeData.value.ioc = undefined;
    mergeData.value.alias = [];
    mergeData.value.aliasStr = undefined;

    // 默认使用第一个选中的出版社作为名称和地址的初始值
    if (selectedPublishers.value.length > 0) {
      mergeData.value.name = selectedPublishers.value[0].name;
      mergeData.value.ioc = selectedPublishers.value[0].ioc;

      // 收集所有选中出版社的别名
      const allAliases = [];
      selectedPublishers.value.forEach(publisher => {
        if (publisher.alias && Array.isArray(publisher.alias)) {
          allAliases.push(...publisher.alias);
        }
      });
      // 去重并设置到textarea
      const uniqueAliases = [...new Set(allAliases)];
      mergeData.value.aliasStr = uniqueAliases.join('\n');
    }
  }

  /** 提交合并 */
  function submitMerge() {
    if (!mergeData.value.name) {
      proxy.$modal.msgWarning('请输入合并后的出版社名称');
      return;
    }

    // 默认将第一个选中的出版社作为目标出版社
    const targetPublisherId = selectedPublishers.value[0].id;
    // 其余出版社作为源出版社
    const sourcePublisherIds = ids.value.filter(id => id !== targetPublisherId);

    const mergeParams = {
      targetId: targetPublisherId,
      sourceIds: sourcePublisherIds,
      name: mergeData.value.name,
      ioc: mergeData.value.ioc,
      alias: mergeData.value.alias,
    };

    proxy.$modal
      .confirm('确认要将选中的出版社合并吗？合并后无法恢复', '警告')
      .then(() => {
        // 设置加载状态
        mergeLoading.value = true;

        mergePublisher(mergeParams)
          .then(response => {
            let data = response.data;
            if (data && Array.isArray(data) && data.length > 0) {
              // 有错误信息，展示错误弹窗
              resultDialogData.value = data;
              resultDialogOpen.value = true;
            } else {
              // 合并成功
              proxy.$modal.msgSuccess('合并成功');
              mergeOpen.value = false;
              // 清空选择状态
              ids.value = [];
              selectedPublishers.value = [];
              single.value = true;
              multiple.value = true;

              // 重置到第一页，并按更新时间倒序排列，确保合并后的出版社显示在第一行
              queryParams.value.pageNum = 1;
              queryParams.value.orderByColumn = 'update_time';
              queryParams.value.isAsc = 'desc';

              getList();
            }
          })
          .catch(error => {
            console.error('合并出版社失败:', error);

            // 检查是否是出版社验证错误
            if (isPublisherValidationError(error)) {
              showValidationErrorDialog(error);
            } else {
              proxy.$modal.msgError('合并失败');
            }
          })
          .finally(() => {
            // 无论成功还是失败都要取消加载状态
            mergeLoading.value = false;
          });
      })
      .catch(() => {});
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    queryParams.value.orderByColumn = column.prop;
    queryParams.value.isAsc = column.order;
    getList();
  }

  /** 状态修改 */
  function handleStatusChange(row) {
    const text = row.status === '0' ? '启用' : '停用';
    const newStatus = row.status === 0 ? 1 : 0;

    proxy.$modal
      .confirm(`确认要${text}"${row.name}"出版社吗？`, '警告')
      .then(() => {
        changePublisherStatus(row.id, newStatus).then(() => {
          proxy.$modal.msgSuccess(`${text}成功`);
          getList();
        });
      })
      .catch(() => {});
  }

  // 检查是否是出版社验证错误
  function isPublisherValidationError(error) {
    // 检查错误响应中是否包含验证错误的特征
    const errorMessage = error?.response?.data?.msg || error?.message || '';
    return (
      errorMessage.includes('字段') &&
      errorMessage.includes('已存在于出版社记录中') &&
      (errorMessage.includes('出版社名称') || errorMessage.includes('别名'))
    );
  }

  // 显示验证错误弹窗
  function showValidationErrorDialog(error) {
    const errorMessage =
      error?.response?.data?.msg || error?.message || '验证失败';

    // 解析错误消息，提取冲突信息
    const conflictData = parsePublisherValidationError(errorMessage);

    open.value = false;
    validationErrorDialog.value = {
      visible: true,
      errorMessage: errorMessage,
      conflictData: conflictData,
    };
  }

  // 解析验证错误消息，提取冲突信息
  function parsePublisherValidationError(errorMessage) {
    const conflicts = [];

    // 使用正则表达式解析错误消息
    // 格式：字段 '出版社名称' 的值 'XXX' 已存在于出版社记录中 (ID: 123, 名称: 现有出版社)
    const regex =
      /字段\s+'([^']+)'\s+的值\s+'([^']+)'\s+已存在于出版社记录中\s+\(ID:\s+(\d+),\s+名称:\s+([^)]+)\)/g;

    let match;
    while ((match = regex.exec(errorMessage)) !== null) {
      conflicts.push({
        field: getPublisherFieldKey(match[1]), // 将显示名称转换为字段键
        fieldDisplay: match[1],
        value: match[2],
        conflictPublisherId: match[3],
        conflictPublisherName: match[4],
      });
    }

    // 如果正则解析失败，创建一个通用的冲突记录
    if (conflicts.length === 0) {
      conflicts.push({
        field: 'unknown',
        fieldDisplay: '未知字段',
        value: '未知值',
        conflictPublisherId: '未知',
        conflictPublisherName: '解析错误消息失败',
      });
    }

    return conflicts;
  }

  // 将字段显示名称转换为字段键
  function getPublisherFieldKey(displayName) {
    const fieldMap = {
      出版社名称: 'name',
      别名: 'alias',
    };
    return fieldMap[displayName] || 'unknown';
  }

  // 获取字段显示名称
  function getPublisherFieldDisplayName(fieldKey) {
    const fieldMap = {
      name: '出版社名称',
      alias: '别名',
    };
    return fieldMap[fieldKey] || fieldKey;
  }

  // 获取字段标签类型
  function getPublisherFieldTagType(fieldKey) {
    const typeMap = {
      name: 'primary',
      alias: 'success',
    };
    return typeMap[fieldKey] || 'default';
  }

  // 导出出版社冲突数据到Excel
  function exportPublisherConflictData() {
    try {
      if (
        !validationErrorDialog.value.conflictData ||
        validationErrorDialog.value.conflictData.length === 0
      ) {
        proxy.$modal.msgWarning('没有冲突数据可导出');
        return;
      }

      // 准备导出数据
      const exportData = validationErrorDialog.value.conflictData.map(
        (conflict, index) => ({
          序号: index + 1,
          冲突字段:
            conflict.fieldDisplay ||
            getPublisherFieldDisplayName(conflict.field),
          冲突值: conflict.value,
          冲突出版社ID: conflict.conflictPublisherId,
          冲突出版社名称: conflict.conflictPublisherName,
          错误描述: validationErrorDialog.value.errorMessage,
        }),
      );

      // 创建工作簿
      const wb = XLSX.utils.book_new();

      // 创建工作表
      const ws = XLSX.utils.json_to_sheet(exportData);

      // 设置列宽
      const colWidths = [
        { wch: 8 }, // 序号
        { wch: 15 }, // 冲突字段
        { wch: 20 }, // 冲突值
        { wch: 15 }, // 冲突出版社ID
        { wch: 40 }, // 冲突出版社名称
        { wch: 50 }, // 错误描述
      ];
      ws['!cols'] = colWidths;

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '出版社冲突数据');

      // 生成文件名
      const timestamp = new Date()
        .toISOString()
        .slice(0, 19)
        .replace(/[:-]/g, '')
        .replace('T', '_');
      const filename = `出版社冲突数据_${timestamp}.xlsx`;

      // 导出文件
      XLSX.writeFile(wb, filename);

      proxy.$modal.msgSuccess('冲突数据导出成功');
    } catch (error) {
      console.error('导出冲突数据失败:', error);
      proxy.$modal.msgError('导出失败，请重试');
    }
  }

  onMounted(() => {
    getList();
  });
</script>

<style scoped>
  .app-container {
    padding: 20px;
  }

  .merge-warning {
    color: #f56c6c;
    text-align: center;
    margin: 20px 0;
  }

  .selected-publishers {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .publisher-tag {
    margin-right: 5px;
  }

  .mb8 {
    margin-bottom: 8px;
  }

  .search-form {
    .search-input {
      width: 200px;
    }
  }

  .more-icon {
    top: 2px;
    margin-left: 4px;
    font-size: 14px;
    color: #7c9eff;
    cursor: pointer;
  }

  /* 增强表格列边框和可拖拽效果 */
  :deep(.el-table) {
    border-collapse: separate;
    border-spacing: 0;
  }

  /* 添加列分隔线 - 表头 */
  :deep(.el-table__header-wrapper .el-table__header thead tr th) {
    border-right: 1px solid #dcdfe6 !important;
    position: relative;
  }

  :deep(.el-table__header-wrapper .el-table__header thead tr th:last-child) {
    border-right: none !important;
  }

  /* 添加列分隔线 - 表体 */
  :deep(.el-table__body-wrapper .el-table__body tbody tr td) {
    border-right: 1px solid #ebeef5 !important;
  }

  :deep(.el-table__body-wrapper .el-table__body tbody tr td:last-child) {
    border-right: none !important;
  }

  /* 增强列拖拽手柄的视觉效果 */
  :deep(.el-table__header-wrapper .el-table__header th:hover) {
    background-color: #f5f7fa;
  }

  /* 拖拽手柄样式 */
  :deep(.el-table .el-table__border-line) {
    background-color: #409eff !important;
    width: 2px !important;
  }

  :deep(.el-table .el-table__border-line:hover) {
    background-color: #66b1ff !important;
  }

  /* 文本省略样式 */
  .text-ellipsis {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
  }

  /* 出版社白色 Tooltip 统一样式 */
  :deep(.publisher-white-tooltip) {
    max-width: 400px !important;
  }

  :deep(.publisher-white-tooltip .el-tooltip__content) {
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: #000000 !important;
    background-color: #ffffff !important;
    border: 1px solid #ebeef5 !important;
    border-radius: 6px !important;
    box-shadow:
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05) !important;
    padding: 8px 12px !important;
    word-break: break-word !important;
    white-space: pre-line !important;
    font-family:
      -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
      Arial, sans-serif !important;
    font-weight: 400 !important;
  }

  /* 别名弹窗样式 */
  .alias-dialog-content {
    padding: 10px 0;
  }

  .publisher-info {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
    font-size: 14px;
  }

  .alias-table-container {
    margin-top: 10px;
  }

  /* 别名表格样式优化 */
  .alias-table-container :deep(.el-table) {
    border-radius: 4px;
  }

  .alias-table-container :deep(.el-table__header) {
    background-color: #fafafa;
  }

  .alias-table-container :deep(.el-table th) {
    background-color: #fafafa !important;
    font-weight: 600;
  }

  .alias-table-container :deep(.el-table td) {
    padding: 8px 0;
  }

  /* 别名表格行悬停效果 */
  .alias-table-container :deep(.el-table__row:hover) {
    background-color: #f5f7fa;
  }

  /* 出版社验证错误弹窗样式 */
  .validation-error-content {
    .mb-16 {
      margin-bottom: 16px;
    }

    .conflict-table {
      margin-bottom: 20px;

      .conflict-value {
        font-weight: bold;
        font-family: 'Courier New', monospace;
      }
    }

    .validation-tips {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 16px;
      margin-top: 16px;

      h4 {
        margin: 0 0 12px 0;
        color: #495057;
        font-size: 14px;
        font-weight: 600;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          color: #6c757d;
          font-size: 13px;
          line-height: 1.5;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
</style>
