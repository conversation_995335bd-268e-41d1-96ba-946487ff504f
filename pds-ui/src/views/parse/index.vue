<template>
  <div class="app-container">
    <!-- 搜索和工具栏 -->
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
      label-width="100px"
      class="search-form"
    >
      <div class="form-row">
        <el-form-item label="来源文献ID" prop="sourceId">
          <el-input
            v-model="queryParams.sourceId"
            placeholder="请输入来源文献ID"
            clearable
            class="search-input"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="原始文件名称" prop="fileName">
          <el-input
            v-model="queryParams.fileName"
            placeholder="请输入原始文件名称"
            clearable
            class="search-input"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="来源" prop="source">
          <el-select
            v-model="queryParams.source"
            placeholder="请选择"
            clearable
            class="search-input"
          >
            <el-option label="Pubmed" value="Pubmed" />
            <el-option label="PMC" value="PMC" />
            <el-option label="bioRxiv" value="bioRxiv" />
            <el-option label="medRxiv" value="medRxiv" />
          </el-select>
        </el-form-item>
      </div>
      <div class="form-row">
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择"
            clearable
            class="search-input"
          >
            <el-option label="未入库" value="1" />
            <el-option label="已入库" value="2" />
            <el-option label="失败" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="失败原因" prop="errorMsg">
          <el-input
            v-model="queryParams.errorMsg"
            placeholder="请输入失败原因"
            clearable
            class="search-input"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="更新时间" prop="updateTime">
          <el-date-picker
            v-model="dateRange"
            class="search-input"
            style="width: 240px"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <div class="action-btns">
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </el-form>

    <!-- 添加右侧工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['parse:retry']"
          type="warning"
          plain
          icon="RefreshRight"
          :disabled="multiple"
          @click="handleBatchRetry"
          >批量重试</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['parse:delete']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleBatchDelete"
          >批量删除</el-button
        >
      </el-col>
      <right-toolbar
        v-model:show-search="showSearch"
        :columns="columns"
        @query-table="getList"
      ></right-toolbar>
    </el-row>

    <!-- 表格数据 -->
    <el-table
      v-loading="loading"
      :data="parseList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="来源文献ID"
        align="center"
        prop="sourceId"
        width="150"
      />
      <el-table-column
        label="原始文件名称"
        align="center"
        prop="fileName"
        :show-overflow-tooltip="true"
        width="160"
      >
        <template #default="scope">
          <el-link type="primary" @click="handleDownloadXml(scope.row)">
            {{ scope.row.fileName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="来源" align="center" prop="source" width="140" />
      <el-table-column label="状态" align="center" prop="status" width="130">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 1" type="warning">未入库</el-tag>
          <el-tag v-else-if="scope.row.status === 2" type="success"
            >已入库</el-tag
          >
          <el-tag v-else-if="scope.row.status === 0" type="danger">失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="columns[0].visible"
        label="MD5"
        align="center"
        prop="fileMd5"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        v-if="columns[1].visible"
        label="版本"
        align="center"
        prop="version"
        width="60"
      />
      <el-table-column
        v-if="columns[2].visible"
        label="文件后缀"
        align="center"
        prop="contentType"
        width="90"
      />
      <el-table-column
        v-if="columns[3].visible"
        label="文件存储相对路径"
        align="center"
        prop="localPath"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="失败原因" align="left" prop="errorMsg">
        <template #default="scope">
          <div v-if="scope.row.errorMsg && scope.row.errorMsg.trim()">
            <el-popover
              placement="top"
              :width="1000"
              trigger="hover"
              :content="scope.row.errorMsg"
              :show-after="300"
              :hide-after="100"
            >
              <template #reference>
                <span class="error-msg-text">
                  {{ scope.row.errorMsg }}
                </span>
              </template>
            </el-popover>
          </div>
          <span v-else class="no-error">-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        width="170"
        prop="createTime"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="更新时间"
        align="center"
        width="170"
        prop="updateTime"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="170"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            v-if="scope.row.status === 0"
            v-hasPermi="['parse:retry']"
            link
            type="warning"
            icon="RefreshRight"
            @click="handleRetry(scope.row)"
            >重试</el-button
          >
          <el-button
            v-hasPermi="['parse:delete']"
            link
            type="danger"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
  import { getCurrentInstance, ref } from 'vue';
  import { listParse, deleteParse, retryParse } from '@/api/task/parse';
  import RightToolbar from '@/components/RightToolbar/index.vue';

  const { proxy } = getCurrentInstance();

  // 列显隐信息
  const columns = ref([
    { key: 0, label: 'MD5', visible: false },
    { key: 1, label: '版本', visible: false },
    { key: 2, label: '文件后缀', visible: false },
    { key: 3, label: '文件存储相对路径', visible: false },
  ]);

  // 遮罩层
  const loading = ref(false);
  // 选中数组
  const ids = ref([]);
  // 非单个禁用
  const single = ref(true);
  // 非多个禁用
  const multiple = ref(true);
  // 显示搜索条件
  const showSearch = ref(true);
  // 总条数
  const total = ref(0);
  // 解析数据表格数据
  const parseList = ref([]);
  // 日期范围
  const dateRange = ref([]);
  // 查询参数
  const queryParams = ref({
    pageNum: 1,
    pageSize: 20,
    id: null,
    fileName: null,
    sourceId: null,
    source: null,
    status: null,
    errorMsg: null,
    updateTimeStart: null,
    updateTimeEnd: null,
  });

  /** 查询解析数据列表 */
  function getList() {
    loading.value = true;
    listParse(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        parseList.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('queryForm');
    handleQuery();
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const parseIds = row?.id || ids.value;
    proxy.$modal
      .confirm(`是否确认删除ID为"${parseIds}"的记录?`)
      .then(function () {
        loading.value = true;
        return deleteParse(parseIds);
      })
      .then(() => {
        proxy.$modal.msgSuccess('删除成功');
        getList();
      })
      .catch(error => {
        if (error !== 'cancel') {
          proxy.$modal.msgError('删除失败');
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 下载XML文件操作 */
  function handleDownloadXml(row) {
    proxy.download(`/parse/download/${row.id}`, {}, `${row.fileName}`);
  }

  /** 重试操作 */
  function handleRetry(row) {
    proxy.$modal
      .confirm(`是否确认重试ID为"${row.id}"的记录?`)
      .then(function () {
        loading.value = true;
        return retryParse(row.id);
      })
      .then(response => {
        if (response && response.code === 200) {
          proxy.$modal.msgSuccess('重试成功');
          getList();
        } else {
          proxy.$modal.msgError(response.msg || '重试失败');
        }
      })
      .catch(error => {
        if (error !== 'cancel') {
          proxy.$modal.msgError('重试失败');
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 批量删除按钮操作 */
  function handleBatchDelete() {
    const parseIds = ids.value;
    proxy.$modal
      .confirm(`是否确认删除选中的${parseIds.length}条记录?`)
      .then(function () {
        loading.value = true;
        return deleteParse(parseIds.join(','));
      })
      .then(() => {
        proxy.$modal.msgSuccess('删除成功');
        getList();
      })
      .catch(error => {
        if (error !== 'cancel') {
          proxy.$modal.msgError('删除失败');
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 批量重试按钮操作 */
  function handleBatchRetry() {
    const parseIds = ids.value;
    proxy.$modal
      .confirm(`是否确认重试选中的${parseIds.length}条记录?`)
      .then(function () {
        loading.value = true;
        return retryParse(parseIds.join(','));
      })
      .then(response => {
        if (response && response.code === 200) {
          proxy.$modal.msgSuccess('批量重试成功');
          getList();
        } else {
          proxy.$modal.msgError(response.msg || '批量重试失败');
        }
      })
      .catch(error => {
        if (error !== 'cancel') {
          proxy.$modal.msgError('批量重试失败');
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }
  getList();
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 10px;
    padding-bottom: 40px;
  }

  .mb8 {
    margin-bottom: 8px;
  }

  .search-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 30px;

    .form-row {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 8px;

      .el-form-item {
        margin-bottom: 0;
        margin-right: 16px;
      }

      .search-input {
        width: 240px;
      }

      .action-btns {
        display: flex;
        gap: 8px;
        margin-left: auto;
      }
    }
  }

  .error-msg-text {
    cursor: pointer;
    line-height: 1.4;
    display: inline-block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    max-width: 100%;
  }

  .no-error {
    color: #909399;
    font-size: 12px;
  }
</style>
