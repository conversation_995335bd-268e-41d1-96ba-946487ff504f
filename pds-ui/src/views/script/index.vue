<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="脚本名称" prop="scriptName">
        <el-input
          v-model="queryParams.scriptName"
          placeholder="请输入脚本名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <!--      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="脚本状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>-->
      <el-form-item label="类型" prop="scriptType">
        <el-select
          v-model="queryParams.scriptType"
          placeholder="脚本类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in script_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="上传时间" prop="uploadTime">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['script:index']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          >上传脚本
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['script:index']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          >删除
        </el-button>
      </el-col>
      <right-toolbar
        v-model:show-search="showSearch"
        @query-table="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="scriptList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="scriptId" width="80" />
      <el-table-column
        label="脚本名称"
        align="center"
        prop="scriptName"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <a
            href="javascript:void(0)"
            class="script-name-link"
            @click="handleDownloadScript(scope.row)"
            >{{ scope.row.scriptName }}</a
          >
        </template>
      </el-table-column>
      <el-table-column
        label="脚本文件名"
        align="center"
        prop="scriptFileName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="MD5值"
        align="center"
        prop="scriptMd5"
        width="280"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="类型"
        align="center"
        prop="scriptTypeStr"
        width="100"
      >
      </el-table-column>
      <!--      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>-->

      <el-table-column
        label="上传时间"
        align="center"
        prop="uploadTime"
        width="100"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="更新时间"
        align="center"
        prop="updateTime"
        width="100"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="最后成功时间"
        align="center"
        prop="lastSuccessTime"
        width="160"
      >
        <template #default="scope">
          <span>{{
            parseTime(scope.row.lastSuccessTime, '{y}-{m}-{d} {h}:{i}:{s}')
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="220"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['script:index']"
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >编辑
          </el-button>
          <el-button
            v-hasPermi="['script:script:remove']"
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加脚本对话框 -->
    <el-dialog v-model="open" :title="title" width="780px" append-to-body>
      <el-form ref="scriptRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="脚本名称" prop="scriptName">
              <el-input
                v-model="form.scriptName"
                placeholder="请输入脚本名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="scriptType">
              <template #label>
                <span>脚本类型</span>
                <el-tooltip
                  content="脚本类型一旦设置后不可修改，请谨慎选择"
                  placement="top"
                >
                  <el-icon class="warning-icon"><WarningFilled /></el-icon>
                </el-tooltip>
              </template>
              <el-select
                v-model="form.scriptType"
                multiple
                :multiple-limit="2"
                placeholder="请选择"
                :disabled="title === '修改脚本'"
              >
                <el-option
                  v-for="dict in script_type"
                  :key="`sctp-${dict.value}`"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="脚本文件" prop="tempFileName">
              <div class="upload-container">
                <el-upload
                  ref="uploadRef"
                  :limit="1"
                  :accept="scriptFileSuffix"
                  :headers="uploadParam.headers"
                  :action="uploadParam.url"
                  :disabled="uploadParam.isUploading"
                  :file-list="uploadParam.fileList"
                  :on-progress="handleFileUploadProgress"
                  :on-success="handleUploadSuccess"
                  :on-error="handleUploadError"
                  :before-upload="beforeUpload"
                  :auto-upload="true"
                  :show-file-list="false"
                  class="script-upload"
                  drag
                >
                  <el-icon class="el-icon--upload">
                    <upload-filled />
                  </el-icon>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                  <template #tip>
                    <div class="el-upload__tip text-center">
                      <div class="el-upload__tip">
                        <span
                          >请上传python脚本文件，必须是{{
                            scriptFileSuffix
                          }}结尾
                        </span>
                      </div>
                    </div>
                  </template>
                </el-upload>
              </div>

              <!-- 显示当前文件名 -->
              <div v-if="currentFileName" class="current-file-info">
                <div>
                  当前文件名称：
                  <el-tag type="primary">
                    {{ currentFileName }}
                  </el-tag>
                  <el-icon class="clear-icon" @click="clearUploadFile">
                    <CircleClose />
                  </el-icon>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <!--          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in sys_normal_disable"
                  :key="`sc-status-${dict.value}`"
                  :value="dict.value"
                  >{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>-->
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                v-model="form.remark"
                type="textarea"
                placeholder="请输入脚本备注"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Script">
  import { getCurrentInstance, reactive, ref, toRefs } from 'vue';
  import { getToken } from '@/utils/auth.js';
  import {
    UploadFilled,
    CircleClose,
    WarningFilled,
  } from '@element-plus/icons-vue';
  import { listScript, saveScript, delScript } from '@/api/task/script';

  const { proxy } = getCurrentInstance();
  const { script_type } = proxy.useDict('script_type');

  const scriptList = ref([]);
  const open = ref(false);
  const monitorOpen = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  // const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/script/upload');
  const uploadRef = ref();

  const uploadParam = reactive({
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + '/script/upload',
    // 上传的文件列表
    fileList: [],
  });

  const dateRange = ref([]);
  const currentFileName = ref(''); // 当前脚本文件名称

  const historyList = ref([]);
  const historyTotal = ref(0);
  const scriptFileSuffix = ref('.pyscript');

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      scriptName: undefined,
      scriptType: undefined,
      status: undefined,
      uploadTime: undefined,
    },
    monitorForm: {
      scriptId: undefined,
      testId: undefined,
      cronExpression: undefined,
    },
    historyQuery: {
      pageNum: 1,
      pageSize: 5,
      scriptId: undefined,
    },
    rules: {
      scriptName: [
        { required: true, message: '脚本名称不能为空', trigger: 'blur' },
      ],
      scriptType: [
        { required: true, message: '脚本类型不能为空', trigger: 'change' },
      ],
    },
    monitorRules: {
      testId: [
        { required: true, message: '测试文献ID不能为空', trigger: 'blur' },
      ],
      cronExpression: [
        { required: true, message: 'Cron表达式不能为空', trigger: 'blur' },
      ],
    },
  });

  const { queryParams, form, rules, monitorForm, monitorRules, historyQuery } =
    toRefs(data);

  /** 查询脚本列表 */
  function getList() {
    loading.value = true;
    listScript(proxy.addDateRange(queryParams.value, dateRange.value)).then(
      response => {
        scriptList.value = response.rows;
        total.value = response.total;
        loading.value = false;
      },
    );
  }

  /** 获取历史记录 */
  function getHistoryList() {
    setTimeout(() => {
      historyList.value = mockHistoryList;
      historyTotal.value = mockHistoryList.length;
    }, 300);
  }

  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }

  /** 表单重置 */
  function reset() {
    form.value = {
      id: undefined,
      scriptId: undefined,
      scriptName: undefined,
      scriptType: undefined,
      scriptPath: undefined,
      md5: undefined,
      remark: undefined,
      status: '1',
      tempFileName: undefined,
      scriptFileId: undefined,
    };
    uploadParam.isUploading = false;
    proxy.resetForm('scriptRef');
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef');
    handleQuery();
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.scriptId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    currentFileName.value = ''; // 清空当前文件名
    open.value = true;
    title.value = '上传脚本';
    nextTick(() => {
      uploadRef.value.clearFiles();
    });
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    // 填充表单数据
    form.value.id = row.scriptId;
    form.value.scriptName = row.scriptName;
    form.value.scriptType = row.scriptTypeStr
      ? row.scriptTypeStr
          .split(',')
          .map(item => {
            // 根据标签找到对应的值
            const dictItem = script_type.value.find(
              dict => dict.label === item.trim(),
            );
            return dictItem ? dictItem.value : '';
          })
          .filter(Boolean)
      : [];
    form.value.status = row.status;
    form.value.remark = row.remark;
    form.value.scriptFileId = row.scriptFileId;

    // 设置当前文件名
    currentFileName.value = row.scriptFileName;

    // 设置上传文件列表
    uploadParam.fileList = [
      {
        name: row.scriptFileName,
        url: '',
      },
    ];

    open.value = true;
    title.value = '修改脚本';

    // 清空上传组件数据
    nextTick(() => {
      uploadRef.value.clearFiles();
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const id = row.scriptId;
    let scriptIds = null;
    let confirmMsg = '';
    if (id) {
      confirmMsg = `是否确认删除脚本"${row.scriptName}"吗？删除后数据不可恢复`;
      scriptIds = id;
    } else {
      scriptIds = ids.value;
      if (!scriptIds || scriptIds.length === 0) {
        proxy.$modal.msgError('请先选择要删除的脚本');
        return false;
      }
      confirmMsg = `是否确认删除选中的脚本吗？删除后数据不可恢复`;
    }
    proxy.$modal
      .confirm(confirmMsg)
      .then(function () {
        return delScript(scriptIds);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .catch(() => {});
  }

  /** 文件上传前的处理 */
  function beforeUpload(file) {
    // 可以在这里添加文件类型和大小限制
    if (!file.name.endsWith(scriptFileSuffix.value)) {
      proxy.$modal.msgError(`只能上传${scriptFileSuffix.value}文件`);
      return false;
    }
    if (file.size > 10 * 1024 * 1024) {
      proxy.$modal.msgError('文件大小不能超过10M');
      return false;
    }
    return true;
  }

  /** 上传成功处理 */
  function handleUploadSuccess(response, file) {
    // 上传成功后，保存临时文件名
    if (response.code === 200) {
      form.value.tempFileName = response.data;
      // 如果上传了新文件，清空scriptFileId
      form.value.scriptFileId = null;
      // 更新当前文件名显示
      currentFileName.value = file.name;
      proxy.$modal.msgSuccess('文件上传成功');
    } else {
      proxy.$modal.msgError(response.msg || '文件上传失败');
    }
  }

  /**文件上传中处理 */
  const handleFileUploadProgress = (event, file, fileList) => {
    uploadParam.isUploading = true;
  };

  // 文件提交处理
  function submitUpload() {
    uploadRef.value.submit();
  }

  /** 清空上传文件 */
  function clearUploadFile() {
    uploadRef.value.clearFiles();
    currentFileName.value = '';
    form.value.tempFileName = undefined;
    form.value.scriptFileId = undefined;
    uploadParam.isUploading = false;
  }

  /** 上传失败处理 */
  function handleUploadError() {
    proxy.$modal.msgError('文件上传失败');
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['scriptRef'].validate(valid => {
      if (valid) {
        // 检查是否上传了文件或者有已存在的文件ID
        if (!form.value.tempFileName && !form.value.scriptFileId) {
          proxy.$modal.msgError('请先上传脚本文件');
          return;
        }

        // 添加确认对话框
        const confirmMessage = form.value.id
          ? `确认要修改"${form.value.scriptName}"脚本吗？`
          : `确认要新增"${form.value.scriptName}"脚本吗？`;

        proxy.$modal
          .confirm(confirmMessage)
          .then(() => {
            // 构造提交数据
            const submitData = {
              id: form.value.id,
              scriptName: form.value.scriptName,
              scriptType: form.value.scriptType,
              status: form.value.status,
            };

            // 根据情况添加文件相关字段
            if (form.value.tempFileName) {
              submitData.tempFileName = form.value.tempFileName;
            }

            if (form.value.scriptFileId) {
              submitData.scriptFileId = form.value.scriptFileId;
            }

            if (form.value.remark) {
              submitData.remark = form.value.remark;
            }

            // 提交到后端
            proxy.$modal.loading('正在提交数据，请稍候...');
            saveScript(submitData)
              .then(res => {
                proxy.$modal.closeLoading();
                if (res.code === 200) {
                  proxy.$modal.msgSuccess(
                    form.value.id ? '修改成功' : '添加成功',
                  );
                  open.value = false;
                  getList();
                } else {
                  proxy.$modal.msgError(res.msg || '操作失败');
                }
              })
              .catch(err => {
                proxy.$modal.closeLoading();
                proxy.$modal.msgError('操作失败：' + err.message);
              });
          })
          .catch(() => {}); // 用户取消操作时不做任何处理
      }
    });
  }

  /** 下载脚本 */
  function handleDownloadScript(row) {
    /*// 调用后端API进行下载
    const url =
      import.meta.env.VITE_APP_BASE_API + '/script/download/' + row.scriptId;
    // 创建一个隐藏的a标签并模拟点击下载
    const link = document.createElement('a');
    link.href = url;
    link.target = '_blank'; // 在新窗口打开或下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);*/
    proxy.download(
      `/script/download/${row.scriptId}`,
      {},
      `${row.scriptName}${scriptFileSuffix.value}`,
    );
    proxy.$modal.msgSuccess(`正在下载脚本: ${row.scriptName}`);
  }

  getList();
</script>

<style lang="scss">
  .monitor-history {
    margin-top: 20px;

    .el-pagination {
      float: right;
    }
  }

  .script-name-link {
    color: #409eff;
    text-decoration: none;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

  .upload-container {
    width: 100%;
    margin-bottom: 10px;
  }

  .script-upload {
    width: 100%;
    display: block;
  }

  .current-file-info {
    margin-top: 10px;
    display: block;
    width: 100%;
    clear: both;

    .clear-icon {
      margin-left: 8px;
      cursor: pointer;
      color: #909399;
      vertical-align: middle;

      &:hover {
        color: #f56c6c;
      }
    }
  }
</style>
