<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div>
      <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
        class="search-form"
      >
        <div class="form-row">
          <el-form-item label="节点ID" prop="id">
            <el-input
              v-model="queryParams.id"
              placeholder="请输入节点ID"
              clearable
              class="search-input"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="节点名称" prop="siteName">
            <el-input
              v-model="queryParams.siteName"
              placeholder="请输入节点名称"
              clearable
              class="search-input"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <!--<el-form-item label="分组" prop="siteGroup">
            <el-select
              v-model="queryParams.siteGroup"
              placeholder="请选择分组"
              clearable
              class="search-input"
            >
              <el-option
                v-for="group in groupOptions"
                :key="group"
                :label="group"
                :value="group"
              />
            </el-select>
          </el-form-item>-->
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="节点状态"
              clearable
              class="search-input"
            >
              <el-option label="启用" value="0" />
              <el-option label="停用" value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="节点类型" prop="siteType">
            <el-select
              v-model="queryParams.siteType"
              placeholder="节点类型"
              clearable
              class="search-input"
            >
              <el-option
                v-for="dict in script_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <div class="action-btns">
            <el-button type="primary" icon="Search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8 mt10">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['node:index']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          >新增节点</el-button
        >
      </el-col>
      <right-toolbar
        v-model:show-search="showSearch"
        @query-table="getNodeList"
      ></right-toolbar>
    </el-row>

    <!-- 节点卡片列表 -->
    <div v-loading="loading" class="node-list">
      <el-empty v-if="nodeList.length === 0" description="暂无数据" />
      <div v-else class="node-grid">
        <el-card
          v-for="node in nodeList"
          :key="`nd-site-${node.id}`"
          class="node-card"
        >
          <div class="node-card-header">
            <div class="node-name">
              <span class="node-type-tag">
                <span :class="getNodeTypeClass(node.siteType)">
                  <i class="el-icon type-icon">
                    <component :is="getNodeIcon(node.siteType)"></component>
                  </i>
                  {{ getNodeTypeName(node.siteType) }}
                </span>
              </span>
              {{ node.siteName }}
            </div>
          </div>

          <div class="node-status-row">
            <div class="status-item">
              <i class="el-icon"><Document /></i>
              <span>节点ID: {{ node.id }}</span>
            </div>
            <div class="status-item">
              <i class="el-icon"><User /></i>
              <span>简写: {{ node.siteAbbr }}</span>
            </div>
            <!--<div class="status-item status-tag">
              <dict-tag :options="sys_normal_disable" :value="node.status" />
            </div>-->
            <div class="status-item date">
              <i class="el-icon"><Calendar /></i>
              <span>{{ parseTime(node.updateTime, '{y}-{m}-{d}') }}</span>
            </div>
          </div>

          <div class="node-card-content">
            <!--<div class="node-info-row">
              <span class="info-label">
                <el-icon><Collection /></el-icon>
                分组:
              </span>
              <span class="info-value">{{ node.siteGroup }}</span>
            </div>-->
            <div class="node-info-row">
              <span class="info-label">
                <el-icon><PriceTag /></el-icon>
                脚本:
              </span>
              <span class="info-value">{{ node.labelName }}</span>
            </div>
            <div class="node-info-row">
              <span class="info-label">
                <el-icon><House /></el-icon>
                单位:
              </span>
              <span class="info-value">{{ node.unit }}</span>
            </div>
            <div class="node-info-row">
              <span class="info-label">
                <el-icon><Location /></el-icon>
                地址:
              </span>
              <span class="info-value">{{ node.address }}</span>
            </div>

            <div class="node-info-row">
              <span class="info-label">
                <el-icon><Location /></el-icon>
                简写:
              </span>
              <span class="info-value">{{ node.siteAbbr }}</span>
            </div>
          </div>

          <div class="node-card-footer">
            <el-button
              v-hasPermi="['node:index']"
              type="primary"
              link
              icon="Edit"
              @click="handleUpdate(node)"
              >编辑</el-button
            >

            <el-button
              v-hasPermi="['node:index']"
              :type="node.status === '0' ? 'warning' : 'success'"
              link
              icon="CircleCheck"
              @click="handleStatusChange(node)"
              >{{ node.status === '0' ? '停用' : '启用' }}</el-button
            >
            <el-button
              v-hasPermi="['node:index']"
              type="danger"
              link
              icon="Delete"
              @click="handleDelete(node)"
              >删除</el-button
            >
          </div>
        </el-card>
      </div>

      <!-- 分页组件 -->
      <!--<pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getNodeList"
      />-->
    </div>

    <!-- 添加或修改节点对话框 -->
    <el-dialog v-model="open" :title="title" width="600px" append-to-body>
      <el-form ref="nodeRef" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="配置克隆" prop="cloneNodeId">
          <el-select
            v-model="form.cloneNodeId"
            placeholder="请选择配置克隆的节点"
            clearable
            style="width: 380px"
          >
            <el-option
              v-for="item in nodeOptions"
              :key="`nd-opt-${item.nodeId}`"
              :label="item.nodeName"
              :value="item.nodeId"
            />
          </el-select>
          <el-tooltip
            content="选择后会自动填充选中节点的配置信息"
            placement="top"
          >
            <el-icon class="ml5"><QuestionFilled /></el-icon>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="节点名称" prop="nodeName">
          <el-input
            v-model="form.nodeName"
            placeholder="请输入节点名称"
            style="width: 380px"
          />
        </el-form-item>
        <el-form-item label="英文简写" prop="nodeShortName">
          <el-input
            v-model="form.nodeShortName"
            placeholder="请输入节点英文简写"
            style="width: 380px"
          />
        </el-form-item>
        <!--<el-form-item label="分组" prop="groupName">
          <el-autocomplete
            v-model="form.groupName"
            :fetch-suggestions="queryGroupSearch"
            placeholder="请输入分组名称，可选择已有分组或输入新分组"
            clearable
            style="width: 380px"
          />
          <el-tooltip placement="top">
            <template #content>
              该分组可以用于下载客户端指定重新分配任务的分配策略指定，如'组内重试'/'组外重试'！<br />
              组内重试：假设A、B是同一个分组。A节点下载失败后，按照重试间隔时间暂停后，会让B节点重新下载当前任务。如果整个同名分组内的节点都下载失败才算失败。<br />
              组外重试：如果在A节点下载失败，就不会在本组中的节点重试，会在不同的组中随机找一个节点重试一次。
            </template>
            <el-icon class="ml5"><QuestionFilled /></el-icon>
          </el-tooltip>
        </el-form-item>-->
        <el-form-item label="任务执行间隔时间" prop="pullInterval">
          <el-input-number
            v-model="form.pullInterval"
            :min="1"
            :max="3600"
            style="width: 380px"
          />
          <el-tooltip
            content="节点执行任务的间隔时间，单位秒，节点执行下载脚本后将会睡眠，睡眠时间为填写的值与值的3倍时间内的随机值，默认60秒。"
            placement="top"
          >
            <el-icon class="ml5"><QuestionFilled /></el-icon>
          </el-tooltip>
        </el-form-item>
        <el-form-item prop="nodeType">
          <template #label>
            <span>节点类型</span>
            <!--<el-tooltip
              content="节点类型一旦设置后不可修改，请谨慎选择"
              placement="top"
            >
              <el-icon class="warning-icon"><WarningFilled /></el-icon>
            </el-tooltip>-->
          </template>
          <el-radio-group
            v-model="form.nodeType"
            @change="handleNodeTypeChange"
          >
            <el-radio
              v-for="dict in script_type"
              :key="`nd-tp-${dict.value}`"
              :value="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
          <!--<el-tooltip
            v-if="form.isEdit"
            content="节点类型一旦设置后不可修改"
            placement="top"
          >
            <el-icon class="ml5"><WarningFilled /></el-icon>
          </el-tooltip>-->
        </el-form-item>
        <el-form-item label="脚本标签" prop="labelId">
          <el-select
            v-model="form.labelId"
            placeholder="选择来自标签管理模块的脚本配置"
            style="width: 380px"
          >
            <el-option
              v-for="item in labelOptions"
              :key="`lb-id-${item.labelId}`"
              :label="item.labelName"
              :value="item.labelId"
            />
          </el-select>
        </el-form-item>
        <!--<el-form-item label="管理员" prop="adminUser">
          <el-input v-model="form.adminUser" placeholder="请输入管理员" style="width: 380px" />
        </el-form-item>-->
        <el-form-item label="单位" prop="organization">
          <el-input
            v-model="form.organization"
            placeholder="请输入节点所属单位名称"
            style="width: 380px"
          />
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input
            v-model="form.address"
            placeholder="请输入节点部署详细地址"
            style="width: 380px"
          />
        </el-form-item>
        <!--        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :value="dict.value"
              :label="dict.value"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup name="Node">
import {
  Calendar,
  Collection,
  Document,
  House,
  Location,
  Monitor,
  PriceTag,
  QuestionFilled,
  Reading,
  School,
  User,
} from '@element-plus/icons-vue';
import {getCurrentInstance, onMounted, ref, toRaw, watch} from 'vue';
import {
  addSite,
  changeSiteStatus,
  delSite,
  getSite,
  listLabelsByType,
  listSite,
  listSiteGroups,
  updateSite,
} from '@/api/task/site';
import {parseTime} from '@/utils/ruoyi.js';

const { proxy } = getCurrentInstance();
  const { script_type } = proxy.useDict('script_type');

  // 遮罩层
  const loading = ref(false);
  // 显示搜索条件
  const showSearch = ref(true);
  // 总条数
  const total = ref(0);
  // 节点表格数据
  const nodeList = ref([]);
  // 弹出层标题
  const title = ref('');
  // 是否显示弹出层
  const open = ref(false);
  // 节点选项
  const nodeOptions = ref([]);
  // 标签选项
  const labelOptions = ref([]);
  // 分组选项
  const groupOptions = ref([]);



  // 查询参数
  const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    id: undefined,
    siteName: undefined,
    siteGroup: undefined,
    status: undefined,
    siteType: undefined,
  });

  // 表单参数
  const form = ref({
    cloneNodeId: undefined,
    nodeId: undefined,
    nodeName: undefined,
    nodeShortName: undefined,
    groupName: undefined,
    pullInterval: 60,
    nodeType: '1',
    labelId: undefined,
    labelName: undefined,
    status: '0',
    organization: undefined,
    address: undefined,
    updateTime: undefined,
    isEdit: false,
  });
  const formInit = proxy.$_.cloneDeep(toRaw(form.value));

  // 表单校验规则
  const rules = ref({
    nodeName: [
      { required: true, message: '节点名称不能为空', trigger: 'blur' },
    ],
    nodeType: [
      { required: true, message: '节点类型不能为空', trigger: 'change' },
    ],
    labelId: [
      { required: true, message: '脚本标签不能为空', trigger: 'change' },
    ],
  });

  /** 查询节点列表 */
  function getNodeList() {
    loading.value = true;

    // 调用API获取节点列表
    listSite(queryParams.value)
      .then(response => {
        if (response.code === 200) {
          nodeList.value = response.rows || [];
          total.value = response.total || 0;
        } else {
          proxy.$modal.msgError(response.msg || '获取节点列表失败');
          nodeList.value = [];
          total.value = 0;
        }
      })
      .catch(error => {
        console.error('获取节点列表失败', error);
        nodeList.value = [];
        total.value = 0;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getNodeList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef');
    queryParams.value.pageNum = 1;
    handleQuery();
  }

  /** 表单重置 */
  function reset() {
    form.value = proxy.$_.cloneDeep(formInit);
    updateLabelOptions(form.value.nodeType);
    proxy.resetForm('nodeRef');
  }

  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();

    // 获取所有节点作为克隆选项
    listSite({}).then(response => {
      if (response.code === 200) {
        nodeOptions.value = (response.rows || []).map(item => ({
          nodeId: item.id,
          nodeName: item.siteName,
        }));
      }
    });

    // 重置标签选项
    labelOptions.value = [];
    open.value = true;
    title.value = '添加节点';
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();

    // 获取所有节点作为克隆选项，排除当前节点
    listSite({}).then(response => {
      if (response.code === 200) {
        nodeOptions.value = (response.rows || [])
          .filter(item => item.id !== row.id)
          .map(item => ({
            nodeId: item.id,
            nodeName: item.siteName,
          }));
      }
    });

    // 获取节点详情
    getSite(row.id).then(response => {
      if (response.code === 200) {
        const nodeData = response.data;
        form.value = {
          nodeId: nodeData.id,
          nodeName: nodeData.siteName,
          nodeShortName: nodeData.siteAbbr,
          groupName: nodeData.siteGroup,
          pullInterval: nodeData.obtainTaskInterval || 60,
          nodeType: nodeData.siteType,
          labelId: nodeData.scriptlabelId,
          status: nodeData.status,
          organization: nodeData.unit,
          address: nodeData.address,
          isEdit: true, // 标记为编辑模式
        };

        // 根据节点类型获取对应标签选项
        updateLabelOptions(nodeData.siteType);
      } else {
        proxy.$modal.msgError(response.msg || '获取节点详情失败');
      }
    });

    open.value = true;
    title.value = '修改节点';
  }

  /** 根据节点类型更新标签选项 */
  function updateLabelOptions(nodeType) {
    if (!nodeType) {
      labelOptions.value = [];
      return;
    }

    // 根据节点类型查询对应的标签列表
    listLabelsByType(nodeType).then(response => {
      if (response.code === 200) {
        labelOptions.value = (response.data || []).map(item => ({
          labelId: item.labelId,
          labelName: item.labelName,
        }));
      }
    });
  }

  /** 节点类型变更 */
  function handleNodeTypeChange(value) {
    form.value.labelId = undefined; // 重置标签选择
    updateLabelOptions(value);
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['nodeRef'].validate(valid => {
      if (valid) {
        // 构建保存的参数
        const siteData = {
          id: form.value.nodeId,
          siteName: form.value.nodeName,
          siteAbbr: form.value.nodeShortName,
          siteGroup: form.value.groupName,
          obtainTaskInterval: form.value.pullInterval,
          siteType: form.value.nodeType,
          scriptlabelId: form.value.labelId,
          status: form.value.status,
          unit: form.value.organization,
          address: form.value.address,
        };

        // 如果选择了克隆节点，则获取克隆节点的部分配置
        /*if (form.value.cloneNodeId && !form.value.nodeId) {
          getSite(form.value.cloneNodeId).then(response => {
            if (response.code === 200) {
              const cloneNode = response.data;
              // 保留用户已填写的信息，复制其他配置
              siteData.siteGroup = siteData.siteGroup || cloneNode.siteGroup;
              siteData.obtainTaskInterval =
                siteData.obtainTaskInterval || cloneNode.obtainTaskInterval;
              siteData.unit = siteData.unit || cloneNode.unit;
              siteData.address = siteData.address || cloneNode.address;

              // 保存节点
              saveNode(siteData);
            }
          });
        } else {
          // 直接保存节点
          saveNode(siteData);
        }*/
        saveNode(siteData);
      }
    });
  }

  /** 保存节点（新增/修改） */
  function saveNode(siteData) {
    // 判断是新增还是修改
    const isUpdate = siteData.id !== undefined;
    const apiCall = isUpdate ? updateSite : addSite;

    apiCall(siteData).then(response => {
      if (response.code === 200) {
        proxy.$modal.msgSuccess(isUpdate ? '修改成功' : '新增成功');
        open.value = false;
        getNodeList();
      } else {
        proxy.$modal.msgError(
          response.msg || (isUpdate ? '修改失败' : '新增失败'),
        );
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    proxy.$modal
      .confirm(`确定要删除节点 ${row.siteName} 吗？`)
      .then(function () {
        delSite(row.id).then(response => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess('删除成功');
            getNodeList();
          } else {
            proxy.$modal.msgError(response.msg || '删除失败');
          }
        });
      })
      .catch(() => {});
  }

  /** 启用/停用操作 */
  function handleStatusChange(row) {
    const text = row.status === '0' ? '停用' : '启用';
    proxy.$modal
      .confirm(`确认要${text}节点 ${row.siteName} 吗？`)
      .then(function () {
        changeSiteStatus({
          siteId: row.id,
          status: row.status === '0' ? '1' : '0',
        }).then(response => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess(`${text}成功`);
            getNodeList();
          } else {
            proxy.$modal.msgError(response.msg || `${text}失败`);
          }
        });
      })
      .catch(() => {});
  }



  /** 监听表单中的克隆选项变化 */
  watch(
    () => form.value.cloneNodeId,
    newVal => {
      if (newVal && form.value.nodeId === undefined) {
        // 自动填充选中节点的配置信息
        const cloneNode = nodeList.value.find(item => item.id === newVal);
        if (cloneNode) {
          /*const siteData = {
            id: form.value.nodeId,
            siteName: form.value.nodeName,
            siteAbbr: form.value.nodeShortName,
            siteGroup: form.value.groupName,
            obtainTaskInterval: form.value.pullInterval,
            siteType: form.value.nodeType,
            scriptlabelId: form.value.labelId,
            status: form.value.status,
            unit: form.value.organization,
            address: form.value.address,
          };*/
          // 保留用户已填写的信息，复制其他配置
          form.value.groupName = cloneNode.siteGroup;
          form.value.pullInterval = cloneNode.obtainTaskInterval;
          form.value.organization = cloneNode.unit;
          form.value.address = cloneNode.address;

          // 更新节点类型
          form.value.nodeType = cloneNode.siteType;
          // 更新标签选项
          updateLabelOptions(cloneNode.siteType);
          // 自动选中相同的标签
          form.value.labelId = cloneNode.scriptlabelId;

          // 填充其他信息但保留nodeName和nodeId
          /*form.value.groupName = cloneNode.groupName;
          form.value.pullInterval = cloneNode.pullInterval;
          form.value.adminUser = cloneNode.adminUser;
          form.value.organization = cloneNode.organization;
          form.value.address = cloneNode.address;*/
        }
      }
    },
  );

  /** 根据节点类型获取图标 */
  function getNodeIcon(nodeType) {
    switch (nodeType) {
      case '1': // 批次
        return Collection;
      case '2': // 源刊
        return Reading;
      case '3': // 高校
        return School;
      default:
        return Monitor;
    }
  }

  /** 根据节点类型获取样式类 */
  function getNodeTypeClass(nodeType) {
    switch (nodeType) {
      case '1': // 批次
        return 'node-type-batch';
      case '2': // 源刊
        return 'node-type-journal';
      case '3': // 高校
        return 'node-type-university';
      default:
        return 'node-type-default';
    }
  }

  /** 根据节点类型获取类型名称 */
  function getNodeTypeName(nodeType) {
    switch (nodeType) {
      case '1': // 批次
        return '批次';
      case '2': // 源刊
        return '源刊';
      case '3': // 高校
        return '高校';
      default:
        return '未知类型';
    }
  }

  // 查询分组搜索建议
  function queryGroupSearch(queryString, cb) {
    // 从已有的分组列表中筛选
    const results = queryString
      ? groupOptions.value.filter(group =>
          group.toLowerCase().includes(queryString.toLowerCase()),
        )
      : groupOptions.value;

    // 转换为autocomplete需要的格式
    const suggestions = results.map(group => ({
      value: group,
    }));

    cb(suggestions);
  }

  // 初始化
  onMounted(() => {
    getNodeList();
    // 获取所有分组选项
    getGroupOptions();
  });

  // 获取所有分组选项
  function getGroupOptions() {
    listSiteGroups().then(response => {
      if (response.code === 200) {
        groupOptions.value = response.data || [];
      }
    });
  }
</script>

<style lang="scss" scoped>
  .search-wrapper {
    background-color: #fff;
    padding: 16px;
    margin-bottom: 16px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  }

  .search-form {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .form-row {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 8px;

      .el-form-item {
        margin-bottom: 0;
        margin-right: 16px;
      }

      .search-input {
        width: 200px;
      }

      .action-btns {
        display: flex;
        gap: 8px;
        margin-left: auto;
      }
    }
  }

  .mb8 {
    margin-bottom: 8px;
  }

  .mt10 {
    margin-top: 10px;
  }

  .ml5 {
    margin-left: 5px;
  }

  .node-list {
    margin-top: 20px;
  }

  .node-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 16px;
  }

  .node-card {
    border-radius: 8px;
    height: 100%;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    overflow: hidden;
    border: none;

    &:hover {
      box-shadow: 0 4px 18px 0 rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    .node-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: none;
      padding: 12px 16px;
      margin-bottom: 0;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #409eff, #67c23a);
      }

      .node-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        display: flex;
        align-items: center;
        padding: 6px 0;
        border-radius: 6px;

        .node-type-tag {
          margin-right: 10px;

          .node-type-batch,
          .node-type-journal,
          .node-type-university,
          .node-type-default {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;

            .type-icon {
              margin-right: 4px;
              font-size: 14px;
            }
          }

          .node-type-batch {
            background-color: rgba(64, 158, 255, 0.1);
            color: #409eff;
            border: 1px solid rgba(64, 158, 255, 0.2);
          }

          .node-type-journal {
            background-color: rgba(103, 194, 58, 0.1);
            color: #67c23a;
            border: 1px solid rgba(103, 194, 58, 0.2);
          }

          .node-type-university {
            background-color: rgba(230, 162, 60, 0.1);
            color: #e6a23c;
            border: 1px solid rgba(230, 162, 60, 0.2);
          }

          .node-type-default {
            background-color: rgba(144, 147, 153, 0.1);
            color: #909399;
            border: 1px solid rgba(144, 147, 153, 0.2);
          }
        }
      }

      .node-actions {
        display: flex;
        gap: 8px;

        .action-btn {
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          border: none;

          &.favorite-btn {
            background-color: #409eff;
          }
        }
      }
    }

    .node-status-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      padding: 12px 16px;
      border-bottom: 1px dashed #e0e0e0;
      margin-bottom: 12px;
      background-color: #ffffff;

      .status-item {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #606266;

        .el-icon {
          margin-right: 5px;
          font-size: 15px;
          color: #909399;
        }

        &.status-tag {
          margin: 0 8px;
        }

        &.date {
          color: #909399;
          font-size: 13px;
        }
      }
    }

    .node-card-content {
      padding: 0 16px 16px;

      .node-info-row {
        display: flex;
        margin-bottom: 12px;
        align-items: center;

        .info-label {
          flex: 0 0 90px;
          color: #606266;
          font-weight: 500;
          display: flex;
          align-items: center;

          .el-icon {
            margin-right: 5px;
            font-size: 16px;
            color: #909399;
          }
        }

        .info-value {
          flex: 1;
          word-break: break-all;
          color: #303133;
          font-weight: 500;
        }
      }
    }

    .node-card-footer {
      margin-top: 0;
      border-top: 1px solid #f0f0f0;
      padding: 12px 16px;
      display: flex;
      justify-content: flex-end;
      gap: 16px;
    }
  }

  :deep(.el-pagination) {
    margin-top: 20px;
    text-align: right;
  }


</style>
