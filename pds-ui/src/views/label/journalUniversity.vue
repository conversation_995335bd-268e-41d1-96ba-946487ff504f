<template>
  <div class="app-container">
    <div class="back-container">
      <el-button type="primary" :text="true" icon="ArrowLeft" @click="goBack">
        返回
      </el-button>
      <div class="page-title">配置{{ typeText }}节点标签: {{ labelName }}</div>
    </div>

    <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
      <!-- 已分配给当前标签的期刊 -->
      <el-tab-pane label="已分配期刊" name="assigned">
        <journal-table
          ref="assignedTableRef"
          :is-assigned="true"
          :query-data="assignedQuery"
          :query-init="assignedQueryInit"
          :journal-list="assignedJournals"
          :total="assignedTotal"
          :loading="assignedLoading"
          @search="getAssignedList"
          @reset="resetAssignedSearch"
          @selection-change="handleAssignedSelectionChange"
          @select-all-change="val => (selectAllAssignedPage = val)"
          @edit-script="editJournalScript"
          @remove-journal="removeJournalAssignment"
        >
          <template #footer-buttons>
            <el-button
              type="primary"
              :disabled="assignedSelected.length === 0"
              @click="batchAssignScriptForAssigned"
              >批量分配脚本</el-button
            >
            <el-button
              type="danger"
              :disabled="assignedSelected.length === 0"
              @click="batchRemoveAssignments"
              >批量移除</el-button
            >
          </template>
        </journal-table>
      </el-tab-pane>

      <!-- 分配期刊 -->
      <el-tab-pane label="分配期刊" name="assign">
        <journal-table
          ref="journalTableRef"
          :is-assigned="false"
          :query-data="journalQuery"
          :query-init="journalQueryInit"
          :journal-list="journalList"
          :total="journalTotal"
          :loading="assignLoading"
          @search="getJournalList"
          @reset="resetJournalSearch"
          @selection-change="handleJournalSelectionChange"
          @select-all-change="val => (selectAllPage = val)"
          @add-to-label="addToLabel"
          @assign-script="assignScript"
          @remove-script="removeScript"
        >
          <template #footer-buttons>
            <el-button
              type="primary"
              :disabled="journalSelected.length === 0"
              @click="batchAssignToLabel"
              >批量应用到当前标签</el-button
            >
            <el-button
              type="primary"
              :disabled="journalSelected.length === 0"
              @click="batchAssignScript"
              >批量分配脚本</el-button
            >
            <el-button
              type="danger"
              :disabled="journalSelected.length === 0"
              @click="batchRemoveScript"
              >批量移除脚本</el-button
            >
          </template>
        </journal-table>
      </el-tab-pane>
    </el-tabs>

    <!-- 分配脚本对话框 -->
    <el-dialog
      v-model="scriptDialogVisible"
      :title="`分配${typeText}脚本`"
      width="800px"
      append-to-body
      @closed="handleScriptDialogClosed"
    >
      <div class="dialog-content">
        <div class="journal-info">
          <p><strong>期刊名称：</strong>{{ currentJournal.journalName }}</p>
          <p><strong>出版社：</strong>{{ currentJournal.publisher }}</p>
        </div>

        <el-form
          :inline="true"
          :model="scriptQuery"
          class="script-search-form"
          @submit.prevent="searchScripts"
        >
          <el-form-item label="脚本名称">
            <el-input
              v-model="scriptQuery.scriptName"
              placeholder="请输入脚本名称"
              clearable
              @keyup.enter.prevent="searchScripts"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="searchScripts"
              >搜索
            </el-button>
          </el-form-item>
        </el-form>

        <el-table :data="scriptList" border style="width: 100%" height="300px">
          <el-table-column type="index" label="序号" width="50" />
          <el-table-column label="脚本名称" prop="scriptName" />
          <el-table-column
            label="MD5值"
            prop="scriptMd5"
            show-overflow-tooltip
          />
          <el-table-column label="备注" prop="remark" show-overflow-tooltip />
          <el-table-column label="操作" width="100" align="center">
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="confirmAssignScript(scope.row)"
                >选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="scriptDialogVisible = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { computed, onMounted, reactive, ref, toRaw } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import {
    applyJournalToLabel,
    getAssignedJournalList,
    getToSelectJournal,
    listScript,
    removeJournalApply,
    removeJournalScript,
    saveJournalScript,
  } from '@/api/task/script';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import JournalTable from '@/components/JournalTable.vue';
  import { cloneAndNormalizeObject } from '@/utils/index.js';

  const props = defineProps({
    labelType: {
      type: String,
      required: true,
      validator: value => ['2', '3'].includes(value),
    },
  });

  const route = useRoute();
  const router = useRouter();
  const { proxy } = getCurrentInstance();

  // 根据labelType确定类型文本
  const typeText = computed(() => (props.labelType === '2' ? '源刊' : '高校'));

  // 标签信息
  const labelId = ref(route.query.id);
  const labelName = ref(route.query.name);

  const assignedLoading = ref(false);
  const assignLoading = ref(false);

  // 标签页控制
  const activeTab = ref('assigned');
  // 当前是否为已分配标签页
  const assignedFlag = computed(() => activeTab.value === 'assigned');

  const journalTableRef = ref();
  const assignedTableRef = ref();
  // 分配期刊全选所有页
  const selectAllPage = ref(false);
  // 已分配期刊全选所有页
  const selectAllAssignedPage = ref(false);

  // 已分配期刊
  const assignedJournals = ref([]);
  const assignedTotal = ref(0);
  const assignedSelected = ref([]);
  const assignedQuery = reactive({
    pageNum: 1,
    pageSize: 20,
    publisher: [],
    journalName: '',
    issnPrint: '',
    issnElectronic: '',
    scriptName: '',
    labelId: labelId.value,
  });
  const assignedQueryInit = proxy.$_.cloneDeep(toRaw(assignedQuery));

  // 期刊列表
  const journalList = ref([]);
  const journalTotal = ref(0);
  const journalSelected = ref([]);
  const journalQuery = reactive({
    pageNum: 1,
    pageSize: 20,
    publisher: [],
    journalName: null,
    issnPrint: null,
    issnElectronic: null,
    hasScript: null,
    scriptName: null,
    scriptType: props.labelType,
    labelId: labelId.value,
  });
  const journalQueryInit = proxy.$_.cloneDeep(toRaw(journalQuery));

  // 脚本列表
  const scriptList = ref([]);
  const scriptQuery = reactive({
    scriptName: '',
  });

  // 当前选中的期刊
  const currentJournal = ref({});
  const scriptDialogVisible = ref(false);

  // 是否为批量分配脚本操作
  const isBatchAssign = ref(false);
  const isBatchAssignForAssigned = ref(false);

  // 获取已分配期刊
  const getAssignedList = () => {
    const params = {
      ...assignedQuery,
      labelId: labelId.value,
      scriptType: props.labelType,
    };

    assignedLoading.value = true;
    getAssignedJournalList(params)
      .then(response => {
        assignedJournals.value = response.rows;
        assignedTotal.value = response.total;
      })
      .catch(() => {
        proxy.$modal.msgError('获取已分配期刊列表失败');
      })
      .finally(() => {
        assignedLoading.value = false;
      });
  };

  // 重置已分配期刊搜索
  const resetAssignedSearch = () => {
    // 重置查询条件
    Object.keys(assignedQueryInit).forEach(key => {
      assignedQuery[key] = assignedQueryInit[key];
    });
    selectAllAssignedPage.value = false;
    searchAssigned();
  };

  // 搜索已分配期刊
  const searchAssigned = () => {
    assignedQuery.pageNum = 1;
    getAssignedList();
  };

  // 获取期刊列表
  const getJournalList = () => {
    journalQuery.scriptType = props.labelType;
    journalQuery.labelId = labelId.value;

    assignLoading.value = true;
    getToSelectJournal(journalQuery)
      .then(response => {
        if (response.code === 200) {
          journalList.value = response.rows;
          journalTotal.value = response.total;
        } else {
          proxy.$modal.msgError(response.msg);
        }
      })
      .catch(error => {
        console.error('获取期刊列表失败', error);
        proxy.$modal.msgError('获取期刊列表失败');
      })
      .finally(() => {
        assignLoading.value = false;
      });
  };

  // 重置期刊搜索
  const resetJournalSearch = () => {
    // 重置查询条件
    Object.keys(journalQueryInit).forEach(key => {
      journalQuery[key] = journalQueryInit[key];
    });
    selectAllPage.value = false;
    searchJournals();
  };

  // 搜索期刊
  const searchJournals = () => {
    journalQuery.pageNum = 1;
    getJournalList();
  };

  // 获取脚本列表
  const getScriptList = () => {
    const queryParams = {
      scriptName: scriptQuery.scriptName,
      scriptType: props.labelType,
      pageSize: 200,
    };

    listScript(queryParams)
      .then(response => {
        if (response.code === 200) {
          scriptList.value = response.rows || [];
        } else {
          scriptList.value = [];
          proxy.$modal.msgError(response.msg);
        }
      })
      .catch(error => {
        console.error('获取脚本列表失败', error);
        scriptList.value = [];
        proxy.$modal.msgError('获取脚本列表失败');
      });
  };

  // 搜索脚本
  const searchScripts = () => {
    getScriptList();
  };

  // 已分配期刊多选
  const handleAssignedSelectionChange = selection => {
    assignedSelected.value = selection;
  };

  // 期刊多选
  const handleJournalSelectionChange = selection => {
    journalSelected.value = selection;
  };

  // 打开分配脚本对话框
  const assignScript = journal => {
    selectAllPage.value = false;

    currentJournal.value = journal;
    isBatchAssign.value = false;
    scriptDialogVisible.value = true;
    scriptQuery.scriptName = '';
    getScriptList();
  };

  // 更换期刊脚本
  const editJournalScript = journal => {
    assignScript(journal);
  };

  // 确认分配脚本
  const confirmAssignScript = script => {
    // 批量分配脚本
    if (isBatchAssign.value) {
      // 根据是否为已分配期刊选项卡，选择不同的参数
      const params = isBatchAssignForAssigned.value
        ? {
            ...cloneAndNormalizeObject(assignedQuery),
            journalIds: assignedSelected.value.map(item => item.journalId),
            scriptId: script.scriptId,
            selectAllPage: selectAllAssignedPage.value,
          }
        : {
            ...cloneAndNormalizeObject(journalQuery),
            journalIds: journalSelected.value.map(item => item.journalId),
            scriptId: script.scriptId,
            selectAllPage: selectAllPage.value,
          };

      // 构建确认信息
      let confirmMessage = '';
      if (isBatchAssignForAssigned.value) {
        // 已分配期刊选项卡
        if (selectAllAssignedPage.value) {
          confirmMessage = `确定要将脚本 "<strong style="color: red">${script.scriptName}</strong>" 分配给当前查询条件下的全部"<strong style="color: red">${assignedTotal.value}</strong>"个期刊吗？`;
        } else {
          confirmMessage = `确定要将脚本 "<strong style="color: red">${script.scriptName}</strong>" 分配给选中的 <strong style="color: red">${assignedSelected.value.length}</strong> 个期刊吗？`;
        }
      } else {
        // 分配期刊选项卡
        if (selectAllPage.value) {
          confirmMessage = `确定要将脚本 "<strong style="color: red">${script.scriptName}</strong>" 分配给当前查询条件下的全部"<strong style="color: red">${journalTotal.value}</strong>"个期刊吗？`;
        } else {
          confirmMessage = `确定要将脚本 "<strong style="color: red">${script.scriptName}</strong>" 分配给选中的 <strong style="color: red">${journalSelected.value.length}</strong> 个期刊吗？`;
        }
      }

      ElMessageBox.confirm(confirmMessage, '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      })
        .then(() => {
          // 根据选项卡设置不同的加载状态
          if (isBatchAssignForAssigned.value) {
            assignedLoading.value = true;
          } else {
            assignLoading.value = true;
          }

          saveJournalScript(params)
            .then(response => {
              if (response.code === 200) {
                proxy.$modal.msgSuccess(response.msg || '脚本分配成功');
                scriptDialogVisible.value = false;

                // 根据选项卡刷新不同的列表
                if (isBatchAssignForAssigned.value) {
                  getAssignedList();
                } else {
                  getJournalList();
                }
              } else {
                proxy.$modal.msgError(response.msg || '脚本分配失败');
              }
            })
            .catch(error => {
              console.error('脚本分配失败', error);
              proxy.$modal.msgError('脚本分配失败');
            })
            .finally(() => {
              // 重置状态
              if (isBatchAssignForAssigned.value) {
                assignedLoading.value = false;
                isBatchAssignForAssigned.value = false;
              } else {
                assignLoading.value = false;
              }
              isBatchAssign.value = false;
            });
        })
        .catch(() => {
          // 用户取消操作，重置状态
          isBatchAssign.value = false;
          isBatchAssignForAssigned.value = false;
        });
      return;
    }

    // 单个期刊分配脚本
    const journalId = currentJournal.value.journalId;
    const params = {
      journalIds: [journalId],
      scriptId: script.scriptId,
      selectAllPage: false,
      labelId: labelId.value,
    };

    ElMessageBox.confirm(
      `确定要将脚本 "<strong style="color: red">${script.scriptName}</strong>" 分配给当前期刊吗？`,
      '确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      },
    )
      .then(() => {
        assignLoading.value = true;
        saveJournalScript(params)
          .then(response => {
            if (response.code === 200) {
              if (assignedFlag.value) {
                getAssignedList();
              } else {
                getJournalList();
              }

              proxy.$modal.msgSuccess('脚本分配成功');
              scriptDialogVisible.value = false;
            } else {
              proxy.$modal.msgError(response.msg || '脚本分配失败');
            }
          })
          .catch(error => {
            console.error('脚本分配失败', error);
            proxy.$modal.msgError('脚本分配失败');
          })
          .finally(() => {
            assignLoading.value = false;
          });
      })
      .catch(() => {});
  };

  // 删除期刊脚本
  const removeScript = journal => {
    selectAllPage.value = false;
    ElMessageBox.confirm(
      `确定要删除期刊 "<strong style="color: red">${journal.journalName}</strong>" 的脚本吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      },
    )
      .then(() => {
        const params = {
          journalIds: [journal.journalId],
          selectAllPage: false,
        };

        assignLoading.value = true;
        removeJournalScript(params)
          .then(response => {
            if (response.code === 200) {
              if (assignedFlag.value) {
                getAssignedList();
              } else {
                getJournalList();
              }

              proxy.$modal.msgSuccess('脚本删除成功');
            } else {
              proxy.$modal.msgError(response.msg || '脚本删除失败');
            }
          })
          .catch(error => {
            console.error('脚本删除失败', error);
            proxy.$modal.msgError('脚本删除失败');
          })
          .finally(() => {
            assignLoading.value = false;
          });
      })
      .catch(() => {});
  };

  // 批量移除脚本
  const batchRemoveScript = () => {
    if (journalSelected.value.length === 0 && !selectAllPage.value) {
      proxy.$modal.msgError('请选择期刊');
      return;
    }

    // 构建确认信息
    let confirmMessage = '';
    if (selectAllPage.value) {
      confirmMessage = `确定要移除当前查询条件下全部"<strong style="color: red">${journalTotal.value}</strong>"个期刊的脚本吗？`;
    } else {
      confirmMessage = `确定要移除选中的 <strong style="color: red">${journalSelected.value.length}</strong> 个期刊的脚本吗？`;
    }

    ElMessageBox.confirm(confirmMessage, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
    })
      .then(() => {
        const params = {
          ...cloneAndNormalizeObject(journalQuery),
          journalIds: journalSelected.value.map(item => item.journalId),
          selectAllPage: selectAllPage.value,
        };

        assignLoading.value = true;
        removeJournalScript(params)
          .then(response => {
            if (response.code === 200) {
              proxy.$modal.msgSuccess(response.msg || '脚本移除成功');

              // 刷新期刊列表
              getJournalList();
            } else {
              proxy.$modal.msgError(response.msg || '脚本移除失败');
            }
          })
          .catch(error => {
            console.error('脚本移除失败', error);
            proxy.$modal.msgError('脚本移除失败');
          })
          .finally(() => {
            assignLoading.value = false;
          });
      })
      .catch(() => {});
  };

  // 添加期刊到标签
  const addToLabel = journal => {
    selectAllPage.value = false;

    // 检查期刊是否有脚本
    if (!journal.scriptId) {
      proxy.$modal.msgError('该期刊没有分配脚本，请先分配脚本');
      return;
    }

    ElMessageBox.confirm(
      `确定要将期刊 "<strong style="color: red">${journal.journalName}</strong>" 添加到标签吗？`,
      '确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      },
    )
      .then(() => {
        const params = {
          journalIds: [journal.journalId],
          labelId: labelId.value,
          selectAllPage: false,
        };

        assignLoading.value = true;
        applyJournalToLabel(params)
          .then(response => {
            if (response.code === 200) {
              proxy.$modal.msgSuccess(
                response.msg || `已将期刊 "${journal.journalName}" 添加到标签`,
              );
              // 刷新期刊列表
              getJournalList();
              // 刷新已分配列表
              getAssignedList();
            } else {
              proxy.$modal.msgError(response.msg || '添加到标签失败');
            }
          })
          .catch(error => {
            console.error('添加到标签失败', error);
            proxy.$modal.msgError('添加到标签失败');
          })
          .finally(() => {
            assignLoading.value = false;
          });
      })
      .catch(() => {});
  };

  // 批量添加期刊到标签
  const batchAssignToLabel = () => {
    if (journalSelected.value.length === 0 && !selectAllPage.value) {
      proxy.$modal.msgError('请选择期刊');
      return;
    }

    // 检查选中的期刊是否都有脚本
    if (!selectAllPage.value) {
      const noScriptJournals = journalSelected.value.filter(
        item => !item.scriptId,
      );
      if (noScriptJournals.length > 0) {
        proxy.$modal.msgError(
          '选中的期刊中有 ' +
            noScriptJournals.length +
            ' 个没有分配脚本，请先分配脚本',
        );
        return;
      }
    }

    // 构建确认信息
    let confirmMessage = '';
    if (selectAllPage.value) {
      confirmMessage = `确定要将当前查询条件下的全部"<strong style="color: red">${journalTotal.value}</strong>"个期刊应用到标签吗？`;
    } else {
      confirmMessage = `确定要将选中的 <strong style="color: red">${journalSelected.value.length}</strong> 个期刊应用到标签吗？`;
    }

    ElMessageBox.confirm(confirmMessage, '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
    })
      .then(() => {
        const params = {
          ...cloneAndNormalizeObject(journalQuery),
          journalIds: journalSelected.value.map(item => item.journalId),
          labelId: labelId.value,
          selectAllPage: selectAllPage.value,
        };

        assignLoading.value = true;
        applyJournalToLabel(params)
          .then(response => {
            if (response.code === 200) {
              proxy.$modal.msgSuccess(response.msg || '应用到标签成功');
              // 刷新期刊列表
              getJournalList();
              // 刷新已分配列表
              getAssignedList();
            } else {
              proxy.$modal.msgError(response.msg || '应用到标签失败');
            }
          })
          .catch(error => {
            console.error('应用到标签失败', error);
            proxy.$modal.msgError('应用到标签失败');
          })
          .finally(() => {
            assignLoading.value = false;
          });
      })
      .catch(() => {});
  };

  // 批量移除分配
  const batchRemoveAssignments = () => {
    if (assignedSelected.value.length === 0 && !selectAllAssignedPage.value) {
      ElMessage.warning('请选择要移除的期刊');
      return;
    }

    // 构建确认信息
    let confirmMessage = '';
    if (selectAllAssignedPage.value) {
      confirmMessage = `确定要移除当前查询条件下全部"<strong style="color: red">${assignedTotal.value}</strong>"个期刊与标签的关联吗？`;
    } else {
      confirmMessage = `确定要移除选中的 <strong style="color: red">${assignedSelected.value.length}</strong> 个期刊与标签的关联吗？`;
    }

    ElMessageBox.confirm(confirmMessage, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
    })
      .then(() => {
        const params = {
          ...cloneAndNormalizeObject(assignedQuery),
          journalIds: assignedSelected.value.map(item => item.journalId),
          selectAllPage: selectAllAssignedPage.value,
          labelId: labelId.value,
        };

        assignedLoading.value = true;
        removeJournalApply(params)
          .then(response => {
            if (response.code === 200) {
              ElMessage.success(response.msg || '移除成功');
              // 重置选择状态
              selectAllAssignedPage.value = false;
              // 刷新列表
              getAssignedList();
              getJournalList();
            } else {
              proxy.$modal.msgError(response.msg || '移除失败');
            }
          })
          .catch(error => {
            console.error('移除失败', error);
            ElMessage.error('移除失败');
          })
          .finally(() => {
            assignedLoading.value = false;
          });
      })
      .catch(() => {});
  };

  // 移除单个期刊分配
  const removeJournalAssignment = row => {
    // console.log(row);
    ElMessageBox.confirm(
      `确定要移除期刊 "<strong style="color: red">${row.journalName}</strong>" 与标签的关联吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
      },
    )
      .then(() => {
        assignedLoading.value = true;
        removeJournalApply({
          journalIds: [row.journalId],
          selectAllPage: false,
          labelId: labelId.value,
        })
          .then(response => {
            if (response.code === 200) {
              ElMessage.success(response.msg || '移除成功');
              getAssignedList();
              getJournalList();
            } else {
              proxy.$modal.msgError(response.msg || '移除失败');
            }
          })
          .catch(error => {
            console.error('移除失败', error);
            ElMessage.error('移除失败');
          })
          .finally(() => {
            assignedLoading.value = false;
          });
      })
      .catch(() => {});
  };

  // 返回上一级
  const goBack = () => {
    router.go(-1);
  };

  // 批量分配脚本
  const batchAssignScript = () => {
    if (journalSelected.value.length === 0 && !selectAllPage.value) {
      proxy.$modal.msgError('请选择期刊');
      return;
    }

    // 打开脚本选择对话框，复用脚本选择功能
    scriptQuery.scriptName = '';
    getScriptList();

    // 设置当前操作为批量分配
    isBatchAssign.value = true;
    scriptDialogVisible.value = true;
  };

  // 已分配期刊的批量分配脚本
  const batchAssignScriptForAssigned = () => {
    if (assignedSelected.value.length === 0 && !selectAllAssignedPage.value) {
      proxy.$modal.msgError('请选择期刊');
      return;
    }

    // 打开脚本选择对话框，复用脚本选择功能
    scriptQuery.scriptName = '';
    getScriptList();

    // 设置当前操作为批量分配，并标记为已分配期刊
    isBatchAssign.value = true;
    isBatchAssignForAssigned.value = true;
    scriptDialogVisible.value = true;
  };

  // 处理标签页切换
  const handleTabChange = tab => {
    if (tab === 'assigned') {
      resetAssignedSearch();
    } else if (tab === 'assign') {
      resetJournalSearch();
    }
  };

  // 初始化
  onMounted(() => {
    // 初始化获取数据
    searchAssigned();
    searchJournals();
  });

  // 处理脚本对话框关闭
  const handleScriptDialogClosed = () => {
    // 重置状态
    isBatchAssign.value = false;
    isBatchAssignForAssigned.value = false;
  };
</script>

<style lang="scss" scoped>
  .app-container {
    padding-bottom: 20px;

    .back-container {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      :deep(.el-button) {
        margin: 0;
        padding: 0;
        height: auto;
        line-height: normal;
        display: inline-flex;
        align-items: center;
      }

      .page-title {
        margin-left: 20px;
        font-size: 18px;
        font-weight: bold;
        line-height: 1.5;
      }
    }

    .dialog-content {
      .journal-info {
        margin-bottom: 15px;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .script-search-form {
        margin-bottom: 15px;
      }
    }
  }
</style>
