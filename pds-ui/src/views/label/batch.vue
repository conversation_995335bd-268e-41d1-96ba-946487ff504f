<template>
  <div class="app-container">
    <div class="back-container">
      <el-button type="primary" :text="true" icon="ArrowLeft" @click="goBack">
        返回
      </el-button>
      <div class="page-title">配置批次节点标签: {{ labelName }}</div>
    </div>

    <!-- 已选脚本列表 -->
    <div class="selected-scripts-container">
      <div class="section-title">已选择脚本</div>
      <el-table :data="selectedScripts" border style="width: 100%">
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column label="脚本名称" prop="scriptName" />
        <el-table-column label="MD5值" prop="scriptMd5" show-overflow-tooltip />
        <el-table-column label="备注" prop="remark" show-overflow-tooltip />
        <el-table-column label="排序" width="160">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.sort"
              :min="1"
              :max="999"
              size="small"
              @change="handleSortChange"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button
              type="danger"
              link
              icon="Delete"
              @click="removeScript(scope.row, scope.$index)"
              >移除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="action-bar">
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
      </div>
    </div>

    <!-- 可选脚本列表 -->
    <div class="available-scripts-container">
      <div class="section-title">可选择脚本</div>

      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="脚本名称">
          <el-input
            v-model="queryParams.scriptName"
            placeholder="请输入脚本名称"
            clearable
            @keyup.enter="searchScripts"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="searchScripts"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="filteredScripts" border style="width: 100%">
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column label="脚本名称" prop="scriptName" />
        <el-table-column label="MD5值" prop="scriptMd5" show-overflow-tooltip />
        <el-table-column label="备注" prop="remark" show-overflow-tooltip />
        <!--        <el-table-column label="状态" prop="status" width="100">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>-->
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              link
              icon="Plus"
              :disabled="isScriptSelected(scope.row.scriptId)"
              @click="addScript(scope.row)"
              >添加</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getScriptList"
      />
    </div>
  </div>
</template>

<script setup>
  import { reactive, ref, computed, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import {
    listScript,
    saveLabelAndScript,
    getScriptListOfLabel,
  } from '@/api/task/script';

  const route = useRoute();
  const router = useRouter();
  const { proxy } = getCurrentInstance();
  // const { sys_normal_disable } = proxy.useDict('sys_normal_disable');

  // 标签信息
  const labelId = ref(route.query.id);
  const labelName = ref(route.query.name);
  const labelType = ref(route.query.labelType);

  // 已选脚本列表
  const selectedScripts = ref([]);

  // 可选脚本列表
  const scriptList = ref([]);
  const total = ref(0);
  const loading = ref(false);
  const loadingSelected = ref(false);

  // 查询参数
  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    scriptName: '',
    scriptType: '1', // 批次类型
  });

  // 过滤后的脚本列表
  const filteredScripts = computed(() => {
    return scriptList.value;
  });

  // 检查脚本是否已经被选择
  const isScriptSelected = scriptId => {
    return selectedScripts.value.some(item => item.scriptId === scriptId);
  };

  // 脚本排序变更
  const handleSortChange = () => {
    selectedScripts.value.sort((a, b) => a.sort - b.sort);
  };

  // 获取脚本列表
  const getScriptList = () => {
    loading.value = true;
    listScript(queryParams)
      .then(response => {
        scriptList.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  // 搜索脚本
  const searchScripts = () => {
    queryParams.pageNum = 1;
    getScriptList();
  };

  // 重置搜索
  const resetSearch = () => {
    queryParams.scriptName = '';
    searchScripts();
  };

  // 添加脚本到已选
  const addScript = script => {
    if (isScriptSelected(script.scriptId)) {
      return;
    }

    // 计算最大排序值
    const maxSort =
      selectedScripts.value.length > 0
        ? Math.max(...selectedScripts.value.map(item => item.sort))
        : 0;

    selectedScripts.value.push({
      ...script,
      sort: maxSort + 1,
    });

    proxy.$modal.msgSuccess('已添加脚本');
  };

  // 从已选中移除脚本
  const removeScript = (script, index) => {
    selectedScripts.value.splice(index, 1);
    proxy.$modal.msgSuccess('已移除脚本');
  };

  // 保存配置
  const saveConfig = () => {
    if (selectedScripts.value.length === 0) {
      proxy.$modal.msgWarning('请至少选择一个脚本');
      return;
    }
    proxy.$modal
      .confirm('确认要保存当前配置吗？')
      .then(() => {
        // 准备提交数据
        const items = selectedScripts.value.map(script => {
          return {
            scriptId: script.scriptId,
            scriptlabelId: parseInt(labelId.value),
            sort: script.sort,
          };
        });

        const data = {
          items: items,
        };

        saveLabelAndScript(data).then(response => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess('保存成功');
          } else {
            proxy.$modal.msgError(response.msg || '保存失败');
          }
        });
      })
      .catch(() => {
        // 用户取消操作
      });
  };

  // 返回上一级
  const goBack = () => {
    router.go(-1);
  };

  // 获取已选脚本列表
  const getSelectedScripts = async () => {
    if (!labelId.value) {
      return Promise.resolve();
    }

    loadingSelected.value = true;
    try {
      const response = await getScriptListOfLabel(labelId.value);
      if (response.code === 200 && response.data) {
        selectedScripts.value = response.data;
      }
      return Promise.resolve();
    } catch (error) {
      console.error('获取已选脚本列表失败', error);
      proxy.$modal.msgError('获取已选脚本列表失败');
      return Promise.reject(error);
    } finally {
      loadingSelected.value = false;
    }
  };

  // 初始化加载
  onMounted(async () => {
    try {
      // 先加载已选脚本
      await getSelectedScripts();
      // 再加载可选脚本
      getScriptList();
    } catch (error) {
      console.error('初始化加载失败', error);
      // 即使获取已选脚本失败，也尝试加载可选脚本
      getScriptList();
    }
  });
</script>

<style lang="scss" scoped>
  .app-container {
    .back-container {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      :deep(.el-button) {
        margin: 0;
        padding: 0;
        height: auto;
        line-height: normal;
        display: inline-flex;
        align-items: center;
      }

      .page-title {
        margin-left: 20px;
        font-size: 18px;
        font-weight: bold;
        line-height: 1.5;
      }
    }

    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      padding-left: 10px;
      border-left: 3px solid #409eff;
    }

    .selected-scripts-container {
      margin-bottom: 30px;

      .action-bar {
        margin-top: 15px;
        text-align: right;
      }
    }

    .available-scripts-container {
      .search-form {
        margin-bottom: 15px;
      }
    }
  }
</style>
