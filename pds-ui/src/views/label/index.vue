<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="标签名称" prop="labelName">
        <el-input
          v-model="queryParams.labelName"
          placeholder="请输入标签名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="labelType">
        <el-select
          v-model="queryParams.labelType"
          placeholder="标签类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in script_type"
            :key="`tf-sctp-${dict.value}`"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!--      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="标签状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>-->
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['label:index']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          >新增标签
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['label:index']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          >删除
        </el-button>
      </el-col>
      <right-toolbar
        v-model:show-search="showSearch"
        @query-table="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="labelList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="labelId" width="80" />
      <el-table-column
        label="标签名称"
        align="center"
        prop="labelName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="类型" align="center" prop="labelType" width="100">
        <template #default="scope">
          <dict-tag :options="script_type" :value="scope.row.labelType" />
        </template>
      </el-table-column>
      <!--      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>-->
      <el-table-column label="节点" align="center" width="100">
        <template #default="scope">
          <el-button type="primary" link @click="showNodes(scope.row)">{{
            scope.row.nodeCount
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="100"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['label:index']"
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            >编辑
          </el-button>
          <el-button
            v-hasPermi="['label:index']"
            link
            type="success"
            icon="Setting"
            @click="navigateToConfig(scope.row)"
            >脚本配置
          </el-button>
          <el-button
            v-hasPermi="['label:index']"
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改脚本标签对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="labelRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="标签名称" prop="labelName">
          <el-input v-model="form.labelName" placeholder="请输入标签名称" />
        </el-form-item>
        <el-form-item prop="labelType">
          <template #label>
            <span>标签类型</span>
            <el-tooltip
              content="标签类型一旦设置后不可修改，请谨慎选择"
              placement="top"
            >
              <el-icon class="warning-icon"><WarningFilled /></el-icon>
            </el-tooltip>
          </template>

          <el-select
            v-model="form.labelType"
            placeholder="请选择"
            :disabled="form.isEdit"
          >
            <el-option
              v-for="dict in script_type"
              :key="`sctp-${dict.value}`"
              :label="dict.label"
              :value="dict.value"
              >{{ dict.label }}</el-option
            >
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="`sys_normal_disable-${dict.value}`"
              :value="dict.value"
              >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>-->
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入标签备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 显示节点列表对话框 -->
    <el-dialog
      v-model="nodeOpen"
      title="关联节点列表"
      width="600px"
      append-to-body
    >
      <el-table
        v-loading="loading"
        :data="nodeList"
        stripe
        style="width: 100%"
        :empty-text="'暂无关联节点数据'"
      >
        <el-table-column prop="nodeId" label="节点ID" width="80" />
        <el-table-column prop="nodeName" label="节点名称" />
        <el-table-column prop="nodeType" label="节点类型">
          <template #default="scope">
            <dict-tag :options="script_type" :value="scope.row.nodeType" />
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup name="Label">
  import { getCurrentInstance, reactive, ref, toRefs } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    listLabel,
    saveLabel,
    delLabel,
    getSiteInfo,
  } from '@/api/task/label';
  import { WarningFilled } from '@element-plus/icons-vue';

  const router = useRouter();
  const { proxy } = getCurrentInstance();
  const { script_type } = proxy.useDict('script_type');

  const labelList = ref([]);
  const open = ref(false);
  const nodeOpen = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  const nodeList = ref([]);
  const dateRange = ref([]);

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      labelName: undefined,
      labelType: undefined,
      status: undefined,
      createTime: undefined,
    },
    rules: {
      labelName: [
        { required: true, message: '标签名称不能为空', trigger: 'blur' },
      ],
      labelType: [
        { required: true, message: '标签类型不能为空', trigger: 'change' },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询标签列表 */
  function getList() {
    loading.value = true;
    listLabel(proxy.addDateRange(queryParams.value, dateRange.value)).then(
      response => {
        labelList.value = response.rows;
        total.value = response.total;
        loading.value = false;
      },
    );
  }

  /** 显示关联节点 */
  function showNodes(row) {
    nodeList.value = [];
    nodeOpen.value = true;
    loading.value = true;

    // 调用API获取关联节点信息
    getSiteInfo(row.labelId)
      .then(response => {
        if (response.code === 200) {
          // 转换后端返回的数据为前端需要的格式
          nodeList.value = response.data.map(item => {
            return {
              nodeId: item.id,
              nodeName: item.siteName,
              nodeType: item.siteType,
            };
          });
        } else {
          proxy.$modal.msgError(response.msg || '获取节点列表失败');
        }
        loading.value = false;
      })
      .catch(err => {
        proxy.$modal.msgError('获取节点列表失败：' + err.message);
        loading.value = false;
      });
  }

  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }

  /** 表单重置 */
  function reset() {
    form.value = {
      labelId: undefined,
      labelName: undefined,
      labelType: undefined,
      remark: undefined,
      status: '0',
      isEdit: false, // 重置为非编辑模式
    };
    proxy.resetForm('labelRef');
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('queryRef');
    handleQuery();
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.labelId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加标签';
  }

  /**修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const labelId = row.labelId || ids.value[0];
    // 获取当前行数据
    form.value = {
      labelId: row.labelId,
      labelName: row.labelName,
      labelType: row.labelType,
      status: row.status,
      remark: row.remark,
      isEdit: true, // 标记为编辑模式
    };
    open.value = true;
    title.value = '修改标签';
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['labelRef'].validate(valid => {
      if (valid) {
        // 弹出确认对话框
        const confirmMessage = form.value.labelId
          ? `确认要修改"${form.value.labelName}"标签吗？`
          : `确认要新增"${form.value.labelName}"标签吗？`;

        proxy.$modal
          .confirm(confirmMessage)
          .then(() => {
            // 显示提交中
            proxy.$modal.loading('正在提交数据，请稍候...');

            // 调用保存API
            saveLabel(form.value)
              .then(res => {
                proxy.$modal.closeLoading();
                if (res.code === 200) {
                  proxy.$modal.msgSuccess(
                    form.value.labelId ? '修改成功' : '添加成功',
                  );
                  open.value = false;
                  getList();

                  // 如果需要导航到配置页面
                  /*if (res.data) {
                    const labelInfo = {
                      labelId: res.data,
                      labelName: form.value.labelName,
                      labelType: form.value.labelType,
                    };
                    navigateToConfig(labelInfo);
                  }*/
                } else {
                  proxy.$modal.msgError(res.msg || '操作失败');
                }
              })
              .catch(err => {
                proxy.$modal.closeLoading();
                proxy.$modal.msgError('操作失败：' + err.message);
              });
          })
          .catch(() => {}); // 用户取消操作时不做任何处理
      }
    });
  }

  /** 导航到配置页面 */
  function navigateToConfig(row) {
    // console.log(row);
    if (row.labelType === '1') {
      // 批次标签配置
      router.push({
        path: '/label/batch',
        query: {
          id: row.labelId,
          name: row.labelName,
        },
      });
    } else if (row.labelType === '2') {
      // 源刊标签配置
      router.push({
        path: '/label/journal',
        query: {
          id: row.labelId,
          name: row.labelName,
        },
      });
    } else if (row.labelType === '3') {
      // 高校标签配置
      router.push({
        path: '/label/university',
        query: {
          id: row.labelId,
          name: row.labelName,
        },
      });
    }
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const labelIds = row.labelId || ids.value;
    proxy.$modal
      .confirm('是否确认删除标签编号为"' + labelIds + '"的数据项？')
      .then(function () {
        // 调用删除API
        return delLabel(labelIds);
      })
      .then(() => {
        proxy.$modal.msgSuccess('删除成功');
        getList();
      })
      .catch(() => {});
  }

  getList();
</script>

<style lang="scss">
  .app-container {
    padding-bottom: 20px;
    .el-pagination {
      float: right;
      margin-top: 10px;
    }
  }
</style>
