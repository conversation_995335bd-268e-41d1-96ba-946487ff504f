<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form
        ref="queryRef"
        :inline="true"
        :model="queryParams"
        class="search-form"
      >
        <el-form-item label="任务号" prop="id">
          <el-input
            v-model="queryParams.id"
            placeholder="请输入任务号"
            clearable
            class="search-input"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="任务名称" prop="description">
          <el-input
            v-model="queryParams.description"
            placeholder="请输入任务名称"
            clearable
            class="search-input"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="发布时间" prop="dateRange">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            class="search-date"
          />
        </el-form-item>
        <el-form-item
          v-hasRole="['admin', 'pdsAdmin']"
          label="发布人"
          prop="creator"
        >
          <el-select
            v-model="queryParams.creator"
            placeholder="请选择发布人"
            clearable
            class="search-input"
          >
            <el-option
              v-for="creator in creatorOptions"
              :key="`crt-${creator.id}`"
              :label="creator.text"
              :value="creator.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 系统状态信息 -->
    <div class="system-status-info">
      <div class="status-item">
        <span class="status-label">
          <el-tooltip
            content="小蚂蚁已经拉取，但还未完成的任务总数量"
            placement="top"
          >
            系统下载中任务数：
          </el-tooltip>
        </span>
        <span class="status-value" v-text="downloadingTotal"></span>
      </div>
      <div class="status-item">
        <span class="status-label">
          <el-tooltip content="等待小蚂蚁拉取的任务总数量" placement="top">
            待拉取任务数：
          </el-tooltip>
        </span>
        <span class="status-value" v-text="waitingTotal"></span>
      </div>
    </div>

    <!-- 任务列表 -->
    <div v-loading="loadingFlag" class="task-list-container">
      <el-empty v-if="taskList.length === 0" description="暂无数据" />

      <div v-else class="task-list">
        <div
          v-for="task in taskList"
          :key="`tsk-${task.taskId}`"
          class="task-card"
        >
          <!-- 任务卡片头部 -->
          <div class="task-header">
            <div class="task-title">
              <el-tooltip :content="task.taskId" placement="top">
                <span class="task-id">{{ task.taskId }}</span>
              </el-tooltip>
              <span class="task-name">{{ task.taskName }}</span>
            </div>
            <div class="task-actions">
              <el-button type="primary" link @click="handleViewDetail(task)"
                >查看详情
              </el-button>
              <el-button type="success" link @click="handleViewLog(task)"
                >查看日志
              </el-button>

              <el-button
                v-if="task.status === 'paused'"
                type="success"
                link
                @click="handleTaskControl(task)"
              >
                恢复任务
              </el-button>
              <el-button
                v-if="task.status === 'assigned'"
                type="warning"
                link
                @click="handleTaskControl(task)"
              >
                暂停任务
              </el-button>

              <el-button type="info" link @click="handleDownloadResult(task)"
                >下载结果
              </el-button>
              <el-popconfirm
                title="确定要删除该任务吗？删除后无法恢复，且该任务的文献全文将不再下载"
                @confirm="handleDeleteTask(task)"
              >
                <template #reference>
                  <el-button type="danger" link>删除</el-button>
                </template>
              </el-popconfirm>
            </div>
          </div>

          <!-- 任务卡片内容 -->
          <div class="task-content">
            <div class="task-info-row">
              <div class="task-info-item">
                <span class="info-label">文献总数：</span>
                <span class="info-value">{{ task.totalCount }}</span>
              </div>
              <div class="task-info-item">
                <span class="info-label">状态：</span>
                <el-tag :type="getStatusType(task.status)"
                  >{{ task.statusStr }}
                </el-tag>
              </div>
              <div class="task-info-item">
                <span class="info-label">发布人：</span>
                <span class="info-value">{{ task.creator }}</span>
              </div>
              <div class="task-info-item">
                <span class="info-label">发布时间：</span>
                <span class="info-value">{{ task.createTime }}</span>
              </div>
            </div>

            <div class="task-info-row">
              <div class="task-info-item">
                <span class="info-label">最后更新：</span>
                <span class="info-value">{{ task.updateTime }}</span>
              </div>
              <div class="task-info-item">
                <span class="info-label">优先级：</span>
                <el-tag :type="getPriorityType(task.priority)" size="small">
                  {{ getPriorityText(task.priority) }}
                </el-tag>
              </div>
              <div class="task-info-item">
                <span class="info-label">测试任务：</span>
                <el-tag :type="task.isTest ? 'warning' : 'info'" size="small">
                  {{ task.isTest ? '是' : '否' }}
                </el-tag>
                <span v-if="task.isTest && task.siteId">
                  &nbsp;指定节点id：{{ task.siteId }}</span
                >
              </div>
              <div class="task-info-item">
                <span class="info-label">节点类型：</span>
                <div class="node-types">
                  <el-tag
                    v-if="task.nodeTypes.includes('1')"
                    type="primary"
                    size="small"
                    >批次
                  </el-tag>
                  <el-tag
                    v-if="task.nodeTypes.includes('2')"
                    type="success"
                    size="small"
                    >源刊
                  </el-tag>
                  <el-tag
                    v-if="task.nodeTypes.includes('3')"
                    type="warning"
                    size="small"
                    >高校
                  </el-tag>
                </div>
              </div>
            </div>

            <!-- 下载情况 -->
            <div class="download-status">
              <div class="download-header">
                <span class="info-label">下载情况：</span>
                <el-progress
                  :percentage="getCompletionRate(task)"
                  :status="getProgressStatus(task.status)"
                />
              </div>
              <div class="download-items">
                <div class="download-item">
                  <span class="item-label">
                    <el-tooltip content="文献PDF已存在的数量" placement="top">
                      存量库:
                    </el-tooltip>
                  </span>
                  <span class="item-value">{{ task.stockCount }}</span>
                </div>
                <div class="download-item">
                  <span class="item-label">
                    <el-tooltip content="小蚂蚁成功下载的数量" placement="top">
                      已下载:
                    </el-tooltip>
                  </span>
                  <span class="item-value">{{ task.downloadedCount }}</span>
                </div>
                <div class="download-item">
                  <span class="item-label">
                    <el-tooltip content="小蚂蚁下载失败的数量" placement="top">
                      下载失败:
                    </el-tooltip>
                  </span>
                  <span class="item-value">{{ task.failedCount }}</span>
                </div>
                <div class="download-item">
                  <span class="item-label">
                    <el-tooltip
                      content="文献题录数据不存在的数量"
                      placement="top"
                    >
                      库中不存在:
                    </el-tooltip>
                  </span>
                  <span class="item-value">{{ task.notExistCount }}</span>
                </div>
                <div class="download-item">
                  <span class="item-label">
                    <el-tooltip
                      content="任务等待中，以及小蚂蚁已拉取但是还未完成的数量"
                      placement="top"
                    >
                      待下载:
                    </el-tooltip>
                  </span>
                  <span class="item-value">{{ task.pendingCount }}</span>
                </div>
              </div>
            </div>

            <!-- 站点分组
            <div class="site-group">
              <span class="info-label">站点分组：</span>
              <span class="info-value">{{ task.siteGroup || '不限' }}</span>
            </div>
            -->
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 任务详情弹窗 -->
    <el-dialog
      v-model="detailDialog.visible"
      :title="`任务详情-${detailDialog.taskName}`"
      width="80%"
      append-to-body
    >
      <div class="detail-header">
        <div class="detail-search">
          <div class="search-controls">
            <el-input
              v-model="detailDialog.searchLiteratureId"
              placeholder="请输入文献ID"
              clearable
              style="width: 200px; margin-right: 10px"
            />
            <el-select
              v-model="detailDialog.searchStatus"
              placeholder="选择任务类型"
              clearable
              style="width: 200px; margin-right: 10px"
            >
              <el-option
                v-for="item in paperStatusOptions"
                :key="`pap-status-${item.id}`"
                :label="item.text"
                :value="item.id"
              />
            </el-select>
            <el-button type="primary" @click="filterDetailList">搜索</el-button>
            <el-button @click="resetDetailFilter">重置</el-button>
          </div>

          <div class="detail-actions">
            <el-button type="danger" @click="handleDownloadFailedList"
              >下载失败列表
            </el-button>
            <el-button type="success" @click="handleDownloadSuccessList"
              >下载成功列表
            </el-button>
          </div>
        </div>
      </div>

      <el-table
        v-loading="paperDialogLoading"
        :data="paperDialogList"
        style="width: 100%; margin-top: 10px"
        max-height="500"
      >
        <el-table-column prop="docId" label="文献ID" width="180" />
        <el-table-column prop="pmid" label="PMID" width="120" />
        <el-table-column
          prop="title"
          label="文献标题"
          min-width="180"
          :show-overflow-tooltip="true"
        />
        <el-table-column prop="taskType" label="任务类型" width="120">
          <template #default="scope">
            <el-tag :type="getTaskTypeTag(scope.row.taskType)"
              >{{ scope.row.taskType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="executeLog"
          label="执行情况"
          min-width="230"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <div
              v-for="(log, index) in scope.row.executeLog"
              :key="`exe-log-${index}`"
              class="execute-log-item"
            >
              {{ log }}
            </div>
            <el-button
              v-if="scope.row.executeLog && scope.row.executeLog.length > 1"
              link
              type="primary"
              @click="viewFullLog(scope.row)"
            >
              查看完整日志
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="downloadStatus" label="下载状态" width="180">
          <template #default="scope">
            <div v-if="scope.row.downloadStatus === '失败'">
              <el-tag type="danger">
                {{ scope.row.downloadStatus }}
              </el-tag>
              <el-button
                style="margin-left: 15px"
                type="primary"
                round
                size="small"
                @click="handleOptView(scope.row)"
                >手动上传
              </el-button>
            </div>
            <el-tag
              v-else-if="scope.row.downloadStatus === '完成'"
              type="success"
            >
              {{ scope.row.downloadStatus }}
            </el-tag>
            <el-tag v-else type="info">
              {{ scope.row.downloadStatus }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="detailDialog.pageNum"
          v-model:page-size="detailDialog.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="paperDialogTotal"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleDetailDialogSizeChange"
          @current-change="handleDetailDialogCurrentChange"
        />
      </div>
    </el-dialog>

    <!-- 日志弹窗 -->
    <el-dialog
      v-model="logDialog.visible"
      title="任务日志"
      width="80%"
      append-to-body
    >
      <div class="log-content">
        <div
          v-for="(log, index) in logDialog.logs"
          :key="`log-${index}`"
          class="log-item"
          v-html="log"
        ></div>
      </div>
    </el-dialog>

    <!-- 执行日志详情弹窗 -->
    <el-dialog
      v-model="fullLogDialog.visible"
      title="执行详情"
      width="700px"
      append-to-body
    >
      <div class="full-log-content">
        <div
          v-for="(log, index) in fullLogDialog.logs"
          :key="index"
          class="log-item"
        >
          {{ log }}
        </div>
      </div>
    </el-dialog>

    <!-- 文献详情弹窗 -->
    <uploadPdf
      v-if="detailDialogDocId"
      :key="`du-${detailDialogDocId}-${detailDialogPaperId}`"
      v-model="detailDialogVisible"
      :doc-id="detailDialogDocId"
      :paper-id="detailDialogPaperId"
    >
    </uploadPdf>
  </div>
</template>

<script setup>
  import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import { ElMessage } from 'element-plus';
  import {
    allPaperStatus,
    getAllPdsUsers,
    listTask,
    taskArticleViewPage,
    getTaskLogs,
    updateTaskStatus,
    downloadTaskArticleAttachment,
    deleteTask,
  } from '@/api/task/task';
  import UploadPdf from '@/views/taskTrace/uploadPdf.vue';
  import { formatDate } from '@/utils/index.js';

  const { proxy } = getCurrentInstance();

  // 查询参数
  const queryParams = reactive({
    id: '', // 对应后台的taskId
    description: '', // 对应后台的taskName
    creator: null,
    pageNum: 1,
    pageSize: 10,
  });

  // 日期范围
  const dateRange = ref([]);
  const loadingFlag = ref(false);

  // 总数量
  const total = ref(0);
  const paperDialogTotal = ref(0);

  const currUserEmail = ref('');
  const downloadingTotal = ref('');
  const waitingTotal = ref('');

  // 任务列表
  const taskList = ref([]);
  const paperDialogList = ref([]);
  const paperDialogLoading = ref(false);

  // 任务详情弹窗
  const detailDialog = reactive({
    visible: false,
    taskId: null,
    taskDes: null,
    detailList: [],
    searchLiteratureId: '',
    searchStatus: '',
    filteredList: [],
    pageNum: 1,
    pageSize: 10,
  });

  // 分页大小变化
  const handleDetailDialogSizeChange = size => {
    detailDialog.pageSize = size;
    getDetailDialogList();
  };

  // 页码变化
  const handleDetailDialogCurrentChange = page => {
    detailDialog.pageNum = page;
    getDetailDialogList();
  };

  // 日志弹窗
  const logDialog = reactive({
    visible: false,
    taskId: '',
    logs: [],
  });

  // 执行日志详情弹窗
  const fullLogDialog = reactive({
    visible: false,
    logs: [],
  });

  // 发布人选项
  const creatorOptions = ref([]);
  // 任务状态选项
  const paperStatusOptions = ref([]);
  const detailDialogVisible = ref(false);
  const detailDialogDocId = ref(null);
  const detailDialogPaperId = ref(null);

  /** 查看按钮操作 - 修改为打开详情弹窗 */
  function handleOptView(row) {
    let docId = row.docId;
    let paperId = row.paperId;
    detailDialogVisible.value = true;
    detailDialogDocId.value = docId;
    detailDialogPaperId.value = paperId;
  }

  // 格式化日期时间
  const formatDateTime = dateTime => {
    if (!dateTime) return '';
    return formatDate(dateTime);
  };

  // 获取发布人选项
  const getCreatorOptions = () => {
    getAllPdsUsers()
      .then(response => {
        creatorOptions.value = response || [];
      })
      .catch(error => {
        console.error('获取发布人列表失败:', error);
        creatorOptions.value = [];
      });
  };

  // 获取任务状态
  const getPaperStatusOptions = () => {
    allPaperStatus()
      .then(response => {
        paperStatusOptions.value = response || [];
      })
      .catch(error => {
        console.error('获取任务状态列表失败:', error);
        paperStatusOptions.value = [];
      });
  };

  const getDetailDialogList = () => {
    // 构建查询参数
    const params = {
      ...detailDialog,
    };
    paperDialogLoading.value = true;
    // 调用API获取数据
    taskArticleViewPage(params)
      .then(response => {
        if (response) {
          // 处理数据格式，确保字段名匹配
          paperDialogList.value = response.rows || [];
          paperDialogTotal.value = response.total || 0;
        } else {
          paperDialogList.value = [];
          paperDialogTotal.value = 0;
        }
      })
      .catch(error => {
        console.error('获取任务详情列表失败:', error);
        ElMessage.error('获取任务详情列表失败');
        paperDialogList.value = [];
        paperDialogTotal.value = 0;
      })
      .finally(() => {
        paperDialogLoading.value = false;
      });
  };

  // 查询任务列表
  const getTaskList = () => {
    // 构建查询参数
    const params = {
      ...queryParams,
    };

    // 处理日期范围
    if (dateRange.value && dateRange.value.length === 2) {
      params.beginTime = dateRange.value[0];
      params.endTime = dateRange.value[1];
    }

    // let addDateRange = proxy.addDateRange(params, dateRange.value);
    // console.log(addDateRange);
    // 调用API获取数据
    loadingFlag.value = true;
    listTask(params)
      .then(response => {
        if (response && response.tableDataInfo) {
          currUserEmail.value = response.currUserEmail;
          downloadingTotal.value = response.downloadingTotal;
          waitingTotal.value = response.waitingTotal;

          const rows = response.tableDataInfo.rows || [];
          // 处理数据格式，确保字段名匹配
          taskList.value = rows.map(item => ({
            ...item,
            // 确保日期格式正确
            createTime: formatDateTime(item.createTime),
            updateTime: formatDateTime(item.updateTime),
            // 确保状态字段存在
            // status: getTaskStatus(item),
            // 确保节点类型是数组
            nodeTypes: item.nodeTypes || [],
            // 确保测试标志是布尔值
            isTest: item.testFlag === true || item.testFlag === 1,
          }));
          total.value = response.tableDataInfo.total || 0;
        } else {
          taskList.value = [];
          total.value = 0;
        }
      })
      .catch(error => {
        console.error('获取任务列表失败:', error);
        ElMessage.error('获取任务列表失败');
        taskList.value = [];
        total.value = 0;
      })
      .finally(() => {
        loadingFlag.value = false;
      });
  };

  // 查询
  const handleQuery = () => {
    queryParams.pageNum = 1;
    getTaskList();
  };

  // 重置
  const resetQuery = () => {
    dateRange.value = [];
    proxy.resetForm('queryRef');
    handleQuery();
  };

  // 分页大小变化
  const handleSizeChange = size => {
    queryParams.pageSize = size;
    getTaskList();
  };

  // 页码变化
  const handleCurrentChange = page => {
    queryParams.pageNum = page;
    getTaskList();
  };

  // 获取状态类型
  const getStatusType = status => {
    switch (status) {
      case 'complete':
        return 'success';
      case 'paused':
        return 'info';
      case 'assign_error':
        return 'danger';
      case 'create':
        return 'warning';
      case 'assigning':
        return 'primary';
      case 'assigned':
        return 'success';
      default:
        return 'info';
    }
  };

  // 获取优先级类型
  const getPriorityType = priority => {
    if (priority === 3) return 'danger';
    if (priority === 2) return 'info';
    if (priority >= 70) return 'warning';
    return 'info';
  };

  // 获取优先级文本
  const getPriorityText = priority => {
    if (priority === 3) return '高';
    if (priority === 2) return '普通';
    return priority.toString();
  };

  // 获取完成率
  const getCompletionRate = task => {
    const { totalCount, pendingCount } = task;
    if (totalCount === 0) return 0;
    if (pendingCount === 0) return 100; // 如果没有待下载的，进度为100%
    return Math.round(((totalCount - pendingCount) * 100) / totalCount);
  };

  // 获取进度状态
  const getProgressStatus = status => {
    if (status === '已完成') return 'success';
    if (status === '已暂停' || status === '分配失败') return 'exception';
    return '';
  };

  // 获取任务类型标签样式
  const getTaskTypeTag = type => {
    switch (type) {
      case '自动下载成功':
        return 'success';
      case '存量库':
        return 'info';
      case '正在执行':
        return 'warning';
      case '执行失败':
        return 'danger';
      case '等待站点执行':
        return 'info';
      default:
        return 'info';
    }
  };

  // 处理查看详情
  const handleViewDetail = task => {
    detailDialog.taskId = task.taskId;
    detailDialog.taskName = task.taskName;
    // 模拟从API获取详情数据
    // detailDialog.detailList = mockDetailData[task.taskId] || [];
    // detailDialog.searchLiteratureId = '';
    // detailDialog.searchStatus = '';
    detailDialog.visible = true;
    resetDetailFilter();
  };

  // 处理下载结果
  const handleDownloadResult = task => {
    proxy.$modal
      .confirm(
        '数据将在后台生成，生成完毕后下载链接将发送到您的邮箱，是否继续？',
      )
      .then(() => {
        // console.log(123, task);
        downloadTaskArticleAttachment(task.taskId).then(response => {
          if (response && response.code === 200) {
            const email = response.msg;
            ElMessage.success(
              `系统已将下载路径以邮件形式发送到邮箱 ${email} ，请及时查收`,
            );
          } else {
            ElMessage.error('下载结果生成失败');
          }
        });
      });
  };

  // 处理查看日志
  const handleViewLog = task => {
    logDialog.taskId = task.taskId;
    logDialog.visible = true;

    // 调用API获取真实的日志数据
    getTaskLogs(task.taskId)
      .then(response => {
        if (response && response.data) {
          // 将日志数据转换为字符串数组格式，按时间倒序显示
          logDialog.logs = response.data.map(
            log => `[${formatDate(log.createTime)}] ${log.message}`,
          );
        } else {
          logDialog.logs = ['暂无日志数据'];
        }
      })
      .catch(error => {
        console.error('获取任务日志失败:', error);
        logDialog.logs = ['获取日志数据失败'];
        ElMessage.error('获取任务日志失败');
      });
  };

  // 处理任务控制（暂停/恢复）
  const handleTaskControl = task => {
    const action = task.status === 'paused' ? '恢复' : '暂停';
    // 在实际应用中，这里应该调用API暂停或恢复任务

    const newStatus = task.status === 'paused' ? 'assigned' : 'paused';
    loadingFlag.value = true;
    updateTaskStatus(task.taskId, newStatus)
      .then(response => {
        if (response && response.data) {
          // 将日志数据转换为字符串数组格式，按时间倒序显示
          ElMessage.success(`${action}任务成功`);
          getTaskList();
        } else {
          ElMessage.success(`${action}任务失败`);
        }
      })
      .finally(() => {
        loadingFlag.value = false;
      });
  };

  // 处理删除任务
  const handleDeleteTask = task => {
    loadingFlag.value = true;
    deleteTask(task.taskId)
      .then(response => {
        if (response && response.code === 200) {
          ElMessage.success(`删除任务成功`);
          getTaskList();
        } else {
          ElMessage.error('删除任务失败');
        }
      })
      .finally(() => {
        loadingFlag.value = false;
      });
  };

  // 过滤详情列表
  const filterDetailList = () => {
    // 通过computed属性自动过滤
    detailDialog.pageNum = 1;
    getDetailDialogList();
  };

  // 重置详情过滤
  const resetDetailFilter = () => {
    detailDialog.searchLiteratureId = '';
    detailDialog.searchStatus = '';
    paperDialogList.value = [];
    getDetailDialogList();
  };

  // 查看完整执行日志
  const viewFullLog = row => {
    fullLogDialog.logs = row.executeLog;
    fullLogDialog.visible = true;
  };

  // 处理下载失败列表
  const handleDownloadFailedList = () => {
    /*const failedList = detailDialog.detailList.filter(
      item => item.downloadStatus === '失败',
    );
    if (failedList.length === 0) {
      ElMessage.warning('当前没有下载失败的文献');
      return;
    }
    // 在实际应用中，这里应该调用API下载失败列表
    ElMessage.success(
      `已生成${failedList.length}条失败记录的下载文件，链接已发送到您的邮箱`,
    );*/
    proxy.download(
      `/task/taskArticleDownloadFile`,
      {
        taskId: detailDialog.taskId,
        downloadStatus: 'failed',
      },
      'download_failed.tsv',
    );
  };

  // 处理下载成功列表
  const handleDownloadSuccessList = () => {
    /*const successList = detailDialog.detailList.filter(
      item => item.downloadStatus === '完成',
    );
    if (successList.length === 0) {
      ElMessage.warning('当前没有下载成功的文献');
      return;
    }

    // 在实际应用中，这里应该调用API下载成功列表
    ElMessage.success(
      `已生成${successList.length}条成功记录的下载文件，链接已发送到您的邮箱`,
    );*/
    proxy.download(
      `/task/taskArticleDownloadFile`,
      {
        taskId: detailDialog.taskId,
        downloadStatus: 'success',
      },
      'download_success.tsv',
    );
  };

  onMounted(() => {
    getCreatorOptions();
    getPaperStatusOptions();
    getTaskList();
  });
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 20px;
    padding-bottom: 40px;
  }

  .search-container {
    padding: 20px;
    /*background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);*/
  }

  .search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .search-input {
    width: 220px;
  }

  .search-date {
    width: 360px;
  }

  .task-list-container {
    /*  background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    padding: 0 0 20px 0;*/
    min-height: 500px;
  }

  .task-list {
    padding: 20px;
  }

  .task-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
    border: 1px solid #ebeef5;
  }

  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
  }

  .task-title {
    display: flex;
    align-items: center;

    .task-id {
      font-family: monospace;
      background-color: #ebeef5;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 14px;
      margin-right: 10px;
      color: #606266;
    }

    .task-name {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }

  .task-actions {
    display: flex;
    gap: 10px;
  }

  .task-content {
    padding: 20px;
  }

  .task-info-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
  }

  .task-info-item {
    min-width: 25%;
    margin-bottom: 10px;
    display: flex;
    align-items: center;

    .info-label {
      color: #909399;
      margin-right: 5px;
    }

    .info-value {
      color: #606266;
    }

    .node-types {
      display: flex;
      gap: 5px;
    }
  }

  .download-status {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
  }

  .download-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .info-label {
      margin-right: 10px;
      color: #909399;
    }

    .el-progress {
      flex: 1;
    }
  }

  .download-items {
    display: flex;
    flex-wrap: wrap;
    gap: 10px 30px;
  }

  .download-item {
    display: flex;
    align-items: center;

    .item-label {
      color: #909399;
      margin-right: 5px;
    }

    .item-value {
      margin-top: 3px;
      color: #606266;
      font-weight: 500;
    }
  }

  .site-group {
    display: flex;
    align-items: center;

    .info-label {
      color: #909399;
      margin-right: 5px;
    }

    .info-value {
      color: #606266;
    }
  }

  .pagination-container {
    padding: 0 20px;
    display: flex;
    justify-content: flex-end;
  }

  .log-content,
  .full-log-content {
    height: 800px;
    overflow-y: auto;
    font-family: monospace;
    background-color: #292929;
    color: #f0f0f0;
    padding: 15px;
    border-radius: 4px;
  }

  .log-item {
    line-height: 1.6;
    padding: 3px 0;
  }

  .execute-log-item {
    line-height: 1.4;
    margin-bottom: 4px;
    font-size: 12px;
    color: #606266;
  }

  .detail-header {
    margin-bottom: 15px;

    .detail-search {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .search-controls {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
      }

      .detail-actions {
        display: flex;
        gap: 10px;
      }
    }
  }

  .system-status-info {
    padding: 10px 20px;
    margin: 0 20px;
    background-color: #f2f6fc;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 40px;

    .status-item {
      display: flex;
      align-items: center;

      .status-label {
        color: #606266;
        font-size: 14px;
      }

      .status-value {
        color: #409eff;
        font-weight: 600;
        font-size: 16px;
        margin-left: 5px;
      }
    }
  }
</style>
