<template>
  <div>
    <!-- 文献详情弹窗 -->
    <el-dialog
      v-model="openFlag"
      title="手动上传PDF"
      width="83%"
      append-to-body
      @opened="handleOptView"
    >
      <el-descriptions :column="2" border direction="vertical">
        <!-- 标题 -->
        <el-descriptions-item label="标题" :span="2">{{
          detailData.title || '-'
        }}</el-descriptions-item>

        <!-- 标识符 - 每行两个项目 -->
        <el-descriptions-item label="PMID">{{
          detailData.pmid || '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="PMCID">{{
          detailData.pmcId || '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="DOI">{{
          detailData.doi || '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="自定义ID">{{
          detailData.customId || '-'
        }}</el-descriptions-item>

        <!-- 基本信息 -->
        <el-descriptions-item label="作者" :span="2">
          <div class="article-info-cell">
            {{ formatArticleAuthors(detailData.authorInfo) || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="单位" :span="1">
          <div class="article-info-cell">
            {{ formatAffiliations(detailData.affiliation) || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="期刊" :span="1">
          <div class="article-info-cell">
            {{ detailData.journalName || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="发表信息" :span="2">
          {{ detailData.publishedYear || '-' }}
          {{ detailData.volume ? ',' + detailData.volume : '' }}
          {{ detailData.issue ? '(' + detailData.issue + ')' : '' }}
          {{ detailData.page ? ':' + detailData.page : '' }}
        </el-descriptions-item>

        <!-- ISSN信息 - 每行两个项目 -->
        <el-descriptions-item label="ISSN Print">{{
          detailData.issnPrint || '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="ISSN Electronic">{{
          detailData.issnElectronic || '-'
        }}</el-descriptions-item>

        <!-- 摘要 - 占一整行 -->
        <el-descriptions-item label="摘要" :span="2">
          <div
            class="article-info-cell"
            v-html="detailData.articleAbstract || '暂无摘要'"
          ></div>
        </el-descriptions-item>

        <!-- 系统信息 - 每行两个项目 -->
        <el-descriptions-item label="入库时间">{{
          formatDateTime(detailData.createTime) || '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="最后修改时间">{{
          formatDateTime(detailData.updateTime) || '-'
        }}</el-descriptions-item>
      </el-descriptions>
      <el-card style="margin-top: 5px">
        <div>
          <el-form ref="editFormRef" :model="editDialogForm">
            <!-- PDF文件上传 -->
            <el-form-item label="PDF文件上传">
              <el-upload
                ref="pdfUploadRef"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :data="{
                  docId: editDialogForm.id,
                  paperId: editDialogForm.paperId,
                  fileType: 'PDF',
                }"
                :auto-upload="false"
                :on-change="file => handleFileChange(file, 'PDF')"
                :on-success="response => handleUploadSuccess(response, 'PDF')"
                :on-error="handleUploadError"
                :before-remove="file => handleBeforeFileRemove(file, 'PDF')"
                :on-preview="handleFilePreview"
                :limit="1"
                accept=".pdf"
                :file-list="pdfFileList"
              >
                <template #trigger>
                  <el-button type="primary" size="small">选择PDF文件</el-button>
                </template>
                <el-button
                  v-if="hasPdfToUpload"
                  type="success"
                  size="small"
                  style="margin-left: 10px"
                  @click="submitUpload('pdfUploadRef')"
                >
                  上传
                </el-button>
              </el-upload>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </el-dialog>
  </div>
</template>
<script setup>
  import { deleteAttachment, getArticle } from '@/api/task/task.js';
  import { formatDate, isStrBlank } from '@/utils/index.js';
  import { getToken } from '@/utils/auth.js';
  import { downloadUseForm } from '@/utils/download.js';

  const { proxy } = getCurrentInstance();

  const openFlag = defineModel({
    type: Boolean,
    default: false,
  });

  const props = defineProps({
    docId: {
      type: String,
      required: true,
    },
    paperId: {
      type: String,
      default: null,
    },
  });

  const emits = defineEmits(['refreshTable']);

  const editDialogForm = ref({
    id: props.docId,
    paperId: props.paperId,
  });

  // 上传配置
  const uploadUrl =
    import.meta.env.VITE_APP_BASE_API + '/article/uploadAttachment';
  const uploadHeaders = {
    Authorization: 'Bearer ' + getToken(),
  };

  // 文献详情弹窗
  const detailData = ref({});

  // 文件上传相关数据
  const pdfFileList = ref([]);
  const suppFileList = ref([]);

  /** 查看按钮操作 - 修改为打开详情弹窗 */
  function handleOptView() {
    let doc_id = props.docId;
    // console.log(doc_id);
    // 使用API获取文献详情
    getArticle(doc_id)
      .then(response => {
        let data = response.data;
        detailData.value = data;
        // 设置文件回显
        setupFileList(data);
      })
      .catch(() => {
        // 如果API调用失败，使用传入的行数据作为备选
        detailData.value = {};
      });
  }

  // 设置文件列表回显
  function setupFileList(data) {
    // PDF文件回显
    if (data.pdfFile) {
      pdfFileList.value = [
        {
          name: data.pdfFile.fileName,
          url: `/article/file/download/${data.pdfFile.id}`,
          uid: data.pdfFile.id,
          status: 'success',
        },
      ];
    } else {
      pdfFileList.value = [];
    }
  }

  // 格式化日期时间
  const formatDateTime = dateTime => {
    if (!dateTime) return '';
    return formatDate(dateTime);
  };

  // 修改格式化函数 - 支持新的authorInfo格式
  function formatArticleAuthors(data) {
    // 优先使用authorInfo字段
    if (data && data.length > 0) {
      // 检查是否是新的authorInfo格式
      return data
        .map(authorInfo => {
          const lastname = isStrBlank(authorInfo.lastname)
            ? ''
            : authorInfo.lastname;
          const forename = isStrBlank(authorInfo.forename)
            ? ''
            : authorInfo.forename;
          return `${lastname} ${forename}`.trim();
        })
        .join(', ');
    }
    return '-';
  }

  function formatAffiliations(affiliations) {
    if (!affiliations || affiliations.length === 0) return '-';
    return affiliations.join('; ');
  }

  // 处理文件选择变化
  function handleFileChange(file, fileType) {
    // 文件选择后更新对应的文件列表，但不自动上传
    console.log(`${fileType} 文件已选择:`, file.name);

    // 确保新选择的文件状态不是success，并且文件列表中不存在该文件
    if (file.status !== 'success') {
      // 直接使用file对象，而不是file.raw
      const fileItem = {
        name: file.name,
        uid: file.uid,
        status: 'ready',
        raw: file.raw,
      };

      if (fileType === 'PDF') {
        // PDF只允许一个文件，直接替换
        pdfFileList.value = [fileItem];
      } else if (fileType === 'SUPP') {
        // SUPP可以有多个文件，添加到列表中
        if (!suppFileList.value.some(f => f.uid === file.uid)) {
          suppFileList.value.push(fileItem);
        }
      }
    }
  }

  // 上传成功回调
  function handleUploadSuccess(response, fileType) {
    if (response.code === 200) {
      proxy.$modal.msgSuccess('上传成功');

      // 更新文件列表为回显状态
      let fileList = response.data.map(it => {
        return {
          name: it.fileName,
          url: `/article/file/download/${it.id}`,
          uid: it.id,
          status: 'success',
        };
      });
      if (fileType === 'PDF') {
        pdfFileList.value = fileList;
        emits('refreshTable');
      } else if (fileType === 'SUPP') {
        // SUPP文件：移除待上传的文件，添加已上传的文件
        // 先移除状态为ready的文件
        suppFileList.value = suppFileList.value.filter(
          file => file.status === 'success',
        );
        // 添加新上传的文件
        suppFileList.value.push(...fileList);
      }
    } else {
      proxy.$modal.msgError(response.msg || '上传失败');
    }
  }

  // 上传失败回调
  function handleUploadError(error) {
    console.error('上传失败:', error);
    proxy.$modal.msgError('上传失败，请重试');
  }

  // 文件删除前确认
  function handleBeforeFileRemove(file, fileType) {
    // 如果是已上传的文件（status为success），需要确认删除
    if (file.status === 'success') {
      return proxy.$modal
        .confirm(`确定要删除该${file.name}文件吗？删除后将无法恢复。`)
        .then(() => {
          // 确认删除后，发送删除请求到后台
          return deleteAttachment(file.uid);
        })
        .then(() => {
          proxy.$modal.msgSuccess('删除成功');
          return true;
        })
        .catch(() => {
          return false; // 取消删除或删除失败
        });
    } else {
      // 如果是新选择但未上传的文件，直接删除
      return true;
    }
  }

  // 处理文件预览（点击下载）
  function handleFilePreview(file) {
    if (file && file.url) {
      downloadUseForm(file.url);
    } else {
      proxy.$modal.msgError('文件信息不完整，无法下载');
    }
  }

  // 判断是否有未上传的文件（修改为计算属性）
  const hasPdfToUpload = computed(() => {
    return pdfFileList.value.some(file => file.status !== 'success');
  });

  // 提交上传
  function submitUpload(refName) {
    if (!editDialogForm.value.id) {
      proxy.$modal.msgError('文献ID不能为空');
      return;
    }
    proxy.$refs[refName].submit();
  }

  function isEmptyItem(value) {
    return isStrBlank(value) || value === '-';
  }
</script>
<style scoped lang="scss">
  .article-info-cell {
    max-height: 70px;
    overflow-y: auto;
  }
</style>
