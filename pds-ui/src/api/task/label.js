import request from '@/utils/request';

// 查询脚本标签列表
export function listLabel(query) {
  return request({
    url: '/scriptLabel/list',
    method: 'get',
    params: query,
  });
}

// 保存脚本标签（新增/修改）
export function saveLabel(data) {
  return request({
    url: '/scriptLabel/save',
    method: 'post',
    data: data,
  });
}

// 删除脚本标签
export function delLabel(ids) {
  return request({
    url: '/scriptLabel/' + ids,
    method: 'get',
  });
}

// 获取脚本标签关联站点信息
export function getSiteInfo(scriptlabelId) {
  return request({
    url: '/scriptLabel/site/' + scriptlabelId,
    method: 'get',
  });
}
