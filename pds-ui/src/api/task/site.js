import request from '@/utils/request';

// 查询节点列表
export function listSite(query) {
  return request({
    url: '/site/list',
    method: 'get',
    params: query,
  });
}

// 获取节点详细信息
export function getSite(siteId) {
  return request({
    url: `/site/get/${siteId}`,
    method: 'get',
  });
}

// 新增节点
export function addSite(data) {
  return request({
    url: '/site/add',
    method: 'post',
    data: data,
  });
}

// 修改节点
export function updateSite(data) {
  return request({
    url: '/site/update',
    method: 'post',
    data: data,
  });
}

// 删除节点
export function delSite(siteId) {
  return request({
    url: `/site/delete/${siteId}`,
    method: 'post',
  });
}

// 修改节点状态
export function changeSiteStatus(data) {
  return request({
    url: '/site/changeStatus',
    method: 'post',
    data: data,
  });
}

// 获取所有分组
export function listSiteGroups() {
  return request({
    url: '/site/groups',
    method: 'get',
  });
}

// 根据节点类型获取标签选项
export function listLabelsByType(type) {
  return request({
    url: '/site/labels',
    method: 'get',
    params: { type },
  });
}

// 获取节点监控详情信息
export function getNodeMonitorInfo(nodeId) {
  return request({
    url: `/site/nodeMonitor/${nodeId}`,
    method: 'get',
  });
}

// 获取站点日志列表
export function getSiteLogList(siteId) {
  return request({
    url: `/site/logs/${siteId}`,
    method: 'get',
  });
}

// 刷新站点日志（设置to_refresh_log为1）
export function refreshSiteLog(siteId) {
  return request({
    url: `/site/refreshLog/${siteId}`,
    method: 'post',
  });
}

// 查询列表
// export function listScript(query) {
//   return request({
//     url: '/script/list',
//     method: 'get',
//     params: query,
//   });
// }
