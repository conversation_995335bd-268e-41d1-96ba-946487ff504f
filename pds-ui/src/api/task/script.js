import request from '@/utils/request';

// 查询脚本列表
export function listScript(query) {
  return request({
    url: '/script/list',
    method: 'get',
    params: query,
  });
}

// 上传脚本文件
export function uploadScript(data) {
  return request({
    url: '/script/upload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 保存脚本信息（新增/修改）
export function saveScript(data) {
  return request({
    url: '/script/saveScript',
    method: 'post',
    data: data,
  });
}

// 使用JavaScript中的FormData提交数据，保存脚本信息（新增/修改）
export function saveScriptWithFormData(data) {
  return request({
    url: '/script/saveScriptWithFormData',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  });
}

// 下载脚本
export function downloadScript(scriptId) {
  return request({
    url: '/script/download/' + scriptId,
    method: 'get',
    responseType: 'blob',
  });
}

// 删除脚本
export function delScript(scriptId) {
  return request({
    url: '/script/remove/' + scriptId,
    method: 'delete',
  });
}

// 保存脚本标签与脚本关联
export function saveLabelAndScript(data) {
  return request({
    url: '/scriptLabel/saveLabelAndScript',
    method: 'post',
    data: data,
  });
}

// 获取标签对应的脚本列表
export function getScriptListOfLabel(scriptlabelId) {
  return request({
    url: '/scriptLabel/scriptListOfLabel/' + scriptlabelId,
    method: 'get',
  });
}

// 根据名称查询出版社数据
export function findPublisherByName(name) {
  return request({
    url: '/scriptLabel/findPublisherByName',
    method: 'get',
    params: { name },
  });
}

// 获取待分配期刊列表
export function getToSelectJournal(query) {
  return request({
    url: '/scriptLabel/toSelectJournalList',
    method: 'get',
    params: query,
  });
}

// 获取已分配期刊列表
export function getAssignedJournalList(query) {
  return request({
    url: '/scriptLabel/assignedJournalList',
    method: 'get',
    params: query,
  });
}

// 保存期刊与脚本关联
export function saveJournalScript(data) {
  return request({
    url: '/scriptLabel/saveJournalScript',
    method: 'post',
    data: data,
  });
}

// 移除期刊脚本
export function removeJournalScript(data) {
  return request({
    url: '/scriptLabel/removeJournalScript',
    method: 'post',
    data: data,
  });
}

// 应用期刊到标签
export function applyJournalToLabel(data) {
  return request({
    url: '/scriptLabel/applyJournalToLabel',
    method: 'post',
    data: data,
  });
}

// 移除标签的期刊分配
export function removeJournalApply(data) {
  return request({
    url: '/scriptLabel/removeJournalApply',
    method: 'post',
    data: data,
  });
}
