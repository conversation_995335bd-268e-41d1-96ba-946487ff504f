import request from '@/utils/request';

// 查询解析列表
export function listParse(query) {
  return request({
    url: '/parse/list',
    method: 'get',
    params: query,
  });
}

// 删除文献解析
export function deleteParse(id) {
  return request({
    url: `/parse/delete/${id}`,
    method: 'delete',
  });
}

// 重置文献解析
export function retryParse(id) {
  return request({
    url: `/parse/retry/${id}`,
    method: 'post',
  });
}
