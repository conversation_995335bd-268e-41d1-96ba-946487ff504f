import request from '@/utils/request';

// 查询文献列表
export function listLiterature(query) {
  return request({
    url: '/literature/list',
    method: 'get',
    params: query
  });
}

// 查询文献详细
export function getLiterature(id) {
  return request({
    url: '/literature/' + id,
    method: 'get'
  });
}

// 新增文献
export function addLiterature(data) {
  return request({
    url: '/literature',
    method: 'post',
    data: data
  });
}

// 修改文献
export function updateLiterature(data) {
  return request({
    url: '/literature',
    method: 'put',
    data: data
  });
}

// 删除文献
export function delLiterature(id) {
  return request({
    url: '/literature/' + id,
    method: 'delete'
  });
}

// 导出文献
export function exportLiterature(query) {
  return request({
    url: '/literature/export',
    method: 'get',
    params: query
  });
}

// 导入文献题录
export function importLiterature(data) {
  return request({
    url: '/literature/import',
    method: 'post',
    data: data
  });
}

// 上传文献附件
export function uploadAttachment(data) {
  return request({
    url: '/literature/upload',
    method: 'post',
    data: data
  });
}

// 下载文献PDF
export function downloadPdf(id) {
  return request({
    url: '/literature/download/' + id,
    method: 'get',
    responseType: 'blob'
  });
} 