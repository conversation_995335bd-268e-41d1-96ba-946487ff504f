import globals from 'globals';
import pluginJs from '@eslint/js';
import pluginVue from 'eslint-plugin-vue';
import eslintPluginPrettierRecommended from 'eslint-plugin-prettier/recommended';

// 加载unplugin-auto-import插件自动生成的全局变量
import { readFileSync } from 'node:fs';
// 动态加载 JSON
let autoImportGlobals = null;
try {
  autoImportGlobals = JSON.parse(
    readFileSync(
      new URL('./.eslintrc-auto-import.json', import.meta.url),
      'utf8',
    ),
  );
} catch (e) {
  console.error('Failed to load auto-import globals:', e);
}
if (!autoImportGlobals || !autoImportGlobals.globals) {
  autoImportGlobals = [];
} else {
  autoImportGlobals = Object.fromEntries(
    Object.entries(autoImportGlobals.globals).map(([key]) => [key, 'readonly']),
  );
}

/** @type {import('eslint').Linter.Config[]} */
export default [
  {
    files: ['**/*.{js,mjs,cjs,vue,ts}'],
  },
  {
    ignores: [
      'vite/',
      'vite.config.js',
      'dist/', // 忽略构建目录
      'node_modules/', // 忽略依赖目录
      'public/**',
      'html/**',
    ],
  },
  {
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
      globals: {
        // 使浏览器自带全局变量不报错（document、window等）
        ...globals.browser,
        // 使unplugin-auto-import插件自动引入的全局变量不报错（ref、reactive等）
        ...autoImportGlobals,
        // 开发node服务时，使node自带全局变量不报错（process、__dirname等）,本项目为web服务，生产环境无node
        // ...globals.node,
      },
    },
  },
  pluginJs.configs.recommended,
  ...pluginVue.configs['flat/recommended'],
  eslintPluginPrettierRecommended,
  {
    rules: {
      'prettier/prettier': 'error', // 确保 Prettier 的规则被当作错误处理
      'vue/multi-word-component-names': 'off',
      'vue/require-default-prop': 'off',
    },
  },
];
