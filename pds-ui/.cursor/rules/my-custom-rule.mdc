---
description: 
globs: 
alwaysApply: true
---
#Project Overview

The PDS Document Delivery System is a web application on Vue 3nt providoverviey componend files of the.

##Technology-Frameworkmdc: src/main. js)
-UI componentement Plus]: src/main. js)
-State management: [Minia] (mdc: src/store/index.
-Route: [Vue Router] c/outer/index. js)
-Buite] (mdc: vi js)

##Main 
-[main. js] (mdc: srs) - The ma point for lication
-[App.mdc: src/Ap - Root Component
-[index. htmlindex. html) - HTML file

##Core functions
-Uion - [Login iews/login/indeuting Control - [Permission Control] (mdcssion. js)

#Catalog Structure Description

The PDS project adopts a modular directory structure, and the responsibilities of each directory are as follows:

##Source code directory (` src/`)
-[api/] (mdc: src/pai) - API request module, organized by functional module
-[assets/] (mdc: src/assets) - Static resources (images, fonts, global styles, etc.)
-[components/] (mdc: src/components) - Reusable components
-[direct/] (mdc: src/direct) - directives
-[layout/] (mdc: src/layout) - Layout components (sidebar, top navigation, etc.)
-[plugins/] (mdc: src/pl- Plugin Configuration
-[router/] (mdc: sroute Conf-[store/] (e) - Pinia Snagement
-[t src/tils) nctions
-[ src/views)components d by functional modules

##Build relevant directories
-[public/] (mdatic resources not require packaging
-[vite/] (mdc: vite) - Vite configuration files and plugins

##Configuration file
-[vite. config. js] (mdc: vite. config. js) - Vite configuration
-[eslint. config. js] (mdc: eslint. config. js) - ESLint configuration
-[. prettier rc] (mdc:. prettier rc) - Prettier code formatting configuration

#Routing Configuration Guide

The PDS system uses [Vue Router] (mdc: src/outer/index. js) for route management, which includes two types of routes:

##Route type
1. Constant Routes - Routes accessible to all users, such as login and homepage
2. DynamicRoutes - Routes dynamically loaded based on user permissions

##Routing configuration properties
The routing configuration items include the following common attributes:

-Hidden: true - This route is not displayed in the sidebar
-Always Show: true - Always display the root route, ignore the number of sub routes
-` redirect: noRedirect ` - This route is not clickable in breadcrumbs
-` name ` - Route name, required (when using ` keep alive `)
-Meta - Routing metadata, including:
-Title - Names displayed in the sidebar and breadcrumbs
-Icon - Icon
-NoCache - When set to true, it will not be cached
-ActiveMenu - Highlighted sidebar path

##Permission control
Permission control is implemented through [permission. js] (mdc: src/permission. js), and the main process is as follows:
1. The routing guard checks if the user has logged in
2. The logged in user dynamically loads routes based on their permissions
3. Unregistered users are redirected to the login page

#Component Usage Guide

The PDS system contains various types of components, organized in the following manner:

##Layout components
-[Layout] (mdc: src/layout/index. vue) - Main layout component, including sidebar, top navigation bar, and content area

##Public components
The public components are located in the [components/] (mdc: src/components) directory:

-[Pagination] (mdc: src/components/Pagination) - Pagination component
-[RightToolbar] (mdc: src/components/RightToolbar) - Table Toolbar
-[Editor] (mdc: src/components/Editor) - Rich Text Editor
-[FileUpload] (mdc: src/components/FileUpload) - File Upload
-[ImageUpload] (mdc: src/components/ImageUpload) - Image Upload
-[ImagePreview] (mdc: src/components/ImagePreview) - Image Preview
-[DictTag] (mdc: src/components/DictTag) - Dictionary tag
-[SVgIcon] (mdc: src/components/SvgIcon) - SVG Icon Component

##Using Element Plus
The system uses Element Plus as the UI component library, and all components can be directly used. Please refer to the [Element Plus documentation] for details（ https://element-plus.org/ ）.

##Global approach
A series of global methods have been registered in [main. js] (mdc: src/main. js):

-UseDict - Dictionary Data Processing
-Download - File Download
-ParseTime - Time Format
-` resetForm ` - Reset Form
-HandleTree - Tree Data Processing

-AddDateRange - Date Range Processing