rabbit:
  # 交换机
  exchanges:
    - name: plosp
      type: DIRECT
    # 延时交换机
    - name: plosp_delay
      type: CUSTOM
      customType: x-delayed-message
      arguments:
        x-delayed-type: direct

  # 队列
  queues:
    # 实体解析消息队列
    - name: entity_parse_start_queue
      routing-key: entity_parse_start_routing_key
      exchange-name: plosp_delay
    - name: entity_parse_status_queue
      routing-key: entity_parse_status_routing_key
      exchange-name: plosp_delay
    #=============================================
    # 全文识别消息队列
    - name: fulltext_parse_start_queue
      routing-key: fulltext_parse_start_routing_key
      exchange-name: plosp_delay
    - name: fulltext_parse_status_queue
      routing-key: fulltext_parse_status_routing_key
      exchange-name: plosp_delay
