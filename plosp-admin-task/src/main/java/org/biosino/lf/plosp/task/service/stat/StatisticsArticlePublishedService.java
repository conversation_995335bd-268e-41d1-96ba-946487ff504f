package org.biosino.lf.plosp.task.service.stat;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.Article;
import org.biosino.lf.pds.article.domain.statistics.StatisticsArticlePublished;
import org.biosino.lf.pds.article.mapper.statistics.StatisticsArticlePublishedMapper;
import org.biosino.lf.pds.article.service.IArticleService;
import org.biosino.lf.pds.common.utils.uuid.IdUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Date;

/**
 * 下载量统计
 *
 * <AUTHOR>
 * @date 2025/8/29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsArticlePublishedService {

    private final IArticleService articleService;
    private final StatisticsArticlePublishedMapper articlePublishedMapper;

    /**
     * 全量统计
     */
    public void calculateAll() {
        Article oldestArticle = articleService.getOne(
                Wrappers.<Article>lambdaQuery()
                        .isNotNull(Article::getPublishedYear)
                        .orderByAsc(Article::getPublishedYear)
                        .last("LIMIT 1")
        );

        if (oldestArticle != null) {
            YearMonth start = YearMonth.of(oldestArticle.getPublishedYear(), 1);
            YearMonth end = YearMonth.now();

            YearMonth current = start;
            while (!current.isAfter(end)) {
                LocalDate localDate = current.atDay(1);
                Date date = DateUtil.date(localDate);
                calculateByMonth(date);
                current = current.plusMonths(1);
            }
        }
    }

    public void calculateByMonth() {
        calculateByMonth(null);
    }

    @Transactional(rollbackFor = Exception.class)
    public void calculateByMonth(Date date) {
        if (date == null) {
            date = new Date();
        }

        // 删除记录
        int year = DateUtil.year(date);
        int month = DateUtil.month(date);

        // 统计 publishedYear 小于等于year ， publishedMonth 小于等于month 的数量，如果publishedMonth 是null，就视为publishedMonth = 1
        long total = articleService.count(Wrappers.<Article>lambdaQuery()
                .le(Article::getPublishedYear, year)
                .and(w -> w.isNull(Article::getPublishedMonth)
                        .or()
                        .le(Article::getPublishedMonth, month)));

        // 统计这个月内的
        LambdaQueryWrapper<Article> wrapper = Wrappers.<Article>lambdaQuery().eq(Article::getPublishedYear, year);
        if (month == 1) {
            wrapper.and(w -> w.isNull(Article::getPublishedMonth)
                    .or()
                    .eq(Article::getPublishedMonth, 1));
        } else {
            wrapper.eq(Article::getPublishedMonth, month);
        }
        long totalGrowth = articleService.count(wrapper);

        StatisticsArticlePublished item = new StatisticsArticlePublished();
        item.setId(IdUtils.getSnowflakeNextId());
        item.setYear(year);
        item.setMonth(month);
        item.setTotal(total);
        item.setTotalGrowth(totalGrowth);

        // 删除原来的
        articlePublishedMapper.deleteByYearAndMonth(year, month);
        // 新增
        articlePublishedMapper.insert(item);
    }
}
