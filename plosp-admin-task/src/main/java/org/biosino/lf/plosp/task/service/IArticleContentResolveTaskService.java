package org.biosino.lf.plosp.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticleContentResolveTask;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/27
 */
public interface IArticleContentResolveTaskService extends IService<ArticleContentResolveTask> {
    List<ArticleContentResolveTask> findReadyEntityParseTaskByLimit(int limit);

    List<ArticleContentResolveTask> findReadyFulltextParseTaskByLimit(int limit);
}
