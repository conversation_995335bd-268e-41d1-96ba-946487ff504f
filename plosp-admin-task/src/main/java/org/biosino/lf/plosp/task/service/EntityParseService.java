package org.biosino.lf.plosp.task.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.ArticleContentResolveTask;
import org.biosino.lf.pds.common.core.redis.RedisCache;
import org.biosino.lf.pds.common.enums.ParseTaskEnum;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.plosp.task.rabbitmq.MessageSender;
import org.biosino.lf.plosp.task.rabbitmq.msg.EntityParseTaskStartMsg;
import org.biosino.lf.plosp.task.rabbitmq.msg.EntityParseTaskStatusMsg;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Li
 * @date 2025/8/27
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EntityParseService {

    private final RabbitAdmin rabbitAdmin;

    private final MessageSender messageSender;

    private final IArticleContentResolveTaskService resolveTaskService;

    private final RedisCache redisService;

    private static final String TASK_KEY = "entity_parse_lock_key";

    private final static Integer EXPIRE_TIME = 5;

    public synchronized void pushStartMsg() {
        if (redisService.getCacheObject(TASK_KEY) != null) {
            log.info("pushEntityParseTaskStartMsg is running");
            return;
        }
        redisService.setCacheObject(TASK_KEY, true, EXPIRE_TIME, TimeUnit.MINUTES);
        try {
            Properties startProps = rabbitAdmin.getQueueProperties("entity_parse_start_queue");
            int messageCount = (int) startProps.get(RabbitAdmin.QUEUE_MESSAGE_COUNT);
            // 如果队列中还有超过40条消息，不重新推送消息
            if (messageCount > 40) {
                log.info("entity_parse_start_queue messageCount: {}", messageCount);
                return;
            }

            List<ArticleContentResolveTask> taskList = resolveTaskService.findReadyEntityParseTaskByLimit(40);
            if (CollUtil.isEmpty(taskList)) {
                log.info("There are no tasks ready to start");
                return;
            }
            List<EntityParseTaskStartMsg> startMsgs = new ArrayList<>();
            for (ArticleContentResolveTask task : taskList) {
                if (StrUtil.isBlank(task.getArticleAbstract())) {
                    task.setEntityParseStartTime(new Date());
                    task.setEntityParseEndTime(new Date());
                    task.setEntityParseStatus(ParseTaskEnum.success.getCode());

                    continue;
                }

                EntityParseTaskStartMsg msg = new EntityParseTaskStartMsg();
                msg.setDocId(task.getDocId());
                msg.setArticleAbstract(HtmlUtil.cleanHtmlTag(task.getArticleAbstract()));
                startMsgs.add(msg);

                task.setEntityParseStatus(ParseTaskEnum.queuing.getCode());
                task.setEntityParseStartTime(new Date());
            }
            // 保存
            resolveTaskService.updateBatchById(taskList);

            // 发送消息
            for (EntityParseTaskStartMsg startMsg : startMsgs) {
                messageSender.sendDelayMsg("entity_parse_start_routing_key", startMsg);
            }


        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.deleteObject(TASK_KEY);
        }
    }

    // 接受结束的消息
    public void handlerStatusChange(EntityParseTaskStatusMsg statusMsg) {
        ArticleContentResolveTask task = resolveTaskService.getOptById(statusMsg.getDocId()).orElseThrow(() -> new ServiceException("Task not found"));
        if (statusMsg.getStatus().equals(ParseTaskEnum.running.getCode())) {
            task.setEntityParseStatus(ParseTaskEnum.running.getCode());
            resolveTaskService.updateById(task);
            return;
        }

        if (statusMsg.getStatus().equals(ParseTaskEnum.error.getCode())) {
            task.setEntityParseStatus(ParseTaskEnum.error.getCode());
            task.setEntityParseEndTime(new Date());
            task.setEntityParseErrorMsg(statusMsg.getFailCause());
            resolveTaskService.updateById(task);
            return;
        }
        if (statusMsg.getStatus().equals(ParseTaskEnum.success.getCode())) {
            task.setEntityParseEndTime(new Date());
            task.setEntityParseStatus(ParseTaskEnum.success.getCode());
            task.setEntityParseResult(JSONUtil.toJsonStr(statusMsg.getResult()));
            resolveTaskService.updateById(task);
        }
    }
}
