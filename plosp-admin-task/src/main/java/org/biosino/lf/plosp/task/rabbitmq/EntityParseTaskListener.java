package org.biosino.lf.plosp.task.rabbitmq;

import com.alibaba.fastjson2.JSON;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.plosp.task.rabbitmq.msg.EntityParseTaskStatusMsg;
import org.biosino.lf.plosp.task.service.EntityParseService;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/8/27
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EntityParseTaskListener {

    private final EntityParseService entityParseService;

    @RabbitListener(queues = "entity_parse_status_queue")
    @RabbitHandler
    public void handlerTaskStatus(@Payload EntityParseTaskStatusMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.info("entity_parse_status_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            entityParseService.handlerStatusChange(msg);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }
}
