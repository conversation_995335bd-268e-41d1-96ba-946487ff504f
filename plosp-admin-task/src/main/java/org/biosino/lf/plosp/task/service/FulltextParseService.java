package org.biosino.lf.plosp.task.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.ArticleContentResolveTask;
import org.biosino.lf.pds.common.core.redis.RedisCache;
import org.biosino.lf.pds.common.enums.ParseTaskEnum;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.plosp.task.rabbitmq.MessageSender;
import org.biosino.lf.plosp.task.rabbitmq.msg.FulltextParseTaskStartMsg;
import org.biosino.lf.plosp.task.rabbitmq.msg.FulltextParseTaskStatusMsg;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/8/27
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FulltextParseService {

    private final RabbitAdmin rabbitAdmin;

    private final MessageSender messageSender;

    private final IArticleContentResolveTaskService resolveTaskService;

    private final RedisCache redisService;

    private static final String TASK_KEY = "fulltext_parse_lock_key";

    private final static Integer EXPIRE_TIME = 5;

    public synchronized void pushStartMsg() {
        if (redisService.getCacheObject(TASK_KEY) != null) {
            log.info("pushFulltextParseTaskStartMsg is running");
            return;
        }
        redisService.setCacheObject(TASK_KEY, true, EXPIRE_TIME, TimeUnit.MINUTES);
        try {
            Properties startProps = rabbitAdmin.getQueueProperties("fulltext_parse_start_queue");
            int messageCount = (int) startProps.get(RabbitAdmin.QUEUE_MESSAGE_COUNT);
            // 如果队列中还有超过40条消息，不重新推送消息
            if (messageCount > 40) {
                log.info("fulltext_parse_start_queue messageCount: {}", messageCount);
                return;
            }

            List<ArticleContentResolveTask> taskList = resolveTaskService.findReadyFulltextParseTaskByLimit(40);
            if (CollUtil.isEmpty(taskList)) {
                log.info("There are no tasks ready to start");
                return;
            }
            List<FulltextParseTaskStartMsg> startMsgs = new ArrayList<>();
            for (ArticleContentResolveTask task : taskList) {
                if (StrUtil.isBlank(task.getArticleAbstract())) {
                    task.setFulltextParseStartTime(new Date());
                    task.setFulltextParseEndTime(new Date());
                    task.setFulltextParseStatus(ParseTaskEnum.success.getCode());

                    continue;
                }

                FulltextParseTaskStartMsg msg = new FulltextParseTaskStartMsg();
                msg.setDocId(task.getDocId());
                msg.setFilePath(task.getFilePath());
                startMsgs.add(msg);

                task.setFulltextParseStatus(ParseTaskEnum.queuing.getCode());
                task.setFulltextParseStartTime(new Date());
            }
            // 保存
            resolveTaskService.updateBatchById(taskList);

            // 发送消息
            for (FulltextParseTaskStartMsg startMsg : startMsgs) {
                messageSender.sendDelayMsg("fulltext_parse_start_routing_key", startMsg);
            }


        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.deleteObject(TASK_KEY);
        }
    }

    // 接受结束的消息
    public void handlerStatusChange(FulltextParseTaskStatusMsg statusMsg) {
        ArticleContentResolveTask task = resolveTaskService.getOptById(statusMsg.getDocId()).orElseThrow(() -> new ServiceException("Task not found"));
        if (statusMsg.getStatus().equals(ParseTaskEnum.running.getCode())) {
            task.setFulltextParseStatus(ParseTaskEnum.running.getCode());
            resolveTaskService.updateById(task);
            return;
        }

        if (statusMsg.getStatus().equals(ParseTaskEnum.error.getCode())) {
            task.setFulltextParseStatus(ParseTaskEnum.error.getCode());
            task.setFulltextParseEndTime(new Date());
            task.setFulltextParseErrorMsg(statusMsg.getFailCause());
            resolveTaskService.updateById(task);
            return;
        }
        if (statusMsg.getStatus().equals(ParseTaskEnum.success.getCode())) {
            task.setFulltextParseEndTime(new Date());
            task.setFulltextParseStatus(ParseTaskEnum.success.getCode());
            resolveTaskService.updateById(task);
        }
    }
}
