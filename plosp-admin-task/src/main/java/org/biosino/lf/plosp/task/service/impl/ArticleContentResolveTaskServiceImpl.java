package org.biosino.lf.plosp.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleContentResolveTask;
import org.biosino.lf.pds.article.mapper.ArticleContentResolveTaskMapper;
import org.biosino.lf.pds.common.enums.ParseTaskEnum;
import org.biosino.lf.plosp.task.service.IArticleContentResolveTaskService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @date 2025/8/27
 */
@Service
public class ArticleContentResolveTaskServiceImpl extends ServiceImpl<ArticleContentResolveTaskMapper, ArticleContentResolveTask> implements IArticleContentResolveTaskService {

    @Override
    public List<ArticleContentResolveTask> findReadyEntityParseTaskByLimit(int limit) {
        Wrapper<ArticleContentResolveTask> qw = Wrappers.<ArticleContentResolveTask>lambdaQuery().eq(
                ArticleContentResolveTask::getEntityParseStatus, ParseTaskEnum.ready.getCode()
        ).last("LIMIT " + limit);
        return this.list(qw);
    }

    @Override
    public List<ArticleContentResolveTask> findReadyFulltextParseTaskByLimit(int limit) {
        Wrapper<ArticleContentResolveTask> qw = Wrappers.<ArticleContentResolveTask>lambdaQuery().eq(
                ArticleContentResolveTask::getFulltextParseStatus, ParseTaskEnum.ready.getCode()
        ).last("LIMIT " + limit);
        return this.list(qw);
    }
}
