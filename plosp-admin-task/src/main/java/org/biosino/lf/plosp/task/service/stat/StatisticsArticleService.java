package org.biosino.lf.plosp.task.service.stat;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.Article;
import org.biosino.lf.pds.article.domain.statistics.StatisticsArticle;
import org.biosino.lf.pds.article.mapper.statistics.StatisticsArticleMapper;
import org.biosino.lf.pds.article.service.IArticleService;
import org.biosino.lf.pds.common.utils.uuid.IdUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 下载量统计
 *
 * <AUTHOR>
 * @date 2025/8/29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsArticleService {

    private final IArticleService articleService;
    private final StatisticsArticleMapper statisticsArticleMapper;

    /**
     * 全量统计
     */
    public void calculateAll() {
        // 找到最老的数据
        Article oldestArticle = articleService.getOne(
                Wrappers.<Article>lambdaQuery()
                        .orderByAsc(Article::getCreateTime)
                        .last("LIMIT 1")
        );

        if (oldestArticle != null) {
            DateTime dateTime = DateUtil.beginOfMonth(oldestArticle.getCreateTime());
            Date now = new Date();

            while (dateTime.isBefore(now)) {
                calculateByMonth(dateTime.toJdkDate());
                dateTime = dateTime.offset(DateField.MONTH, 1);
            }
        }
    }

    public void calculateByMonth() {
        calculateByMonth(null);
    }

    @Transactional(rollbackFor = Exception.class)
    public void calculateByMonth(Date date) {
        if (date == null) {
            date = new Date();
        }

        // 获取当前日期的结束时间
        DateTime endOfMonth = DateUtil.endOfMonth(date);
        DateTime beginOfMonth = DateUtil.beginOfMonth(date);

        // 删除记录
        int year = DateUtil.year(date);
        int month = DateUtil.month(date);

        // 统计这个月底之前的
        long totalCount = articleService.count(Wrappers.<Article>lambdaQuery().le(Article::getCreateTime, endOfMonth));

        // 统计这个月内的
        long newCount = articleService.count(
                Wrappers.<Article>lambdaQuery()
                        .ge(Article::getCreateTime, beginOfMonth)
                        .le(Article::getCreateTime, endOfMonth)
        );

        StatisticsArticle item = new StatisticsArticle();
        item.setId(IdUtils.getSnowflakeNextId());
        item.setYear(year);
        item.setMonth(month);
        item.setTotal(totalCount);
        item.setTotalGrowth(newCount);

        // 删除原来的
        statisticsArticleMapper.deleteByYearAndMonth(year, month);
        // 新增
        statisticsArticleMapper.insert(item);
    }
}
