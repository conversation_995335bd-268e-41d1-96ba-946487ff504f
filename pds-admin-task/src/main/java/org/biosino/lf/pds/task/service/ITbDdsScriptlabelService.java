package org.biosino.lf.pds.task.service;

import org.biosino.lf.pds.article.custbean.dto.JournalScriptDTO;
import org.biosino.lf.pds.article.custbean.dto.SelectJournalDTO;
import org.biosino.lf.pds.article.custbean.dto.TbDdsScriptlabelDTO;
import org.biosino.lf.pds.article.custbean.dto.TbDdsScriptlabelScriptDTO;
import org.biosino.lf.pds.article.custbean.vo.ScriptVO;
import org.biosino.lf.pds.article.custbean.vo.SelectJournalVO;
import org.biosino.lf.pds.article.custbean.vo.SelectVO;
import org.biosino.lf.pds.article.custbean.vo.TbDdsScriptlabelVO;
import org.biosino.lf.pds.article.domain.TbDdsScriptlabel;
import org.biosino.lf.pds.article.domain.TbDdsSite;
import org.biosino.lf.pds.article.service.CommonService;
import org.biosino.lf.pds.common.core.domain.AjaxResult;

import java.util.List;

/**
 * 脚本标签Service接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface ITbDdsScriptlabelService extends CommonService<TbDdsScriptlabel> {

    /**
     * 查询脚本标签列表
     *
     * @param dto 查询参数
     * @return 脚本标签列表
     */
    List<TbDdsScriptlabelVO> selectTbDdsScriptlabelList(TbDdsScriptlabelDTO dto);

    /**
     * 新增/编辑脚本标签
     *
     * @param dto    脚本标签DTO
     * @param userId 用户ID
     * @return 操作结果
     */
    AjaxResult saveScriptLabel(TbDdsScriptlabelDTO dto, Long userId);

    /**
     * 批量删除脚本标签
     *
     * @param ids 需要删除的脚本标签主键集合
     * @return 结果
     */
    AjaxResult deleteTbDdsScriptlabelByIds(Integer[] ids);

    /**
     * 获取脚本标签关联站点信息
     *
     * @param scriptlabelId 脚本标签ID
     * @return 站点列表
     */
    List<TbDdsSite> siteInfo(Integer scriptlabelId);

    /**
     * 保存脚本标签与脚本的关联关系
     *
     * @param dto    脚本标签与脚本关联DTO
     * @param userId 用户ID
     * @return 操作结果
     */
    AjaxResult saveLabelAndScript(TbDdsScriptlabelScriptDTO dto, Long userId);

    /**
     * 查询当前标签对应脚本列表
     *
     * @param scriptlabelId 脚本标签ID
     * @return 脚本列表
     */
    List<ScriptVO> scriptListOfLabel(Integer scriptlabelId);

    /**
     * 根据名称查询出版社数据（组成下拉框数据）
     *
     * @param name 出版社名称
     * @return 出版社列表
     */
    List<SelectVO> findPublisherByName(String name);

    /**
     * 源刊、高校待分配期刊列表
     *
     * @param dto 查询参数
     * @return 期刊列表
     */
    List<SelectJournalVO> toSelectJournalList(SelectJournalDTO dto);

    /**
     * 已分配期刊列表
     *
     * @param dto 查询参数
     * @return 期刊列表
     */
    List<SelectJournalVO> assignedJournalList(SelectJournalDTO dto);

    /**
     * 保存期刊与脚本关联
     *
     * @param dto 期刊与脚本关联DTO
     * @return 操作结果
     */
    AjaxResult saveJournalScript(JournalScriptDTO dto);

    /**
     * 移除期刊脚本关联
     *
     * @param dto 期刊与脚本关联DTO
     * @return 操作结果
     */
    AjaxResult removeJournalScript(JournalScriptDTO dto);

    /**
     * 应用期刊到标签
     *
     * @param dto 期刊与标签关联DTO
     * @return 操作结果
     */
    AjaxResult applyJournalToLabel(JournalScriptDTO dto, Long userId);

    /**
     * 移除标签的期刊分配
     *
     * @param dto 期刊与标签关联DTO
     * @param userId 用户ID
     * @return 操作结果
     */
    AjaxResult removeJournalApply(JournalScriptDTO dto, Long userId);

    /**
     * 根据标签类型查询标签列表
     * 
     * @param type 标签类型
     * @return 标签列表
     */
    List<TbDdsScriptlabelVO> selectLabelsByType(String type);
}
