package org.biosino.lf.pds.task.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.ArticleParse;
import org.biosino.lf.pds.article.domain.ArticleXML;
import org.biosino.lf.pds.article.dto.ArticleParseDTO;
import org.biosino.lf.pds.article.mapper.ArticleParseMapper;
import org.biosino.lf.pds.article.mapper.ArticleXmlMapper;
import org.biosino.lf.pds.common.enums.ParseStatusEnums;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.task.service.ITbDdsArticleParseService;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 文献管理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TbDdsArticleParseServerImpl extends ServiceImpl<ArticleParseMapper, ArticleParse> implements ITbDdsArticleParseService {
    private final ArticleParseMapper articleParseMapper;
    private final ArticleXmlMapper articleXmlMapper;

    @Override
    public List<ArticleParse> selectParseList(ArticleParseDTO articleParseDTO) {
        return articleParseMapper.selectParseList(articleParseDTO);
    }

    @Override
    public Resource downloadFile(Long id) {
        // 判断id是否为空
        if (id == null) {
            throw new ServiceException("参数不能为空");
        }
        // 获取文件
        ArticleXML article = articleXmlMapper.selectById(id);
        if (article == null || article.getFileData() == null) {
            throw new ServiceException("文件不存在");
        }
        byte[] fileData = article.getFileData();
        return new ByteArrayResource(fileData);
    }

    @Override
    public void deleteParses(Long[] id) {
        if (id == null || id.length == 0) {
            throw new ServiceException("参数不能为空");
        }
        // 获取id列表
        final List<Long> ids = CollUtil.toList(id);
        // 删除文献
        articleParseMapper.deleteByIds(ids);
        // 删除文献xml表中的数据
        articleXmlMapper.deleteByIds(ids);
    }

    @Override
    public void retryParse(Long[] id) {
        // 如果id为空，则抛出异常
        if (id == null || id.length == 0) {
            throw new ServiceException("参数不能为空");
        }

        ArrayList<Long> ids = CollUtil.toList(id);
        List<ArticleParse> articleParseList = articleParseMapper.selectByIds(ids);

        // 如果文献不存在，抛出异常
        if (articleParseList == null || articleParseList.isEmpty()) {
            throw new ServiceException("文献不存在");
        }

        for (ArticleParse articleParse : articleParseList) {
            if (articleParse.getStatus() != 0) {
                throw new ServiceException("文献状态不为失败，无法重试");
            }
            articleParse.setErrorMsg(null);
            articleParse.setStatus(ParseStatusEnums.Pending.getCode());
            articleParse.setDocId(null);
            articleParseMapper.updateById(articleParse);
        }
    }

}
