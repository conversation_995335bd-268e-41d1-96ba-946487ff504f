package org.biosino.lf.pds.task.service;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.biosino.lf.pds.article.custbean.dto.ArticleViewQueryDTO;
import org.biosino.lf.pds.article.custbean.dto.TaskPublishDTO;
import org.biosino.lf.pds.article.custbean.dto.TaskTaskDTO;
import org.biosino.lf.pds.article.custbean.vo.SelectVO;
import org.biosino.lf.pds.article.custbean.vo.TbDdsTaskVO;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.service.CommonService;
import org.biosino.lf.pds.common.core.domain.model.LoginUser;
import org.biosino.lf.pds.common.core.page.PageDomain;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.pds.common.enums.task.ScriptTypeEnum;
import org.biosino.lf.pds.common.enums.task.TaskDownloadModeEnum;
import org.biosino.lf.pds.common.enums.task.TaskPaperStatusEnum;

import java.util.List;
import java.util.Map;

import org.biosino.lf.pds.common.enums.task.TaskSourceEnum;
import org.springframework.web.multipart.MultipartFile;

/**
 * pds任务服务层
 *
 * <AUTHOR>
 * @date 2025/6/23
 */
public interface ITbDdsTaskService extends CommonService<TbDdsTask> {
//    AjaxResult uploadIdExcel(MultipartFile file);

    /**
     * 发布文献传递任务
     *
     * @param dto    任务发布数据
     * @return 创建的任务实体
     */
    TbDdsTask publishTask(TaskPublishDTO dto);

    /**
     * 分配任务
     */
    void schedule(String taskId);

    Article getArticleInfo(final String line);

    boolean hasPaperByTaskIdAndStatus(String taskId, List<TaskPaperStatusEnum> status);

    TbDdsTaskPaper findTaskPaper(String taskId, Long docId);

    TbDdsTaskSchedule findTaskSchedule(Long paperId, Integer siteId);

    TbDdsTaskVO searchTaskTrackList(TaskTaskDTO queryDto, PageDomain pageDomain, LoginUser loginUser);

    List<SelectVO> allPdsUsers();

    List<SelectVO> allPdsSites();

    /**
     * 文献校验
     *
     * @param file Excel文件
     * @return 校验结果数据
     */
    Map<String, Object> validateLiterature(MultipartFile file);

    /**
     * 下载校验结果文件
     *
     * @param filePath 文件路径
     * @param request
     * @param response HTTP响应
     */
    void downloadValidationResult(String filePath, HttpServletRequest request, HttpServletResponse response);

    TableDataInfo taskArticleViewPage(ArticleViewQueryDTO queryDto);

    void taskArticleDownloadFile(ArticleViewQueryDTO queryDto, HttpServletRequest request, HttpServletResponse response);

    /**
     * 获取任务日志
     *
     * @param taskId 任务ID
     * @return 日志列表
     */
    List<TbDdsTaskLog> getTaskLogs(String taskId);

    boolean updateTaskStatus(String taskId, String status);

    String downloadTaskPDF(String taskId, String username);

    boolean deleteTask(String taskId);

    void scheduleExecutingTaskTimeout();

    boolean updateTaskCompleteStatus(String taskId);

    void scheduleAutoCreateTask();

    /**
     * 批量更新任务论文表中的doc_id字段（用于文章合并）
     * 将所有匹配源doc_id的记录更新为目标doc_id
     *
     * @param targetDocId 目标文档ID
     * @param sourceDocId 源文档ID
     * @return 更新的记录数
     */
    int updateTaskPaperDocIdBatch(Long targetDocId, Long sourceDocId);


    void initAndCreateTask(final List<Article> articles, final long startTime, String taskName, String taskDesc,
                           int priority, List<ScriptTypeEnum> nodeTypeEnums, boolean testFlag,
                           TaskDownloadModeEnum downloadModeEnum, int retryInterval, TaskSourceEnum sourceEnum, Long taskUserId);

}
