package org.biosino.lf.pds.framework.web.service;

import cn.hutool.core.collection.CollUtil;
import org.biosino.lf.pds.common.core.domain.entity.SysRole;
import org.biosino.lf.pds.common.core.domain.entity.SysUser;
import org.biosino.lf.pds.common.core.domain.model.LoginUser;
import org.biosino.lf.pds.common.enums.AppNameEnum;
import org.biosino.lf.pds.common.enums.UserStatus;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.MessageUtils;
import org.biosino.lf.pds.common.utils.StringUtils;
import org.biosino.lf.pds.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private Environment environment;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        final SysUser user = userService.selectUserByUserName(username);
        if (user != null) {
            // 检验是否可登录（pds、plosp账号不互通）
            checkCanLogin(user);
        }

        if (StringUtils.isNull(user)) {
            log.info("登录用户：{} 不存在.", username);
            throw new ServiceException(MessageUtils.message("user.not.exists"));
        } else if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            log.info("登录用户：{} 已被删除.", username);
            throw new ServiceException(MessageUtils.message("user.password.delete"));
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            throw new ServiceException(MessageUtils.message("user.blocked"));
        }

        passwordService.validate(user);

        return createLoginUser(user);
    }

    public UserDetails createLoginUser(SysUser user) {
        return new LoginUser(user.getUserId(), user.getDeptId(), user, permissionService.getMenuPermission(user));
    }

    /**
     * 检验是否可登录（pds、plosp账号不互通）
     */
    private void checkCanLogin(SysUser user) {
        final AppNameEnum appNameEnum = AppNameEnum.getCurrAppName(environment).orElseThrow(() -> new IllegalStateException("Unknown Application name(app.name)"));

        final List<SysRole> roles = user.getRoles();

        if (CollUtil.isEmpty(roles)) {
            throw new ServiceException("您的账号未分配角色，请联系管理员开通！");
        }

        if (CollUtil.isNotEmpty(roles)) {
            // 当前应用是否为PDS
            final boolean isPds = appNameEnum.equals(AppNameEnum.PDS);
            // roleKey的前缀
            final String requiredPrefix = isPds ? "pds" : "plosp";

            final boolean canLogin = roles.stream().anyMatch(role -> {
                final String roleKey = role.getRoleKey();
                return "admin".equals(roleKey) || roleKey.toLowerCase().startsWith(requiredPrefix);
            });

            if (!canLogin) {
                throw new ServiceException("您的账号没有访问该系统的权限！");
            }
        }
    }

}
