package org.biosino.lf.pds.framework.config;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.io.IOException;
import java.util.TimeZone;

/**
 * 程序注解配置
 *
 * <AUTHOR>
 */
@Configuration
// 表示通过aop框架暴露该代理对象,AopContext能够访问
@EnableAspectJAutoProxy(exposeProxy = true)
public class ApplicationConfig {
    /**
     * 时区配置
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jacksonObjectMapperCustomization() {
        return jacksonObjectMapperBuilder -> jacksonObjectMapperBuilder.timeZone(TimeZone.getDefault());
    }

    /**
     * Jackson配置，添加字符串的trim处理
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper(Jackson2ObjectMapperBuilder builder) {
        // 先应用yml中的配置
        final ObjectMapper objectMapper = builder.build();
        // 然后添加自定义模块
        objectMapper.registerModule(customJsonModule());
        return objectMapper;
    }

    /**
     * Jackson配置，添加自定义模块
     * 字符串处理自定义模块
     */
    public SimpleModule customJsonModule() {
        final SimpleModule module = new SimpleModule();
        // 添加自定义解析器
        module.addDeserializer(String.class, new StringTrimDeserializer());
        // 添加自定义序列化器，此处会修改所有返回值中Long型，包括统计值，如分页的total，不再使用全局配置
        // module.addSerializer(Long.class, new LongToStringSerializer());
        return module;
    }

    /**
     * 字符串自定义解析器，对字符串执行trimToNull操作
     */
    public static class StringTrimDeserializer extends JsonDeserializer<String> {
        @Override
        public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
            return StrUtil.trimToNull(p.getValueAsString());
        }
    }

    /**
     * Long类型转换为String，以避免JavaScript中Long型数字精度丢失问题
     */
    public static class LongToStringSerializer extends JsonSerializer<Long> {
        @Override
        public void serialize(Long value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            if (value == null) {
                gen.writeNull();
            } else {
                gen.writeString(value.toString());
            }
        }
    }

}
