package org.biosino.lf.plosp.portal.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.*;
import org.biosino.lf.pds.article.mapper.JournalMapper;
import org.biosino.lf.pds.article.mapper.PublisherMapper;
import org.biosino.lf.pds.article.mapper.TbDdsIfYearMapper;
import org.biosino.lf.pds.article.mapper.TbDdsZkySectionMapper;
import org.biosino.lf.pds.article.service.ITbDdsFileService;
import org.biosino.lf.pds.common.enums.SourceTypeEnums;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.plosp.portal.enums.ArticleStatusCodeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.biosino.lf.pds.article.service.IArticleService;
import org.biosino.lf.plosp.portal.service.IArticleSearchService;
import org.biosino.lf.plosp.portal.vo.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ArticleSearchServiceImpl implements IArticleSearchService {
    // 支持的数据库类型
    private static final Set<String> SUPPORTED_DATABASES = Set.of(
            "PUBMED", "PMC", "BIORXIV", "MEDRXIV", "CUSTOM"
    );

    // 支持的字段类型
    private static final Set<String> SUPPORTED_FIELDS = Set.of(
            "journal", "publisher", "impact_factor", "jcr_quartile", "zky_sections", "full_text"
    );

    private final IArticleService articleService;
    private final JournalMapper journalMapper;
    private final PublisherMapper publisherMapper;
    private final ITbDdsFileService fileService;
    private final TbDdsIfYearMapper tbDdsIfYearMapper;
    private final TbDdsZkySectionMapper tbDdsZkySectionMapper;

    @Value("${download.temp-dir}")
    private String downloadTempDir;

    @Override
    public ArticleSearchResponseVO findById(String id, String db, String fields) {
        if (StrUtil.isBlank(id)) {
            throw new ServiceException("id不能为空");
        }

        // 验证参数
        validateParameters(db, fields);

        String[] ids = id.split(",");
        List<String> idList = Arrays.stream(ids)
                .map(String::trim)
                .filter(StrUtil::isNotBlank)
                .toList();

        BatchQueryResult batchResult = batchFindArticlesByIds(idList, db);
        return buildUnifiedResponse(batchResult, fields, idList);
    }

    @Override
    public ArticleSearchResponseVO findByTitle(String title, String db, String fields) {
        if (StrUtil.isBlank(title)) {
            throw new ServiceException("title不能为空");
        }

        // 验证参数
        validateParameters(db, fields);

        String trimmedTitle = title.trim();
        try {
            Article article = findArticleByTitle(trimmedTitle, db);
            List<Article> articles = article != null ? List.of(article) : new ArrayList<>();
            List<ErrorItemVO> errors = article == null ?
                    List.of(createError(trimmedTitle, "未找到匹配的文章")) : new ArrayList<>();

            return buildUnifiedResponse(new BatchQueryResult(articles, errors), fields, List.of(trimmedTitle));
        } catch (Exception e) {
            log.error("按标题查询文章异常: title={}, error={}", trimmedTitle, e.getMessage(), e);
            return buildErrorResponse(trimmedTitle, "查询异常: " + e.getMessage(), fields);
        }
    }

    @Override
    public PdfDownloadResult downloadPdfById(String id) {
        if (StrUtil.isBlank(id)) {
            return PdfDownloadResult.error(
                    ArticleStatusCodeEnum.BAD_REQUEST.getCode(),
                    "ID不能为空",
                    HttpStatus.BAD_REQUEST
            );
        }
        try {
            // 验证输入ID，确保只有一个ID
            String trimmedId = id.trim();
            if (trimmedId.contains(",")) {
                return PdfDownloadResult.error(
                        ArticleStatusCodeEnum.BAD_REQUEST.getCode(),
                        "PDF下载仅支持单篇文章，不支持批量下载",
                        HttpStatus.BAD_REQUEST
                );
            }

            BatchQueryResult batchResult = batchFindArticlesByIds(List.of(trimmedId), null);

            if (batchResult.getArticles().isEmpty()) {
                return PdfDownloadResult.error(
                        ArticleStatusCodeEnum.ARTICLE_NOT_FOUND.getCode(),
                        ArticleStatusCodeEnum.ARTICLE_NOT_FOUND.getMessage(),
                        HttpStatus.NOT_FOUND
                );
            }

            // 确保只有一篇文章
            if (batchResult.getArticles().size() > 1) {
                return PdfDownloadResult.error(
                        ArticleStatusCodeEnum.BAD_REQUEST.getCode(),
                        "找到多篇文章，PDF下载仅支持单篇文章",
                        HttpStatus.BAD_REQUEST
                );
            }

            return downloadSingleArticlePdf(batchResult.getArticles().get(0));
        } catch (Exception e) {
            log.error("下载PDF异常: id={}, error={}", id, e.getMessage(), e);
            return handleDownloadException(e);
        }
    }

    /**
     * 下载单篇文章的PDF文件
     */
    private PdfDownloadResult downloadSingleArticlePdf(Article article) throws IOException {
        // 查询该文章的最新PDF文件
        TbDdsFile latestPdf = fileService.getOne(
                Wrappers.<TbDdsFile>lambdaQuery()
                        .eq(TbDdsFile::getDocId, article.getId())
                        .eq(TbDdsFile::getType, "PDF")
                        .orderByDesc(TbDdsFile::getCreateTime)
                        .last("LIMIT 1")
        );

        if (latestPdf == null) {
            return PdfDownloadResult.error(
                    ArticleStatusCodeEnum.PDF_NOT_FOUND.getCode(),
                    ArticleStatusCodeEnum.PDF_NOT_FOUND.getMessage(),
                    HttpStatus.NOT_FOUND
            );
        }

        // 构建文件路径并读取
        String fullPath = buildPdfFilePath(latestPdf);
        return readPdfFile(new PdfFileInfo(latestPdf, fullPath));
    }

    /**
     * 处理下载异常
     */
    private PdfDownloadResult handleDownloadException(Exception e) {
        if (e instanceof ServiceException) {
            return PdfDownloadResult.error(
                    ArticleStatusCodeEnum.BAD_REQUEST.getCode(),
                    e.getMessage(),
                    HttpStatus.BAD_REQUEST
            );
        }
        return PdfDownloadResult.error(
                ArticleStatusCodeEnum.INTERNAL_SERVER_ERROR.getCode(),
                "服务器内部错误",
                HttpStatus.INTERNAL_SERVER_ERROR
        );
    }

    /**
     * 构建PDF文件完整路径
     */
    private String buildPdfFilePath(TbDdsFile pdfFile) {
        return downloadTempDir + File.separator + pdfFile.getFilePath();
    }

    /**
     * 读取PDF文件
     */
    private PdfDownloadResult readPdfFile(PdfFileInfo pdfInfo) throws IOException {
        File file = new File(pdfInfo.getFullPath());

        if (!file.exists() || !file.isFile()) {
            return PdfDownloadResult.error(
                    ArticleStatusCodeEnum.PDF_NOT_FOUND.getCode(),
                    ArticleStatusCodeEnum.PDF_NOT_FOUND.getMessage(),
                    HttpStatus.NOT_FOUND
            );
        }

        // 读取文件内容
        byte[] fileData = Files.readAllBytes(file.toPath());
        String fileName = pdfInfo.getPdfFile().getFileName();

        return PdfDownloadResult.success(fileData, fileName);
    }

    /**
     * 验证请求参数
     */
    private void validateParameters(String db, String fields) {
        // 验证db参数
        if (StrUtil.isNotBlank(db)) {
            String upperDb = db.toUpperCase();
            if (!SUPPORTED_DATABASES.contains(upperDb)) {
                throw new ServiceException("不支持的数据库类型: " + db +
                        ", 支持的类型: " + String.join(", ", SUPPORTED_DATABASES));
            }
        }

        // 验证fields参数
        if (StrUtil.isNotBlank(fields)) {
            String[] fieldArray = fields.split(",");
            for (String field : fieldArray) {
                String trimmedField = field.trim();
                if (StrUtil.isNotBlank(trimmedField) && !SUPPORTED_FIELDS.contains(trimmedField)) {
                    throw new ServiceException("不支持的字段类型: " + trimmedField +
                            ", 支持的字段: " + String.join(", ", SUPPORTED_FIELDS));
                }
            }
        }
    }

    /**
     * 批量查询文章 - 按ID查询
     */
    private BatchQueryResult batchFindArticlesByIds(List<String> idList, String db) {
        // 分类存储不同类型的ID
        List<Long> customIds = new ArrayList<>();
        List<Long> pmids = new ArrayList<>();
        List<Long> pmcIds = new ArrayList<>();
        List<String> dois = new ArrayList<>();

        // ID分类映射，用于后续匹配
        Map<String, String> originalIdMap = new HashMap<>();
        List<ErrorItemVO> errors = new ArrayList<>();

        // 分类处理ID
        for (String originalId : idList) {
            try {
                if (originalId.toUpperCase().startsWith("PMC")) {
                    // PMC ID格式：PMC + 数字
                    Long pmcId = Long.valueOf(originalId.substring(3));
                    pmcIds.add(pmcId);
                    originalIdMap.put("PMC_" + pmcId, originalId);
                } else if (originalId.matches("^10\\.\\d{4,}/.*$")) {
                    // DOI格式
                    dois.add(originalId);
                    originalIdMap.put("DOI_" + originalId, originalId);
                } else if (originalId.matches("\\d+")) {
                    // 纯数字，判断是自定义ID还是PMID
                    Long numId = Long.valueOf(originalId);
                    if (numId >= 800000000000L && numId < 802000000000L) {
                        customIds.add(numId);
                        originalIdMap.put("CUSTOM_" + numId, originalId);
                    } else {
                        pmids.add(numId);
                        originalIdMap.put("PMID_" + numId, originalId);
                    }
                } else {
                    // 无法识别的ID格式
                    addError(errors, originalId, "ID格式未知");
                }
            } catch (Exception e) {
                addError(errors, originalId, "ID格式未知");
            }
        }

        // 批量查询数据库
        List<Article> allArticles = performBatchQueries(customIds, pmids, pmcIds, dois, db);

        // 检查未找到的ID并添加到错误列表
        addMissingIdErrors(allArticles, originalIdMap, errors);

        return new BatchQueryResult(allArticles, errors);
    }

    /**
     * 执行批量数据库查询
     */
    private List<Article> performBatchQueries(List<Long> customIds, List<Long> pmids,
                                              List<Long> pmcIds, List<String> dois, String db) {
        List<Article> allArticles = new ArrayList<>();

        // 查询自定义ID
        List<Article> articles = batchFindByCustomIds(customIds, db);
        if (CollUtil.isNotEmpty(articles)) {
            allArticles.addAll(articles);
        }

        // 查询PMID
        if (!pmids.isEmpty()) {
            allArticles.addAll(batchFindByPmids(pmids, db));
        }

        // 查询PMC ID
        if (!pmcIds.isEmpty()) {
            allArticles.addAll(batchFindByPmcIds(pmcIds, db));
        }

        // 查询DOI
        if (!dois.isEmpty()) {
            allArticles.addAll(batchFindByDois(dois, db));
        }

        return allArticles;
    }

    /**
     * 批量查询自定义ID
     */
    private List<Article> batchFindByCustomIds(List<Long> customIds, String db) {
        if (customIds.isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<Article> queryWrapper = Wrappers.<Article>lambdaQuery()
                .in(Article::getCustomId, customIds)
                .apply(StrUtil.isNotBlank(db), "source @> ARRAY[{0}]::text[]", SourceTypeEnums.processUserInput(db));

        return articleService.list(queryWrapper);
    }

    /**
     * 批量查询PMID
     */
    private List<Article> batchFindByPmids(List<Long> pmids, String db) {
        if (pmids.isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<Article> queryWrapper = Wrappers.<Article>lambdaQuery()
                .in(Article::getPmid, pmids)
                .apply(StrUtil.isNotBlank(db), "source @> ARRAY[{0}]::text[]", SourceTypeEnums.processUserInput(db));
        return articleService.list(queryWrapper);
    }

    /**
     * 批量查询PMC ID
     */
    private List<Article> batchFindByPmcIds(List<Long> pmcIds, String db) {
        if (pmcIds.isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<Article> queryWrapper = Wrappers.<Article>lambdaQuery()
                .in(Article::getPmcId, pmcIds)
                .apply(StrUtil.isNotBlank(db), "source @> ARRAY[{0}]::text[]", SourceTypeEnums.processUserInput(db));
        return articleService.list(queryWrapper);
    }

    /**
     * 批量查询DOI
     */
    private List<Article> batchFindByDois(List<String> dois, String db) {
        if (dois.isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<Article> queryWrapper = Wrappers.<Article>lambdaQuery()
                .in(Article::getDoi, dois)
                .apply(StrUtil.isNotBlank(db), "source @> ARRAY[{0}]::text[]", SourceTypeEnums.processUserInput(db));
        return articleService.list(queryWrapper);
    }

    /**
     * 添加错误信息
     */
    private void addError(List<ErrorItemVO> errors, String id, String errorMsg) {
        errors.add(createError(id, errorMsg));
    }

    /**
     * 检查未找到的ID并添加错误信息
     */
    private void addMissingIdErrors(List<Article> articles, Map<String, String> originalIdMap, List<ErrorItemVO> errors) {
        Set<String> foundIds = new HashSet<>();

        for (Article article : articles) {
            if (article.getCustomId() != null) {
                foundIds.add("CUSTOM_" + article.getCustomId());
            }
            if (article.getPmid() != null) {
                foundIds.add("PMID_" + article.getPmid());
            }
            if (article.getPmcId() != null) {
                foundIds.add("PMC_" + article.getPmcId());
            }
            if (StrUtil.isNotBlank(article.getDoi())) {
                foundIds.add("DOI_" + article.getDoi());
            }
        }

        // 添加未找到的ID到错误列表
        for (Map.Entry<String, String> entry : originalIdMap.entrySet()) {
            if (!foundIds.contains(entry.getKey())) {
                addError(errors, entry.getValue(), "数据未找到");
            }
        }
    }

    /**
     * 根据标题查询文章 - 仅返回一条记录
     */
    private Article findArticleByTitle(String title, String db) {
        LambdaQueryWrapper<Article> queryWrapper = Wrappers.<Article>lambdaQuery()
                .eq(Article::getTitle, title)
                .last("LIMIT 1");
        if (StrUtil.isNotBlank(db)) {
            queryWrapper.apply("source @> ARRAY[{0}]::text[]", db.toUpperCase());
        }
        return articleService.getOne(queryWrapper);
    }

    /**
     * 解析字段需求
     */
    private Set<String> parseFields(String fields) {
        if (StrUtil.isBlank(fields)) {
            return Set.of();
        }

        return Arrays.stream(fields.split(","))
                .map(String::trim)
                .collect(Collectors.toSet());
    }

    /**
     * 检查是否需要期刊信息
     */
    private boolean needsJournalInfo(Set<String> fieldSet) {
        return fieldSet.contains("journal")
                || fieldSet.contains("impact_factor")
                || fieldSet.contains("jcr_quartile")
                || fieldSet.contains("publisher");
    }

    /**
     * 检查是否需要影响因子信息
     */
    private boolean needsImpactFactorInfo(Set<String> fieldSet) {
        return fieldSet.contains("impact_factor") || fieldSet.contains("jcr_quartile");
    }

    /**
     * 批量加载期刊信息
     */
    private Map<Long, Journal> batchLoadJournals(List<Article> articles) {
        List<Long> journalIds = getJournalIds(articles);

        if (journalIds.isEmpty()) {
            return new HashMap<>();
        }

        List<Journal> journals = journalMapper.selectList(
                Wrappers.<Journal>lambdaQuery()
                        .in(Journal::getId, journalIds)
        );

        return journals.stream()
                .collect(Collectors.toMap(Journal::getId, journal -> journal));
    }

    /**
     * 获取期刊ID列表
     */
    private List<Long> getJournalIds(List<Article> articles) {
        List<Long> journalIds = articles.stream()
                .map(Article::getJournalId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        return journalIds;
    }

    /**
     * 批量加载出版社信息
     */
    private Map<Long, Publisher> batchLoadPublishers(Collection<Journal> journals) {
        List<Long> publisherIds = journals.stream()
                .map(Journal::getPublisherId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (publisherIds.isEmpty()) {
            return new HashMap<>();
        }

        List<Publisher> publishers = publisherMapper.selectList(
                Wrappers.<Publisher>lambdaQuery()
                        .in(Publisher::getId, publisherIds)
        );

        return publishers.stream()
                .collect(Collectors.toMap(Publisher::getId, publisher -> publisher));
    }

    /**
     * 批量加载影响因子信息
     */
    private Map<Long, List<TbDdsIfYear>> batchLoadImpactFactors(List<Article> articles) {
        List<Long> journalIds = getJournalIds(articles);

        if (journalIds.isEmpty()) {
            return new HashMap<>();
        }

        List<TbDdsIfYear> ifYears = tbDdsIfYearMapper.selectList(
                Wrappers.<TbDdsIfYear>lambdaQuery()
                        .in(TbDdsIfYear::getJournalId, journalIds)
                        .orderByDesc(TbDdsIfYear::getYear)
        );

        return ifYears.stream()
                .collect(Collectors.groupingBy(TbDdsIfYear::getJournalId));
    }

    /**
     * 加载中科院最新的分区信息
     */
    private TbDdsZkySection batchLoadZkySections(List<Article> articles) {
        List<Long> journalIds = getJournalIds(articles);
        if (journalIds.isEmpty()) {
            return new TbDdsZkySection();
        }
        TbDdsZkySection zkySections = tbDdsZkySectionMapper.selectOne(
                Wrappers.<TbDdsZkySection>lambdaQuery()
                        .in(TbDdsZkySection::getJournalId, journalIds)
                        .orderByDesc(TbDdsZkySection::getYear)
                        .last("LIMIT 1")
        );
        return zkySections;
    }

    /**
     * 使用预加载的Map转换为VO，避免重复查询
     */
    private ArticleDetailVO convertToDetailVOWithMaps(Article article, Set<String> fieldSet,
                                                      Map<Long, Journal> journalMap,
                                                      Map<Long, Publisher> publisherMap,
                                                      Map<Long, List<TbDdsIfYear>> ifYearMap,
                                                      TbDdsZkySection zkySection) {
        ArticleDetailVO vo = new ArticleDetailVO();

        // 基本信息转换
        BeanUtils.copyProperties(article, vo);
        vo.setPmcId(article.getPmcId() != null ? "PMC" + article.getPmcId() : null);
        vo.setCustomId(article.getCustomId() != null ? article.getCustomId().toString() : "");
        if (article.getJournalId() == null) {
            return vo;
        }
        Journal journal = journalMap.get(article.getJournalId());
        // 设置期刊基本信息
        if (journal != null && fieldSet.contains("journal")) {
            vo.setJournalTitle(journal.getTitle());
            vo.setIsoAbbreviation(journal.getIsoabbreviation());
            vo.setJcrAbbreviation(journal.getJcrabbreviation());
            vo.setPmcAbbreviation(journal.getPmcabbreviation());
            vo.setJournalIssnElectronic(journal.getIssnElectronic());
            vo.setJournalIssnPrint(journal.getIssnPrint());
        }

        // 设置出版社信息
        if (journal != null && fieldSet.contains("publisher") && journal.getPublisherId() != null) {
            Publisher publisher = publisherMap.get(journal.getPublisherId());
            if (publisher != null) {
                vo.setPublisherTitle(publisher.getName());
                vo.setPublisherIoc(publisher.getIoc());
            }
        }

        // 设置影响因子信息
        if (article.getJournalId() != null && needsImpactFactorInfo(fieldSet)) {
            setImpactFactorInfo(vo, article.getJournalId(), ifYearMap, fieldSet);
        }

        // 设置中科院分区信息
        if (zkySection != null && fieldSet.contains("zky_sections")) {
            setZkySectionInfo(vo, zkySection, fieldSet);
        }
        return vo;
    }

    /**
     * 设置影响因子相关信息
     */
    private void setImpactFactorInfo(ArticleDetailVO vo, Long journalId,
                                     Map<Long, List<TbDdsIfYear>> ifYearMap, Set<String> fieldSet) {
        List<TbDdsIfYear> ifYearList = ifYearMap.get(journalId);
        if (ifYearList == null || ifYearList.isEmpty()) {
            return;
        }

        // 获取最新年份的影响因子作为当前影响因子
        TbDdsIfYear latestIfYear = ifYearList.get(0);

        if (needsImpactFactorInfo(fieldSet)) {
            vo.setImpactFactor(parseDouble(latestIfYear.getImpactFactor()));
            vo.setJcrQuartile(latestIfYear.getJcrQuartile());
        }

        // 构建历史影响因子列表
        if (needsImpactFactorInfo(fieldSet)) {
            List<HistoricalImpactFactorVO> historicalFactors = new ArrayList<>();
            for (TbDdsIfYear ifYear : ifYearList) {
                HistoricalImpactFactorVO factorVO = new HistoricalImpactFactorVO();
                factorVO.setYear(Integer.parseInt(ifYear.getYear()));
                factorVO.setImpactFactor(parseDouble(ifYear.getImpactFactor()));
                if (StrUtil.isNotBlank(ifYear.getJcrQuartile())) {
                    factorVO.setJcrQuartile(ifYear.getJcrQuartile());
                }
                historicalFactors.add(factorVO);
            }
            vo.setHistoricalImpactFactors(historicalFactors);
        }
    }

    /**
     * 设置中科院分区相关信息
     */
    private void setZkySectionInfo(ArticleDetailVO vo, TbDdsZkySection zkySection, Set<String> fieldSet) {
        if (fieldSet.contains("zky_sections")) {
            ZkySectionVO zkySectionVO = new ZkySectionVO();
            BeanUtils.copyProperties(zkySection, zkySectionVO);
            vo.setZkySections(zkySectionVO);
        }
    }

    /**
     * 安全解析Double值
     */
    private Double parseDouble(String value) {
        if (StrUtil.isBlank(value)) {
            return null;
        }
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 统一构建响应方法
     */
    private ArticleSearchResponseVO buildUnifiedResponse(BatchQueryResult batchResult, String fields, List<String> originalInputs) {
        ArticleSearchResponseVO response = new ArticleSearchResponseVO();

        List<Article> articles = batchResult.getArticles();
        List<ErrorItemVO> errors = batchResult.getErrors();

        // 解析字段需求
        Set<String> fieldSet = parseFields(fields);

        // 按需批量查询期刊和出版社信息
        Map<Long, Journal> journalMap = new HashMap<>();
        Map<Long, Publisher> publisherMap = new HashMap<>();
        Map<Long, List<TbDdsIfYear>> ifYearMap = new HashMap<>();
        TbDdsZkySection zkySection = new TbDdsZkySection();

        if (!articles.isEmpty()) {
            boolean needsJournalInfo = needsJournalInfo(fieldSet);
            boolean needsPublisherInfo = fieldSet.contains("publisher");
            boolean needsIfInfo = needsImpactFactorInfo(fieldSet);
            boolean needsZkyInfo = fieldSet.contains("zky_sections");

            if (needsJournalInfo) {
                journalMap = batchLoadJournals(articles);
            }
            if (needsPublisherInfo && !journalMap.isEmpty()) {
                publisherMap = batchLoadPublishers(journalMap.values());
            }
            if (needsIfInfo) {
                ifYearMap = batchLoadImpactFactors(articles);
            }
            if (needsZkyInfo) {
                zkySection = batchLoadZkySections(articles);
            }
        }

        // 使用final变量在lambda中引用
        final Map<Long, Journal> finalJournalMap = journalMap;
        final Map<Long, Publisher> finalPublisherMap = publisherMap;
        final Map<Long, List<TbDdsIfYear>> finalIfYearMap = ifYearMap;
        final Set<String> finalFieldSet = fieldSet;
        final TbDdsZkySection finalZkySection = zkySection;

        // 转换为VO
        List<ArticleDetailVO> successList = articles.stream()
                .map(article -> convertToDetailVOWithMaps(article, finalFieldSet, finalJournalMap, finalPublisherMap, finalIfYearMap, finalZkySection))
                .collect(Collectors.toList());

        // 设置汇总信息
        SummaryVO summary = new SummaryVO();
        summary.setTotal(originalInputs.size());
        summary.setSuccess(successList.size());
        summary.setError(errors.size());

        response.setSummary(summary);
        response.setData(successList);
        response.setErrorItems(errors);

        return response;
    }

    /**
     * 构建错误响应
     */
    private ArticleSearchResponseVO buildErrorResponse(String input, String errorMsg, String fields) {
        List<ErrorItemVO> errors = List.of(createError(input, errorMsg));
        BatchQueryResult batchResult = new BatchQueryResult(new ArrayList<>(), errors);
        return buildUnifiedResponse(batchResult, fields, List.of(input));
    }

    /**
     * 创建错误项
     */
    private ErrorItemVO createError(String id, String errorMsg) {
        ErrorItemVO errorItem = new ErrorItemVO();
        errorItem.setId(id);
        errorItem.setErrorMsg(errorMsg);
        return errorItem;
    }

    /**
     * 批量查询结果类
     */
    private static class BatchQueryResult {
        private final List<Article> articles;
        private final List<ErrorItemVO> errors;

        public BatchQueryResult(List<Article> articles, List<ErrorItemVO> errors) {
            this.articles = articles;
            this.errors = errors;
        }

        public List<Article> getArticles() {
            return articles;
        }

        public List<ErrorItemVO> getErrors() {
            return errors;
        }
    }

    /**
     * PDF文件信息类
     */
    private static class PdfFileInfo {
        private final TbDdsFile pdfFile;
        private final String fullPath;

        public PdfFileInfo(TbDdsFile pdfFile, String fullPath) {
            this.pdfFile = pdfFile;
            this.fullPath = fullPath;
        }

        public TbDdsFile getPdfFile() {
            return pdfFile;
        }

        public String getFullPath() {
            return fullPath;
        }
    }
}
