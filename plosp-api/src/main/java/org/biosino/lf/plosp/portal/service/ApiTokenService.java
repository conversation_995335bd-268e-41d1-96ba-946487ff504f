package org.biosino.lf.plosp.portal.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.UserApiKey;
import org.biosino.lf.pds.article.service.IUserApiKeyService;
import org.biosino.lf.pds.common.constant.CacheConstants;
import org.biosino.lf.pds.common.constant.Constants;
import org.biosino.lf.pds.common.core.redis.RedisCache;
import org.biosino.lf.pds.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;

import java.util.concurrent.TimeUnit;

/**
 * API Token验证服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiTokenService {

    private final RedisCache redisCache;
    private final IUserApiKeyService userApiKeyService;

    // 令牌自定义标识
    @Value("${token.header:Authorization}")
    private String header;

    private static final String API_TOKEN_CACHE_KEY = CacheConstants.API_TOKEN_KEY;
    private static final int CACHE_EXPIRE_MINUTES = 30;

    /**
     * 从请求Header中验证Token
     *
     * @param request HTTP请求
     * @return 验证通过返回用户API密钥信息，否则返回null
     */
    public UserApiKey validateTokenFromHeader(HttpServletRequest request) {
        String token = getTokenFromHeader(request);

        if (StrUtil.isBlank(token)) {
            log.warn("Token为空");
            return null;
        }

        return validateToken(token);
    }

    /**
     * 验证API Token
     *
     * @param token API Token
     * @return 验证通过返回用户API密钥信息，否则返回null
     */
    public UserApiKey validateToken(String token) {
        if (StrUtil.isBlank(token) || "null".equals(token)) {
            return null;
        }

        try {
            String cacheKey = getTokenCacheKey(token);

            // 先从缓存获取
            UserApiKey cachedApiKey = redisCache.getCacheObject(cacheKey);
            if (cachedApiKey != null) {
                log.debug("Token验证成功(缓存)，用户ID: {}", cachedApiKey.getUserId());
                return cachedApiKey;
            }

            // 缓存未命中，查询数据库
            LambdaQueryWrapper<UserApiKey> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserApiKey::getApiKey, token);
            UserApiKey userApiKey = userApiKeyService.getOne(wrapper);

            if (userApiKey == null) {
                log.warn("API密钥不存在: {}", maskApiKey(token));
                return null;
            }

            // 缓存用户API密钥信息
            redisCache.setCacheObject(cacheKey, userApiKey, CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);

            log.info("Token验证成功(数据库)，用户ID: {}, 密钥名称: {}", userApiKey.getUserId(), userApiKey.getKeyName());
            return userApiKey;

        } catch (Exception e) {
            log.error("验证Token异常", e);
            return null;
        }
    }

    /**
     * 从请求Header中获取Token
     */
    private String getTokenFromHeader(HttpServletRequest request) {
        String token = request.getHeader(header);

        // 优先从Header获取
        if (StringUtils.isNotEmpty(token)) {
            if (token.startsWith(Constants.TOKEN_PREFIX)) {
                token = token.replace(Constants.TOKEN_PREFIX, "");
                return token;
            }
        }
        return null;
    }

    /**
     * 校验Token是否有效（仅校验，不返回用户信息）
     */
    public boolean isValidToken(String token) {
        return validateToken(token) != null;
    }

    /**
     * 校验Header中的Token是否有效
     */
    public boolean isValidTokenFromHeader(HttpServletRequest request) {
        return validateTokenFromHeader(request) != null;
    }

    /**
     * 清除Token缓存
     */
    public void clearTokenCache(String token) {
        if (StrUtil.isNotBlank(token)) {
            String cacheKey = getTokenCacheKey(token);
            redisCache.deleteObject(cacheKey);
            log.info("清除Token缓存: {}", maskApiKey(token));
        }
    }

    /**
     * 清除所有Token缓存
     */
    public void clearAllTokenCache() {
        try {
            redisCache.deleteObject(redisCache.keys(API_TOKEN_CACHE_KEY + "*"));
            log.info("清除所有API Token缓存");
        } catch (Exception e) {
            log.error("清除Token缓存失败", e);
        }
    }

    private String getTokenCacheKey(String token) {
        return API_TOKEN_CACHE_KEY + token;
    }

    /**
     * API密钥脱敏显示
     */
    private String maskApiKey(String apiKey) {
        if (StrUtil.isBlank(apiKey) || apiKey.length() <= 8) {
            return "****";
        }
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }
}
