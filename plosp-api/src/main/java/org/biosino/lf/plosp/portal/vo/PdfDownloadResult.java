package org.biosino.lf.plosp.portal.vo;

import org.springframework.http.HttpStatus;

/**
 * PDF下载结果封装类
 */
public class PdfDownloadResult {

    private boolean success;
    private byte[] fileData;
    private String fileName;
    private int errorCode;
    private String errorMessage;
    private HttpStatus httpStatus;

    private PdfDownloadResult() {}

    public static PdfDownloadResult success(byte[] fileData, String fileName) {
        PdfDownloadResult result = new PdfDownloadResult();
        result.success = true;
        result.fileData = fileData;
        result.fileName = fileName;
        return result;
    }

    public static PdfDownloadResult error(int errorCode, String errorMessage, HttpStatus httpStatus) {
        PdfDownloadResult result = new PdfDownloadResult();
        result.success = false;
        result.errorCode = errorCode;
        result.errorMessage = errorMessage;
        result.httpStatus = httpStatus;
        return result;
    }

    // Getters
    public boolean isSuccess() { return success; }
    public byte[] getFileData() { return fileData; }
    public String getFileName() { return fileName; }
    public int getErrorCode() { return errorCode; }
    public String getErrorMessage() { return errorMessage; }
    public HttpStatus getHttpStatus() { return httpStatus; }
}
