package org.biosino.lf.plosp.portal.service;

import org.biosino.lf.plosp.portal.vo.ArticleSearchResponseVO;
import org.biosino.lf.plosp.portal.vo.PdfDownloadResult;

/**
 * <AUTHOR>
 */
public interface IArticleSearchService {

    /**
     * 通过ID获取文献题录等信息
     *
     * @param id     文献ID，支持自定义ID、PMID、PMCID、DOI，多个数据之间使用逗号分隔
     * @param db     文献来源数据库
     * @param fields 可选的返回字段列表
     * @return 文献检索结果
     */
    ArticleSearchResponseVO findById(String id, String db, String fields);

    /**
     * 通过标题获取文献题录等信息
     *
     * @param title  文章标题（精确查询，不支持批量查询）
     * @param db     文献来源数据库
     * @param fields 可选的返回字段列表，默认无指定，详细见下表
     * @return 查询结果
     */
    ArticleSearchResponseVO findByTitle(String title, String db, String fields);

    /**
     * 根据ID下载文献PDF
     *
     * @param id 文献ID
     * @return PDF下载结果
     */
    PdfDownloadResult downloadPdfById(String id);
}
