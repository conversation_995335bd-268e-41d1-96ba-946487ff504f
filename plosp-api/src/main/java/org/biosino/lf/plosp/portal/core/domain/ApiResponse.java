package org.biosino.lf.plosp.portal.core.domain;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.biosino.lf.plosp.portal.enums.ArticleStatusCodeEnum;
import org.biosino.lf.plosp.portal.vo.ErrorItemVO;
import org.biosino.lf.plosp.portal.vo.SummaryVO;

/**
 * PLOSP API 响应信息主体
 *
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成功状态码
     */
    public static final int SUCCESS = ArticleStatusCodeEnum.SUCCESS.getCode();

    /**
     * 失败状态码
     */
    public static final int FAIL = ArticleStatusCodeEnum.INTERNAL_SERVER_ERROR.getCode();

    private int code;
    private String message;
    private SummaryVO summary;
    private T data;
    private List<ErrorItemVO> errorItems;

    public ApiResponse() {
    }

    public ApiResponse(int code, String message, T data, SummaryVO summary, List<ErrorItemVO> errorItems) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.summary = summary;
        this.errorItems = errorItems;
    }

    // 成功响应方法
    public static <T> ApiResponse<T> ok() {
        return restResult(null, SUCCESS, "操作成功", null, null);
    }

    public static <T> ApiResponse<T> ok(T data) {
        return restResult(data, SUCCESS, "操作成功", null, null);
    }

    public static <T> ApiResponse<T> ok(T data, String message) {
        return restResult(data, SUCCESS, message, null, null);
    }

    public static <T> ApiResponse<T> ok(T data, SummaryVO summary, List<ErrorItemVO> errorItems) {
        return restResult(data, SUCCESS, "操作成功", summary, errorItems);
    }

    public static <T> ApiResponse<T> ok(T data, SummaryVO summary, List<ErrorItemVO> errorItems, String message) {
        return restResult(data, SUCCESS, message, summary, errorItems);
    }

    // 失败响应方法
    public static <T> ApiResponse<T> fail() {
        return restResult(null, FAIL, "操作失败", null, null);
    }

    public static <T> ApiResponse<T> fail(String message) {
        return restResult(null, FAIL, message, null, null);
    }

    public static <T> ApiResponse<T> fail(int code, String message) {
        return restResult(null, code, message, null, null);
    }

    public static <T> ApiResponse<T> fail(int code, String message, List<ErrorItemVO> errorItems) {
        return restResult(null, code, message, null, errorItems);
    }

    // 未授权响应
    public static <T> ApiResponse<T> unauthorized(String message) {
        return restResult(null, 401, message, null, null);
    }

    // 核心构建方法
    private static <T> ApiResponse<T> restResult(T data, int code, String message, SummaryVO summary, List<ErrorItemVO> errorItems) {
        ApiResponse<T> apiResult = new ApiResponse<>();
        apiResult.setCode(code);
        apiResult.setMessage(message);
        apiResult.setData(data);
        apiResult.setSummary(summary);
        apiResult.setErrorItems(errorItems);
        return apiResult;
    }

    // 判断方法
    public static <T> Boolean isError(ApiResponse<T> ret) {
        return !isSuccess(ret);
    }

    public static <T> Boolean isSuccess(ApiResponse<T> ret) {
        return SUCCESS == ret.getCode();
    }

    @Override
    public String toString() {
        return "ApiResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", summary=" + summary +
                ", data=" + data +
                ", errorItems=" + errorItems +
                '}';
    }
}
