package org.biosino.lf.plosp.portal.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.biosino.lf.pds.article.domain.UserApiKey;
import org.biosino.lf.pds.common.utils.ServletUtils;
import org.biosino.lf.pds.common.utils.ip.IpUtils;
import org.biosino.lf.plosp.portal.annotation.ApiValidation;
import org.biosino.lf.plosp.portal.core.domain.ApiResponse;
import org.biosino.lf.plosp.portal.service.ApiLogService;
import org.biosino.lf.plosp.portal.service.ApiTokenService;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;

/**
 * API验证切面
 * 统一处理Token验证和日志记录
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@Order(1)
@RequiredArgsConstructor
public class ApiValidationAspect {

    private final ApiTokenService apiTokenService;
    private final ApiLogService apiLogService;

    @Around("@annotation(apiValidation)")
    public Object around(ProceedingJoinPoint joinPoint, ApiValidation apiValidation) throws Throwable {
        long startTime = System.currentTimeMillis();

        // 获取方法信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String interfaceName = getInterfaceName(apiValidation, signature);

        // 获取请求信息
        HttpServletRequest request = ServletUtils.getRequest();
        String clientIp = IpUtils.getIpAddr(request);
        String requestUri = request.getRequestURI();
        String httpMethod = request.getMethod();

        String userId = "unknown";

        try {
            // Token验证
            if (apiValidation.requireAuth()) {
                UserApiKey userApiKey = apiTokenService.validateTokenFromHeader(request);
                if (userApiKey == null) {
                    recordLogWithResponse(apiValidation, interfaceName, httpMethod, clientIp, userId, requestUri, 401, startTime, "未授权或Token无效");
                    return ApiResponse.unauthorized("未授权或Token无效");
                }
                userId = userApiKey.getUserId().toString();
            }

            // 执行目标方法
            Object result = joinPoint.proceed();

            // 记录成功日志（包含响应数据）
            String responseData = null;
            if (result != null) {
                try {
                    responseData = new ObjectMapper().writeValueAsString(result);
                } catch (Exception e) {
                    responseData = "响应序列化失败";
                }
            }

            recordLogWithResponse(apiValidation, interfaceName, httpMethod, clientIp, userId, requestUri, 200, startTime, responseData);

            return result;

        } catch (Exception e) {
            // 记录异常日志
            recordLogWithResponse(apiValidation, interfaceName, httpMethod, clientIp, userId, requestUri, 500, startTime, "服务内部错误: " + e.getMessage());

            log.error("API调用异常: {}", interfaceName, e);

            if (isApiResponseReturnType(signature)) {
                return ApiResponse.fail("服务内部错误: " + e.getMessage());
            }
            throw e;
        }
    }


    /**
     * 获取接口名称
     */
    private String getInterfaceName(ApiValidation apiValidation, MethodSignature signature) {
        if (!apiValidation.value().isEmpty()) {
            return apiValidation.value();
        }
        return signature.getDeclaringTypeName() + "." + signature.getName();
    }

    /**
     * 判断返回类型是否为ApiResponse
     */
    private boolean isApiResponseReturnType(MethodSignature signature) {
        Class<?> returnType = signature.getReturnType();
        return ApiResponse.class.isAssignableFrom(returnType);
    }

    private void recordLogWithResponse(ApiValidation apiValidation, String interfaceName, String httpMethod,
                                       String clientIp, String userId, String requestUri, int status, long startTime, String responseData) {
        if (apiValidation.logEnabled()) {
            long responseTime = System.currentTimeMillis() - startTime;
            apiLogService.recordApiLogAsync(interfaceName, httpMethod, clientIp, userId, requestUri, status, responseTime, responseData);
        }
    }
}
