package org.biosino.lf.plosp.portal.service;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.TbDdsApiLog;
import org.biosino.lf.pds.common.utils.ServletUtils;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;

import java.util.Date;

/**
 * API日志记录服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiLogService {

    private final TbDdsApiLogService tbDdsApiLogService;

    public void recordApiLogAsync(String interfaceName, String requestMethod, String clientIp,
                                  String userId, String requestUrl, int status, long responseTime) {
        recordApiLogAsync(interfaceName, requestMethod, clientIp, userId, requestUrl, status, responseTime, null);
    }

    public void recordApiLogAsync(String interfaceName, String requestMethod, String clientIp,
                                  String userId, String requestUrl, int status, long responseTime, String responseData) {
        // 在主线程中提取请求参数
        String requestParams;
        try {
            HttpServletRequest request = ServletUtils.getRequest();
            requestParams = getRequestParams(request);
        } catch (Exception e) {
            log.warn("获取请求参数失败", e);
            requestParams = "参数获取失败";
        }

        // 将参数传递给异步线程
        final String finalRequestParams = requestParams;

        ThreadUtil.execAsync(() -> {
            try {
                TbDdsApiLog apiLog = new TbDdsApiLog();
                apiLog.setInterfaceName(StrUtil.sub(interfaceName, 0, 200));
                apiLog.setRequestMethod(StrUtil.sub(requestMethod, 0, 10));
                apiLog.setRequestParams(StrUtil.sub(finalRequestParams, 0, 2000));
                apiLog.setClientIp(StrUtil.sub(clientIp, 0, 50));
                apiLog.setUserId(StrUtil.sub(userId, 0, 50));
                apiLog.setRequestUrl(StrUtil.sub(requestUrl, 0, 500));
                apiLog.setResponseData(responseData);
                apiLog.setStatus(status);
                apiLog.setResponseTime(responseTime);
                apiLog.setCreateTime(new Date());

                tbDdsApiLogService.save(apiLog);
            } catch (Exception e) {
                log.error("记录API日志失败: {}", interfaceName, e);
            }
        });
    }

    /**
     * 获取请求参数字符串
     */
    private String getRequestParams(HttpServletRequest request) {
        StringBuilder params = new StringBuilder();

        try {
            String pathInfo = request.getPathInfo();
            if (StrUtil.isNotBlank(pathInfo)) {
                params.append("Path: ").append(pathInfo);
            }

            // GET查询参数
            String queryString = request.getQueryString();
            if (StrUtil.isNotBlank(queryString)) {
                if (!params.isEmpty()) {
                    params.append(", ");
                }
                params.append("Query: ").append(queryString);
            }

            // POST表单参数（只在POST请求时记录）
            if ("POST".equalsIgnoreCase(request.getMethod())) {
                request.getParameterMap().forEach((key, values) -> {
                    // 避免重复记录查询参数中已有的参数
                    if (queryString == null || !queryString.contains(key + "=")) {
                        if (!params.isEmpty()) {
                            params.append(", ");
                        }
                        params.append("Form: ").append(key).append("=");
                        if (values != null && values.length > 0) {
                            params.append(String.join(",", values));
                        }
                    }
                });
            }

        } catch (Exception e) {
            log.warn("获取请求参数失败", e);
            return "参数获取失败";
        }

        return params.toString();
    }
}
