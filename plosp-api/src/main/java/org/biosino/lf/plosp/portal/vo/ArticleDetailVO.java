package org.biosino.lf.plosp.portal.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ArticleDetailVO {
    private String customId;
    private Long pmid;
    private String pmcId;
    private List<String> source;
    private String doi;
    private String title;
    private List<String> author;
    private List<String> abs;
    private List<String> keywords;
    private Integer year;
    private String volume;
    private String issue;
    private String page;
    private String publisherTitle;
    private String publisherIoc;
    private String journalTitle;
    private String isoAbbreviation;
    private String jcrAbbreviation;
    private String pmcAbbreviation;
    private String journalIssnElectronic;
    private String journalIssnPrint;
    private Double impactFactor;
    private String jcrQuartile;
    private List<HistoricalImpactFactorVO> historicalImpactFactors;
    private ZkySectionVO zkySections;
}
