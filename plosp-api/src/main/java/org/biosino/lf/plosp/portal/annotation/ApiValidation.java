package org.biosino.lf.plosp.portal.annotation;

import java.lang.annotation.*;

/**
 * API验证注解
 * 用于标记需要进行Token验证和日志记录的方法
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiValidation {

    /**
     * 接口名称，默认为类名.方法名
     */
    String value() default "";

    /**
     * 是否需要Token验证，默认为true
     */
    boolean requireAuth() default true;

    /**
     * 是否记录日志，默认为true
     */
    boolean logEnabled() default true;
}
