package org.biosino.lf.plosp.portal;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

/**
 * PLOSPApi应用程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"org.biosino.lf.pds.**", "org.biosino.lf.plosp.**"})
@MapperScan({"org.biosino.lf.plosp.portal.mapper", "org.biosino.lf.pds.article.mapper", "org.biosino.lf.pds.system.mapper"})
public class PlospApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(PlospApiApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  PLOSP API启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
