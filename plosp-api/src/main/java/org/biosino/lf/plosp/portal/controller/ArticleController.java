package org.biosino.lf.plosp.portal.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.plosp.portal.annotation.ApiValidation;
import org.biosino.lf.plosp.portal.core.domain.ApiResponse;
import org.biosino.lf.plosp.portal.enums.ArticleStatusCodeEnum;
import org.biosino.lf.plosp.portal.service.IArticleSearchService;
import org.biosino.lf.plosp.portal.vo.ArticleDetailVO;
import org.biosino.lf.plosp.portal.vo.ArticleSearchResponseVO;
import org.biosino.lf.plosp.portal.vo.PdfDownloadResult;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 文献检索相关接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/article")
@Tag(name = "文献检索", description = "文献检索相关接口")
public class ArticleController {

    private final IArticleSearchService articleSearchService;

    @Operation(summary = "通过ID获取文献题录等信息")
    @GetMapping("/findById")
    @ApiValidation("ArticleController.findById")
    public ApiResponse<List<ArticleDetailVO>> findById(
            @RequestParam String id,
            @RequestParam(required = false) String db,
            @RequestParam(required = false) String fields) {

        ArticleSearchResponseVO result = articleSearchService.findById(id, db, fields);
        return ApiResponse.ok(result.getData(), result.getSummary(), result.getErrorItems());
    }

    @Operation(summary = "通过标题获取文献题录等信息")
    @GetMapping("/findByTitle")
    @ApiValidation("ArticleController.findByTitle")
    public ApiResponse<List<ArticleDetailVO>> findByTitle(
            @RequestParam String title,
            @RequestParam(required = false) String db,
            @RequestParam(required = false) String fields) {

        ArticleSearchResponseVO result = articleSearchService.findByTitle(title, db, fields);
        return ApiResponse.ok(result.getData(), result.getSummary(), result.getErrorItems());
    }

    @Operation(summary = "获取文献全文PDF")
    @GetMapping("/downPdfById")
    @ApiValidation("ArticleController.downPdfById")
    public ResponseEntity<?> downPdfById(@RequestParam String id) {
        try {
            PdfDownloadResult result = articleSearchService.downloadPdfById(id);

            if (!result.isSuccess()) {
                return ResponseEntity.status(result.getHttpStatus())
                        .body(ApiResponse.fail(result.getErrorCode(), result.getErrorMessage()));
            }

            // 构建响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", result.getFileName());
            headers.setContentLength(result.getFileData().length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(new ByteArrayResource(result.getFileData()));

        } catch (Exception e) {
            log.error("下载PDF文件异常: id={}", id, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.fail(ArticleStatusCodeEnum.INTERNAL_SERVER_ERROR.getCode(), "服务器内部错误"));
        }
    }
}
