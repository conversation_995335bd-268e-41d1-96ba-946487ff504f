package org.biosino.lf.plosp.portal.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.UserApiKey;
import org.biosino.lf.pds.common.utils.ip.IpUtils;
import org.biosino.lf.plosp.portal.enums.ArticleStatusCodeEnum;
import org.biosino.lf.plosp.portal.service.ApiLogService;
import org.biosino.lf.plosp.portal.service.ApiTokenService;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.HashMap;
import java.util.Map;

/**
 * API Token验证拦截器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApiTokenInterceptor implements HandlerInterceptor {

    private final ApiTokenService apiTokenService;
    private final ApiLogService apiLogService;
    private final ObjectMapper objectMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        long startTime = System.currentTimeMillis();
        String requestUri = request.getRequestURI();

        // 排除不需要验证的接口
        if (isExcludedPath(requestUri)) {
            return true;
        }

        try {
            // 验证Token
            UserApiKey userApiKey = apiTokenService.validateTokenFromHeader(request);
            if (userApiKey == null) {
                // Token验证失败，记录日志并返回401
                long responseTime = System.currentTimeMillis() - startTime;
                apiLogService.recordApiLogAsync(
                        "TokenValidation",
                        request.getMethod(),
                        IpUtils.getIpAddr(request),
                        "unknown",
                        requestUri,
                        ArticleStatusCodeEnum.UNAUTHORIZED.getCode(),
                        responseTime
                );

                // 返回401错误
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("application/json;charset=UTF-8");

                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", ArticleStatusCodeEnum.UNAUTHORIZED.getCode());
                errorResponse.put("msg", "未授权或Token无效");

                response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
                return false;
            }

            // 将站点信息存储到请求属性中，供后续使用
            request.setAttribute("currentSite", userApiKey);
            request.setAttribute("siteId", userApiKey.getId().toString());

            return true;

        } catch (Exception e) {
            log.error("Token验证异常", e);

            // 记录异常日志
            long responseTime = System.currentTimeMillis() - startTime;
            apiLogService.recordApiLogAsync(
                    "TokenValidation",
                    request.getMethod(),
                    IpUtils.getIpAddr(request),
                    "unknown",
                    requestUri,
                    ArticleStatusCodeEnum.INTERNAL_SERVER_ERROR.getCode(),
                    responseTime
            );

            // 返回500错误
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setContentType("application/json;charset=UTF-8");

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", ArticleStatusCodeEnum.INTERNAL_SERVER_ERROR.getCode());
            errorResponse.put("msg", "服务内部错误");

            response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
            return false;
        }
    }

    /**
     * 判断是否为排除路径
     */
    private boolean isExcludedPath(String requestUri) {
        // 排除健康检查、文档等不需要验证的接口
        String[] excludedPaths = {
                "/health",
                "/actuator",
                "/swagger-ui",
                "/v3/api-docs",
                "/swagger-resources",
                "/webjars",
                "/favicon.ico"
        };

        for (String excludedPath : excludedPaths) {
            if (requestUri.startsWith(excludedPath)) {
                return true;
            }
        }

        return false;
    }
}
