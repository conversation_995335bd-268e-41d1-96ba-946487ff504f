package org.biosino.lf.plosp.portal.enums;

/**
 * 文章查询相关状态码枚举
 *
 * <AUTHOR>
 */
public enum ArticleStatusCodeEnum {

    // 成功状态
    SUCCESS(200, "操作成功"),

    // 客户端错误
    BAD_REQUEST(400, "参数非法"),
    UNAUTHORIZED(401, "未授权或Token无效"),
    ARTICLE_NOT_FOUND(402, "该文章在数据库中不存在"),
    PDF_NOT_FOUND(403, "该文章未找到PDF附件"),
    NOT_FOUND(404, "资源未找到"),

    // 服务器错误
    INTERNAL_SERVER_ERROR(500, "服务内部错误");

    private final int code;
    private final String message;

    ArticleStatusCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
