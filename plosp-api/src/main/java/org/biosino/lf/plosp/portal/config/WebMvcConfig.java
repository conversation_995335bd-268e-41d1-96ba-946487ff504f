package org.biosino.lf.plosp.portal.config;

import lombok.RequiredArgsConstructor;
import org.biosino.lf.plosp.portal.interceptor.ApiTokenInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置
 *
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
public class WebMvcConfig implements WebMvcConfigurer {

    private final ApiTokenInterceptor apiTokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(apiTokenInterceptor)
                // 拦截所有API接口
                .addPathPatterns("/api/**")
                .excludePathPatterns(
                        // 健康检查
                        "/api/health",
                        // Swagger文档
                        "/api/swagger-ui/**",
                        // API文档
                        "/api/v3/api-docs/**"
                );
    }
}
