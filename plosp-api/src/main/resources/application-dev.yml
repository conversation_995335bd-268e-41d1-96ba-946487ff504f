# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8082
  servlet:
    # 应用的访问路径
    context-path: /

# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: org.postgresql.Driver
    url: **************************************************************************************************************************************************************
    username: postgres
    password: Biosino+2025
    druid:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT version()
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: portal
        login-password: portal123
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  data:
    redis:
      # 地址
      host: ************
      # 端口，默认为6379
      port: 32641
      # 数据库索引 (使用不同的数据库索引以避免与admin冲突)
      database: 3
      # 密码
      password:
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
  jackson:
    default-property-inclusion: non_empty


# Elasticsearch配置
easy-es:
  enable: true
  address: ************:31000
  global-config:
    print-dsl: true

# 日志配置
logging:
  level:
    org.biosino.lf.plosp.portal: debug
    org.biosino.lf.pds: debug
    org.springframework.security: info
    org.springframework.web: info
    org.springframework: info
  file:
    name: logs/plosp-portal-dev.log

# 应用配置
app:
  # 公开文件路径(在ResourcesConfig已配置为资源路径，可通过url直接访问)
  profile: D:/tmp/plosp-portal/uploadPath
  # 数据文件路径
  data-home: D:/tmp/plosp-portal/data

# 开发环境特定的token配置
token:
  # 开发环境令牌有效期更长（单位分钟）
  expireTime: 1440

download:
  # 下载文件的临时目录
  temp-dir: D:/download

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /api/auth/register,/api/auth/login
  # 匹配链接
  urlPatterns: /api/*
