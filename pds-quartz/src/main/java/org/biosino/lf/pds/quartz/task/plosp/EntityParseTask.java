package org.biosino.lf.pds.quartz.task.plosp;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.plosp.task.service.EntityParseService;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/8/27
 */
@Slf4j
@Component("entityParseTask")
@RequiredArgsConstructor
public class EntityParseTask {

    private final EntityParseService entityParseService;

    /**
     * 发送实体解析开始的消息
     */
    public void pushStartMsg() {
        try {
            entityParseService.pushStartMsg();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
