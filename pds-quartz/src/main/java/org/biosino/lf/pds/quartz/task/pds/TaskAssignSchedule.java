package org.biosino.lf.pds.quartz.task.pds;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.TbDdsTask;
import org.biosino.lf.pds.common.enums.task.TaskStatusEnum;
import org.biosino.lf.pds.task.service.ITbDdsTaskService;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * PDS任务分配定时任务
 * 负责定期检查待分配任务并触发分配流程
 *
 * <AUTHOR>
 */
@Slf4j
@Component("taskAssignSch")
@RequiredArgsConstructor
public class TaskAssignSchedule {

    private final ITbDdsTaskService tbDdsTaskService;

    /**
     * 定时任务执行方法
     * 每30s执行一次（0/30 * * * * ?），查询状态为创建(create)的任务并进行分配
     * 使用分页查询避免一次性加载过多数据，提高系统性能和稳定性
     */
    public void scheduleAssignTask() {
        final long start = System.currentTimeMillis();
        log.info("开始分配任务...");
        int pageNo = 1, pageSize = 10;
        List<TbDdsTask> tasks;
        LambdaQueryWrapper<TbDdsTask> queryWrapper = Wrappers.lambdaQuery(TbDdsTask.class)
                .eq(TbDdsTask::getStatus, TaskStatusEnum.create.name())
                .orderByAsc(TbDdsTask::getCreateTime)
                .select(TbDdsTask::getId);
        do {
            // 分页查询待分配任务
            tasks = tbDdsTaskService.list(PageDTO.of(pageNo++, pageSize), queryWrapper);
            if (CollUtil.isEmpty(tasks)) {
                break;
            }
            // 逐个处理待分配任务
            for (TbDdsTask task : tasks) {
                tbDdsTaskService.schedule(task.getId());
            }
        } while (true);
        log.info(StrUtil.format("结束分配任务. 耗时：{}", DateUtil.formatBetween(System.currentTimeMillis() - start, BetweenFormatter.Level.SECOND)));
    }

    /**
     * 定时清理超时任务
     * 每10分钟执行一次（0 0/10 * * * ?）
     */
    public void cleanTaskTimeout() {
        final long start = System.currentTimeMillis();
        log.info("开始处理超时执行中的任务...");
        tbDdsTaskService.scheduleExecutingTaskTimeout();
        log.info(StrUtil.format("结束处理超时任务. 耗时：{}", DateUtil.formatBetween(System.currentTimeMillis() - start, BetweenFormatter.Level.SECOND)));
    }

    /**
     * 系统空闲时，自动给批量站点分配任务
     * 每15分钟执行一次 (0 0/15 * * * ?)
     */
    public void autoCreateTask() {
        final long start = System.currentTimeMillis();
        log.info("开始自动给批量站点分配任务...");
        tbDdsTaskService.scheduleAutoCreateTask();
        log.info(StrUtil.format("结束自动给批量站点分配任务. 耗时：{}", DateUtil.formatBetween(System.currentTimeMillis() - start, BetweenFormatter.Level.SECOND)));
    }

}
