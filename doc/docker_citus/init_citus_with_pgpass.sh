#!/usr/bin/env bash
set -euo pipefail

# init_and_deploy_pgpass_compose_simple.sh (container-name-based hosts)
# Simplified, compose-specific init script that:
# - waits for containers to be ready
# - creates Citus extension
# - sets citus_set_coordinator_host on workers (using container_name as host)
# - sets postgres password on all nodes
# - creates and deploys .pgpass to coordinator & workers (hosts = container_name)
# - registers workers on coordinator via citus_add_node(..., noderole := 'primary') using container_name
# - prints verification outputs
#
# NOTE: edit the variables in the "CONFIG" section below if needed.
#
# Usage:
#   ./init_and_deploy_pgpass_compose_simple.sh

#########################
# CONFIG (edit if needed)
#########################

# Container names as defined in your docker-compose.yml (hard-coded)
# These container_name values will be used as hostnames in .pgpass and citus_add_node
COORD_CONTAINER="citus-coordinator"
WORKER_CONTAINERS=( "citus-worker-1" "citus-worker-2" "citus-worker-3" "citus-worker-4" )

# Timeout (seconds) to wait for PostgreSQL to become available in a container
WAIT_TIMEOUT=120

#########################
# End CONFIG
#########################

# Use hardcoded password
PG_PASS="123456Pgpw"

# Basic checks
command -v docker >/dev/null 2>&1 || { echo "docker CLI not found in PATH"; exit 1; }

echo "Using coordinator container: $COORD_CONTAINER"
echo "Using worker containers: ${WORKER_CONTAINERS[*]}"

# helper: wait for postgres readiness inside a container
wait_for_pg() {
  local container="$1"
  local start_ts=$(date +%s)
  echo "Waiting for Postgres in container '$container' to be ready..."
  while true; do
    if docker exec "$container" pg_isready -U postgres -h localhost -p 5432 >/dev/null 2>&1; then
      echo "  -> $container postgres is ready"
      break
    fi
    now_ts=$(date +%s)
    if [ $((now_ts - start_ts)) -ge $WAIT_TIMEOUT ]; then
      echo "Timed out waiting for $container after ${WAIT_TIMEOUT}s" >&2
      exit 1
    fi
    sleep 1
  done
}

# Ensure containers are running
echo
echo "Checking that required containers are running..."
all_containers=( "$COORD_CONTAINER" "${WORKER_CONTAINERS[@]}" )
for c in "${all_containers[@]}"; do
  if ! docker ps --format '{{.Names}}' | grep -q -x "$c"; then
    echo "ERROR: required container '$c' is not running. Start your compose stack first." >&2
    exit 1
  fi
done

# Wait for postgres readiness
wait_for_pg "$COORD_CONTAINER"
for c in "${WORKER_CONTAINERS[@]}"; do
  wait_for_pg "$c"
done

echo
echo "=== Create Citus extension on all nodes (idempotent) ==="
docker exec -i "$COORD_CONTAINER" psql -U postgres -c "CREATE EXTENSION IF NOT EXISTS citus;" || true
for c in "${WORKER_CONTAINERS[@]}"; do
  docker exec -i "$c" psql -U postgres -c "CREATE EXTENSION IF NOT EXISTS citus;" || true
done

echo
echo "=== Set coordinator host on workers (citus_set_coordinator_host using container_name) ==="
for wc in "${WORKER_CONTAINERS[@]}"; do
  echo "  -> telling $wc that coordinator is '${COORD_CONTAINER}:5432'"
  docker exec -i "$wc" psql -U postgres -c "SELECT citus_set_coordinator_host('${COORD_CONTAINER}', 5432);" || true
done


echo
echo "=== Build .pgpass and deploy to all nodes (hosts = container_name) ==="
PGPASS_TMP="$(mktemp)"
trap 'rm -f \"$PGPASS_TMP\"' EXIT

# Build entries using container names only (these are the hostnames we'll use)
# Coordinator entry
printf "%s:5432:*:postgres:%s\n" "$COORD_CONTAINER" "$PG_PASS" >> "$PGPASS_TMP"

# Worker entries
for cont in "${WORKER_CONTAINERS[@]}"; do
  printf "%s:5432:*:postgres:%s\n" "$cont" "$PG_PASS" >> "$PGPASS_TMP"
done

chmod 600 "$PGPASS_TMP"
echo "Built temporary .pgpass at $PGPASS_TMP (mode 600)."

# Function to copy .pgpass into container (copies to /root/.pgpass only)
copy_pgpass_into() {
  local container="$1"
  local dest="/root/.pgpass"

  echo "  -> writing .pgpass to $container:$dest"
  if docker exec -i "$container" sh -lc "cat > '${dest}'" < "$PGPASS_TMP"; then
    docker exec -i "$container" sh -lc "chmod 600 '${dest}' || true"
    echo "     wrote $dest"
    return 0
  fi

  # fallback docker cp to /tmp then move into place
  echo "  -> fallback copy (docker cp) for $container"
  docker cp "$PGPASS_TMP" "$container:/tmp/.pgpass" >/dev/null 2>&1 || true
  if docker exec -i "$container" sh -lc "mv /tmp/.pgpass /root/.pgpass >/dev/null 2>&1 && chmod 600 /root/.pgpass"; then
    echo "     copied to /root/.pgpass"
    return 0
  fi

  echo "WARNING: failed to install .pgpass in $container" >&2
  return 1
}

echo "Copying .pgpass into coordinator..."
copy_pgpass_into "$COORD_CONTAINER"
for c in "${WORKER_CONTAINERS[@]}"; do
  copy_pgpass_into "$c"
done

echo "Reloading postgres configs (best-effort)..."
docker exec -i "$COORD_CONTAINER" psql -U postgres -c "SELECT pg_reload_conf();" || true
for c in "${WORKER_CONTAINERS[@]}"; do
  docker exec -i "$c" psql -U postgres -c "SELECT pg_reload_conf();" || true
done

echo
echo "=== Register worker containers on coordinator (citus_add_node using container_name) ==="
for svc in "${WORKER_CONTAINERS[@]}"; do
  echo "  -> checking registration of container '$svc' in coordinator metadata..."
  count=$(docker exec -i "$COORD_CONTAINER" psql -U postgres -tAc "SELECT count(*) FROM pg_dist_node WHERE nodename = '$svc' AND nodeport = 5432;")
  count=${count//[[:space:]]/}
  if [ -z "$count" ]; then count=0; fi
  if [ "$count" -eq 0 ]; then
    echo "     adding $svc ..."
    docker exec -i "$COORD_CONTAINER" psql -U postgres -c "SELECT citus_add_node('$svc', 5432, noderole := 'primary');"
  else
    echo "     $svc already present (count=$count) - skipping"
  fi
done

echo
echo "=== Verification: cluster metadata & health ==="
echo "pg_dist_node:"
docker exec -i "$COORD_CONTAINER" psql -U postgres -c "SELECT nodeid, nodename, nodeport, noderole, groupid, isactive FROM pg_dist_node ORDER BY nodeid;" || true

echo
echo "citus_get_active_worker_nodes():"
docker exec -i "$COORD_CONTAINER" psql -U postgres -c "SELECT * FROM citus_get_active_worker_nodes();" || true

echo
echo "citus_check_cluster_node_health() (debug):"
docker exec -i "$COORD_CONTAINER" psql -U postgres -c "SET client_min_messages = DEBUG1; SELECT * FROM citus_check_cluster_node_health();" || true

echo
echo "Initialization finished."
