version: "3.8"

#postgres
#      -c config_file=/etc/postgresql/postgresql.conf
#      -c hba_file=/etc/postgresql/pg_hba.conf

services:
  coordinator:
    #image: citusdata/citus:13.0
    image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/citusdata.citus:13.0.3
    container_name: citus-coordinator
    environment:
      - POSTGRES_PASSWORD=123456Pgpw
      - POSTGRES_USER=postgres
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
    ports:
      - "5432:5432"
    volumes:
      - ./data/coord:/var/lib/postgresql/data
      - ./configs/coord_postgresql.conf:/etc/postgresql/postgresql.conf
      #- ./configs/coord_pg_hba.conf:/etc/postgresql/pg_hba.conf
    networks:
      - citus-net

  worker1:
    image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/citusdata.citus:13.0.3
    container_name: citus-worker-1
    environment:
      - POSTGRES_PASSWORD=123456Pgpw
      - POSTGRES_USER=postgres
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
    expose:
      - "5432"
    volumes:
      - ./data/worker1:/var/lib/postgresql/data
      - ./configs/worker_postgresql.conf:/etc/postgresql/postgresql.conf
      #- ./configs/worker_pg_hba.conf:/etc/postgresql/pg_hba.conf
    depends_on:
      - coordinator
    networks:
      - citus-net

  worker2:
    image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/citusdata.citus:13.0.3
    container_name: citus-worker-2
    environment:
      - POSTGRES_PASSWORD=123456Pgpw
      - POSTGRES_USER=postgres
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
    expose:
      - "5432"
    volumes:
      - ./data/worker2:/var/lib/postgresql/data
      - ./configs/worker_postgresql.conf:/etc/postgresql/postgresql.conf
      #- ./configs/worker_pg_hba.conf:/etc/postgresql/pg_hba.conf
    depends_on:
      - coordinator
    networks:
      - citus-net

  worker3:
    image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/citusdata.citus:13.0.3
    container_name: citus-worker-3
    environment:
      - POSTGRES_PASSWORD=123456Pgpw
      - POSTGRES_USER=postgres
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
    expose:
      - "5432"
    volumes:
      - ./data/worker3:/var/lib/postgresql/data
      - ./configs/worker_postgresql.conf:/etc/postgresql/postgresql.conf
      #- ./configs/worker_pg_hba.conf:/etc/postgresql/pg_hba.conf
    depends_on:
      - coordinator
    networks:
      - citus-net

  worker4:
    image: registry.cn-hangzhou.aliyuncs.com/samjoy_public/citusdata.citus:13.0.3
    container_name: citus-worker-4
    environment:
      - POSTGRES_PASSWORD=123456Pgpw
      - POSTGRES_USER=postgres
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
    expose:
      - "5432"
    volumes:
      - ./data/worker4:/var/lib/postgresql/data
      - ./configs/worker_postgresql.conf:/etc/postgresql/postgresql.conf
      #- ./configs/worker_pg_hba.conf:/etc/postgresql/pg_hba.conf
    depends_on:
      - coordinator
    networks:
      - citus-net

networks:
  citus-net:
    driver: bridge
