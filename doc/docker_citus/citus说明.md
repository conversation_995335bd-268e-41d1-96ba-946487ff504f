## Citus集群搭建说明

### Docker Compose 配置

本配置包含1个协调节点(coordinator)和4个工作节点(worker)：

- **协调节点**: `citus-coordinator` (端口映射: 5432:5432)
- **工作节点**: `citus-worker-1`, `citus-worker-2`, `citus-worker-3`, `citus-worker-4`
- **数据库密码**: 所有节点的postgres用户密码为 `123456Pgpw`
- **网络**: 使用自定义bridge网络 `citus-net`

### 自动化初始化脚本

`init_citus_with_pgpass.sh` 脚本执行流程：

1. **启动容器检查**: 确认所有必需的容器正在运行
2. **等待数据库就绪**: 使用 `pg_isready` 等待所有节点的PostgreSQL服务启动完成
3. **创建Citus扩展**: 在所有节点上执行 `CREATE EXTENSION IF NOT EXISTS citus;`
4. **配置协调节点**: 在所有worker节点上执行 `citus_set_coordinator_host('citus-coordinator', 5432)`
5. **创建.pgpass文件**: 生成包含所有节点连接信息的.pgpass文件，密码使用硬编码的 `123456Pgpw`
6. **部署.pgpass文件**: 将.pgpass文件拷贝到所有容器的 `/root/.pgpass` 位置，注意文件权限设置为600
7. **重载配置**: 在所有节点上执行 `pg_reload_conf()`
8. **注册工作节点**: 在协调节点上使用 `citus_add_node` 注册所有worker节点（包含幂等检查）
9. **验证集群**: 显示集群元数据和健康状态

### 使用方法

```bash
# 1. 启动Docker Compose服务
docker compose -f docker-compose.yml up -d

# 2. 等待服务启动后，运行初始化脚本
chmod +x init_citus_with_pgpass.sh
./init_citus_with_pgpass.sh
```

### 手动执行步骤（参考）

如果需要手动执行，可以参考以下SQL命令：

```sql
-- 1. 在所有节点执行（包括协调节点和工作节点）
CREATE
EXTENSION IF NOT EXISTS citus;

-- 2. 在所有 worker 工作节点上执行，让工作节点知道协调节点的位置
SELECT citus_set_coordinator_host('citus-coordinator', 5432);

-- 3. 在协调节点上运行citus_add_node，将工作节点注册到协调节点
SELECT citus_add_node('citus-worker-1', 5432, noderole := 'primary');
SELECT citus_add_node('citus-worker-2', 5432, noderole := 'primary');
SELECT citus_add_node('citus-worker-3', 5432, noderole := 'primary');
SELECT citus_add_node('citus-worker-4', 5432, noderole := 'primary');

-- 4. 验证集群状态
SELECT *
FROM citus_get_active_worker_nodes();
SELECT nodeid, nodename, nodeport, noderole, groupid, isactive
FROM pg_dist_node
ORDER BY nodeid;
```

### .pgpass文件示例

```md
citus-coordinator:5432:*:postgres:123456Pgpw
citus-worker-1:5432:*:postgres:123456Pgpw
citus-worker-2:5432:*:postgres:123456Pgpw
```

### 重要说明

- **容器名称**: 脚本使用容器名称作为主机名进行节点间通信
- **自动化**: 脚本包含完整的错误处理和幂等性检查，可以安全地重复运行
- **配置修改**: 如需修改容器名称或端口，请编辑脚本顶部的配置变量
