# -*- coding: utf-8 -*-

"""
 postgresql 系统表（sys_开头）迁移脚本
 Author: sw
 Date: 2025/5/29
 Version: 1.0.0
"""

import logging
import os
import sys
from datetime import datetime

import psycopg

# 配置日志
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("pg_migration")

# 数据库连接配置
SOURCE_DB_CONFIG = {
    "host": "************",
    "port": 31909,
    "dbname": "pds2",
    "user": "postgres",
    "password": "Biosino+2025",
    "schema": "dev"
}

TARGET_DB_CONFIG = {
    "host": "************",
    "port": 31910,
    "dbname": "postgres",
    "user": "postgres",
    "password": "Biosino+2025",
    "schema": "public"
}


def get_connection(config):
    """创建数据库连接"""
    conn_string = f"host={config['host']} port={config['port']} dbname={config['dbname']} user={config['user']} password={config['password']}"
    try:
        conn = psycopg.connect(conn_string)
        conn.autocommit = False
        logger.info(f"已成功连接到数据库 {config['host']}:{config['port']}/{config['dbname']}")
        return conn
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise


def get_sys_tables(conn, schema):
    """获取所有以sys_开头的表"""
    try:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = %s AND table_name LIKE 'sys\\_%%' ESCAPE '\\'
            """, (schema,))
            tables = [row[0] for row in cur.fetchall()]
            logger.info(f"找到 {len(tables)} 个以sys_开头的表")
            return tables
    except Exception as e:
        logger.error(f"获取表列表时出错: {e}")
        raise


def get_table_structure(conn, schema, table):
    """获取表结构"""
    try:
        with conn.cursor() as cur:
            # 获取列信息
            cur.execute("""
                SELECT column_name, data_type, character_maximum_length, 
                       is_nullable, column_default
                FROM information_schema.columns
                WHERE table_schema = %s AND table_name = %s
                ORDER BY ordinal_position
            """, (schema, table))
            columns = cur.fetchall()

            # 获取主键信息
            cur.execute("""
                SELECT c.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.constraint_column_usage AS ccu 
                    ON tc.constraint_name = ccu.constraint_name
                    AND tc.table_schema = ccu.table_schema
                JOIN information_schema.columns AS c
                    ON c.table_schema = tc.table_schema
                    AND tc.table_name = c.table_name 
                    AND ccu.column_name = c.column_name
                WHERE tc.constraint_type = 'PRIMARY KEY' 
                    AND tc.table_schema = %s
                    AND tc.table_name = %s
            """, (schema, table))
            primary_keys = [row[0] for row in cur.fetchall()]

            # 获取索引信息
            cur.execute("""
                SELECT
                    i.relname AS index_name,
                    a.attname AS column_name,
                    ix.indisunique AS is_unique
                FROM
                    pg_class t,
                    pg_class i,
                    pg_index ix,
                    pg_attribute a,
                    pg_namespace n
                WHERE
                    t.oid = ix.indrelid
                    AND i.oid = ix.indexrelid
                    AND a.attrelid = t.oid
                    AND a.attnum = ANY(ix.indkey)
                    AND t.relkind = 'r'
                    AND n.oid = t.relnamespace
                    AND n.nspname = %s
                    AND t.relname = %s
                ORDER BY
                    i.relname, a.attnum
            """, (schema, table))
            indices = cur.fetchall()

            # 获取表注释
            cur.execute("""
                SELECT obj_description(oid) 
                FROM pg_class 
                WHERE relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = %s) 
                AND relname = %s
            """, (schema, table))
            table_comment_row = cur.fetchone()
            table_comment = table_comment_row[0] if table_comment_row and table_comment_row[0] else None

            # 获取列注释
            cur.execute("""
                SELECT a.attname, pg_catalog.col_description(a.attrelid, a.attnum) 
                FROM pg_catalog.pg_attribute a
                JOIN pg_catalog.pg_class c ON a.attrelid = c.oid
                JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                WHERE n.nspname = %s
                AND c.relname = %s
                AND a.attnum > 0
                AND NOT a.attisdropped
                AND pg_catalog.col_description(a.attrelid, a.attnum) IS NOT NULL
            """, (schema, table))
            column_comments = {row[0]: row[1] for row in cur.fetchall()}

            return {
                "columns": columns,
                "primary_keys": primary_keys,
                "indices": indices,
                "table_comment": table_comment,
                "column_comments": column_comments
            }
    except Exception as e:
        logger.error(f"获取表 {table} 结构时出错: {e}")
        raise


def get_sequences(conn, schema, table):
    """获取表中使用的序列"""
    try:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT column_name, column_default
                FROM information_schema.columns
                WHERE table_schema = %s AND table_name = %s
                AND column_default LIKE 'nextval%%'
            """, (schema, table))
            sequences = []
            for column_name, column_default in cur.fetchall():
                # 从默认值中提取序列名称
                seq_name = column_default.split("'")[1].split(".")[-1]
                sequences.append({
                    "column_name": column_name,
                    "sequence_name": seq_name,
                    "full_default": column_default
                })
            return sequences
    except Exception as e:
        logger.error(f"获取表 {table} 的序列信息时出错: {e}")
        raise


def get_sequence_details(conn, schema, seq_name):
    """获取序列的详细信息"""
    try:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT 
                    last_value, 
                    start_value, 
                    increment_by, 
                    max_value, 
                    min_value, 
                    cache_size, 
                    cycle
                FROM 
                    pg_sequences 
                WHERE 
                    schemaname = %s AND sequencename = %s
            """, (schema, seq_name))
            result = cur.fetchone()
            if result:
                return {
                    "last_value": result[0],
                    "start_value": result[1],
                    "increment_by": result[2],
                    "max_value": result[3],
                    "min_value": result[4],
                    "cache_size": result[5],
                    "cycle": result[6]
                }
            return None
    except Exception as e:
        logger.error(f"获取序列 {seq_name} 详细信息时出错: {e}")
        raise


def create_table(conn, schema, table, structure):
    """在目标数据库中创建表"""
    try:
        with conn.cursor() as cur:
            # 构建表的创建SQL语句
            columns_sql = []
            for col_name, data_type, max_length, nullable, default in structure["columns"]:
                column_def = '"{}" {}'.format(col_name, data_type)
                if max_length:
                    column_def += f"({max_length})"
                if nullable == 'NO':
                    column_def += " NOT NULL"
                if default and "nextval" not in default:  # 序列后面单独处理
                    column_def += f" DEFAULT {default}"
                columns_sql.append(column_def)

            # 添加主键约束
            if structure["primary_keys"]:
                pk_columns = []
                for pk in structure["primary_keys"]:
                    pk_columns.append('"{}"'.format(pk))
                pk_constraint = ", PRIMARY KEY ({})".format(', '.join(pk_columns))
            else:
                pk_constraint = ""

            # 创建表
            create_table_sql = f"""
                CREATE TABLE IF NOT EXISTS {schema}.{table} (
                    {', '.join(columns_sql)}
                    {pk_constraint}
                )
            """
            cur.execute(create_table_sql)
            logger.info(f"已创建表 {schema}.{table}")

            # 设置表注释
            if structure.get("table_comment"):
                # 对注释进行转义处理，避免SQL注入
                escaped_comment = structure["table_comment"].replace("'", "''")
                comment_sql = f"""
                    COMMENT ON TABLE {schema}.{table} IS '{escaped_comment}'
                """
                cur.execute(comment_sql)
                logger.info(f"已设置表 {schema}.{table} 的注释")

            # 设置列注释
            for col_name, comment in structure.get("column_comments", {}).items():
                if comment:
                    # 对注释进行转义处理，避免SQL注入
                    escaped_comment = comment.replace("'", "''")
                    col_comment_sql = f"""
                        COMMENT ON COLUMN {schema}.{table}."{col_name}" IS '{escaped_comment}'
                    """
                    cur.execute(col_comment_sql)
                    logger.info(f"已设置表 {schema}.{table} 列 {col_name} 的注释")

            # 创建索引
            index_mapping = {}  # 用于跟踪已创建的索引
            for idx_name, column_name, is_unique in structure["indices"]:
                if idx_name in index_mapping:
                    index_mapping[idx_name]["columns"].append(column_name)
                else:
                    index_mapping[idx_name] = {
                        "columns": [column_name],
                        "is_unique": is_unique
                    }

            for idx_name, idx_info in index_mapping.items():
                if idx_name.endswith("_pkey"):  # 主键索引已在创建表时处理
                    continue

                unique_str = "UNIQUE " if idx_info["is_unique"] else ""
                idx_columns = []
                for col in idx_info["columns"]:
                    idx_columns.append('"{}"'.format(col))
                columns_str = ', '.join(idx_columns)
                create_idx_sql = f"""
                    CREATE {unique_str}INDEX IF NOT EXISTS {idx_name}
                    ON {schema}.{table} ({columns_str})
                """
                cur.execute(create_idx_sql)
                logger.info(f"已创建索引 {idx_name} 在表 {schema}.{table} 上")
    except Exception as e:
        logger.error(f"创建表 {table} 时出错: {e}")
        conn.rollback()
        raise


def create_sequence(conn, target_schema, seq_info, seq_details):
    """在目标数据库中创建序列"""
    try:
        with conn.cursor() as cur:
            seq_name = seq_info["sequence_name"]

            # 为可能为None的值提供默认值
            last_value = seq_details['last_value'] if seq_details['last_value'] is not None else 1
            start_value = seq_details['start_value'] if seq_details['start_value'] is not None else 1
            increment_by = seq_details['increment_by'] if seq_details['increment_by'] is not None else 1
            min_value = seq_details['min_value'] if seq_details['min_value'] is not None else 1
            max_value = seq_details['max_value'] if seq_details[
                                                        'max_value'] is not None else 2147483647  # PostgreSQL默认最大值
            cache_size = seq_details['cache_size'] if seq_details['cache_size'] is not None else 1
            cycle = seq_details['cycle'] if seq_details['cycle'] is not None else False

            # 创建序列
            create_seq_sql = f"""
                CREATE SEQUENCE IF NOT EXISTS {target_schema}.{seq_name}
                START WITH {start_value}
                INCREMENT BY {increment_by}
                MINVALUE {min_value}
                MAXVALUE {max_value}
                CACHE {cache_size}
                {'' if not cycle else 'CYCLE'}
            """
            cur.execute(create_seq_sql)
            logger.info(f"已创建序列 {target_schema}.{seq_name}")

            # 设置序列当前值
            cur.execute(f"SELECT setval('{target_schema}.{seq_name}', {last_value}, true)")
            logger.info(f"已设置序列 {target_schema}.{seq_name} 当前值为 {last_value}")

            return seq_name
    except Exception as e:
        logger.error(f"创建序列 {seq_info['sequence_name']} 时出错: {e}")
        conn.rollback()
        raise


def migrate_table_data(source_conn, target_conn, source_schema, target_schema, table, batch_size=1000):
    """迁移表数据"""
    try:
        # 获取表的总行数
        with source_conn.cursor() as cur:
            cur.execute(f"SELECT COUNT(*) FROM {source_schema}.{table}")
            total_rows = cur.fetchone()[0]
            logger.info(f"表 {table} 共有 {total_rows} 行数据需要迁移")

        # 如果数据量为0，直接返回
        if total_rows == 0:
            logger.info(f"表 {table} 没有数据需要迁移")
            return

        # 获取列名
        with source_conn.cursor() as cur:
            cur.execute(f"""
                SELECT column_name 
                FROM information_schema.columns
                WHERE table_schema = %s AND table_name = %s
                ORDER BY ordinal_position
            """, (source_schema, table))
            columns = [row[0] for row in cur.fetchall()]
            column_list = []
            for col in columns:
                column_list.append('"{}"'.format(col))
            columns_str = ', '.join(column_list)

        # 分批读取和写入数据
        offset = 0
        while offset < total_rows:
            # 从源数据库读取一批数据
            with source_conn.cursor() as cur:
                cur.execute(f"""
                    SELECT {columns_str}
                    FROM {source_schema}.{table}
                    LIMIT {batch_size} OFFSET {offset}
                """)
                batch_data = cur.fetchall()

            if not batch_data:
                break

            # 构建插入语句
            placeholders = ', '.join(['%s'] * len(columns))
            insert_sql = f"""
                INSERT INTO {target_schema}.{table} ({columns_str})
                VALUES ({placeholders})
            """

            # 写入目标数据库
            with target_conn.cursor() as cur:
                cur.executemany(insert_sql, batch_data)

            target_conn.commit()
            offset += len(batch_data)
            logger.info(f"已迁移表 {table} 的数据 {offset}/{total_rows} 行")
    except Exception as e:
        logger.error(f"迁移表 {table} 的数据时出错: {e}")
        target_conn.rollback()
        raise


def update_sequence_column_default(conn, schema, table, column, seq_name):
    """更新列的默认值为新序列"""
    try:
        with conn.cursor() as cur:
            alter_sql = f"""
                ALTER TABLE {schema}.{table} 
                ALTER COLUMN "{column}" 
                SET DEFAULT nextval('{schema}.{seq_name}'::regclass)
            """
            cur.execute(alter_sql)
            logger.info(f"已更新表 {schema}.{table} 的列 {column} 的默认值为序列 {schema}.{seq_name}")
    except Exception as e:
        logger.error(f"更新列 {column} 的默认值时出错: {e}")
        conn.rollback()
        raise


def table_exists(conn, schema, table):
    """检查表是否存在"""
    try:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT EXISTS (
                    SELECT 1 
                    FROM information_schema.tables 
                    WHERE table_schema = %s
                    AND table_name = %s
                )
            """, (schema, table))
            return cur.fetchone()[0]
    except Exception as e:
        logger.error(f"检查表 {table} 是否存在时出错: {e}")
        raise


def check_and_handle_existing_table(conn, schema, table, force_recreate=False):
    """检查并处理已存在的表"""
    if table_exists(conn, schema, table):
        if force_recreate:
            logger.info(f"表 {schema}.{table} 已存在，将删除并重建")
            with conn.cursor() as cur:
                # 删除表 (CASCADE会同时删除依赖对象如索引、触发器等)
                cur.execute(f"DROP TABLE IF EXISTS {schema}.{table} CASCADE")
            conn.commit()
            return False  # 表将被重建
        else:
            logger.info(f"表 {schema}.{table} 已存在，跳过迁移")
            return True  # 表已存在且不重建
    return False  # 表不存在，需要创建


def main():
    """主函数"""
    try:
        # 连接源数据库和目标数据库
        source_conn = get_connection(SOURCE_DB_CONFIG)
        target_conn = get_connection(TARGET_DB_CONFIG)
        
        source_schema = SOURCE_DB_CONFIG["schema"]
        target_schema = TARGET_DB_CONFIG["schema"]
        
        # 是否强制重建表（如果存在）
        # 可以根据需求将此参数设置为True以重建表
        force_recreate = False
        
        # 获取所有以sys_开头的表
        sys_tables = get_sys_tables(source_conn, source_schema)
        
        for table in sys_tables:
            logger.info(f"开始处理表 {table}")
            
            # 检查表是否已存在，并根据策略决定是否跳过
            if check_and_handle_existing_table(target_conn, target_schema, table, force_recreate):
                continue
            
            # 1. 获取表结构
            structure = get_table_structure(source_conn, source_schema, table)
            
            # 2. 获取表中使用的序列信息
            sequences = get_sequences(source_conn, source_schema, table)
            
            # 3. 在目标数据库中创建表
            create_table(target_conn, target_schema, table, structure)
            
            # 4. 创建序列并更新列的默认值
            for seq_info in sequences:
                # 获取源序列详情
                seq_details = get_sequence_details(source_conn, source_schema, seq_info["sequence_name"])
                if seq_details:
                    # 创建目标序列
                    create_sequence(target_conn, target_schema, seq_info, seq_details)
                    # 更新列的默认值
                    update_sequence_column_default(
                        target_conn, 
                        target_schema, 
                        table, 
                        seq_info["column_name"], 
                        seq_info["sequence_name"]
                    )
            
            # 5. 迁移数据
            migrate_table_data(source_conn, target_conn, source_schema, target_schema, table)
            
            target_conn.commit()
            logger.info(f"表 {table} 处理完成")
            # print("hello world", 123)
        
        logger.info("所有sys_开头的表迁移完成！")
        
    except Exception as e:
        logger.error(f"迁移过程中出现错误: {e}", exc_info=True)
        if 'target_conn' in locals():
            target_conn.rollback()
        return 1
    finally:
        # 关闭连接
        if 'source_conn' in locals():
            source_conn.close()
        if 'target_conn' in locals():
            target_conn.close()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
