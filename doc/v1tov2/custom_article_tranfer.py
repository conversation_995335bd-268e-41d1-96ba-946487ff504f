#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本：从v1 MySQL数据库迁移到v2 PostgreSQL数据库
迁移表：tb_dds_article、tb_dds_article_attachment
迁移范围：pmid >= ************ AND pmid <= ************
"""

import logging
import psycopg2.extras
import pymysql.cursors
import sys
import traceback
from datetime import datetime
from toollib.guid import SnowFlake

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
V1_MYSQL_CONFIG = {
    'host': '************',  # TODO: 修改为实际的MySQL主机地址
    'port': 32526,
    'user': 'root',  # TODO: 修改为实际的MySQL用户名
    'password': 'Lfgzs@2021',  # TODO: 修改为实际的MySQL密码
    'database': 'plosp_online',
    'charset': 'utf8mb4'
}

V2_PGSQL_CONFIG = {
    'host': '************',  # TODO: 修改为实际的PostgreSQL主机地址
    'port': 31909,
    'user': 'postgres',  # TODO: 修改为实际的PostgreSQL用户名
    'password': 'Biosino+2025',  # TODO: 修改为实际的PostgreSQL密码
    'database': 'plosp',  # TODO: 修改为实际的PostgreSQL数据库名
    'options': '-c search_path=public'  # TODO: 修改为实际的schema名称，如果不是public的话
}

# 迁移范围配置
PMID_MIN = ************
PMID_MAX = ************

# 雪花ID生成器
# 分布式需要根据ip或其他唯一标识映射机器码：worker_id与datacenter_id
# 如：SnowFlake(worker_id=1, datacenter_id=1)
# 更多参数详见源码
snow = SnowFlake(worker_id=1, datacenter_id=1)


def get_mysql_connection():
    """获取MySQL数据库连接"""
    try:
        connection = pymysql.connect(**V1_MYSQL_CONFIG)
        logger.info("MySQL数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"MySQL数据库连接失败: {e}")
        raise


def get_pgsql_connection():
    """获取PostgreSQL数据库连接"""
    try:
        connection = psycopg2.connect(**V2_PGSQL_CONFIG)
        logger.info("PostgreSQL数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"PostgreSQL数据库连接失败: {e}")
        raise


def check_migration_scope():
    """检查迁移范围内的数据量"""
    mysql_conn = None
    try:
        mysql_conn = get_mysql_connection()
        cursor = mysql_conn.cursor()

        # 检查文章数量
        article_query = """
        SELECT count(1) FROM `tb_dds_article`
        WHERE `pmid` >= %s AND `pmid` <= %s
        """
        cursor.execute(article_query, (PMID_MIN, PMID_MAX))
        article_count = cursor.fetchone()[0]
        logger.info(f"待迁移文章数量: {article_count}")

        # 检查附件数量
        attachment_query = """
        SELECT count(1) FROM `tb_dds_article_attachment`
        WHERE `pmid` >= %s AND `pmid` <= %s AND type = 'PDF'
        """
        cursor.execute(attachment_query, (PMID_MIN, PMID_MAX))
        attachment_count = cursor.fetchone()[0]
        logger.info(f"待迁移PDF附件数量: {attachment_count}")

        return article_count, attachment_count

    except Exception as e:
        logger.error(f"检查迁移范围失败: {e}")
        raise
    finally:
        if mysql_conn:
            mysql_conn.close()


def convert_text_to_array(text, separators=None):
    """将文本按分隔符转换为数组格式

    Args:
        text: 待转换的文本
        separators: 分隔符列表，默认为 [';']

    Returns:
        转换后的数组，如果文本为空则返回None
    """
    if not text or not text.strip():
        return None

    if separators is None:
        separators = [';']

    # 按优先级尝试分隔符
    for separator in separators:
        if separator in text:
            items = [item.strip() for item in text.split(separator) if item.strip()]
            return items if items else None

    # 如果没有找到分隔符，返回单个元素的数组
    return [text.strip()]


def convert_source_to_array(source_str):
    """将v1的source字符串转换为v2的数组格式"""
    return convert_text_to_array(source_str)


def convert_author_to_array(author_text):
    """将v1的author文本转换为v2的数组格式"""
    return convert_text_to_array(author_text)


def convert_affiliation_to_array(affiliation_text):
    """将v1的affiliation文本转换为v2的数组格式"""
    return convert_text_to_array(affiliation_text)


def convert_keyword_to_array(keyword_text):
    """将v1的keyword文本转换为v2的数组格式"""
    return convert_text_to_array(keyword_text)


def get_v1_journal_metadata(mysql_conn, journal_id):
    """从v1数据库获取期刊元数据"""
    if not journal_id:
        return None

    try:
        cursor = mysql_conn.cursor(pymysql.cursors.DictCursor)
        query = """
        SELECT Unique_Nlm_ID, issn_print, issn_electronic, title, isoabbreviation, publisher_id
        FROM tb_dds_journal
        WHERE id = %s
        """
        cursor.execute(query, (journal_id,))
        result = cursor.fetchone()
        cursor.close()
        return result
    except Exception as e:
        logger.error(f"查询v1期刊元数据失败 journal_id={journal_id}: {e}")
        return None


def get_v1_publisher_metadata(mysql_conn, publisher_id):
    """从v1数据库获取出版商元数据"""
    if not publisher_id:
        return None

    try:
        cursor = mysql_conn.cursor(pymysql.cursors.DictCursor)
        query = """
        SELECT name
        FROM tb_dds_publisher
        WHERE id = %s
        """
        cursor.execute(query, (publisher_id,))
        result = cursor.fetchone()
        cursor.close()
        return result
    except Exception as e:
        logger.error(f"查询v1出版商元数据失败 publisher_id={publisher_id}: {e}")
        return None


def find_v2_publisher_by_name(pgsql_conn, name):
    """在v2数据库中按name或alias查找出版商"""
    if not name or not name.strip():
        return None

    cursor = None
    try:
        cursor = pgsql_conn.cursor()

        # 先按name查询
        cursor.execute("SELECT id FROM tb_dds_publisher WHERE name = %s LIMIT 1", (name.strip(),))
        result = cursor.fetchone()
        if result:
            return result[0]

        # 再按alias查询
        cursor.execute("SELECT id FROM tb_dds_publisher WHERE alias @> ARRAY[%s]::text[] LIMIT 1", (name.strip(),))
        result = cursor.fetchone()

        if result:
            return result[0]
        return None
    except Exception as e:
        logger.error(f"查询v2出版商失败 name={name}: {e}")
        # 回滚当前事务以清除错误状态
        try:
            pgsql_conn.rollback()
        except:
            pass
        return None
    finally:
        if cursor:
            cursor.close()


def create_v2_publisher(pgsql_conn, name):
    """在v2数据库中创建新出版商"""
    if not name or not name.strip():
        return None

    cursor = None
    try:
        cursor = pgsql_conn.cursor()

        # 生成雪花ID
        publisher_id = snow.gen_uid()

        # 创建别名数组，包含name本身
        alias_array = [name.strip()]

        # 插入新出版商
        insert_query = """
        INSERT INTO tb_dds_publisher (
            id, name, alias, source_type, status, create_time, update_time
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s
        )
        """

        now = datetime.now()

        cursor.execute(insert_query, (
            publisher_id, name.strip(), alias_array, 'system', 0, now, now
        ))

        logger.info(f"创建新出版商成功 id={publisher_id}, name={name}")
        return publisher_id

    except Exception as e:
        logger.error(f"创建v2出版商失败 name={name}: {e}")
        # 回滚当前事务以清除错误状态
        try:
            pgsql_conn.rollback()
        except:
            pass
        return None
    finally:
        if cursor:
            cursor.close()


def find_v2_journal_by_metadata(pgsql_conn, journal_metadata):
    """在v2数据库中按照Java实现的优先级查找期刊"""
    if not journal_metadata:
        return None

    cursor = None
    try:
        cursor = pgsql_conn.cursor()

        # 构建查询条件，按照Java实现的优先级
        unique_nlm_id = journal_metadata.get('Unique_Nlm_ID')
        issn_print = journal_metadata.get('issn_print')
        issn_electronic = journal_metadata.get('issn_electronic')
        title = journal_metadata.get('title')
        isoabbreviation = journal_metadata.get('isoabbreviation')

        # 优先级1: 使用unique_history查询
        if unique_nlm_id and unique_nlm_id.strip():
            cursor.execute(
                "SELECT id FROM tb_dds_journal WHERE unique_history @> ARRAY[%s]::text[] LIMIT 1",
                (unique_nlm_id.strip(),)
            )
            result = cursor.fetchone()
            if result:
                return result[0]

        # 优先级2: 使用issn_history查询（包含issn_print和issn_electronic）
        issn_list = []
        if issn_print and issn_print.strip():
            issn_list.append(issn_print.strip())
        if issn_electronic and issn_electronic.strip():
            issn_list.append(issn_electronic.strip())

        for issn in issn_list:
            cursor.execute(
                "SELECT id FROM tb_dds_journal WHERE issn_history @> ARRAY[%s]::text[] LIMIT 1",
                (issn,)
            )
            result = cursor.fetchone()
            if result:
                return result[0]

        # 优先级3: 使用issn_print查询
        if issn_print and issn_print.strip():
            cursor.execute(
                "SELECT id FROM tb_dds_journal WHERE issn_print = %s LIMIT 1",
                (issn_print.strip(),)
            )
            result = cursor.fetchone()
            if result:
                return result[0]

        # 优先级4: 使用issn_electronic查询
        if issn_electronic and issn_electronic.strip():
            cursor.execute(
                "SELECT id FROM tb_dds_journal WHERE issn_electronic = %s LIMIT 1",
                (issn_electronic.strip(),)
            )
            result = cursor.fetchone()
            if result:
                return result[0]

        # 优先级5: 使用isoabbreviation查询
        if isoabbreviation and isoabbreviation.strip():
            cursor.execute(
                "SELECT id FROM tb_dds_journal WHERE isoabbreviation = %s LIMIT 1",
                (isoabbreviation.strip(),)
            )
            result = cursor.fetchone()
            if result:
                return result[0]

        # 优先级6: 使用title查询
        if title and title.strip():
            cursor.execute(
                "SELECT id FROM tb_dds_journal WHERE title = %s LIMIT 1",
                (title.strip(),)
            )
            result = cursor.fetchone()
            if result:
                return result[0]

        return None

    except Exception as e:
        logger.error(f"查询v2期刊失败: {e}")
        # 回滚当前事务以清除错误状态
        try:
            pgsql_conn.rollback()
        except:
            pass
        return None
    finally:
        if cursor:
            cursor.close()


def create_v2_journal(pgsql_conn, journal_metadata, publisher_id=None):
    """在v2数据库中创建新期刊"""
    if not journal_metadata:
        return None

    title = journal_metadata.get('title')
    if not title or not title.strip():
        logger.error("期刊标题为空，无法创建期刊")
        return None

    cursor = None
    try:
        cursor = pgsql_conn.cursor()

        # 生成雪花ID
        journal_id = snow.gen_uid()

        # 准备字段值
        unique_nlm_id = journal_metadata.get('Unique_Nlm_ID')
        issn_print = journal_metadata.get('issn_print')
        issn_electronic = journal_metadata.get('issn_electronic')
        isoabbreviation = journal_metadata.get('isoabbreviation')

        # 构建unique_history数组
        unique_history = None
        if unique_nlm_id and unique_nlm_id.strip():
            unique_history = [unique_nlm_id.strip()]

        # 构建issn_history数组
        issn_history = []
        if issn_print and issn_print.strip():
            issn_history.append(issn_print.strip())
        if issn_electronic and issn_electronic.strip():
            issn_history.append(issn_electronic.strip())

        # 去重
        if issn_history:
            issn_history = list(set(issn_history))
        else:
            issn_history = None

        # 插入新期刊
        insert_query = """
        INSERT INTO tb_dds_journal (
            id, publisher_id, unique_nlm_id, issn_print, issn_electronic,
            title, isoabbreviation, issn_history, unique_history, source,
            source_type, status, create_time, update_time
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        now = datetime.now()

        # 设置source数组，表示这是从系统迁移的数据
        source_array = ['Custom']

        cursor.execute(insert_query, (
            journal_id, publisher_id, unique_nlm_id, issn_print, issn_electronic,
            title.strip(), isoabbreviation, issn_history, unique_history, source_array,
            'system', 0, now, now
        ))

        logger.info(f"创建新期刊成功 id={journal_id}, title={title}")
        return journal_id

    except Exception as e:
        logger.error(f"创建v2期刊失败 title={title}: {e}")
        # 回滚当前事务以清除错误状态
        try:
            pgsql_conn.rollback()
        except:
            pass
        return None
    finally:
        if cursor:
            cursor.close()


def process_journal_and_publisher_mapping(mysql_conn, pgsql_conn, v1_journal_id):
    """处理期刊和出版商的映射关系"""
    if not v1_journal_id:
        return None

    # 1. 从v1获取期刊元数据
    journal_metadata = get_v1_journal_metadata(mysql_conn, v1_journal_id)
    if not journal_metadata:
        logger.warning(f"未找到v1期刊数据 journal_id={v1_journal_id}")
        return None

    # 2. 在v2中查找现有期刊
    v2_journal_id = find_v2_journal_by_metadata(pgsql_conn, journal_metadata)

    if v2_journal_id:
        # 找到现有期刊，直接返回
        logger.debug(f"找到现有期刊 v1_journal_id={v1_journal_id} -> v2_journal_id={v2_journal_id}")
        return v2_journal_id

    # 3. 未找到期刊，需要创建新期刊
    # 首先处理出版商
    v2_publisher_id = None
    v1_publisher_id = journal_metadata.get('publisher_id')

    if v1_publisher_id:
        # 获取v1出版商信息
        publisher_metadata = get_v1_publisher_metadata(mysql_conn, v1_publisher_id)
        if publisher_metadata and publisher_metadata.get('name'):
            publisher_name = publisher_metadata['name']

            # 在v2中查找出版商
            v2_publisher_id = find_v2_publisher_by_name(pgsql_conn, publisher_name)

            if not v2_publisher_id:
                # 创建新出版商
                v2_publisher_id = create_v2_publisher(pgsql_conn, publisher_name)
                if v2_publisher_id:
                    try:
                        pgsql_conn.commit()
                        logger.info(f"创建新出版商 v1_publisher_id={v1_publisher_id} -> v2_publisher_id={v2_publisher_id}")
                    except Exception as e:
                        logger.error(f"提交出版商创建事务失败: {e}")
                        pgsql_conn.rollback()
                        v2_publisher_id = None
            else:
                logger.debug(f"找到现有出版商 v1_publisher_id={v1_publisher_id} -> v2_publisher_id={v2_publisher_id}")

    # 4. 创建新期刊
    v2_journal_id = create_v2_journal(pgsql_conn, journal_metadata, v2_publisher_id)
    if v2_journal_id:
        try:
            pgsql_conn.commit()
            logger.info(f"创建新期刊 v1_journal_id={v1_journal_id} -> v2_journal_id={v2_journal_id}")
        except Exception as e:
            logger.error(f"提交期刊创建事务失败: {e}")
            pgsql_conn.rollback()
            v2_journal_id = None

    return v2_journal_id


def migrate_articles():
    """迁移文章数据"""
    mysql_conn = None
    pgsql_conn = None
    migrated_count = 0
    failed_count = 0

    try:
        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor(pymysql.cursors.DictCursor)

        pgsql_conn = get_pgsql_connection()
        pgsql_cursor = pgsql_conn.cursor()

        # 查询v1数据库中的文章数据
        select_query = """
        SELECT pmid, pmc_id, article_type, subject_type, source, pub_status, language,
               vernacular_title, title, date_pubmed, date_received_accepted,
               published_year, published_month, published_day, print_published_year,
               print_published_month, print_published_day, journal_id, medline_date,
               year, volume, issue, page, e_location_type, e_location_id, author,
               author_isparsed, affiliation, affiliation_isparsed, article_abstract,
               article_abstract_cn, copyright, article_other_abstract, other_copyright,
               keyword, reference_number, reference_id_list, note, create_date,
               update_date, hit_num, update_validate_date
        FROM `tb_dds_article`
        WHERE `pmid` >= %s AND `pmid` <= %s
        ORDER BY pmid
        """

        logger.info("开始查询v1数据库中的文章数据...")
        mysql_cursor.execute(select_query, (PMID_MIN, PMID_MAX))

        # 准备v2数据库的插入语句
        insert_query = """
        INSERT INTO tb_dds_article (
            id, custom_id, pmid, pmc_id, doi, source, pub_status, language,
            vernacular_title, title, published_year, published_month, published_day,
            other_date, journal_id, year, volume, issue, page, author, affiliation,
            keywords, abstract, other_abstract, copyright, hit_num, download,
            create_time, update_time
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        logger.info("开始迁移文章数据...")
        batch_size = 1000
        batch_data = []

        for row in mysql_cursor:
            try:
                # 生成雪花ID作为v2的主键
                new_id = snow.gen_uid()

                # 字段映射和转换
                custom_id = row['pmid']  # v1的pmid作为v2的custom_id
                pmid = None  # v2的pmid字段设为None，因为这些是自定义文章，不是真正的PubMed文章
                pmc_id = row['pmc_id']
                doi = None  # 先设为None，稍后批量获取

                sourceStr = row['source']
                if not sourceStr:
                    sourceStr = 'Custom'
                else:
                    # 检查source是否在允许的列表中
                    allowed_sources = ['medRxiv', 'bioRxiv', 'arXiv', 'ChemRxiv', 'ChinaXiv',
                                     'Research Square', 'SSRN', 'OSF Preprints', 'Preprints']
                    if sourceStr not in allowed_sources:
                        sourceStr = 'Custom'

                source = convert_source_to_array(sourceStr)

                pub_status = row['pub_status']
                language = row['language']
                vernacular_title = row['vernacular_title']
                title = row['title']
                published_year = row['published_year']
                published_month = row['published_month']
                published_day = row['published_day']

                # other_date字段需要特殊处理，v1中有多个日期字段
                # TODO: 根据实际需求构建other_date数组
                other_date = None

                # 处理journal_id映射
                v1_journal_id = row['journal_id']
                journal_id = None
                if v1_journal_id:
                    try:
                        journal_id = process_journal_and_publisher_mapping(mysql_conn, pgsql_conn, v1_journal_id)
                        if not journal_id:
                            logger.warning(f"期刊映射失败 pmid={row['pmid']}, v1_journal_id={v1_journal_id}")
                    except Exception as e:
                        logger.error(f"处理期刊映射时出错 pmid={row['pmid']}, v1_journal_id={v1_journal_id}: {e}")
                        # 继续处理，但journal_id为None
                year = row['year']
                volume = row['volume']
                issue = row['issue']
                page = row['page']

                # 转换文本字段为数组
                author = convert_author_to_array(row['author'])
                affiliation = convert_affiliation_to_array(row['affiliation'])
                keywords = convert_keyword_to_array(row['keyword'])

                # 摘要字段
                abstract = row['article_abstract']
                other_abstract = row['article_other_abstract']
                copyright_info = row['copyright']

                hit_num = row['hit_num'] or 0
                download = 0  # v1中没有下载数字段，设为0

                create_time = row['create_date']
                update_time = row['update_date']

                # 准备插入数据
                insert_data = [
                    new_id, custom_id, pmid, pmc_id, doi, source, pub_status, language,
                    vernacular_title, title, published_year, published_month, published_day,
                    other_date, journal_id, year, volume, issue, page, author, affiliation,
                    keywords, abstract, other_abstract, copyright_info, hit_num, download,
                    create_time, update_time
                ]

                batch_data.append(insert_data)

                # 执行批量插入
                if len(batch_data) >= batch_size:
                    # 批量获取DOI并更新batch_data
                    pmid_list = [data[1] for data in batch_data]  # 从batch_data中提取pmid
                    doi_mapping = get_doi_mapping_batch(pmid_list)
                    for data in batch_data:
                        data[4] = doi_mapping.get(data[1])  # 更新DOI字段（索引4），data[1]是custom_id即pmid

                    pgsql_cursor.executemany(insert_query, batch_data)
                    pgsql_conn.commit()
                    migrated_count += len(batch_data)
                    logger.info(f"已迁移文章数量: {migrated_count}")
                    batch_data = []

            except Exception as e:
                logger.error(f"迁移文章失败 pmid={row['pmid']}: {e}")
                failed_count += 1
                continue

        # 处理剩余的批次数据
        if batch_data:
            # 批量获取DOI并更新batch_data
            pmid_list = [data[1] for data in batch_data]  # 从batch_data中提取pmid
            doi_mapping = get_doi_mapping_batch(pmid_list)
            for data in batch_data:
                data[4] = doi_mapping.get(data[1])  # 更新DOI字段（索引4），data[1]是custom_id即pmid

            pgsql_cursor.executemany(insert_query, batch_data)
            pgsql_conn.commit()
            migrated_count += len(batch_data)

        logger.info(f"文章迁移完成! 成功: {migrated_count}, 失败: {failed_count}")
        return migrated_count, failed_count

    except Exception as e:
        logger.error(f"文章迁移过程中发生错误: {e}")
        logger.error(traceback.format_exc())
        if pgsql_conn:
            pgsql_conn.rollback()
        raise
    finally:
        if mysql_conn:
            mysql_conn.close()
        if pgsql_conn:
            pgsql_conn.close()


def get_doi_mapping_batch(pmid_list):
    """批量获取pmid对应的DOI"""
    if not pmid_list:
        return {}

    mysql_conn = None
    mapping = {}

    try:
        mysql_conn = get_mysql_connection()
        cursor = mysql_conn.cursor()

        # 分批处理，避免IN子句过长
        batch_size = 1000
        for i in range(0, len(pmid_list), batch_size):
            batch_pmids = pmid_list[i:i + batch_size]
            placeholders = ','.join(['%s'] * len(batch_pmids))

            # 查询DOI
            query = f"""
            SELECT pmid, other_id FROM tb_dds_article_otherid
            WHERE pmid IN ({placeholders}) AND source = 'doi'
            """
            cursor.execute(query, batch_pmids)
            for row in cursor.fetchall():
                pmid, doi = row
                if doi and doi.strip():  # 确保DOI不为空
                    mapping[pmid] = doi.strip()

        logger.info(f"批量获取到DOI映射关系 {len(mapping)} 条")
        return mapping

    except Exception as e:
        logger.error(f"批量获取DOI映射关系失败: {e}")
        return {}
    finally:
        if mysql_conn:
            mysql_conn.close()


def get_article_id_mapping():
    """获取文章ID映射关系：v1的pmid -> v2的id"""
    pgsql_conn = None
    mapping = {}

    try:
        pgsql_conn = get_pgsql_connection()
        cursor = pgsql_conn.cursor()

        # 查询已迁移的文章，获取custom_id和id的映射关系
        query = """
        SELECT id, custom_id FROM tb_dds_article
        WHERE custom_id >= %s AND custom_id <= %s
        """
        cursor.execute(query, (PMID_MIN, PMID_MAX))

        for row in cursor.fetchall():
            article_id, custom_id = row
            mapping[custom_id] = article_id

        logger.info(f"获取到文章ID映射关系 {len(mapping)} 条")
        return mapping

    except Exception as e:
        logger.error(f"获取文章ID映射关系失败: {e}")
        raise
    finally:
        if pgsql_conn:
            pgsql_conn.close()


def migrate_attachments():
    """迁移附件数据"""
    mysql_conn = None
    pgsql_conn = None
    migrated_count = 0
    failed_count = 0

    try:
        # 获取文章ID映射关系
        article_mapping = get_article_id_mapping()
        if not article_mapping:
            logger.warning("没有找到文章ID映射关系，跳过附件迁移")
            return 0, 0

        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor(pymysql.cursors.DictCursor)

        pgsql_conn = get_pgsql_connection()
        pgsql_cursor = pgsql_conn.cursor()

        # 查询v1数据库中的附件数据
        select_query = """
        SELECT id, pmid, type, origin_name, local_path, file_suffix, source,
               description, create_userid, create_siteid, create_time, file_md5
        FROM `tb_dds_article_attachment`
        WHERE `pmid` >= %s AND `pmid` <= %s AND type = 'PDF'
        ORDER BY pmid, create_time
        """

        logger.info("开始查询v1数据库中的附件数据...")
        mysql_cursor.execute(select_query, (PMID_MIN, PMID_MAX))

        # 准备v2数据库的插入语句
        insert_query = """
        INSERT INTO tb_dds_file (
            id, file_name, content_type, file_size, md5, file_path,
            create_time, type, doc_id, source, create_site_id
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        logger.info("开始迁移附件数据...")
        batch_size = 100
        batch_data = []

        for row in mysql_cursor:
            try:
                # 检查对应的文章是否已迁移
                v1_pmid = row['pmid']
                if v1_pmid not in article_mapping:
                    logger.warning(f"附件对应的文章未找到，跳过 pmid={v1_pmid}, attachment_id={row['id']}")
                    failed_count += 1
                    continue

                # 生成雪花ID作为v2的主键
                new_id = snow.gen_uid()

                # 字段映射和转换
                file_name = row['origin_name']
                content_type = row['file_suffix']  # v1的file_suffix对应v2的content_type
                file_size = None  # v1表中没有文件大小字段，需要根据实际文件计算
                md5 = row['file_md5']
                file_path = row['local_path']
                create_time = row['create_time']
                file_type = 'PDF'  # 固定为PDF类型
                doc_id = article_mapping[v1_pmid]  # 对应的文章ID
                source = row['source']
                create_site_id = row['create_siteid']
                # TODO: 如果需要计算文件大小，可以在这里添加文件大小计算逻辑
                # import os
                # if file_path and os.path.exists(file_path):
                #     file_size = os.path.getsize(file_path)

                # 准备插入数据
                insert_data = (
                    new_id, file_name, content_type, file_size, md5, file_path,
                    create_time, file_type, doc_id, source, create_site_id
                )

                batch_data.append(insert_data)

                # 执行批量插入
                if len(batch_data) >= batch_size:
                    pgsql_cursor.executemany(insert_query, batch_data)
                    pgsql_conn.commit()
                    migrated_count += len(batch_data)
                    logger.info(f"已迁移附件数量: {migrated_count}")
                    batch_data = []

            except Exception as e:
                logger.error(f"迁移附件失败 pmid={row['pmid']}, attachment_id={row['id']}: {e}")
                failed_count += 1
                continue

        # 处理剩余的批次数据
        if batch_data:
            pgsql_cursor.executemany(insert_query, batch_data)
            pgsql_conn.commit()
            migrated_count += len(batch_data)

        logger.info(f"附件迁移完成! 成功: {migrated_count}, 失败: {failed_count}")
        return migrated_count, failed_count

    except Exception as e:
        logger.error(f"附件迁移过程中发生错误: {e}")
        logger.error(traceback.format_exc())
        if pgsql_conn:
            pgsql_conn.rollback()
        raise
    finally:
        if mysql_conn:
            mysql_conn.close()
        if pgsql_conn:
            pgsql_conn.close()


def main():
    """主函数：执行完整的迁移流程"""
    start_time = datetime.now()
    logger.info("=" * 60)
    logger.info("开始执行数据库迁移任务")
    logger.info(f"迁移范围: pmid >= {PMID_MIN} AND pmid <= {PMID_MAX}")
    logger.info(f"开始时间: {start_time}")
    logger.info("=" * 60)

    try:
        # 步骤1: 检查迁移范围
        logger.info("步骤1: 检查迁移范围内的数据量...")
        article_count, attachment_count = check_migration_scope()

        if article_count == 0:
            logger.warning("没有找到需要迁移的文章数据，退出程序")
            return

        # 确认是否继续
        logger.info(f"即将迁移 {article_count} 篇文章和 {attachment_count} 个PDF附件")

        # 步骤2: 迁移文章数据
        logger.info("步骤2: 开始迁移文章数据...")
        article_migrated, article_failed = migrate_articles()

        if article_migrated == 0:
            logger.error("文章迁移失败，跳过附件迁移")
            return

        # 步骤3: 迁移附件数据
        logger.info("步骤3: 开始迁移附件数据...")
        attachment_migrated, attachment_failed = migrate_attachments()

        # 步骤4: 输出迁移结果
        end_time = datetime.now()
        duration = end_time - start_time

        logger.info("=" * 60)
        logger.info("数据库迁移任务完成!")
        logger.info(f"结束时间: {end_time}")
        logger.info(f"总耗时: {duration}")
        logger.info("迁移结果统计:")
        logger.info(f"  文章: 成功 {article_migrated}, 失败 {article_failed}")
        logger.info(f"  附件: 成功 {attachment_migrated}, 失败 {attachment_failed}")
        logger.info("=" * 60)

        # 如果有失败的记录，提醒检查日志
        if article_failed > 0 or attachment_failed > 0:
            logger.warning("存在迁移失败的记录，请检查日志文件 migration.log 获取详细信息")

    except Exception as e:
        logger.error(f"迁移任务执行失败: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


def verify_migration():
    """验证迁移结果"""
    logger.info("开始验证迁移结果...")

    mysql_conn = None
    pgsql_conn = None

    try:
        mysql_conn = get_mysql_connection()
        pgsql_conn = get_pgsql_connection()

        mysql_cursor = mysql_conn.cursor()
        pgsql_cursor = pgsql_conn.cursor()

        # 验证文章数量
        mysql_cursor.execute(
            "SELECT count(1) FROM `tb_dds_article` WHERE `pmid` >= %s AND `pmid` <= %s",
            (PMID_MIN, PMID_MAX)
        )
        v1_article_count = mysql_cursor.fetchone()[0]

        pgsql_cursor.execute(
            "SELECT count(1) FROM tb_dds_article WHERE custom_id >= %s AND custom_id <= %s",
            (PMID_MIN, PMID_MAX)
        )
        v2_article_count = pgsql_cursor.fetchone()[0]

        # 验证附件数量
        mysql_cursor.execute(
            "SELECT count(1) FROM `tb_dds_article_attachment` WHERE `pmid` >= %s AND `pmid` <= %s AND type = 'PDF'",
            (PMID_MIN, PMID_MAX)
        )
        v1_attachment_count = mysql_cursor.fetchone()[0]

        pgsql_cursor.execute(
            """SELECT count(1) FROM tb_dds_file f
               JOIN tb_dds_article a ON f.doc_id = a.id
               WHERE a.custom_id >= %s AND a.custom_id <= %s AND f.type = 'PDF'""",
            (PMID_MIN, PMID_MAX)
        )
        v2_attachment_count = pgsql_cursor.fetchone()[0]

        logger.info("迁移结果验证:")
        logger.info(f"  文章数量 - v1: {v1_article_count}, v2: {v2_article_count}")
        logger.info(f"  附件数量 - v1: {v1_attachment_count}, v2: {v2_attachment_count}")

        if v1_article_count == v2_article_count and v1_attachment_count == v2_attachment_count:
            logger.info("✓ 迁移验证通过，数据量一致")
        else:
            logger.warning("✗ 迁移验证失败，数据量不一致，请检查迁移过程")

    except Exception as e:
        logger.error(f"验证迁移结果失败: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()
        if pgsql_conn:
            pgsql_conn.close()


if __name__ == "__main__":
    # 检查必要的依赖包
    try:
        import pymysql
        import psycopg2
    except ImportError as e:
        logger.error(f"缺少必要的依赖包: {e}")
        logger.error("请安装依赖包: pip install pymysql psycopg2-binary")
        sys.exit(1)

    # 执行迁移
    main()

    # 验证迁移结果
    verify_migration()
