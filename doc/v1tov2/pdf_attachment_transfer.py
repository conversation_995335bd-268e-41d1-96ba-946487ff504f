#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF附件迁移脚本：从v1 MySQL数据库迁移PDF附件到v2 PostgreSQL数据库
迁移表：tb_dds_article_attachment (type='PDF') -> tb_dds_file_2
迁移范围：pmid < ************ OR pmid > ************
"""

import logging
import os
import psycopg2.extras
import pymysql.cursors
import sys
import traceback
from datetime import datetime
from toollib.guid import SnowFlake

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_migration.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
V1_MYSQL_CONFIG = {
    'host': '************',  # TODO: 修改为实际的MySQL主机地址
    'port': 32526,
    'user': 'root',  # TODO: 修改为实际的MySQL用户名
    'password': 'Lfgzs@2021',  # TODO: 修改为实际的MySQL密码
    'database': 'plosp_online',
    'charset': 'utf8mb4'
}

V2_PGSQL_CONFIG = {
    'host': '************',  # TODO: 修改为实际的PostgreSQL主机地址
    'port': 31910,
    'user': 'postgres',  # TODO: 修改为实际的PostgreSQL用户名
    'password': 'Biosino+2025',  # TODO: 修改为实际的PostgreSQL密码
    'database': 'postgres',  # TODO: 修改为实际的PostgreSQL数据库名
    'options': '-c search_path=public'  # TODO: 修改为实际的schema名称，如果不是public的话
}

# 迁移范围配置
PMID_EXCLUDE_MIN = ************
PMID_EXCLUDE_MAX = ************

# 雪花ID生成器
snow = SnowFlake(worker_id=1, datacenter_id=1)


def get_mysql_connection():
    """获取MySQL数据库连接"""
    try:
        connection = pymysql.connect(**V1_MYSQL_CONFIG)
        logger.info("MySQL数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"MySQL数据库连接失败: {e}")
        raise


def get_pgsql_connection():
    """获取PostgreSQL数据库连接"""
    try:
        connection = psycopg2.connect(**V2_PGSQL_CONFIG)
        logger.info("PostgreSQL数据库连接成功")
        return connection
    except Exception as e:
        logger.error(f"PostgreSQL数据库连接失败: {e}")
        raise


def check_migration_scope():
    """检查迁移范围内的PDF附件数量"""
    mysql_conn = None
    try:
        mysql_conn = get_mysql_connection()
        cursor = mysql_conn.cursor()

        # 检查PDF附件数量
        attachment_query = """
        SELECT pmid, type FROM `tb_dds_article_attachment`
        """
        cursor.execute(attachment_query)

        # 在Python中统计符合条件的数量
        attachment_count = 0
        for row in cursor:
            pmid, attachment_type = row
            if attachment_type == 'PDF' and (pmid < PMID_EXCLUDE_MIN or pmid > PMID_EXCLUDE_MAX):
                attachment_count += 1
        logger.info(f"待迁移PDF附件数量: {attachment_count}")

        return attachment_count

    except Exception as e:
        logger.error(f"检查迁移范围失败: {e}")
        raise
    finally:
        if mysql_conn:
            mysql_conn.close()


def get_article_id_mapping_by_pmid_batch(pmid_list):
    """批量获取指定pmid列表的文章ID映射关系（查询pmid字段）"""
    if not pmid_list:
        return {}

    pgsql_conn = None
    mapping = {}

    try:
        pgsql_conn = get_pgsql_connection()
        cursor = pgsql_conn.cursor()

        # 分批处理，避免IN子句过长
        batch_size = 1000
        for i in range(0, len(pmid_list), batch_size):
            batch_pmids = pmid_list[i:i + batch_size]
            placeholders = ','.join(['%s'] * len(batch_pmids))

            # 查询pmid字段
            query_pmid = f"SELECT id, pmid FROM tb_dds_article WHERE pmid IN ({placeholders})"
            cursor.execute(query_pmid, batch_pmids)
            for row in cursor.fetchall():
                article_id, pmid = row
                mapping[pmid] = article_id

        logger.info(f"批量获取到pmid映射关系 {len(mapping)} 条")
        return mapping

    except Exception as e:
        logger.error(f"批量获取pmid映射关系失败: {e}")
        return {}
    finally:
        if pgsql_conn:
            pgsql_conn.close()


def get_pmcid_from_v1_by_pmid_batch(pmid_list):
    """从v1数据库中批量获取pmid对应的pmc_id"""
    if not pmid_list:
        return {}

    mysql_conn = None
    mapping = {}

    try:
        mysql_conn = get_mysql_connection()
        cursor = mysql_conn.cursor()

        # 分批处理，避免IN子句过长
        batch_size = 1000
        for i in range(0, len(pmid_list), batch_size):
            batch_pmids = pmid_list[i:i + batch_size]
            placeholders = ','.join(['%s'] * len(batch_pmids))

            # 查询v1的tb_dds_article表获取pmc_id
            query = f"SELECT pmid, pmc_id FROM tb_dds_article WHERE pmid IN ({placeholders})"
            cursor.execute(query, batch_pmids)
            for row in cursor.fetchall():
                pmid, pmc_id = row
                if pmc_id:  # 只有pmc_id不为空才添加映射
                    mapping[pmid] = pmc_id

        logger.info(f"从v1获取到pmid->pmc_id映射关系 {len(mapping)} 条")
        return mapping

    except Exception as e:
        logger.error(f"从v1获取pmid->pmc_id映射关系失败: {e}")
        return {}
    finally:
        if mysql_conn:
            mysql_conn.close()


def get_article_id_mapping_by_pmcid_batch(pmcid_list):
    """批量获取指定pmcid列表的文章ID映射关系（查询pmc_id字段）"""
    if not pmcid_list:
        return {}

    pgsql_conn = None
    mapping = {}

    try:
        pgsql_conn = get_pgsql_connection()
        cursor = pgsql_conn.cursor()

        # 分批处理，避免IN子句过长
        batch_size = 1000
        for i in range(0, len(pmcid_list), batch_size):
            batch_pmcids = pmcid_list[i:i + batch_size]
            placeholders = ','.join(['%s'] * len(batch_pmcids))

            # 查询pmc_id字段
            query_pmc = f"SELECT id, pmc_id FROM tb_dds_article WHERE pmc_id IN ({placeholders})"
            cursor.execute(query_pmc, batch_pmcids)
            for row in cursor.fetchall():
                article_id, pmc_id = row
                mapping[pmc_id] = article_id

        logger.info(f"批量获取到pmcid映射关系 {len(mapping)} 条")
        return mapping

    except Exception as e:
        logger.error(f"批量获取pmcid映射关系失败: {e}")
        return {}
    finally:
        if pgsql_conn:
            pgsql_conn.close()


def get_article_id_by_pmid(pmid_value):
    """根据pmid获取v2数据库中对应的文章ID（保留兼容性）

    Args:
        pmid_value: v1数据库中的pmid值

    Returns:
        对应的v2文章ID，如果未找到返回None
    """
    pgsql_conn = None
    try:
        pgsql_conn = get_pgsql_connection()
        cursor = pgsql_conn.cursor()

        # 判断pmid是否以90000开头
        if float(pmid_value) >= 900000000000:
            # 如果是90000开头，去pmc_id字段查找
            query = "SELECT id FROM tb_dds_article WHERE pmc_id = %s"
            cursor.execute(query, (pmid_value,))
        else:
            # 否则去pmid字段查找
            query = "SELECT id FROM tb_dds_article WHERE pmid = %s"
            cursor.execute(query, (pmid_value,))

        result = cursor.fetchone()
        if result:
            return result[0]
        else:
            return None

    except Exception as e:
        logger.error(f"查询文章ID失败 pmid={pmid_value}: {e}")
        return None
    finally:
        if pgsql_conn:
            pgsql_conn.close()


def calculate_file_size(file_path):
    """计算文件大小"""
    try:
        if file_path and os.path.exists(file_path):
            return os.path.getsize(file_path)
        else:
            logger.warning(f"文件不存在: {file_path}")
            return None
    except Exception as e:
        logger.warning(f"计算文件大小失败 {file_path}: {e}")
        return None


def process_attachment_batch(attachment_batch, pmid_mapping, pmcid_mapping, pmid_to_pmcid_mapping, insert_query, pgsql_cursor):
    """处理一批附件数据"""
    batch_data = []
    failed_count = 0

    for attachment_row in attachment_batch:
        try:
            # 从映射关系中查找对应的文章ID
            v1_pmid = attachment_row['pmid']
            doc_id = None

            # 根据pmid类型选择对应的映射
            if float(v1_pmid) >= 900000000000:
                # 90000开头的先通过pmid找到pmc_id，再通过pmc_id找到doc_id
                v1_pmcid = pmid_to_pmcid_mapping.get(v1_pmid)
                if v1_pmcid:
                    doc_id = pmcid_mapping.get(v1_pmcid)
            else:
                # 其他的从pmid_mapping中查找
                doc_id = pmid_mapping.get(v1_pmid)

            # 如果没有找到doc_id，跳过这个附件
            if doc_id is None:
                logger.warning(f"PDF附件对应的文章未找到，跳过 pmid={v1_pmid}, attachment_id={attachment_row['id']}")
                failed_count += 1
                continue

            # 生成雪花ID作为v2的主键
            new_id = snow.gen_uid()

            # 字段映射和转换
            file_name = attachment_row['origin_name']
            content_type = attachment_row['file_suffix']  # v1的file_suffix对应v2的content_type
            file_size = None  # v1表中没有文件大小字段
            md5 = attachment_row['file_md5']
            file_path = attachment_row['local_path']
            create_time = attachment_row['create_time']
            file_type = 'PDF'  # 固定为PDF类型
            source = attachment_row['source']
            create_site_id = attachment_row['create_siteid']

            # 准备插入数据
            insert_data = (
                new_id, file_name, content_type, file_size, md5, file_path,
                create_time, file_type, doc_id, source, create_site_id
            )

            batch_data.append(insert_data)

        except Exception as e:
            logger.error(f"迁移PDF附件失败 pmid={attachment_row['pmid']}, attachment_id={attachment_row['id']}: {e}")
            failed_count += 1
            continue

    # 批量插入数据
    migrated_count = 0
    if batch_data:
        pgsql_cursor.executemany(insert_query, batch_data)
        migrated_count = len(batch_data)

    return migrated_count, failed_count


def migrate_pdf_attachments():
    """迁移PDF附件数据"""
    mysql_conn = None
    pgsql_conn = None
    migrated_count = 0
    failed_count = 0

    try:
        mysql_conn = get_mysql_connection()
        mysql_cursor = mysql_conn.cursor(pymysql.cursors.SSDictCursor)

        pgsql_conn = get_pgsql_connection()
        pgsql_cursor = pgsql_conn.cursor()

        # 查询v1数据库中的所有附件数据
        select_query = """
        SELECT id, pmid, type, origin_name, local_path, file_suffix, source,
               description, create_userid, create_siteid, create_time, file_md5
        FROM `tb_dds_article_attachment`
        """

        logger.info("开始查询v1数据库中的附件数据...")
        mysql_cursor.execute(select_query)

        # 准备v2数据库的插入语句
        insert_query = """
        INSERT INTO tb_dds_file_2 (
            id, file_name, content_type, file_size, md5, file_path,
            create_time, type, doc_id, source, create_site_id
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """

        logger.info("开始迁移PDF附件数据...")

        # 分批处理附件数据，避免一次性加载所有数据到内存
        batch_size = 1000
        processed_count = 0
        attachment_batch = []

        # 逐行处理，每收集到batch_size个附件就处理一批
        for row in mysql_cursor:
            # 在Python中过滤type和pmid范围
            if row['type'] != 'PDF':
                continue  # 只处理PDF类型的附件

            pmid = row['pmid']
            if not (pmid < PMID_EXCLUDE_MIN or pmid > PMID_EXCLUDE_MAX):
                continue  # 跳过在排除范围内的数据

            attachment_batch.append(row)

            # 当收集到足够的附件时，进行批量处理
            if len(attachment_batch) >= batch_size:
                # 分别收集pmid和90000开头的pmid
                pmid_list = []
                pmid_90000_list = []

                for r in attachment_batch:
                    if float(r['pmid']) >= 900000000000:
                        # 90000开头的pmid，需要先查询对应的pmc_id
                        pmid_90000_list.append(r['pmid'])
                    else:
                        # 其他的使用pmid字段查询
                        pmid_list.append(r['pmid'])

                # 先从v1获取90000开头pmid对应的pmc_id
                pmid_to_pmcid_mapping = get_pmcid_from_v1_by_pmid_batch(pmid_90000_list)
                pmcid_list = list(pmid_to_pmcid_mapping.values())

                # 分别批量获取文章ID映射关系
                pmid_mapping = get_article_id_mapping_by_pmid_batch(pmid_list)
                pmcid_mapping = get_article_id_mapping_by_pmcid_batch(pmcid_list)

                # 处理这批附件
                batch_migrated, batch_failed = process_attachment_batch(
                    attachment_batch, pmid_mapping, pmcid_mapping, pmid_to_pmcid_mapping, insert_query, pgsql_cursor
                )

                # 提交事务
                pgsql_conn.commit()
                migrated_count += batch_migrated
                failed_count += batch_failed
                processed_count += len(attachment_batch)

                logger.info(f"已处理批次，成功迁移: {migrated_count}, 失败: {failed_count}")

                # 清空当前批次，准备下一批
                attachment_batch = []

        # 处理最后一批数据（如果有剩余的）
        if attachment_batch:
            # 分别收集pmid和90000开头的pmid
            pmid_list = []
            pmid_90000_list = []

            for r in attachment_batch:
                if float(r['pmid']) >= 900000000000:
                    # 90000开头的pmid，需要先查询对应的pmc_id
                    pmid_90000_list.append(r['pmid'])
                else:
                    # 其他的使用pmid字段查询
                    pmid_list.append(r['pmid'])

            # 先从v1获取90000开头pmid对应的pmc_id
            pmid_to_pmcid_mapping = get_pmcid_from_v1_by_pmid_batch(pmid_90000_list)
            pmcid_list = list(pmid_to_pmcid_mapping.values())

            # 分别批量获取文章ID映射关系
            pmid_mapping = get_article_id_mapping_by_pmid_batch(pmid_list)
            pmcid_mapping = get_article_id_mapping_by_pmcid_batch(pmcid_list)

            # 处理这批附件
            batch_migrated, batch_failed = process_attachment_batch(
                attachment_batch, pmid_mapping, pmcid_mapping, pmid_to_pmcid_mapping, insert_query, pgsql_cursor
            )

            # 提交事务
            pgsql_conn.commit()
            migrated_count += batch_migrated
            failed_count += batch_failed

            logger.info(f"处理最后一批，成功迁移: {migrated_count}, 失败: {failed_count}")

        logger.info(f"PDF附件迁移完成! 成功: {migrated_count}, 失败: {failed_count}")
        return migrated_count, failed_count

    except Exception as e:
        logger.error(f"PDF附件迁移过程中发生错误: {e}")
        logger.error(traceback.format_exc())
        if pgsql_conn:
            pgsql_conn.rollback()
        raise
    finally:
        if mysql_conn:
            mysql_conn.close()
        if pgsql_conn:
            pgsql_conn.close()


def main():
    """主函数：执行PDF附件迁移流程"""
    start_time = datetime.now()
    logger.info("=" * 60)
    logger.info("开始执行PDF附件迁移任务")
    logger.info(f"迁移范围: pmid < {PMID_EXCLUDE_MIN} OR pmid > {PMID_EXCLUDE_MAX}")
    logger.info(f"开始时间: {start_time}")
    logger.info("=" * 60)

    try:
        # 步骤1: 检查迁移范围
        # logger.info("步骤1: 检查迁移范围内的PDF附件数量...")
        # attachment_count = check_migration_scope()
        #
        # if attachment_count == 0:
        #     logger.warning("没有找到需要迁移的PDF附件数据，退出程序")
        #     return
        #
        # # 确认是否继续
        # logger.info(f"即将迁移 {attachment_count} 个PDF附件")

        # 步骤2: 迁移PDF附件数据
        logger.info("步骤2: 开始迁移PDF附件数据...")
        attachment_migrated, attachment_failed = migrate_pdf_attachments()

        # 步骤3: 输出迁移结果
        end_time = datetime.now()
        duration = end_time - start_time

        logger.info("=" * 60)
        logger.info("PDF附件迁移任务完成!")
        logger.info(f"结束时间: {end_time}")
        logger.info(f"总耗时: {duration}")
        logger.info("迁移结果统计:")
        logger.info(f"  PDF附件: 成功 {attachment_migrated}, 失败 {attachment_failed}")
        logger.info("=" * 60)

        # 如果有失败的记录，提醒检查日志
        if attachment_failed > 0:
            logger.warning("存在迁移失败的记录，请检查日志文件 pdf_migration.log 获取详细信息")

    except Exception as e:
        logger.error(f"PDF附件迁移任务执行失败: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


def verify_migration():
    """验证PDF附件迁移结果"""
    logger.info("开始验证PDF附件迁移结果...")

    mysql_conn = None
    pgsql_conn = None

    try:
        mysql_conn = get_mysql_connection()
        pgsql_conn = get_pgsql_connection()

        mysql_cursor = mysql_conn.cursor()
        pgsql_cursor = pgsql_conn.cursor()

        # 验证PDF附件数量
        mysql_cursor.execute(
            "SELECT pmid, type FROM `tb_dds_article_attachment`"
        )

        # 在Python中统计符合条件的数量
        v1_attachment_count = 0
        for row in mysql_cursor:
            pmid, attachment_type = row
            if attachment_type == 'PDF' and (pmid < PMID_EXCLUDE_MIN or pmid > PMID_EXCLUDE_MAX):
                v1_attachment_count += 1

        # 统计v2中迁移的PDF附件数量（通过pmid和pmc_id字段关联）
        pgsql_cursor.execute(
            """SELECT count(1) FROM tb_dds_file_2 f
               JOIN tb_dds_article a ON f.doc_id = a.id
               WHERE f.type = 'PDF' AND (
                   (a.pmid IS NOT NULL AND (a.pmid < %s OR a.pmid > %s)) OR
                   (a.pmc_id IS NOT NULL AND (a.pmc_id < %s OR a.pmc_id > %s))
               )""",
            (PMID_EXCLUDE_MIN, PMID_EXCLUDE_MAX, PMID_EXCLUDE_MIN, PMID_EXCLUDE_MAX)
        )
        v2_attachment_count = pgsql_cursor.fetchone()[0]

        logger.info("PDF附件迁移结果验证:")
        logger.info(f"  v1 PDF附件数量: {v1_attachment_count}")
        logger.info(f"  v2 PDF附件数量: {v2_attachment_count}")

        if v1_attachment_count == v2_attachment_count:
            logger.info("✓ PDF附件迁移验证通过，数据量一致")
        else:
            logger.warning("✗ PDF附件迁移验证失败，数据量不一致，请检查迁移过程")

        # 验证文件大小统计
        pgsql_cursor.execute(
            """SELECT count(1) as total_files,
                      count(file_size) as files_with_size,
                      sum(file_size) as total_size
               FROM tb_dds_file_2 f
               JOIN tb_dds_article a ON f.doc_id = a.id
               WHERE f.type = 'PDF' AND (
                   (a.pmid IS NOT NULL AND (a.pmid < %s OR a.pmid > %s)) OR
                   (a.pmc_id IS NOT NULL AND (a.pmc_id < %s OR a.pmc_id > %s))
               )""",
            (PMID_EXCLUDE_MIN, PMID_EXCLUDE_MAX, PMID_EXCLUDE_MIN, PMID_EXCLUDE_MAX)
        )
        size_stats = pgsql_cursor.fetchone()
        total_files, files_with_size, total_size = size_stats

        logger.info("文件大小统计:")
        logger.info(f"  总文件数: {total_files}")
        logger.info(f"  有大小信息的文件数: {files_with_size}")
        logger.info(f"  总大小: {total_size if total_size else 0} 字节")

    except Exception as e:
        logger.error(f"验证PDF附件迁移结果失败: {e}")
    finally:
        if mysql_conn:
            mysql_conn.close()
        if pgsql_conn:
            pgsql_conn.close()


if __name__ == "__main__":
    # 检查必要的依赖包
    try:
        import pymysql
        import psycopg2
    except ImportError as e:
        logger.error(f"缺少必要的依赖包: {e}")
        logger.error("请安装依赖包: pip install pymysql psycopg2-binary")
        sys.exit(1)

    # 执行迁移
    main()

    # 验证迁移结果
    verify_migration()
