import {getToken} from "@/utils/auth.js";

/**
 * 通过表单提交方式下载文件
 * @param {string} url - 下载请求的URL
 * @param {Object} data - 需要提交的数据对象
 * @param {string} method - 请求方法，默认为'post'
 * @param {boolean} openInNewWindow - 是否在新窗口中打开，默认为false
 */
export function downloadUseForm(url, data, method = 'post', openInNewWindow = true) {
    // 创建临时表单
    const form = document.createElement('form');
    form.method = method;
    form.action = `${import.meta.env.VITE_APP_BASE_API}${url}`;
    form.style.display = 'none';

    // 如果需要在新窗口中打开
    if (openInNewWindow) {
        form.target = '_blank';
    }

    // 加一个token
    if (getToken()) {
        let authorizationInput = document.createElement('input');
        authorizationInput.type = 'hidden';
        authorizationInput.name = 'Authorization';
        authorizationInput.value = 'Bearer ' + getToken();
        form.appendChild(authorizationInput);
    }

    // 处理参数
    if (data) {
        // 收集所有需要创建的input字段
        const fields = [];
        // 递归处理对象，将其扁平化为字段数组
        flattenObject(data, '', fields);

        // 根据收集的字段创建input元素
        fields.forEach(field => {
            const input = document.createElement('input');
            input.type = 'hidden'; // 明确表示这是隐藏的数据字段
            input.name = field.key;
            input.value = field.value;
            form.appendChild(input);
        });
    }

    // 添加到DOM中并提交
    document.body.appendChild(form);

    try {
        form.submit();

        // 延迟1秒后移除表单，确保提交完成
        setTimeout(() => {
            if (document.body.contains(form)) {
                document.body.removeChild(form);
            }
        }, 1000);
    } catch (error) {
        console.error('Form submission failed:', error);
        // 确保即使提交失败也移除表单
        if (document.body.contains(form)) {
            document.body.removeChild(form);
        }
    }
}

/**
 * 递归地将对象扁平化为键值对数组
 * @param {any} obj - 要处理的对象
 * @param {string} prefix - 当前的键前缀
 * @param {Array} result - 结果数组，存储{key, value}对象
 */
function flattenObject(obj, prefix = '', result = []) {
    // 跳过null和undefined
    if (obj === null || obj === undefined) {
        return;
    }

    // 处理数组
    if (Array.isArray(obj)) {
        obj.forEach((item, index) => {
            if (item === null || item === undefined) {
                // 跳过数组中的null和undefined
                return;
            } else if (typeof item === 'object' && !Array.isArray(item)) {
                // 递归处理数组中的对象，使用索引
                const newPrefix = prefix ? `${prefix}[${index}]` : `[${index}]`;
                flattenObject(item, newPrefix, result);
            } else if (Array.isArray(item)) {
                // 递归处理嵌套数组，使用索引
                const newPrefix = prefix ? `${prefix}[${index}]` : `[${index}]`;
                flattenObject(item, newPrefix, result);
            } else {
                // 处理基本类型，直接使用父级key名（不加索引）
                result.push({
                    key: prefix || 'value',
                    value: formatValue(item)
                });
            }
        });
        return;
    }

    // 处理对象
    if (typeof obj === 'object') {
        Object.keys(obj).forEach(key => {
            const value = obj[key];
            const newPrefix = prefix ? `${prefix}.${key}` : key;

            if (value === null || value === undefined) {
                // 跳过null和undefined值
                return;
            } else if (typeof value === 'object') {
                // 递归处理嵌套对象或数组
                flattenObject(value, newPrefix, result);
            } else {
                // 处理基本类型
                result.push({
                    key: newPrefix,
                    value: formatValue(value)
                });
            }
        });
        return;
    }

    // 处理基本类型（作为根对象的情况，通常不会发生）
    result.push({
        key: prefix,
        value: formatValue(obj)
    });
}

/**
 * 格式化表单字段值
 * @param {any} value - 需要格式化的值
 * @returns {string} - 格式化后的字符串
 */
function formatValue(value) {
    if (value === undefined || value === null) {
        return '';
    }

    if (typeof value === 'boolean') {
        return value ? 'true' : 'false';
    }

    // 对象和数组应该已经被flattenObject处理，这里是保险措施
    if (typeof value === 'object') {
        try {
            return encodeURIComponent(JSON.stringify(value));
        } catch (e) {
            console.error('Failed to stringify object:', e);
            return '';
        }
    }

    // 对字符串值进行编码，防止注入攻击
    return typeof value === 'string' ? encodeURIComponent(value) : String(value);
}
