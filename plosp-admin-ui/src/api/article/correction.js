import request from '@/utils/request'

// 查询纠错列表
export function listCorrections(query) {
    return request({
        url: '/article/correction/list',
        method: 'get',
        params: query
    })
}

// 接受操作
export function acceptCorrection(id) {
    return request({
        url: '/article/correction/accept',
        method: 'post',
        params: {id}
    })
}

// 驳回操作
export function rejectCorrection(id, reason) {
    return request({
        url: '/article/correction/reject',
        method: 'post',
        params: {id, reason}
    })
}
