import request from '@/utils/request';

// 查询文献列表
export function listArticle(query) {
  return request({
    url: '/article/list',
    method: 'get',
    params: query
  });
}

// 获取文献详情
export function getArticle(id) {
  return request({
    url: `/article/${id}`,
    method: 'get'
  });
}

// 修改文献
export function updateArticle(data) {
  return request({
    url: '/article/updateArticle',
    method: 'post',
    data: data
  });
}


// 导入文献题录
export function importArticle(data) {
  return request({
    url: '/article/importArticles',
    method: 'post',
    data: data
  });
}

// 删除文件
export function deleteAttachment(fileId) {
  return request({
    url: `/article/file/deleteAttachment/${fileId}`,
    method: 'delete'
  });
}

// 获取指定范围内的最大customId
export function getMaxCustomId(minValue, maxValue) {
  return request({
    url: '/article/getMaxCustomId',
    method: 'get',
    params: {
      minValue,
      maxValue
    }
  });
}
