import request from '@/utils/request'

// 查询上传列表
export function listUploads(query) {
    return request({
        url: '/article/attachmentUpload/list',
        method: 'get',
        params: query
    })
}

// 接受操作
export function acceptUpload(id) {
    return request({
        url: '/article/attachmentUpload/accept',
        method: 'post',
        params: {id}
    })
}

// 驳回操作
export function rejectUpload(id, reason) {
    return request({
        url: '/article/attachmentUpload/reject',
        method: 'post',
        params: {id, reason}
    })
}

