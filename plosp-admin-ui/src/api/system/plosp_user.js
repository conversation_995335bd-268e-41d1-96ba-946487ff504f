import request from '@/utils/request'

// 查询前台用户列表
export function listUser(query) {
    return request({
        url: '/system/plospUser/list',
        method: 'get',
        params: query
    })
}

// 查询前台用户详细
export function getUser(id) {
    return request({
        url: '/system/plospUser/' + id,
        method: 'get'
    })
}

// 新增前台用户
export function addUser(data) {
    return request({
        url: '/system/plospUser',
        method: 'post',
        data: data
    })
}

// 修改前台用户
export function updateUser(data) {
    return request({
        url: '/system/plospUser',
        method: 'put',
        data: data
    })
}

// 删除前台用户
export function delUser(id) {
    return request({
        url: '/system/plospUser/' + id,
        method: 'delete'
    })
}

// 修改用户状态
export function changeUserStatus(id, status) {
    return request({
        url: '/system/plospUser/changeStatus',
        method: 'put',
        params: {
            id,
            status
        }
    })
}

// 重置用户密码
export function resetUserPwd(data) {
    return request({
        url: '/system/plospUser/resetPwd',
        method: 'put',
        data: data
    })
}
