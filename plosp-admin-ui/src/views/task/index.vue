<template>
  <div class="app-container box-card">
    <el-form ref="taskFormRef" :model="taskForm" :rules="rules" label-width="140px">
      <!-- 任务名称 -->
      <el-form-item label="任务名称" prop="taskDesc">
        <el-input
          v-model="taskForm.taskDesc"
          placeholder="请输入任务名称"
          class="standard-width"
        />
      </el-form-item>

      <!-- 优先级 -->
      <el-form-item label="优先级" prop="priority">
        <div class="priority-container">
          <el-radio-group v-model="taskForm.priorityType" @change="handlePriorityChange">
            <el-radio :label="3">高</el-radio>
            <el-radio :label="2">普通</el-radio>
            <el-radio :label="1" class="custom-priority-radio">
              自定义
              <el-input-number
                v-if="taskForm.priorityType === 1"
                v-model="taskForm.customPriority"
                :min="0"
                :max="99"
                placeholder="0~99"
                class="custom-priority-input"
                size="small"
              />
                </el-radio>
          </el-radio-group>
        </div>
        <div class="form-item-tip">高优先级任务将会优先被执行，普通为默认级别，自定义可以设置0-99的优先级值</div>
      </el-form-item>

      <!-- 支持的节点类型 -->
      <el-form-item label="支持的节点类型" prop="nodeTypes">
        <el-checkbox-group v-model="taskForm.nodeTypes">
          <el-checkbox label="1">批次节点</el-checkbox>
          <el-checkbox label="2">源刊节点</el-checkbox>
          <el-checkbox label="3">高校节点</el-checkbox>
        </el-checkbox-group>
        <div class="form-item-tip">选择任务可以分发到哪些类型的节点执行，默认全选(执行顺序：批次->源刊->高校)</div>
      </el-form-item>

      <!-- 是否测试任务 -->
      <el-form-item label="测试任务" prop="isTest">
        <el-radio-group v-model="taskForm.isTest">
          <el-radio :label="false">否</el-radio>
          <el-radio :label="true">是</el-radio>
        </el-radio-group>
        <div class="form-item-tip">测试任务下载成功的PDF不会入库到数据库中</div>
      </el-form-item>

      <!-- 下载模式 -->
      <el-form-item label="下载模式" prop="downloadMode">
        <el-radio-group v-model="taskForm.downloadMode">
          <el-radio label="speed">速度优先</el-radio>
          <el-radio label="complete">完整度优先</el-radio>
        </el-radio-group>
        <div class="form-item-tip">速度优先模式会优先分配给节点少的分组缩短下载时间，完整度优先模式会优先分配给节点多的分组增加成功率</div>
      </el-form-item>

      <!-- 重试间隔 -->
      <el-form-item label="重试间隔" prop="retryInterval">
        <el-input-number
          v-model="taskForm.retryInterval"
          :min="0"
          :max="120"
          placeholder="请输入重试间隔时间"
          class="standard-width"
        />
        <div class="form-item-tip">单位为秒，下载失败后等待多长时间进行重试，推荐5秒</div>
      </el-form-item>

      <!-- 文献提交方式 -->
      <el-form-item label="文献提交方式" prop="submitType">
        <div class="literature-input-container">
          <!-- Excel模板下载与上传 -->
          <div class="excel-actions">
            <el-button type="primary" @click="downloadExcelTemplate">
              <el-icon><Download /></el-icon>下载Excel模板
            </el-button>
            <el-upload
              class="excel-upload"
              action="#"
              :auto-upload="false"
              :on-change="handleExcelUpload"
              :limit="1"
              accept=".xlsx,.xls"
            >
              <el-button type="success">
                <el-icon><Upload /></el-icon>上传Excel文件
              </el-button>
            </el-upload>
          </div>
          <div class="form-item-tip">填写需要获取全文的PMID/PMCID/DOI，3列中仅需填写任意一种ID即可</div>
          <!-- 在线表格 -->
          <div class="hot-container" ref="hotTableContainer"></div>

        </div>
      </el-form-item>

      <!-- 提交按钮 -->
      <el-form-item>
        <el-button type="primary" @click="submitTask">发布任务</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 任务列表区域 可以在这里添加已发布的任务列表展示 -->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Download, Upload, Plus } from '@element-plus/icons-vue';
import { registerAllModules } from 'handsontable/registry';
import Handsontable from 'handsontable';
import 'handsontable/dist/handsontable.full.min.css';
import * as XLSX from 'xlsx';

// 注册所有Handsontable模块
registerAllModules();

// 任务表单数据
const taskForm = reactive({
  taskDesc: '',
  priorityType: 2, // 默认普通优先级
  customPriority: 50, // 默认自定义优先级值
  nodeTypes: ['1', '2', '3'], // 默认全选
  isTest: false, // 默认非测试任务
  downloadMode: 'speed', // 默认速度优先
  retryInterval: 5, // 默认重试间隔
  submitType: 'direct', // 默认直接输入
  literatureIds: '', // 用于存储直接输入的文献ID
  file: null // 用于存储上传的文件
});

// 文件内容和预览
const fileContent = ref('');
const filePreview = ref('');

// 表单验证规则
const rules = {
  taskDesc: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  nodeTypes: [
    { type: 'array', required: true, message: '请至少选择一种节点类型', trigger: 'change' }
  ],
  retryInterval: [
    { required: true, message: '请输入重试间隔', trigger: 'blur' }
  ]
};

const taskFormRef = ref(null);
const hotTableContainer = ref(null);
const tableRowCount = ref(0);
let hotInstance = null;

// 表格初始数据
const initialTableData = [
  ['', '', ''],
  ['', '', ''],
  ['', '', ''],
  ['', '', ''],
  ['', '', ''],
  ['', '', ''],
  ['', '', ''],
  ['', '', ''],
  ['', '', ''],
  ['', '', ''],
  ['', '', '']
];

// 初始化表格
const initTable = () => {
  if (hotTableContainer.value) {
    hotInstance = new Handsontable(hotTableContainer.value, {
      data: initialTableData,
      rowHeaders: true,
      colHeaders: ['PMID', 'PMCID', 'DOI'],
      columns: [
        { type: 'text' },
        { type: 'text' },
        { type: 'text' }
      ],
      width: '100%',
      height: 300,
      licenseKey: 'non-commercial-and-evaluation',
      stretchH: 'all',
      contextMenu: true,
      afterChange: () => {
        updateRowCount();
      }
    });
    updateRowCount();
  }
};

// 更新行数计数
const updateRowCount = () => {
  if (hotInstance) {
    const data = hotInstance.getData();
    const validRows = data.filter(row => row.some(cell => cell && cell.trim() !== ''));
    tableRowCount.value = validRows.length;
  }
};

// 添加新行
const addRow = () => {
  if (hotInstance) {
    hotInstance.alter('insert_row', hotInstance.countRows());
    updateRowCount();
  }
};

// 下载Excel模板
const downloadExcelTemplate = () => {
  const templateData = [
    ['PMID', 'PMCID', 'DOI'],
    ['12345678', '', ''],
    ['', 'PMC7654321', ''],
    ['', '', '10.1016/j.cell.2020.01.001'],
    ['', '', '']
  ];

  const ws = XLSX.utils.aoa_to_sheet(templateData);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, '文献标识符模板');

  XLSX.writeFile(wb, '文献标识符模板.xlsx');
};

// 处理Excel上传
const handleExcelUpload = (file) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });

      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      // 转换为二维数组
      const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      // 如果第一行是表头，从第二行开始加载数据
      const tableData = excelData.length > 1 &&
                       (excelData[0][0] === 'PMID' ||
                        excelData[0][0] === 'PMCID' ||
                        excelData[0][0] === 'DOI') ?
                       excelData.slice(1) : excelData;

      // 加载数据到表格
      if (hotInstance && tableData.length > 0) {
        hotInstance.loadData(tableData);
        updateRowCount();
        ElMessage.success('Excel文件导入成功');
      }
    } catch (error) {
      console.error('Excel导入错误:', error);
      ElMessage.error('Excel文件导入失败，请检查文件格式');
    }
  };
  reader.readAsArrayBuffer(file.raw);
};

// 处理优先级变更
const handlePriorityChange = (value) => {
  if (value !== 1) {
    taskForm.customPriority = null;
  } else {
    taskForm.customPriority = 50;
  }
};

// 处理文件变更
const handleFileChange = (file) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    fileContent.value = e.target.result;
    // 提取前20行作为预览
    const lines = fileContent.value.split('\n');
    filePreview.value = lines.slice(0, 20).join('\n');

    if (lines.length > 20) {
      filePreview.value += '\n...（更多内容省略）';
    }
  };

  if (file.raw) {
    reader.readAsText(file.raw);
    taskForm.file = file.raw;
  }
};

// 获取文件总行数
const getTotalLines = () => {
  if (!fileContent.value) return 0;
  return fileContent.value.split('\n').filter(line => line.trim()).length;
};

// 提交任务
const submitTask = () => {
  taskFormRef.value.validate((valid) => {
    if (valid) {
      const formData = {
        ...taskForm,
        priority: taskForm.priorityType === 1 ? taskForm.customPriority : taskForm.priorityType
      };

      // 从表格获取文献标识符数据
      const tableData = hotInstance ? hotInstance.getData() : [];
      const literatureData = tableData.filter(row => row.some(cell => cell && cell.trim() !== ''));

      if (literatureData.length === 0) {
        ElMessage.warning('请添加至少一条文献标识符数据');
        return;
      }

      // 模拟提交 - 这里应该替换为实际的API调用
      console.log('提交任务数据:', formData);
      console.log('文献标识符数据:', literatureData);

      ElMessage.success('任务发布成功！');
      resetForm();
    } else {
      return false;
    }
  });
};

// 重置表单
const resetForm = () => {
  taskFormRef.value.resetFields();
  fileContent.value = '';
  filePreview.value = '';
  taskForm.customPriority = 50;
  taskForm.priorityType = 2;
  taskForm.nodeTypes = ['1', '2', '3'];
  taskForm.isTest = false;
  taskForm.downloadMode = 'speed';
  taskForm.retryInterval = 60;
  taskForm.submitType = 'direct';
  taskForm.literatureIds = '';
  taskForm.file = null;

  // 重置表格
  if (hotInstance) {
    hotInstance.loadData(initialTableData);
    updateRowCount();
  }
};

onMounted(() => {
  console.log('任务分发模块已加载');
  initTable();
});

onUnmounted(() => {
  // 销毁表格实例
  if (hotInstance) {
    hotInstance.destroy();
    hotInstance = null;
  }
});
</script>

<style lang="scss" scoped>
.box-card {
  margin-left: 20%;
  margin-bottom: 20px;
  margin-top: 30px;
  width: 800px;
  padding: 20px;
  padding-top: 30px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.standard-width {
  width: 400px !important;
}

.standard-width-full {
  width: 100% !important;
}

.submit-tabs {
  width: 100%;
}

.literature-upload {
  width: 100%;
  margin-bottom: 15px;
}

.file-preview {
  margin-top: 15px;
  padding: 15px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;

  .preview-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-weight: bold;
  }
}

.form-item-tip {
  width: 100%;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  margin-top: 6px;
  padding-left: 2px;
}

// 优先级自定义部分的特殊样式
.priority-container {
  display: flex;
  align-items: center;
}

.custom-priority-radio {
  display: flex !important;
  align-items: center !important;
}

.custom-priority-input {
  margin-left: 8px;
  width: 120px !important;
}

// 文献表格相关样式
.literature-input-container {
  width: 100%;
}

.excel-actions {
  display: flex;
  gap: 16px;
  margin-bottom: -16px;

  .el-button {
    display: flex;
    align-items: center;

    .el-icon {
      margin-right: 4px;
    }
  }
}

.hot-container {
  width: 100%;
  margin-bottom: 12px;
  border-radius: 4px;
  overflow: hidden;
}

.table-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
}
</style>
