<template>
  <div class="app-container">
    <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
    >
      <el-form-item label="脚本名称" prop="scriptName">
        <el-input
            v-model="queryParams.scriptName"
            placeholder="请输入脚本名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
            v-model="queryParams.status"
            placeholder="脚本状态"
            clearable
            style="width: 200px"
        >
          <el-option
              v-for="dict in script_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="类型" prop="scriptType">
        <el-select
            v-model="queryParams.scriptType"
            placeholder="脚本类型"
            clearable
            style="width: 200px"
        >
          <el-option
              v-for="dict in script_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="上传时间" prop="uploadTime">
        <el-date-picker
            v-model="queryParams.uploadTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
        >搜索
        </el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            v-hasPermi="['script:script:add']"
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
        >上传脚本
        </el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
            v-hasPermi="['script:script:remove']"
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
        >删除
        </el-button
        >
      </el-col>
      <right-toolbar
          v-model:show-search="showSearch"
          @query-table="getList"
      ></right-toolbar>
    </el-row>

    <el-table
        v-loading="loading"
        :data="scriptList"
        @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column
          label="序号"
          align="center"
          prop="scriptId"
          width="80"
      />
      <el-table-column
          label="脚本名称"
          align="center"
          prop="scriptName"
          :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <a
            href="javascript:void(0)"
            class="script-name-link"
            @click="handleDownloadScript(scope.row)"
          >{{ scope.row.scriptName }}</a>
        </template>
      </el-table-column>
      <!--      <el-table-column
              label="存放路径"
              align="center"
              prop="scriptPath"
              :show-overflow-tooltip="true"
            />-->
      <el-table-column
          label="备注"
          align="center"
          prop="remark"
          :show-overflow-tooltip="true"
      />
      <el-table-column
          label="MD5值"
          align="center"
          prop="md5"
          width="280"
          :show-overflow-tooltip="true"
      />
      <el-table-column
          label="类型"
          align="center"
          prop="scriptType"
          width="100"
      >
        <template #default="scope">
          <dict-tag :options="script_type" :value="scope.row.scriptType"/>
        </template>
      </el-table-column>
      <el-table-column
          label="状态"
          align="center"
          prop="status"
          width="100"
      >
        <template #default="scope">
          <dict-tag :options="script_status" :value="scope.row.status"/>
        </template>
      </el-table-column>

      <el-table-column
          label="上传时间"
          align="center"
          prop="uploadTime"
          width="100"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
          label="操作"
          width="80"
          align="center"
          class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
              v-hasPermi="['script:script:remove']"
              link
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
          >删除
          </el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
    />

    <!-- 添加脚本对话框 -->
    <el-dialog v-model="open" :title="title" width="780px" append-to-body>
      <el-form ref="scriptRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="脚本名称" prop="scriptName">
              <el-input
                  v-model="form.scriptName"
                  placeholder="请输入脚本名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="脚本类型" prop="scriptType">
              <el-select v-model="form.scriptType" placeholder="请选择">
                <el-option
                    v-for="dict in script_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="脚本文件" prop="file">
              <el-upload
                  class="upload-demo"
                  :action="uploadUrl"
                  :on-success="handleUploadSuccess"
                  :on-error="handleUploadError"
                  :before-upload="beforeUpload"
              >
                <el-button type="primary">选择文件</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    请上传python脚本文件，必须是.pyscript结尾
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                    v-for="dict in script_status"
                    :key="dict.value"
                    :value="dict.value"
                >{{ dict.label }}
                </el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input
                  v-model="form.remark"
                  type="textarea"
                  placeholder="请输入脚本备注"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup name="Script">
import {getCurrentInstance, reactive, ref, toRefs} from 'vue';

const {proxy} = getCurrentInstance();
const {script_status, script_type} = proxy.useDict(
    'script_status',
    'script_type'
);

const scriptList = ref([]);
const open = ref(false);
const monitorOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const uploadUrl = ref(import.meta.env.VITE_APP_BASE_API + '/script/upload');
const historyList = ref([]);
const historyTotal = ref(0);
const currentScriptId = ref(null);

// 模拟数据
const mockScriptList = [
  {
    scriptId: 1,
    scriptName: 'sit_01_pubmed_is.pyscript',
    scriptPath: '/scripts/xxxx/pubmed.py',
    md5: 'b231a5fbd76005f3bc607b622f2f66de',
    remark: 'PubMed网站文献爬虫',
    status: '0', // 启用
    scriptType: '1', // 批次
    uploadTime: new Date('2023-08-15')
  },
  {
    scriptId: 2,
    scriptName: 'sit_04_science.pyscript',
    scriptPath: '/scripts/xxxx/science.js',
    md5: '1890af4b034a6834893506cc6f6107a3',
    remark: 'Science期刊网站内容抓取',
    status: '1', // 异常
    scriptType: '2', // 源刊
    uploadTime: new Date('2023-09-20')
  },
  {
    scriptId: 3,
    scriptName: 'pmc.pyscript',
    scriptPath: '/scripts/xxxx/tsinghua.py',
    md5: 'aa854743d6a94464356d2e851c4a420f',
    remark: 'PMC内容抓取',
    status: '2', // 停用
    scriptType: '3', // 高校
    uploadTime: new Date('2023-10-25')
  },
  {
    scriptId: 4,
    scriptName: 'natrue.pyscript',
    scriptPath: '/scripts/xxxx/nature.py',
    md5: '20d644386fcf2bb52954a20dd18eacac',
    remark: 'Nature期刊网站内容抓取',
    status: '0', // 启用
    scriptType: '2', // 源刊
    uploadTime: new Date('2023-11-05')
  },
  {
    scriptId: 5,
    scriptName: 'beijin.pyscript',
    scriptPath: '/scripts/xxxx/pku.py',
    md5: 'e9aa00a103cab0622585000ddbe3eefa',
    remark: '北京大学学报内容抓取',
    status: '0', // 启用
    scriptType: '3', // 高校
    uploadTime: new Date('2023-12-10')
  }
];

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    scriptName: undefined,
    scriptType: undefined,
    status: undefined,
    uploadTime: undefined,
  },
  monitorForm: {
    scriptId: undefined,
    testId: undefined,
    cronExpression: undefined
  },
  historyQuery: {
    pageNum: 1,
    pageSize: 5,
    scriptId: undefined
  },
  rules: {
    scriptName: [
      {required: true, message: '脚本名称不能为空', trigger: 'blur'},
    ],
    scriptType: [
      {required: true, message: '脚本类型不能为空', trigger: 'change'},
    ],
    file: [
      {required: true, message: '请上传脚本文件', trigger: 'change'},
    ],
  },
  monitorRules: {
    testId: [
      {required: true, message: '测试文献ID不能为空', trigger: 'blur'},
    ],
    cronExpression: [
      {required: true, message: 'Cron表达式不能为空', trigger: 'blur'},
    ],
  }
});

const {queryParams, form, rules, monitorForm, monitorRules, historyQuery} = toRefs(data);

/** 查询脚本列表 */
function getList() {
  loading.value = true;
  setTimeout(() => {
    // 模拟过滤逻辑
    let filteredList = [...mockScriptList];

    if (queryParams.value.scriptName) {
      filteredList = filteredList.filter(item =>
          item.scriptName.includes(queryParams.value.scriptName)
      );
    }

    if (queryParams.value.status) {
      filteredList = filteredList.filter(item =>
          item.status === queryParams.value.status
      );
    }

    if (queryParams.value.scriptType) {
      filteredList = filteredList.filter(item =>
          item.scriptType === queryParams.value.scriptType
      );
    }

    if (queryParams.value.uploadTime && queryParams.value.uploadTime.length === 2) {
      const startDate = new Date(queryParams.value.uploadTime[0]);
      const endDate = new Date(queryParams.value.uploadTime[1]);
      filteredList = filteredList.filter(item => {
        const itemDate = new Date(item.uploadTime);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    scriptList.value = filteredList;
    total.value = filteredList.length;
    loading.value = false;
  }, 300);
}

/** 获取历史记录 */
function getHistoryList() {
  setTimeout(() => {
    historyList.value = mockHistoryList;
    historyTotal.value = mockHistoryList.length;
  }, 300);
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    scriptId: undefined,
    scriptName: undefined,
    scriptType: undefined,
    scriptPath: undefined,
    md5: undefined,
    remark: undefined,
    status: '0',
  };
  proxy.resetForm('scriptRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.scriptId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '上传脚本';
}

/** 删除按钮操作 */
function handleDelete(row) {
  const scriptIds = row.scriptId || ids.value;
  proxy.$modal
      .confirm('是否确认删除脚本编号为"' + scriptIds + '"的数据项？')
      .then(function () {
        // 模拟删除
        setTimeout(() => {
          const idList = Array.isArray(scriptIds) ? scriptIds : [scriptIds];
          scriptList.value = scriptList.value.filter(item => !idList.includes(item.scriptId));
          proxy.$modal.msgSuccess('删除成功');
        }, 300);
      })
      .catch(() => {
      });
}

/** 文件上传前的处理 */
function beforeUpload(file) {
  // 可以在这里添加文件类型和大小限制
  return true;
}

/** 上传成功处理 */
function handleUploadSuccess(response, file) {
  // 模拟上传成功
  form.value.scriptPath = '/scripts/' + file.name;
  form.value.md5 = Math.random().toString(16).substring(2, 10) + Math.random().toString(16).substring(2, 10);
  proxy.$modal.msgSuccess('文件上传成功');
}

/** 上传失败处理 */
function handleUploadError() {
  proxy.$modal.msgError('文件上传失败');
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['scriptRef'].validate(valid => {
    if (valid) {
      if (form.value.scriptId != undefined) {
        // 模拟修改
        setTimeout(() => {
          const index = scriptList.value.findIndex(item => item.scriptId === form.value.scriptId);
          if (index !== -1) {
            scriptList.value[index] = {...form.value};
          }
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        }, 300);
      } else {
        // 模拟新增
        setTimeout(() => {
          const newId = scriptList.value.length > 0
              ? Math.max(...scriptList.value.map(item => item.scriptId)) + 1
              : 1;
          const newScript = {
            ...form.value,
            scriptId: newId,
            uploadTime: new Date()
          };
          mockScriptList.push(newScript);
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        }, 300);
      }
    }
  });
}

/** 下载脚本 */
function handleDownloadScript(row) {
  // 实际项目中应该调用后端API进行下载
  // 这里是模拟下载
  proxy.$modal.msgSuccess(`正在下载脚本: ${row.scriptName}`);
  // 可以使用proxy.download方法（如果在项目中已定义）或其他下载方法
  // 模拟下载，实际项目可能使用类似：proxy.download('/script/download', { scriptId: row.scriptId }, row.scriptName)
}

getList();
</script>

<style lang="scss">
.monitor-history {
  margin-top: 20px;

  .el-pagination {
    float: right;
  }
}

.script-name-link {
  color: #409EFF;
  text-decoration: none;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}
</style>
