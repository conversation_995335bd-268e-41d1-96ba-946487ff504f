<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
      <el-form-item label="文献标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入文献标题"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="纠错类型" prop="defectType">
        <el-select
          v-model="queryParams.defectType"
          placeholder="请选择纠错类型"
          clearable
          style="width: 200px"
        >
          <el-option
              v-for="dict in article_defect_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="纠错人" prop="creatorName">
        <el-input
            v-model="queryParams.creatorName"
          placeholder="请输入纠错人"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          style="width: 200px"
        >
          <el-option
              v-for="dict in audit_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="correctionList">
      <el-table-column label="文献标题" align="center" prop="title" min-width="200" :show-overflow-tooltip="true">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="viewArticleDetail(scope.row)">{{
              scope.row.title
            }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="纠错类型" align="center" prop="defectType" />
      <el-table-column label="纠错人" align="center" prop="creatorName"/>
      <el-table-column label="提交时间" align="center" prop="createTime" width="160"/>
      <el-table-column label="处理时间" align="center" prop="auditTime" width="160"/>
      <el-table-column label="纠错内容" align="center" prop="content" min-width="200" :show-overflow-tooltip="true" />
      <el-table-column label="处理人" align="center" prop="auditorName"/>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="audit_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="left" width="220" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="primary"
            link
            icon="View"
            @click="handleView(scope.row)"
          >查看</el-button>
          <el-button
              v-if="scope.row.status === 0"
            type="success"
            link
            icon="Check"
            @click="handleAccept(scope.row)"
          >接受</el-button>
          <el-button
              v-if="scope.row.status === 0"
            type="danger"
            link
            icon="Close"
            @click="handleReject(scope.row)"
          >驳回</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看纠错详情对话框 -->
    <el-dialog title="纠错详情" v-model="detailDialogVisible" width="700px" append-to-body>
      <div v-if="currentDetail">
        <el-descriptions border>
          <el-descriptions-item label="文献标题">
            <el-link type="primary" :underline="false" @click="viewArticleDetail(currentDetail)">{{
                currentDetail.title
              }}
            </el-link>
          </el-descriptions-item>
          <el-descriptions-item label="纠错类型">{{ currentDetail.defectType }}</el-descriptions-item>
          <el-descriptions-item label="纠错人">{{ currentDetail.creatorName }}</el-descriptions-item>
          <el-descriptions-item label="提交时间">{{ currentDetail.createTime }}</el-descriptions-item>
          <el-descriptions-item label="处理人">{{ currentDetail.auditorName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="处理时间">{{ currentDetail.auditTime || '-' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <dict-tag :options="audit_status" :value="currentDetail.status"/>
          </el-descriptions-item>
          <el-descriptions-item label="纠错内容" :span="2">
            {{ currentDetail.content }}
          </el-descriptions-item>
          <el-descriptions-item v-if="currentDetail.reason" label="驳回原因" :span="2">
            {{ currentDetail.reason }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer v-if="currentDetail && currentDetail.status === 0">
        <div class="dialog-footer">
          <el-button type="success" @click="handleAccept(currentDetail)">接受</el-button>
          <el-button type="danger" @click="handleReject(currentDetail)">驳回</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 驳回原因对话框 -->
    <el-dialog title="驳回原因" v-model="rejectDialogVisible" width="500px" append-to-body>
      <el-form :model="rejectForm" label-width="80px">
        <el-form-item label="驳回原因" prop="reason">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            placeholder="请输入驳回原因"
            :rows="4"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmReject">确 定</el-button>
          <el-button @click="rejectDialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue';
import Pagination from '@/components/Pagination';
import RightToolbar from '@/components/RightToolbar';
import {acceptCorrection, listCorrections, rejectCorrection} from '@/api/article/correction';

const {proxy} = getCurrentInstance();

// 字典数据
const {audit_status, article_defect_type} = proxy.useDict('audit_status', 'article_defect_type');

// 显示搜索条件
const showSearch = ref(true);
// 加载状态
const loading = ref(false);
// 总条数
const total = ref(0);
// 提交时间范围
const createTimeRange = ref([]);
// 驳回对话框
const rejectDialogVisible = ref(false);
// 详情对话框
const detailDialogVisible = ref(false);
// 当前详情数据
const currentDetail = ref(null);
// 驳回表单
const rejectForm = ref({
  reason: ''
});
// 纠错列表
const correctionList = ref([]);

// 查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: undefined,
    defectType: undefined,
    status: undefined,
    creatorName: undefined
  }
});

const { queryParams } = toRefs(data);

/** 查询纠错列表 */
function getList() {
  loading.value = true;
  listCorrections(proxy.addDateRange(queryParams.value, createTimeRange.value)).then(response => {
    correctionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  createTimeRange.value = [];
  proxy.resetForm("queryRef");
  queryParams.value.pageNum = 1;
  handleQuery();
}

/** 查看详情操作 */
function handleView(row) {
  detailDialogVisible.value = true;
  currentDetail.value = row;
}

/** 接受操作 */
function handleAccept(row) {
  proxy.$modal.confirm(`确认接受该纠错信息吗？接受后将发送邮件通知用户。`, '提示').then(() => {
    acceptCorrection(row.id).then(() => {
      proxy.$modal.msgSuccess('接受成功');
      getList();
    });
  }).catch(() => {
  }).finally(() => {
    detailDialogVisible.value = false;
    rejectDialogVisible.value = false;
  });
}

/** 驳回操作 */
function handleReject(row) {
  rejectDialogVisible.value = true;
  currentDetail.value = row;
  rejectForm.value.reason = '';
}

/** 确认驳回 */
function confirmReject() {
  if (!rejectForm.value.reason) {
    proxy.$modal.msgWarning('请输入驳回原因');
    return;
  }

  rejectCorrection(currentDetail.value.id, rejectForm.value.reason).then(() => {
    proxy.$modal.msgSuccess('驳回成功');
    getList();
  }).finally(() => {
    detailDialogVisible.value = false;
    rejectDialogVisible.value = false;
  });
}

/** 查看文献详情 */
function viewArticleDetail(row) {
  // todo 转跳到文献详情页面
}

// 页面加载时获取列表数据
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
</style>
