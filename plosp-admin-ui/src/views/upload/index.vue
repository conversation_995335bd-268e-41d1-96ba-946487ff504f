<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
      <el-form-item label="文献标题" prop="title">
        <el-input
            v-model="queryParams.title"
            placeholder="请输入文献标题"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="上传人" prop="creatorName">
        <el-input
            v-model="queryParams.creatorName"
          placeholder="请输入上传人"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="上传时间" style="width: 308px">
        <el-date-picker
            v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="审核人" prop="auditorName">
        <el-input
            v-model="queryParams.auditorName"
          placeholder="请输入审核人"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 200px"
        >
          <el-option
              v-for="dict in audit_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="uploadList">
      <el-table-column label="文献标题" align="center" prop="title" min-width="200">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="handleViewDetail(scope.row)">{{
              scope.row.title
            }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="全文" align="center" min-width="200">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="handleDownloadPdf(scope.row)">
            {{ scope.row.attachmentName || '无PDF' }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="上传人" align="center" prop="creatorName"/>
      <el-table-column label="上传时间" align="center" prop="createTime" width="180"/>
      <el-table-column label="审核人" align="center" prop="auditorName"/>
      <el-table-column label="审核时间" align="center" prop="auditTime" width="180"/>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="audit_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
              v-if="scope.row.status === 0"
            type="success"
            link
            icon="Check"
            @click="handleAccept(scope.row)"
          >接受</el-button>
          <el-button
              v-if="scope.row.status === 0"
            type="danger"
            link
            icon="Close"
            @click="handleReject(scope.row)"
          >驳回</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 驳回原因对话框 -->
    <el-dialog title="驳回原因" v-model="rejectDialogVisible" width="500px" append-to-body>
      <el-form :model="rejectForm" label-width="80px">
        <el-form-item label="驳回原因" prop="reason">
          <el-input
            v-model="rejectForm.reason"
            type="textarea"
            placeholder="请输入驳回原因"
            :rows="4"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmReject">确 定</el-button>
          <el-button @click="rejectDialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文献详情对话框 -->
    <el-dialog title="文献详情" v-model="detailDialogVisible" width="800px" append-to-body>
      <div class="literature-detail" v-if="currentDetail">
        <!-- 标题 -->
        <div class="detail-section">
          <div class="info-label">标题</div>
          <div class="abstract-content">{{ currentDetail.title || '-' }}</div>
        </div>

        <!-- 标识符区域 -->
        <div class="detail-section">
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">文献编号</div>
              <div class="info-value">{{ currentDetail.docId || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">附件ID</div>
              <div class="info-value">{{ currentDetail.attachmentId || '-' }}</div>
            </div>
          </div>
        </div>

        <!-- 基本信息 -->
        <div class="detail-section">
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">作者</div>
              <div class="info-value">{{ currentDetail.authors || '-' }}</div>
            </div>
            <div class="info-item full-width">
              <div class="info-label">单位</div>
              <div class="info-value">{{ currentDetail.affiliation || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">期刊</div>
              <div class="info-value">{{ currentDetail.journal || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">发表年份</div>
              <div class="info-value">{{ currentDetail.year || '-' }}</div>
            </div>
          </div>
        </div>

        <!-- 摘要 -->
        <div class="detail-section">
          <div class="info-label">摘要</div>
          <div class="abstract-content">{{ currentDetail.abstract || '暂无摘要' }}</div>
        </div>

        <!-- 上传信息 -->
        <div class="detail-section">
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">上传人</div>
              <div class="info-value">{{ currentDetail.creatorName || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">上传时间</div>
              <div class="info-value">{{ currentDetail.createTime || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">审核人</div>
              <div class="info-value">{{ currentDetail.auditorName || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">审核时间</div>
              <div class="info-value">{{ currentDetail.auditTime || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">状态</div>
              <div class="info-value">
                <dict-tag :options="audit_status" :value="currentDetail.status"/>
              </div>
            </div>
            <div class="info-item" v-if="currentDetail.reason">
              <div class="info-label">驳回原因</div>
              <div class="info-value">{{ currentDetail.reason }}</div>
            </div>
          </div>
        </div>

        <!-- PDF下载 -->
        <div class="detail-section">
          <el-button type="primary" icon="Document" @click="handleDownloadPdf(currentDetail)">下载PDF</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import {getCurrentInstance, onMounted, reactive, ref, toRefs} from 'vue';
import Pagination from '@/components/Pagination';
import RightToolbar from '@/components/RightToolbar';
import {acceptUpload, listUploads, rejectUpload} from '@/api/article/upload';

const {proxy} = getCurrentInstance();

// 字典数据
const {audit_status} = proxy.useDict('audit_status');

// 显示搜索条件
const showSearch = ref(true);
// 加载状态
const loading = ref(false);
// 总条数
const total = ref(0);
// 日期范围
const dateRange = ref([]);
// 驳回对话框
const rejectDialogVisible = ref(false);
// 详情对话框
const detailDialogVisible = ref(false);
// 当前详情数据
const currentDetail = ref(null);
// 当前要驳回的数据
const currentRejectItem = ref(null);
// 驳回表单
const rejectForm = ref({
  reason: ''
});
// 上传列表
const uploadList = ref([]);

// 查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    status: undefined,
    creatorName: undefined,
    auditorName: undefined,
    title: undefined
  }
});

const { queryParams } = toRefs(data);

/** 查询上传列表 */
function getList() {
  loading.value = true;
  listUploads(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    uploadList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  queryParams.value.pageNum = 1;
  handleQuery();
}

/** 接受操作 */
function handleAccept(row) {
  proxy.$modal.confirm(`确认接受文献 ${row.docId} 吗？`, "提示").then(() => {
    acceptUpload(row.id).then(() => {
      proxy.$modal.msgSuccess('接受成功');
      getList();
    });
  }).catch(() => {});
}

/** 驳回操作 */
function handleReject(row) {
  rejectDialogVisible.value = true;
  currentRejectItem.value = row;
  rejectForm.value.reason = '';
}

/** 确认驳回 */
function confirmReject() {
  if (!rejectForm.value.reason) {
    proxy.$modal.msgWarning('请输入驳回原因');
    return;
  }

  rejectUpload(currentRejectItem.value.id, rejectForm.value.reason).then(() => {
    rejectDialogVisible.value = false;
    proxy.$modal.msgSuccess('驳回成功');
    getList();
  });
}

/** 查看详情操作 */
function handleViewDetail(row) {
  detailDialogVisible.value = true;
  currentDetail.value = row;
}

/** 下载PDF操作 */
function handleDownloadPdf(row) {
  window.open(`${import.meta.env.VITE_APP_BASE_API}/article/file/download/${row.attachmentId}`, '_blank');
}

/** 跳转到文献页面 */
function handleJumpToLiterature(row) {

}

// 页面加载时获取列表数据
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 10px;
  padding-bottom: 40px;
}

.mb8 {
  margin-bottom: 8px;
}

.literature-detail {
  padding: 0 10px;

  .detail-section {
    margin-bottom: 24px;

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;

      .info-item {
        &.full-width {
          grid-column: span 2;
        }

        .info-label {
          font-size: 14px;
          color: #909399;
          margin-bottom: 4px;
        }

        .info-value {
          font-size: 14px;
          color: #606266;
          word-break: break-word;
        }
      }
    }

    .info-label {
      font-size: 14px;
      color: #909399;
      margin-bottom: 8px;
      font-weight: bold;
    }

    .abstract-content {
      font-size: 14px;
      color: #606266;
      line-height: 1.6;
      text-align: justify;
    }
  }
}
</style>
