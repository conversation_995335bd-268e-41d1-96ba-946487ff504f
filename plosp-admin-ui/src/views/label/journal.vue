<template>
  <div class="app-container">
    <div class="back-container">
      <el-button
          type="text"
          icon="ArrowLeft"
          @click="goBack"
      >返回</el-button>
      <div class="page-title">配置源刊节点标签: {{ labelName }}</div>
    </div>

    <el-tabs v-model="activeTab" type="card">
      <!-- 已分配给当前标签的期刊 -->
      <el-tab-pane label="已分配期刊" name="assigned">
        <div class="search-container">
          <el-form :inline="true" :model="assignedQuery" class="search-form">
            <el-form-item label="期刊名称">
              <el-input v-model="assignedQuery.journalName" placeholder="请输入期刊名称" clearable />
            </el-form-item>
            <el-form-item label="ISSN Print">
              <el-input v-model="assignedQuery.issnPrint" placeholder="请输入ISSN Print" clearable />
            </el-form-item>
            <el-form-item label="ISSN Electronic">
              <el-input v-model="assignedQuery.issnElectronic" placeholder="请输入ISSN Electronic" clearable />
            </el-form-item>
            <el-form-item label="出版社名称">
              <el-input v-model="assignedQuery.publisher" placeholder="请输入出版社名称" clearable />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="searchAssigned">搜索</el-button>
              <el-button icon="Refresh" @click="resetAssignedSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table
          :data="assignedJournals"
          border
          style="width: 100%"
          @selection-change="handleAssignedSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="期刊名称" prop="journalName" />
          <el-table-column label="ISSN Print" prop="issnPrint" width="120" />
          <el-table-column label="ISSN Electronic" prop="issnElectronic" width="140" />
          <el-table-column label="出版社名称" prop="publisher" />
          <el-table-column label="脚本名称" prop="scriptName" show-overflow-tooltip />
          <el-table-column label="操作" width="180" align="center">
            <template #default="scope">
              <el-button
                type="primary"
                link
                icon="Edit"
                @click="editJournalScript(scope.row)"
              >更换脚本</el-button>
              <el-button
                type="danger"
                link
                icon="Delete"
                @click="removeJournalAssignment(scope.row)"
              >移除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="table-footer">
          <el-button
            type="danger"
            :disabled="assignedSelected.length === 0"
            @click="batchRemoveAssignments"
          >批量移除</el-button>

          <pagination
            v-show="assignedTotal > 0"
            :total="assignedTotal"
            v-model:page="assignedQuery.pageNum"
            v-model:limit="assignedQuery.pageSize"
            @pagination="getAssignedList"
          />
        </div>
      </el-tab-pane>

      <!-- 分配期刊 -->
      <el-tab-pane label="分配期刊" name="assign">
        <div class="search-container">
          <el-form :inline="true" :model="journalQuery" class="search-form">

            <el-form-item label="期刊名称">
              <el-input v-model="journalQuery.journalName" placeholder="请输入期刊名称" clearable />
            </el-form-item>
            <el-form-item label="ISSN Print">
              <el-input v-model="journalQuery.issnPrint" placeholder="请输入ISSN Print" clearable />
            </el-form-item>
            <el-form-item label="ISSN Electronic">
              <el-input v-model="journalQuery.issnElectronic" placeholder="请输入ISSN Electronic" clearable />
            </el-form-item>
            <el-form-item label="出版社名称">
              <el-input v-model="journalQuery.publisher" placeholder="请输入出版社名称" clearable />
            </el-form-item>
            <el-form-item label="是否有脚本">
              <el-select v-model="journalQuery.hasScript" placeholder="是否有脚本" clearable style="width: 120px">
                <el-option label="是" value="1" />
                <el-option label="否" value="0" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="searchJournals">搜索</el-button>
              <el-button icon="Refresh" @click="resetJournalSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table
          :data="journalList"
          border
          style="width: 100%"
          @selection-change="handleJournalSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="期刊名称" prop="journalName" />
          <el-table-column label="ISSN Print" prop="issnPrint" width="120" />
          <el-table-column label="ISSN Electronic" prop="issnElectronic" width="140" />
          <el-table-column label="出版社名称" prop="publisher" />
          <el-table-column label="期刊脚本" prop="scriptName" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.scriptId">{{ scope.row.scriptName }}</span>
              <el-tag v-else type="info">无脚本</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="400" align="left">
            <template #default="scope">
              <el-button
                  type="success"
                  link
                  icon="Plus"
                  @click="addToLabel(scope.row)"
                  :disabled="isJournalAssigned(scope.row.journalId)"
              >应用到标签</el-button>
              <el-button
                type="primary"
                link
                icon="Edit"
                @click="assignScript(scope.row)"
              >分配脚本</el-button>
              <el-button
                v-if="scope.row.scriptId"
                type="danger"
                link
                icon="Delete"
                @click="removeScript(scope.row)"
              >移除脚本</el-button>

            </template>
          </el-table-column>
        </el-table>

        <div class="table-footer">
          <el-button
            type="primary"
            :disabled="journalSelected.length === 0"
            @click="batchAssignToLabel"
          >批量应用到当前标签</el-button>

          <pagination
            v-show="journalTotal > 0"
            :total="journalTotal"
            v-model:page="journalQuery.pageNum"
            v-model:limit="journalQuery.pageSize"
            @pagination="getJournalList"
          />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 分配脚本对话框 -->
    <el-dialog v-model="scriptDialogVisible" title="分配源刊脚本" width="800px" append-to-body>
      <div class="dialog-content">
        <div class="journal-info">
          <p><strong>期刊名称：</strong>{{ currentJournal.journalName }}</p>
          <p><strong>出版社：</strong>{{ currentJournal.publisher }}</p>
        </div>

        <el-form :inline="true" :model="scriptQuery" class="script-search-form">
          <el-form-item label="脚本名称">
            <el-input v-model="scriptQuery.scriptName" placeholder="请输入脚本名称" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="searchScripts">搜索</el-button>
          </el-form-item>
        </el-form>

        <el-table
          :data="scriptList"
          border
          style="width: 100%"
          height="300px"
        >
          <el-table-column type="index" label="序号" width="50" />
          <el-table-column label="脚本名称" prop="scriptName" />
          <el-table-column label="MD5值" prop="md5" show-overflow-tooltip />
          <el-table-column label="备注" prop="remark" show-overflow-tooltip />
          <el-table-column label="操作" width="100" align="center">
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="confirmAssignScript(scope.row)"
              >选择</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="scriptDialogVisible = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

// 标签信息
const labelId = ref(route.query.id);
const labelName = ref(route.query.name);

// 标签页控制
const activeTab = ref('assigned');

// 已分配期刊
const assignedJournals = ref([]);
const assignedTotal = ref(0);
const assignedSelected = ref([]);
const assignedQuery = reactive({
  pageNum: 1,
  pageSize: 20,
  publisher: '',
  journalName: '',
  issnPrint: '',
  issnElectronic: '',
  labelId: labelId.value
});

// 期刊列表
const journalList = ref([]);
const journalTotal = ref(0);
const journalSelected = ref([]);
const journalQuery = reactive({
  pageNum: 1,
  pageSize: 20,
  publisher: '',
  journalName: '',
  issnPrint: '',
  issnElectronic: '',
  hasScript: ''
});

// 脚本列表
const scriptList = ref([]);
const scriptQuery = reactive({
  scriptName: '',
  scriptType: '2' // 源刊类型
});

// 当前选中的期刊
const currentJournal = ref({});
const scriptDialogVisible = ref(false);

// 模拟源刊脚本数据
const mockJournalScripts = [
  {
    scriptId: 2,
    scriptName: 'sit_04_science.pyscript',
    md5: '1890af4b034a6834893506cc6f6107a3',
    remark: 'Science期刊网站内容抓取',
    status: '0',
    scriptType: '2' // 源刊
  },
  {
    scriptId: 4,
    scriptName: 'natrue.pyscript',
    md5: '20d644386fcf2bb52954a20dd18eacac',
    remark: 'Nature期刊网站内容抓取',
    status: '0',
    scriptType: '2' // 源刊
  },
  {
    scriptId: 8,
    scriptName: 'cell.pyscript',
    md5: '5a2d644386fcf2bb52954a20dd18ea12',
    remark: 'Cell期刊网站内容抓取',
    status: '0',
    scriptType: '2' // 源刊
  },
  {
    scriptId: 9,
    scriptName: 'nejm.pyscript',
    md5: '6b3d644386fcf2bb52954a20dd18ea34',
    remark: 'NEJM期刊网站内容抓取',
    status: '0',
    scriptType: '2' // 源刊
  }
];

// 模拟期刊数据
const mockJournals = [
  {
    journalId: 1,
    journalName: 'Science',
    issnPrint: '0036-8075',
    issnElectronic: '1095-9203',
    publisher: 'American Association for the Advancement of Science',
    scriptId: 2,
    scriptName: 'sit_04_science.pyscript'
  },
  {
    journalId: 2,
    journalName: 'Nature',
    issnPrint: '0028-0836',
    issnElectronic: '1476-4687',
    publisher: 'Nature Publishing Group',
    scriptId: 4,
    scriptName: 'natrue.pyscript'
  },
  {
    journalId: 3,
    journalName: 'Cell',
    issnPrint: '0092-8674',
    issnElectronic: '1097-4172',
    publisher: 'Elsevier',
    scriptId: 8,
    scriptName: 'cell.pyscript'
  },
  {
    journalId: 4,
    journalName: 'New England Journal of Medicine',
    issnPrint: '0028-4793',
    issnElectronic: '1533-4406',
    publisher: 'Massachusetts Medical Society',
    scriptId: 9,
    scriptName: 'nejm.pyscript'
  },
  {
    journalId: 5,
    journalName: 'JAMA',
    issnPrint: '0098-7484',
    issnElectronic: '1538-3598',
    publisher: 'American Medical Association',
    scriptId: null,
    scriptName: null
  },
  {
    journalId: 6,
    journalName: 'The Lancet',
    issnPrint: '0140-6736',
    issnElectronic: '1474-547X',
    publisher: 'Elsevier',
    scriptId: null,
    scriptName: null
  }
];

// 模拟标签已分配期刊
const mockLabelJournals = {
  2: [1, 2], // 标签ID 2 已分配 journalId 1和2
  5: [3, 4]  // 标签ID 5 已分配 journalId 3和4
};

// 获取已分配期刊
const getAssignedList = () => {
  setTimeout(() => {
    const assignedIds = mockLabelJournals[labelId.value] || [];
    let filtered = mockJournals.filter(journal => assignedIds.includes(journal.journalId));

    // 应用过滤条件
    if (assignedQuery.publisher) {
      filtered = filtered.filter(item =>
        item.publisher.toLowerCase().includes(assignedQuery.publisher.toLowerCase())
      );
    }

    if (assignedQuery.journalName) {
      filtered = filtered.filter(item =>
        item.journalName.toLowerCase().includes(assignedQuery.journalName.toLowerCase())
      );
    }

    if (assignedQuery.issnPrint) {
      filtered = filtered.filter(item =>
        item.issnPrint.includes(assignedQuery.issnPrint)
      );
    }

    if (assignedQuery.issnElectronic) {
      filtered = filtered.filter(item =>
        item.issnElectronic.includes(assignedQuery.issnElectronic)
      );
    }

    assignedTotal.value = filtered.length;
    assignedJournals.value = filtered;
  }, 300);
};

// 获取期刊列表
const getJournalList = () => {
  setTimeout(() => {
    let filtered = [...mockJournals];

    // 应用过滤条件
    if (journalQuery.publisher) {
      filtered = filtered.filter(item =>
        item.publisher.toLowerCase().includes(journalQuery.publisher.toLowerCase())
      );
    }

    if (journalQuery.journalName) {
      filtered = filtered.filter(item =>
        item.journalName.toLowerCase().includes(journalQuery.journalName.toLowerCase())
      );
    }

    if (journalQuery.issnPrint) {
      filtered = filtered.filter(item =>
        item.issnPrint.includes(journalQuery.issnPrint)
      );
    }

    if (journalQuery.issnElectronic) {
      filtered = filtered.filter(item =>
        item.issnElectronic.includes(journalQuery.issnElectronic)
      );
    }

    if (journalQuery.hasScript === '1') {
      filtered = filtered.filter(item => item.scriptId != null);
    } else if (journalQuery.hasScript === '0') {
      filtered = filtered.filter(item => item.scriptId == null);
    }

    journalTotal.value = filtered.length;
    journalList.value = filtered;
  }, 300);
};

// 获取脚本列表
const getScriptList = () => {
  setTimeout(() => {
    let filtered = [...mockJournalScripts];

    if (scriptQuery.scriptName) {
      filtered = filtered.filter(item =>
        item.scriptName.toLowerCase().includes(scriptQuery.scriptName.toLowerCase())
      );
    }

    scriptList.value = filtered;
  }, 300);
};

// 搜索已分配期刊
const searchAssigned = () => {
  assignedQuery.pageNum = 1;
  getAssignedList();
};

// 重置已分配期刊搜索
const resetAssignedSearch = () => {
  assignedQuery.publisher = '';
  assignedQuery.journalName = '';
  assignedQuery.issnPrint = '';
  assignedQuery.issnElectronic = '';
  searchAssigned();
};

// 搜索期刊
const searchJournals = () => {
  journalQuery.pageNum = 1;
  getJournalList();
};

// 重置期刊搜索
const resetJournalSearch = () => {
  journalQuery.publisher = '';
  journalQuery.journalName = '';
  journalQuery.issnPrint = '';
  journalQuery.issnElectronic = '';
  journalQuery.hasScript = '';
  searchJournals();
};

// 搜索脚本
const searchScripts = () => {
  getScriptList();
};

// 已分配期刊多选
const handleAssignedSelectionChange = (selection) => {
  assignedSelected.value = selection;
};

// 期刊多选
const handleJournalSelectionChange = (selection) => {
  journalSelected.value = selection;
};

// 打开分配脚本对话框
const assignScript = (journal) => {
  currentJournal.value = journal;
  scriptDialogVisible.value = true;
  scriptQuery.scriptName = '';
  getScriptList();
};

// 更换期刊脚本
const editJournalScript = (journal) => {
  assignScript(journal);
};

// 确认分配脚本
const confirmAssignScript = (script) => {
  // 更新期刊的脚本
  const journalIndex = mockJournals.findIndex(item => item.journalId === currentJournal.value.journalId);
  if (journalIndex !== -1) {
    mockJournals[journalIndex].scriptId = script.scriptId;
    mockJournals[journalIndex].scriptName = script.scriptName;

    // 如果在已分配列表中，也更新
    const assignedIndex = assignedJournals.value.findIndex(item => item.journalId === currentJournal.value.journalId);
    if (assignedIndex !== -1) {
      assignedJournals.value[assignedIndex].scriptId = script.scriptId;
      assignedJournals.value[assignedIndex].scriptName = script.scriptName;
    }
  }

  proxy.$modal.msgSuccess('脚本分配成功');
  scriptDialogVisible.value = false;
};

// 删除期刊脚本
const removeScript = (journal) => {
  proxy.$modal.confirm(`确定要删除期刊 "${journal.journalName}" 的脚本吗？`).then(() => {
    const journalIndex = mockJournals.findIndex(item => item.journalId === journal.journalId);
    if (journalIndex !== -1) {
      mockJournals[journalIndex].scriptId = null;
      mockJournals[journalIndex].scriptName = null;

      // 如果在已分配列表中，也更新
      const assignedIndex = assignedJournals.value.findIndex(item => item.journalId === journal.journalId);
      if (assignedIndex !== -1) {
        assignedJournals.value[assignedIndex].scriptId = null;
        assignedJournals.value[assignedIndex].scriptName = null;
      }
    }

    proxy.$modal.msgSuccess('脚本删除成功');
    getJournalList();
  }).catch(() => {});
};

// 检查期刊是否已分配给当前标签
const isJournalAssigned = (journalId) => {
  const assignedIds = mockLabelJournals[labelId.value] || [];
  return assignedIds.includes(journalId);
};

// 添加期刊到标签
const addToLabel = (journal) => {
  if (isJournalAssigned(journal.journalId)) {
    return;
  }

  const assignedIds = mockLabelJournals[labelId.value] || [];
  assignedIds.push(journal.journalId);
  mockLabelJournals[labelId.value] = assignedIds;

  proxy.$modal.msgSuccess(`已将期刊 "${journal.journalName}" 添加到标签`);
  getAssignedList();
};

// 批量添加期刊到标签
const batchAssignToLabel = () => {
  if (journalSelected.value.length === 0) {
    return;
  }

  const assignedIds = mockLabelJournals[labelId.value] || [];
  journalSelected.value.forEach(journal => {
    if (!assignedIds.includes(journal.journalId)) {
      assignedIds.push(journal.journalId);
    }
  });

  mockLabelJournals[labelId.value] = assignedIds;
  proxy.$modal.msgSuccess(`已批量添加 ${journalSelected.value.length} 个期刊到标签`);
  getAssignedList();
};

// 移除期刊与标签的关联
const removeJournalAssignment = (journal) => {
  proxy.$modal.confirm(`确定要从标签中移除期刊 "${journal.journalName}" 吗？`).then(() => {
    const assignedIds = mockLabelJournals[labelId.value] || [];
    const index = assignedIds.indexOf(journal.journalId);
    if (index !== -1) {
      assignedIds.splice(index, 1);
      mockLabelJournals[labelId.value] = assignedIds;
    }

    proxy.$modal.msgSuccess('已移除期刊');
    getAssignedList();
  }).catch(() => {});
};

// 批量移除期刊与标签的关联
const batchRemoveAssignments = () => {
  if (assignedSelected.value.length === 0) {
    return;
  }

  proxy.$modal.confirm(`确定要移除选中的 ${assignedSelected.value.length} 个期刊吗？`).then(() => {
    const assignedIds = mockLabelJournals[labelId.value] || [];
    assignedSelected.value.forEach(journal => {
      const index = assignedIds.indexOf(journal.journalId);
      if (index !== -1) {
        assignedIds.splice(index, 1);
      }
    });

    mockLabelJournals[labelId.value] = assignedIds;
    proxy.$modal.msgSuccess(`已批量移除 ${assignedSelected.value.length} 个期刊`);
    getAssignedList();
  }).catch(() => {});
};

// 返回上一级
const goBack = () => {
  router.go(-1);
};

// 初始化
onMounted(() => {
  // 初始化获取数据
  getAssignedList();
  getJournalList();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding-bottom: 20px;
  .back-container {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    :deep(.el-button) {
      margin: 0;
      padding: 0;
      height: auto;
      line-height: normal;
      display: inline-flex;
      align-items: center;
    }

    .page-title {
      margin-left: 20px;
      font-size: 18px;
      font-weight: bold;
      line-height: 1.5;
    }
  }

  .search-container {
    margin-bottom: 20px;
  }

  .table-footer {
    margin-top: 15px;
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .dialog-content {
    .journal-info {
      margin-bottom: 15px;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .script-search-form {
      margin-bottom: 15px;
    }
  }
}
</style>
