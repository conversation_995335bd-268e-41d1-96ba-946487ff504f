<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryRef"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item label="标签名称" prop="labelName">
        <el-input
          v-model="queryParams.labelName"
          placeholder="请输入标签名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="labelType">
        <el-select
          v-model="queryParams.labelType"
          placeholder="标签类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in label_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="标签状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
        >搜索
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['label:label:add']"
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
        >新增标签
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['label:label:remove']"
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <right-toolbar
        v-model:show-search="showSearch"
        @query-table="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="labelList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column
        label="序号"
        align="center"
        prop="labelId"
        width="80"
      />
      <el-table-column
        label="标签名称"
        align="center"
        prop="labelName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="类型"
        align="center"
        prop="labelType"
        width="100"
      >
        <template #default="scope">
          <dict-tag :options="script_type" :value="scope.row.labelType"/>
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        align="center"
        prop="status"
        width="100"
      >
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column
        label="节点"
        align="center"
        width="100"
      >
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="showNodes(scope.row)"
          >{{ scope.row.nodeCount }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="100"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            v-hasPermi="['label:label:edit']"
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
          >编辑
          </el-button>
          <el-button
            v-hasPermi="['label:label:config']"
            link
            type="success"
            icon="Setting"
            @click="navigateToConfig(scope.row)"
          >脚本配置
          </el-button>
          <el-button
            v-hasPermi="['label:label:remove']"
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改标签对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="labelRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="标签名称" prop="labelName">
          <el-input
            v-model="form.labelName"
            placeholder="请输入标签名称"
          />
        </el-form-item>
        <el-form-item label="标签类型" prop="labelType">
          <el-select v-model="form.labelType" placeholder="请选择">
            <el-option
              v-for="dict in script_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            >{{ dict.label }}</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.label"
              :value="dict.value"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入标签备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 显示节点列表对话框 -->
    <el-dialog v-model="nodeOpen" title="关联节点列表" width="600px" append-to-body>
      <el-table :data="nodeList" stripe style="width: 100%">
        <el-table-column prop="nodeId" label="节点ID" width="80" />
        <el-table-column prop="nodeName" label="节点名称" />
        <el-table-column prop="nodeType" label="节点类型">
          <template #default="scope">
            <dict-tag :options="script_type" :value="scope.row.nodeType" />
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup name="Label">
import {getCurrentInstance, reactive, ref, toRefs} from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const {proxy} = getCurrentInstance();
const {sys_normal_disable, script_type} = proxy.useDict(
  'sys_normal_disable',
  'script_type'
);

const labelList = ref([]);
const open = ref(false);
const nodeOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const nodeList = ref([]);

// 模拟数据
const mockLabelList = [
  {
    labelId: 1,
    labelName: '批次标签1',
    remark: '用于处理批次节点的标签',
    labelType: 1, // 批次
    status: 0, // 启用
    nodeCount: 5,
    createTime: new Date('2023-09-15')
  },
  {
    labelId: 2,
    labelName: '源刊标签1',
    remark: 'Science相关期刊处理标签',
    labelType: 2, // 源刊
    status: 0, // 启用
    nodeCount: 8,
    createTime: new Date('2023-10-20')
  },
  {
    labelId: 3,
    labelName: '高校标签1',
    remark: '清华大学相关期刊处理标签',
    labelType: 3, // 高校
    status: 0, // 启用
    nodeCount: 3,
    createTime: new Date('2023-11-05')
  },
  {
    labelId: 4,
    labelName: '批次标签2',
    remark: '处理批量数据的标签',
    labelType: 1, // 批次
    status: 1, // 停用
    nodeCount: 0,
    createTime: new Date('2023-11-25')
  },
  {
    labelId: 5,
    labelName: '源刊标签2',
    remark: 'Nature相关期刊处理标签',
    labelType: 2, // 源刊
    status: 0, // 启用
    nodeCount: 12,
    createTime: new Date('2023-12-10')
  }
];

// 模拟节点数据
const mockNodesList = {
  1: [
    { nodeId: 101, nodeName: '批次节点A', nodeType: 1 },
    { nodeId: 102, nodeName: '批次节点B', nodeType: 1 },
    { nodeId: 103, nodeName: '批次节点C', nodeType: 1 },
    { nodeId: 104, nodeName: '批次节点D', nodeType: 1 },
    { nodeId: 105, nodeName: '批次节点E', nodeType: 1 }
  ],
  2: [
    { nodeId: 201, nodeName: '源刊节点Science-1', nodeType: 2 },
    { nodeId: 202, nodeName: '源刊节点Science-2', nodeType: 2 },
    { nodeId: 203, nodeName: '源刊节点Science-3', nodeType: 2 },
    { nodeId: 204, nodeName: '源刊节点Science-4', nodeType: 2 },
    { nodeId: 205, nodeName: '源刊节点Science-5', nodeType: 2 },
    { nodeId: 206, nodeName: '源刊节点Science-6', nodeType: 2 },
    { nodeId: 207, nodeName: '源刊节点Science-7', nodeType: 2 },
    { nodeId: 208, nodeName: '源刊节点Science-8', nodeType: 2 }
  ],
  3: [
    { nodeId: 301, nodeName: '高校节点-清华A', nodeType: 3 },
    { nodeId: 302, nodeName: '高校节点-清华B', nodeType: 3 },
    { nodeId: 303, nodeName: '高校节点-清华C', nodeType: 3 }
  ],
  5: [
    { nodeId: 501, nodeName: '源刊节点Nature-1', nodeType: 2 },
    { nodeId: 502, nodeName: '源刊节点Nature-2', nodeType: 2 },
    { nodeId: 503, nodeName: '源刊节点Nature-3', nodeType: 2 },
    { nodeId: 504, nodeName: '源刊节点Nature-4', nodeType: 2 },
    { nodeId: 505, nodeName: '源刊节点Nature-5', nodeType: 2 },
    { nodeId: 506, nodeName: '源刊节点Nature-6', nodeType: 2 },
    { nodeId: 507, nodeName: '源刊节点Nature-7', nodeType: 2 },
    { nodeId: 508, nodeName: '源刊节点Nature-8', nodeType: 2 },
    { nodeId: 509, nodeName: '源刊节点Nature-9', nodeType: 2 },
    { nodeId: 510, nodeName: '源刊节点Nature-10', nodeType: 2 },
    { nodeId: 511, nodeName: '源刊节点Nature-11', nodeType: 2 },
    { nodeId: 512, nodeName: '源刊节点Nature-12', nodeType: 2 }
  ]
};

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 20,
    labelName: undefined,
    labelType: undefined,
    status: undefined,
    createTime: undefined,
  },
  rules: {
    labelName: [
      {required: true, message: '标签名称不能为空', trigger: 'blur'},
    ],
    labelType: [
      {required: true, message: '标签类型不能为空', trigger: 'change'},
    ]
  }
});

const {queryParams, form, rules} = toRefs(data);

/** 查询标签列表 */
function getList() {
  loading.value = true;
  setTimeout(() => {
    // 模拟过滤逻辑
    let filteredList = [...mockLabelList];

    if (queryParams.value.labelName) {
      filteredList = filteredList.filter(item =>
        item.labelName.includes(queryParams.value.labelName)
      );
    }

    if (queryParams.value.labelType) {
      filteredList = filteredList.filter(item =>
        item.labelType === queryParams.value.labelType
      );
    }

    if (queryParams.value.status) {
      filteredList = filteredList.filter(item =>
        item.status === queryParams.value.status
      );
    }

    if (queryParams.value.createTime && queryParams.value.createTime.length === 2) {
      const startDate = new Date(queryParams.value.createTime[0]);
      const endDate = new Date(queryParams.value.createTime[1]);
      filteredList = filteredList.filter(item => {
        const itemDate = new Date(item.createTime);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    labelList.value = filteredList;
    total.value = filteredList.length;
    loading.value = false;
  }, 300);
}

/** 显示关联节点 */
function showNodes(row) {
  nodeList.value = mockNodesList[row.labelId] || [];
  nodeOpen.value = true;
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    labelId: undefined,
    labelName: undefined,
    labelType: undefined,
    remark: undefined,
    status: '0',
  };
  proxy.resetForm('labelRef');
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.labelId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加标签';
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();
  const labelId = row.labelId || ids.value[0];
  // 模拟获取数据
  setTimeout(() => {
    form.value = {...mockLabelList.find(item => item.labelId === labelId)};
    open.value = true;
    title.value = '修改标签';
  }, 300);
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs['labelRef'].validate(valid => {
    if (valid) {
      if (form.value.labelId != undefined) {
        // 模拟修改
        setTimeout(() => {
          const index = mockLabelList.findIndex(item => item.labelId === form.value.labelId);
          if (index !== -1) {
            mockLabelList[index] = {...form.value};
          }
          // 如果是提交后需要进入配置页面的逻辑
          navigateToConfig(form.value);

          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        }, 300);
      } else {
        // 模拟新增
        setTimeout(() => {
          const newId = mockLabelList.length > 0
            ? Math.max(...mockLabelList.map(item => item.labelId)) + 1
            : 1;
          const newLabel = {
            ...form.value,
            labelId: newId,
            nodeCount: 0,
            createTime: new Date()
          };
          mockLabelList.push(newLabel);

          // 提交后导航到相应的配置页面
          navigateToConfig(newLabel);

          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        }, 300);
      }
    }
  });
}

/** 导航到配置页面 */
function navigateToConfig(label) {
  if (label.labelType === 1) {
    // 批次标签配置
    router.push({
      path: '/label/batch',
      query: { id: label.labelId, name: label.labelName }
    });
  } else if (label.labelType === 2) {
    // 源刊标签配置
    router.push({
      path: '/label/journal',
      query: { id: label.labelId, name: label.labelName }
    });
  } else if (label.labelType === 3) {
    // 高校标签配置
    router.push({
      path: '/label/university',
      query: { id: label.labelId, name: label.labelName }
    });
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const labelIds = row.labelId || ids.value;
  proxy.$modal
    .confirm('是否确认删除标签编号为"' + labelIds + '"的数据项？')
    .then(function () {
      // 模拟删除
      setTimeout(() => {
        const idList = Array.isArray(labelIds) ? labelIds : [labelIds];
        for (let i = 0; i < mockLabelList.length; i++) {
          if (idList.includes(mockLabelList[i].labelId)) {
            mockLabelList.splice(i, 1);
            i--;
          }
        }
        proxy.$modal.msgSuccess('删除成功');
        getList();
      }, 300);
    })
    .catch(() => {
    });
}

getList();
</script>

<style lang="scss">
.app-container {
  padding-bottom: 20px;
  .el-pagination {
    float: right;
    margin-top: 10px;
  }
}
</style>
