<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="queryParams.email" placeholder="请输入邮箱" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="用户名" prop="lastName">
        <el-input v-model="queryParams.userName" placeholder="请输入用户名" clearable @keyup.enter="handleQuery"/>
        </el-form-item>
        <el-form-item label="用户类型" prop="userType">
          <el-select style="width: 140px" v-model="queryParams.userType" placeholder="请选择" clearable>
            <el-option
                v-for="dict in plosp_user_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select style="width: 140px" v-model="queryParams.status" placeholder="请选择" clearable>
            <el-option
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
              v-model="dateRange"
              style="width: 240px;"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增用户</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete()"
        >批量删除
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" :columns="columns" @queryTable="getUserList"></right-toolbar>
    </el-row>

    <!-- 用户列表 -->
    <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center"/>
      <el-table-column v-if="columns[0].visible" label="用户ID" align="center" prop="userId"/>
      <el-table-column v-if="columns[1].visible" label="邮箱" align="center" prop="email" width="200" show-overflow-tooltip/>
      <el-table-column v-if="columns[2].visible" label="姓名" align="center" prop="name" width="100">
      </el-table-column>
      <el-table-column v-if="columns[3].visible" label="组织机构" align="center" prop="organization" show-overflow-tooltip/>
      <el-table-column v-if="columns[4].visible" label="职位" align="center" prop="title" show-overflow-tooltip/>
      <el-table-column v-if="columns[5].visible" label="部门" align="center" prop="department" show-overflow-tooltip/>
      <el-table-column v-if="columns[6].visible" label="PI姓名" align="center" prop="piName" show-overflow-tooltip/>
      <el-table-column v-if="columns[7].visible" label="电话" align="center" prop="phone" show-overflow-tooltip/>
      <el-table-column v-if="columns[8].visible" label="国家/地区" align="center" prop="countryRegion" show-overflow-tooltip/>
      <el-table-column v-if="columns[9].visible" label="省/市" align="center" prop="stateProvince" show-overflow-tooltip/>
      <el-table-column v-if="columns[10].visible" label="城市" align="center" prop="city" show-overflow-tooltip/>
      <el-table-column v-if="columns[11].visible" label="用户类型" align="center" prop="userType" width="100">
        <template #default="scope">
          <dict-tag :options="plosp_user_type" :value="scope.row.userType"/>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[12].visible" label="积分" align="center" prop="points" width="70">
        <template #default="scope">
          <router-link
              :to="'/searchUser/detail/' + scope.row.userId"
              class="link-type"
          >
            <span>{{ scope.row.points }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[13].visible" label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column v-if="columns[14].visible" label="创建时间" align="center" prop="createTime" width="155"/>

      <el-table-column label="操作" align="center" width="310" fixed="right">
        <template #default="scope">
          <el-button type="primary" link icon="Edit" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="success" link icon="Plus" @click="handleSetPoints(scope.row)">设置积分</el-button>
          <el-button type="warning" link icon="Key" @click="handleResetPwd(scope.row)">修改密码</el-button>
          <el-button
              :type="scope.row.status === 0 ? 'danger' : 'success'"
              link
              :icon="scope.row.status === 0 ? 'CircleClose' : 'CircleCheck'"
              @click="handleStatusChange(scope.row)"
          >
            {{ scope.row.status === 0 ? '禁用' : '启用' }}
          </el-button>
          <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getUserList"
    />

    <!-- 用户编辑弹窗 -->
    <el-dialog v-model="editDialogVisible" :title="editDialogTitle" width="700px" append-to-body>
      <el-form ref="editFormRef" :model="editForm" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="邮箱" prop="email" required>
              <el-input v-model="editForm.email" placeholder="请输入邮箱"/>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isAdd">
            <el-form-item label="密码" prop="password" required>
              <el-input v-model="editForm.password" type="password" placeholder="请输入密码" show-password/>
              <div>密码应为字母、数字和特殊字符(如@!?%#)的组合，长度为8-30位</div>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isAdd">
            <el-form-item label="确认密码" prop="confirmPassword" required>
              <el-input v-model="editForm.confirmPassword" type="password" placeholder="请再次输入密码" show-password/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider/>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="名字" prop="firstName" required>
              <el-input v-model="editForm.firstName" placeholder="请输入名字"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="姓氏" prop="lastName" required>
              <el-input v-model="editForm.lastName" placeholder="请输入姓氏"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="组织机构" prop="organization" required>
              <el-select v-model="editForm.organization" placeholder="请选择或输入组织机构" filterable allow-create style="width: 100%">
                <el-option v-for="org in organizationOptions" :key="org" :label="org" :value="org"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="部门" prop="department">
              <el-input v-model="editForm.department" placeholder="请输入部门"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="PI姓名" prop="piName">
              <el-input v-model="editForm.piName" placeholder="请输入PI姓名"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位" prop="title">
              <el-select v-model="editForm.title" placeholder="请选择职位" style="width: 100%">
                <el-option label="Staff" value="Staff"/>
                <el-option label="Student" value="Student"/>
                <el-option label="Faculty" value="Faculty"/>
                <el-option label="Other" value="Other"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电话" prop="phone">
              <el-input v-model="editForm.phone" placeholder="请输入电话"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="国家/地区" prop="countryRegion" required>
              <el-select v-model="editForm.countryRegion" placeholder="请选择国家/地区" filterable style="width: 100%">
                <el-option v-for="country in countryOptions" :key="country" :label="country" :value="country"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="省/州" prop="stateProvince">
              <el-input v-model="editForm.stateProvince" placeholder="请输入省/州"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市" prop="city">
              <el-input v-model="editForm.city" placeholder="请输入城市"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户权限" prop="userType" required>
              <el-select v-model="editForm.userType" placeholder="请选择用户权限" style="width: 100%">
                <el-option
                    v-for="dict in plosp_user_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitEditForm">确定</el-button>
          <el-button @click="cancelEdit">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 积分设置弹窗 -->
    <el-dialog v-model="pointsDialogVisible" title="设置积分" width="500px" append-to-body>
      <el-form ref="pointsFormRef" :model="pointsForm" :rules="pointsRules" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="pointsForm.username" disabled/>
        </el-form-item>
        <el-form-item label="当前积分">
          <el-input v-model="pointsForm.currentPoints" disabled/>
        </el-form-item>
        <el-form-item label="变动类型" prop="type">
          <el-radio-group v-model="pointsForm.type">
            <el-radio label="add">增加积分</el-radio>
            <el-radio label="subtract">减少积分</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="变动积分" prop="points">
          <el-input-number v-model="pointsForm.points" :min="1" :max="pointsForm.currentPoints"/>
        </el-form-item>
        <el-form-item label="变动原因" prop="reason">
          <el-input
              v-model="pointsForm.reason"
              type="textarea"
              placeholder="请输入积分变动原因"
              :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitPointsForm">确定</el-button>
          <el-button @click="cancelPoints">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改密码弹窗 -->
    <el-dialog v-model="pwdDialogVisible" title="修改密码" width="500px" append-to-body>
      <el-form ref="pwdFormRef" :model="pwdForm" :rules="pwdRules" label-width="100px">
        <el-form-item label="用户邮箱">
          <el-input v-model="pwdForm.email" disabled/>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
              v-model="pwdForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitPwdForm">确定</el-button>
          <el-button @click="cancelPwd">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="UserManagement">
import {getCurrentInstance, ref} from 'vue';
import {useRouter} from 'vue-router';

// API导入
import {
  addUser,
  changeUserStatus,
  delUser,
  getUser,
  listUser,
  resetUserPwd,
  updateUser
} from '@/api/system/plosp_user.js';
import {changeUserPoints} from "@/api/system/userPoints.js";

const router = useRouter();
const {proxy} = getCurrentInstance();
const {sys_normal_disable, plosp_user_type} = proxy.useDict(
    'sys_normal_disable',
    'plosp_user_type'
);
// 国家选项
const countryOptions = ref([
  '中国', '美国', '英国', '加拿大', '澳大利亚', '日本', '德国', '法国', '意大利', '俄罗斯',
  '韩国', '印度', '巴西', '南非', '新加坡', '马来西亚', '泰国', '越南', '菲律宾', '印度尼西亚'
]);

// 组织机构选项
const organizationOptions = ref([
  '北京大学', '清华大学', '复旦大学', '浙江大学', '上海交通大学',
  '中国科学院', '中国医学科学院', '哈佛大学', '斯坦福大学', '麻省理工学院',
  '牛津大学', '剑桥大学', '东京大学', '京都大学', '首尔大学'
]);


// 列显隐信息
const columns = ref([
  {key: 0, label: `用户ID`, visible: false},
  {key: 1, label: `邮箱`, visible: true},
  {key: 2, label: `姓名`, visible: true},
  {key: 3, label: `组织机构`, visible: true},
  {key: 4, label: `职位`, visible: true},
  {key: 5, label: `部门`, visible: true},
  {key: 6, label: `PI姓名`, visible: false},
  {key: 7, label: `电话`, visible: true},
  {key: 8, label: `国家/地区`, visible: false},
  {key: 9, label: `省/市`, visible: false},
  {key: 10, label: `城市`, visible: false},
  {key: 11, label: `用户类型`, visible: true},
  {key: 12, label: `积分`, visible: true},
  {key: 13, label: `状态`, visible: true},
  {key: 14, label: `创建时间`, visible: true}
]);

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 用户表格数据
const userList = ref([]);
// 日期范围
const dateRange = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  email: undefined,
  userName: undefined,
  userType: undefined,
  status: undefined
});

// 编辑弹窗相关的状态
let editDialogVisible = ref(false)
let editDialogTitle = ref('')
let editForm = ref({
  userId: undefined,
  email: '',
  password: '',
  confirmPassword: '',
  firstName: '',
  lastName: '',
  organization: '',
  department: '',
  piName: '',
  title: '',
  phone: '',
  countryRegion: '',
  stateProvince: '',
  city: '',
  userType: 'normal',
  status: 1,
  points: 0
})
let isAdd = ref(false)

// 积分设置弹窗相关的状态
let pointsDialogVisible = ref(false)
let pointsForm = ref({
  userId: undefined,
  username: '',
  currentPoints: 0,
  type: 'add',
  points: 10,
  reason: ''
})

// 修改密码弹窗相关的状态
let pwdDialogVisible = ref(false)
let pwdForm = ref({
  userId: undefined,
  email: '',
  newPassword: ''
})

// 表单校验规则
const rules = {
  email: [
    {required: true, message: '邮箱不能为空', trigger: 'blur'},
    {type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur'}
  ],
  password: [
    {required: true, message: '密码不能为空', trigger: 'blur'},
    {min: 6, max: 30, message: '密码长度必须在6到30个字符之间', trigger: 'blur'}
  ],
  confirmPassword: [
    {required: true, message: '确认密码不能为空', trigger: 'blur'},
    {
      validator: (rule, value, callback) => {
        if (value !== editForm.value.password) {
          callback(new Error('两次输入的密码不一致'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  userType: [
    {required: true, message: '用户类型不能为空', trigger: 'blur'}
  ],
  firstName: [
    {required: true, message: '名字不能为空', trigger: 'blur'}
  ],
  lastName: [
    {required: true, message: '姓氏不能为空', trigger: 'blur'}
  ],
  organization: [
    {required: true, message: '组织机构不能为空', trigger: 'blur'}
  ],
  countryRegion: [
    {required: true, message: '国家/地区不能为空', trigger: 'blur'}
  ]
};

// 修改积分表单验证规则
const pointsRules = {
  type: [
    {required: true, message: '请选择变动类型', trigger: 'change'}
  ],
  points: [
    {required: true, message: '请输入变动积分', trigger: 'blur'}
  ],
  reason: [
    {required: true, message: '请输入变动原因', trigger: 'blur'}
  ]
};

// 修改密码表单验证规则
const pwdRules = {
  newPassword: [
    {required: true, message: '新密码不能为空', trigger: 'blur'},
    {min: 6, max: 30, message: '密码长度必须在6到30个字符之间', trigger: 'blur'}
  ]
};

/** 查询用户列表 */
function getUserList() {
  loading.value = true;
  listUser(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    userList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getUserList();
}

// 重置按钮操作
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryForm");
  queryParams.value.pageNum = 1;
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.userId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  editDialogVisible.value = true;
  editDialogTitle.value = '新增用户';
  isAdd.value = true;
  editForm.value = {
    userId: undefined,
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    name: '',
    organization: '',
    department: '',
    piName: '',
    title: '',
    phone: '',
    countryRegion: '',
    stateProvince: '',
    city: '',
    userType: 'normal',
    status: 0,
    points: 0
  };
}

/** 编辑按钮操作 */
function handleEdit(row) {
  editDialogVisible.value = true;
  editDialogTitle.value = '编辑用户';
  isAdd.value = false;

  const userId = row.userId || ids.value[0];
  getUser(userId).then(response => {
    editForm.value = response.data;
    editForm.value.confirmPassword = '';
  });
}

/** 提交编辑表单 */
function submitEditForm() {
  proxy.$refs.editFormRef.validate(valid => {
    if (!valid) return;

    const formData = Object.assign({}, editForm.value);

    // 如果是编辑模式且未输入密码，则删除密码字段
    if (!isAdd.value && !formData.password) {
      delete formData.password;
    }

    // 删除confirmPassword字段，后端不需要
    delete formData.confirmPassword;

    if (isAdd.value) {
      addUser(formData).then(response => {
        proxy.$modal.msgSuccess("新增成功");
        editDialogVisible.value = false;
        getUserList();
      });
    } else {
      updateUser(formData).then(response => {
        proxy.$modal.msgSuccess("修改成功");
        editDialogVisible.value = false;
        getUserList();
      });
    }
  });
}

/** 取消编辑 */
function cancelEdit() {
  editDialogVisible.value = false;
  proxy.$refs.editFormRef?.resetFields();
}

/** 设置积分操作 */
function handleSetPoints(row) {
  pointsDialogVisible.value = true;
  pointsForm.value = {
    userId: row.userId,
    username: row.name,
    currentPoints: row.points || 0,
    type: 'add',
    points: 10,
    reason: ''
  };
}

/** 提交积分表单 */
function submitPointsForm() {
  proxy.$refs.pointsFormRef.validate(valid => {
    if (!valid) return;
    let points = pointsForm.value.points
    if (pointsForm.value.type !== 'add') {
      points = pointsForm.value.points * -1
    }
    const data = {
      userId: pointsForm.value.userId,
      points: points,
      reason: pointsForm.value.reason
    };

    // 调用API修改积分
    changeUserPoints(data).then(() => {
      proxy.$modal.msgSuccess('积分设置成功');
      getUserList();
      pointsDialogVisible.value = false;
    });
  });
}

/** 取消积分设置 */
function cancelPoints() {
  pointsDialogVisible.value = false;
  proxy.$refs.pointsFormRef?.resetFields();
}

/** 状态修改操作 */
function handleStatusChange(row) {
  const statusText = row.status === 0 ? '禁用' : '启用';
  const newStatus = row.status === 1 ? 0 : 1;

  proxy.$modal.confirm(`确认要${statusText}"${row.name}"用户吗？`).then(() => {
    changeUserStatus(row.userId, newStatus).then(response => {
      proxy.$modal.msgSuccess(`${statusText}成功`);
      getUserList();
    });
  }).catch(() => {
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const userIds = [row?.userId] || ids.value;
  if (!userIds || userIds.length === 0) {
    return;
  }

  proxy.$modal.confirm(`确认删除所选中的${userIds.length}条数据吗？`).then(() => {
    delUser(userIds).then(response => {
      proxy.$modal.msgSuccess("删除成功");
      getUserList();
    });
  }).catch(() => {
  });
}

/** 修改密码操作 */
function handleResetPwd(row) {
  pwdDialogVisible.value = true;
  pwdForm.value = {
    userId: row.userId,
    email: row.email,
    newPassword: ''
  };
}

/** 提交修改密码表单 */
function submitPwdForm() {
  proxy.$refs.pwdFormRef.validate(valid => {
    if (!valid) return;

    const data = {
      userId: pwdForm.value.userId,
      newPassword: pwdForm.value.newPassword
    };

    // 调用API修改密码
    resetUserPwd(data).then(response => {
      proxy.$modal.msgSuccess('密码修改成功');
      pwdDialogVisible.value = false;
      getUserList();
    });
  });
}

/** 取消修改密码 */
function cancelPwd() {
  pwdDialogVisible.value = false;
  proxy.$refs.pwdFormRef?.resetFields();
}

// 初始化
getUserList();
</script>

<style lang="scss" scoped>
</style>
