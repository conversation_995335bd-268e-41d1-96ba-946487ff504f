<template>
  <div class="app-container">
    <div class="back-container">
      <el-button
          type="text"
          icon="ArrowLeft"
          @click="goBack"
      >返回</el-button>
    </div>
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px" class="search-form mt20">
      <el-form-item label="变动类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择变动类型"
          clearable
          style="width: 200px"
        >
          <el-option
              v-for="dict in point_change_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="变动时间" prop="changeTime">
        <el-date-picker
          v-model="dateRange"
          class="search-input"
          style="width: 240px;"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getPointsRecordList"></right-toolbar>
    </el-row>

    <!-- 积分变动记录列表 -->
    <el-table v-loading="loading" :data="pointsRecordList">
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column label="时间" align="center" prop="changeTime" width="180" />
      <el-table-column label="积分变动类型" align="center" prop="type" min-width="120">
        <template #default="scope">
          <dict-tag :options="point_change_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="积分变动原因" align="center" prop="reason" min-width="180" show-overflow-tooltip>

      </el-table-column>
      <el-table-column label="积分变动" align="center" prop="points" width="100">
        <template #default="scope">
          <span :class="scope.row.points >= 0 ? 'text-success' : 'text-danger'">
            {{ scope.row.points >= 0 ? '+' + scope.row.points : scope.row.points }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="相关文献" align="center" prop="literature" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          <span v-if="scope.row.literatureTitle">{{ scope.row.literatureTitle }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getPointsRecordList"
    />
  </div>
</template>

<script setup name="UserPointsDetail">
import {getCurrentInstance, ref} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import {listPointsRecord} from '@/api/system/userPoints';

const route = useRoute();
const router = useRouter();
const userId = route.params.userId;
const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 积分记录列表
const pointsRecordList = ref([]);

const {point_change_type} = proxy.useDict(
    'point_change_type',
);
// 日期范围
const dateRange = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  userId: userId,
  type: undefined
});

// 返回上一级
const goBack = () => {
  router.back();
};

// 查询积分记录列表
function getPointsRecordList() {
  loading.value = true;
  listPointsRecord(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    pointsRecordList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getPointsRecordList();
}

// 重置按钮操作
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryForm");
  queryParams.value.pageNum = 1;
  queryParams.value.userId = userId; // 保留用户ID
  handleQuery();
}

// 获取积分变动类型标签
function getPointsTypeLabel(type) {
  const option = pointsTypeOptions.find(item => item.value === type);
  return option ? option.label : type;
}

onMounted(() => {
  getPointsRecordList();
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 10px;
  padding-bottom: 40px;
}

.mb8 {
  margin-bottom: 8px;
}

.mt10 {
  margin-top: 10px;
}

.mt20 {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.user-info {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;

  .info-item {
    display: flex;
    align-items: center;

    .label {
      width: 80px;
      color: #606266;
      font-weight: bold;
    }

    .value {
      color: #303133;

      &.points {
        font-weight: bold;
        color: #409EFF;
      }
    }
  }
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 16px;

  .el-form-item {
    margin-bottom: 0;
    margin-right: 16px;
  }

  .search-input {
    width: 240px;
  }
}

.text-success {
  color: #67C23A;
  font-weight: bold;
}

.text-danger {
  color: #F56C6C;
  font-weight: bold;
}

.app-container {
  .back-container {
    display: flex;
    align-items: center;

    :deep(.el-button) {
      margin: 0;
      padding: 0;
      height: auto;
      line-height: normal;
      display: inline-flex;
      align-items: center;
    }

    .page-title {
      margin-left: 20px;
      font-size: 18px;
      font-weight: bold;
      line-height: 1.5;
    }
  }
}
</style>
