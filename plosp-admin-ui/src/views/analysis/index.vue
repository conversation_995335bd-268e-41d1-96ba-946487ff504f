<template>
  <div class="app-container">
    <!-- 检索区 -->
    <el-form :inline="true" :model="query" class="mb16">
      <el-form-item label="文献标题">
        <el-input v-model="query.title" placeholder="文献标题" clearable style="width: 220px" />
      </el-form-item>
      <el-form-item label="PMID">
        <el-input v-model="query.pmid" placeholder="PMID" clearable style="width: 120px" />
      </el-form-item>
      <el-form-item label="PMCID">
        <el-input v-model="query.pmcid" placeholder="PMCID" clearable style="width: 120px" />
      </el-form-item>
      <el-form-item label="DOI">
        <el-input v-model="query.doi" placeholder="DOI" clearable style="width: 180px" />
      </el-form-item>
      <el-form-item label="实体">
        <el-input v-model="query.entity" placeholder="实体" clearable style="width: 120px" />
      </el-form-item>
      <el-form-item label="完整度">
        <el-select v-model="query.completeness" clearable style="width: 120px">
          <el-option label="100%" :value="100" />
          <el-option label=">80%" :value="80" />
          <el-option label=">60%" :value="60" />
          <el-option label=">40%" :value="40" />
          <el-option label=">20%" :value="20" />
          <el-option label=">0%" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格区 -->
    <el-table :data="filteredList" border style="width: 100%">
      <el-table-column label="文献标题" min-width="200" show-overflow-tooltip>
        <template #default="scope">
          <el-link type="primary" :underline="false" :href="`https://www.biosino.org/plosp/${scope.row.id}`" target="_blank">{{ scope.row.title }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="pmid" label="PMID" min-width="80" show-overflow-tooltip />
      <el-table-column prop="pmcid" label="PMCID" min-width="80" show-overflow-tooltip />
      <el-table-column prop="doi" label="DOI" min-width="100" show-overflow-tooltip />
      <el-table-column label="摘要解读" min-width="120">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.abstractStatus)">{{ getStatusText(scope.row.abstractStatus) }}</el-tag>
          <el-link
            v-if="scope.row.abstractStatus==='done' || scope.row.abstractStatus==='fail'"
            :type="scope.row.abstractStatus==='fail' ? 'danger' : 'primary'"
            @click="scope.row.abstractStatus==='done' ? showAbstract(scope.row) : showFail(scope.row, 'abstract')"
            style="margin-left: 8px;"
          >
            {{ scope.row.abstractStatus==='fail' ? '[日志]' : '[查看]' }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="实体识别" min-width="120">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.entityStatus)">{{ getStatusText(scope.row.entityStatus) }}</el-tag>
          <el-link
            v-if="scope.row.entityStatus==='done' || scope.row.entityStatus==='fail'"
            :type="scope.row.entityStatus==='fail' ? 'danger' : 'primary'"
            @click="scope.row.entityStatus==='done' ? showEntity(scope.row) : showFail(scope.row, 'entity')"
            style="margin-left: 8px;"
          >
            {{ scope.row.entityStatus==='fail' ? '[日志]' : '[查看]' }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="Embedding" min-width="120">
        <template #default="scope">
          <el-tag v-if="scope.row.embedding" type="success">已向量化</el-tag>
          <el-tag v-else type="info">未向量化</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="全文识别" min-width="120">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.fulltextStatus)">{{ getStatusText(scope.row.fulltextStatus) }}</el-tag>
          <el-link
            v-if="scope.row.fulltextStatus==='done' || scope.row.fulltextStatus==='fail'"
            :type="scope.row.fulltextStatus==='fail' ? 'danger' : 'primary'"
            @click="scope.row.fulltextStatus==='done' ? showFulltext(scope.row) : showFail(scope.row, 'fulltext')"
            style="margin-left: 8px;"
          >
            {{ scope.row.fulltextStatus==='fail' ? '[日志]' : '[查看]' }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="完整度" min-width="80">
        <template #default="scope">
          <el-progress :percentage="scope.row.completeness" :text-inside="true" :stroke-width="16" status="success" />
        </template>
      </el-table-column>
    </el-table>

    <!-- 摘要解读弹窗 -->
    <el-dialog v-model="dialog.abstract" title="摘要解读" width="800px">
      <template v-if="currentRow">
        <div class="mb8">原始摘要：</div>
        <div class="abstract-original-preview">{{ currentRow.originalAbstract || '暂无原始摘要' }}</div>
        <div class="mb8 mt16">解读信息：</div>
        <el-input
          v-model="currentRow.abstractResult"
          type="textarea"
          :rows="8"
          placeholder="暂无解读内容"
        />
        <div class="mt8" style="color:#888;font-size:14px;">
          <span>大模型版本：{{ currentRow.llmVersion || 'v1.0' }}</span>
          <span style="margin-left:24px;">分析时间：{{ currentRow.llmTime || '2024-06-01 10:00:00' }}</span>
        </div>
        <div class="dialog-footer mt16">
          <el-button type="primary" @click="saveAbstract">保存</el-button>
          <el-button type="danger" @click="deleteAbstract">删除</el-button>
          <el-button @click="dialog.abstract=false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 实体识别弹窗 -->
    <el-dialog v-model="dialog.entity" title="实体识别" width="1400px">
      <template v-if="currentRow">
        <div class="mb8">原始JSON：</div>
        <el-input type="textarea" :rows="20" v-model="entityJsonStr" readonly />
        <div class="mb8 mt16">摘要高亮预览：</div>
        <div class="entity-preview" v-html="entityPreviewHtml"></div>
        <div class="entity-group-list">
          <div v-for="(words, type) in entityTypeMap" :key="type" class="entity-group-row">
            <span class="entity-group-title">{{ type }}</span>
            <span v-for="word in words" :key="word" class="entity-group-word" :style="{color: entityTypeColor[type] || '#888'}">{{ word }}</span>
          </div>
        </div>
        <div class="dialog-footer mt16">
          <el-button @click="dialog.entity=false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 全文识别弹窗 -->
    <el-dialog v-model="dialog.fulltext" title="全文识别" width="1400">
      <template v-if="currentRow">
        <!-- 元数据信息 -->
        <div class="metadata-container">
          <div class="metadata-block publication-info">
            <a href="">Antonie Van Leeuwenhoek.</a>  2018 Jun;111(6):859-873. doi: 10.1007/s10482-018-1040-x. Epub 2018 Feb 19.
          </div>
          <div class="metadata-block title">
            Deciphering the trophic interaction between Akkermansia
            muciniphila and the butyrogenic gut commensal
            Anaerostipes caccae using a metatranscriptomic approach
          </div>
          <div class="metadata-block authors">
            Loo Wee Chia . Bastian V. H. Hornung . Steven Aalvink . Peter J. Schaap . Willem M. de Vos . Jan Knol . Clara Belzer
          </div>
          <div class="metadata-block identifiers">
            Host glycans are paramount in regulating the symbiotic relationship between humans and their gut bacteria. The constant flux of host-secreted mucin at the mucosal layer creates a steady niche for bacterial colonization. Mucin degradation by keystone species subsequently shapes the microbial community. This study investigated the transcriptional response during mucin-driven trophic interaction between the specialised mucin-degrader Akkermansia muciniphila and a butyrogenic gut commensal Anaerostipes caccae. A. muciniphila monocultures and co-cultures with non-mucolytic A. caccae from the Lachnospiraceae family were grown anaerobically in minimal media supplemented with mucin. We analysed for growth, metabolites (HPLC analysis), microbial composition (quantitative reverse transcription PCR), and transcriptional response (RNA-seq). Mucin degradation by A. muciniphila supported the growth of A. caccae and concomitant butyrate production predominantly via the acetyl-CoA pathway. Differential expression analysis (DESeq 2) showed the presence of A. caccae induced changes in the A. muciniphila transcriptional response with increased expression of mucin degradation genes and reduced expression of ribosomal genes. Two putative operons that encode for uncharacterised proteins and an efflux system, and several two-component systems were also differentially regulated. This indicated A. muciniphila changed its transcriptional regulation in response to A. caccae. This study provides insight to understand the mucin-driven microbial ecology using metatranscriptomics. Our findings show that the expression of mucolytic enzymes by A. muciniphila increases upon the presence of a community member. This could indicate its role as a keystone species that supports the microbial community in the mucosal environment by increasing the availability of mucin sugars.
          </div>
           <div class="metadata-block keywords">
            <span>关键字:</span>
             Butyrate; Cross feeding; Keystone species; Microbiome; Mucin; Transcriptional regulation; Verrucomicrobia
          </div>
        </div>

        <div class="mb8 mt16">正文：</div>
        <div class="fulltext-section">
          <div class="fulltext-content">
            <div v-for="(p, idx) in currentRow.fulltextResult?.contents || []" :key="idx" class="fulltext-paragraph">{{ p }}</div>
            <span v-if="!currentRow.fulltextResult?.contents?.length">暂无正文</span>
          </div>
        </div>
        <div class="mb8 mt16">图片：</div>
        <div class="fulltext-section">
          <el-carousel
            v-if="currentRow.fulltextResult?.figures?.length"
            :interval="4000"
            type="card"
            height="200px"
            indicator-position="outside"
            @change="onCarouselChange"
            ref="carouselRef"
          >
            <el-carousel-item v-for="(fig, idx) in currentRow.fulltextResult.figures" :key="idx">
              <div class="carousel-img-wrap">
                <img :src="fig.image_path" :alt="fig.title" class="carousel-img"/>
              </div>
            </el-carousel-item>
          </el-carousel>
          <span v-else>暂无图片</span>
          <div v-if="currentRow.fulltextResult?.figures?.length" class="carousel-legend">
            <div class="carousel-legend-title">{{ currentFigure.title }}</div>
            <div class="carousel-legend-desc" v-if="currentFigure.description">{{ currentFigure.description }}</div>
          </div>
        </div>
        <div class="mb8 mt16">表格：</div>
        <div class="fulltext-section">
          <div v-if="currentRow.fulltextResult?.tables?.length">
            <div v-for="(table, tIdx) in currentRow.fulltextResult.tables" :key="tIdx" class="fulltext-table-block">
              <div class="table-title">{{ table.title }}</div>
              <el-table :data="table.content.rows" border size="small" style="width:100%;margin-bottom:8px;">
                <el-table-column
                  v-for="(col, cIdx) in table.content.header"
                  :key="cIdx"
                  :label="col"
                  :align="'center'"
                >
                  <template #default="scope">
                    {{ scope.row[cIdx] }}
                  </template>
                </el-table-column>
              </el-table>
              <div class="table-note" v-if="table.note">{{ table.note }}</div>
            </div>
          </div>
          <span v-else>暂无表格</span>
        </div>
        <div class="dialog-footer mt16">
          <el-button @click="downloadFulltext(currentRow)" type="primary">下载结果</el-button>
          <el-button @click="dialog.fulltext=false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 失败详情弹窗 -->
    <el-dialog v-model="dialog.fail" title="失败详情" width="800px">
      <template v-if="failInfo">
        <div class="fail-detail-section">
          <div class="fail-label">失败原因：</div>
          <div class="fail-content">{{ failInfo.reason }}</div>
        </div>
        <div class="fail-detail-section mt16">
          <div class="fail-label">日志信息：</div>
          <div class="fail-content fail-log">
            <div v-if="failInfo.log && failInfo.log.length > 0">
              <div v-for="(logItem, index) in failInfo.log" :key="index" class="fail-log-item">{{ logItem }}</div>
            </div>
            <span v-else>暂无详细日志</span>
          </div>
        </div>
        <el-button type="primary" @click="retry(failInfo.type, failInfo.row)">重试</el-button>
        <el-button @click="dialog.fail=false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, watch } from 'vue';
import { ElMessage } from 'element-plus';

// 检索条件
const query = reactive({
  title: '',
  pmid: '',
  pmcid: '',
  doi: '',
  entity: '',
  completeness: undefined
});

// 颜色映射
const entityTypeColor = {
  '解剖学术语': '#1890ff',
  '功能指标': '#e255a1',
  '研究方法': '#52c41a',
  '生物学特征': '#faad14',
  '其他': '#888'
};

// 生成20条模拟数据
const titles = [
  '基于深度学习的自然语言处理技术研究',
  '人工智能在医疗领域的应用与发展趋势',
  '区块链技术在供应链管理中的应用研究',
  '5G网络技术在智慧城市建设中的关键作用',
  '量子计算技术发展现状与未来展望',
  '大数据分析在金融风险管理中的应用',
  '云计算架构下的数据安全与隐私保护',
  '物联网技术在智能家居中的应用研究',
  '基因编辑技术在遗传病治疗中的应用与伦理思考',
  '可再生能源发电系统的智能调度与控制',
  '深度学习在医学影像诊断中的应用研究',
  '智能制造中的数字孪生技术应用',
  '新型纳米材料在环境治理中的应用',
  '脑科学中的神经网络建模与仿真',
  '自动驾驶汽车的感知与决策系统',
  '高性能计算在气候模拟中的应用',
  '蛋白质结构预测的最新进展',
  '医学影像三维重建技术综述',
  '多模态情感识别方法研究',
  '生物信息学中的基因组数据挖掘'
];

function randomStatus() {
  const arr = ['done', 'analyzing', 'todo', 'fail'];
  return arr[Math.floor(Math.random() * arr.length)];
}
function randomBool() {
  return Math.random() > 0.5;
}
function randomCompleteness() {
  const arr = [100, 80, 60, 40, 20, 0];
  return arr[Math.floor(Math.random() * arr.length)];
}
function randomPMID() {
  return String(Math.floor(10000000 + Math.random() * 90000000));
}
function randomPMCID() {
  return String(Math.floor(1000000 + Math.random() * 9000000));
}
function randomDOI() {
  return `10.1${Math.floor(Math.random()*10)}.${Math.floor(Math.random()*10000)}.${Math.floor(Math.random()*100000)}`;
}
function randomDate() {
  const d = new Date(Date.now() - Math.random()*1e10);
  return d.toISOString().replace('T',' ').slice(0,19);
}

// 用于全文识别的样例数据结构
const fulltextSample = {
  contents: [
    "Host glycans are paramount in regulating the symbiotic relationship between humans and their gut bacteria. The constant flux of host-secreted mucin at the mucosal layer creates a steady niche for bacterial colonization. Mucin degradation by keystone species subsequently shapes the microbial community. This study investigated the transcriptional response during mucin-driven trophic interaction between the specialised mucin-degrader Akkermansia muciniphila and a butyrogenic gut commensal Anaerostipes caccae.",
    "A. muciniphila monocultures and co-cultures with nonmucolytic A. caccae from the Lachnospiraceae family were grown anaerobically in minimal media supplemented with mucin. We analysed for growth, metabolites (HPLC analysis), microbial composition (quantitative reverse transcription PCR), and transcriptional response (RNA-seq). Mucin degradation by A. muciniphila supported the growth of A. caccae and concomitant butyrate production predominantly via the acetyl-CoA pathway.",
    "Differential expression analysis (DESeq 2) showed the presence of A. caccae induced changes in the A. muciniphila transcriptional response with increased expression of mucin degradation genes and reduced expression of ribosomal genes. Two putative operons that encode for uncharacterised proteins and an efflux system, and several two-component systems were also differentially regulated. This indicated A. muciniphila changed its transcriptional regulation in response to A. caccae."
  ],
  tables: [
    {
      number: 1,
      title: "Table 1: The differential expression of putative two-component systems in A. muciniphila",
      note: "Negative values indicate upregulation in monocultures and positive values indicate upregulation in co-cultures",
      content: {
        header: ["Locus tag", "A.muc-A.cac co-culture", "Function"],
        rows: [
          ["Amuc_0311", "<0.05", "1.96", "Signal transduction histidine kinase, nitrogenspecific, NtrB"],
          ["Amuc_0312", "<0.05", "2.19", "Two-component, sigma54 specific, transcriptional regulator, Fis family"],
          ["Amuc_1010", "<0.05", "5.28", "Response regulator receiver protein"]
        ]
      }
    },
    {
      number: 2,
      title: "Table 2: Genomic prediction of B vitamins biosynthesis (presence = 1 and absence = 0) based on the combination of essential functional roles by Magnusdottir et al. (2015)",
      content: {
        header: ["Locus tag", "A.muc-A.cac co-culture", "Function"],
        rows: [
          ["Amuc_0311", "<0.05", "1.96", "Signal transduction histidine kinase, nitrogenspecific, NtrB"],
          ["Amuc_0312", "<0.05", "2.19", "Two-component, sigma54 specific, transcriptional regulator, Fis family"],
          ["Amuc_1010", "<0.05", "5.28", "Response regulator receiver protein"]
        ]
      }
    }
  ],
  figures: [
    {
      number: 1,
      title: "Fig.1: Schematic overview of the interval-fed batch culture setup.",
      description: "A. muciniphila was inoculated at t = 0 h followed by A. caccae at t = 8 h to ensure substrate availability for butyrogen via extracellular mucin degradation by A. muciniphila. Limited amounts of pure mucin, 0.15% (v/v) were supplemented at 2 days intervals to maintain the abundance of A. muciniphila and to support the emergence of A. caccae. A sample for RNA-seq analysis was collected on day 8. The pH and metabolite profile of monocultures and co-cultures of the interval-fed batch culture, with arrow showing day 8. Quantification of microbial composition on day 8 by RT-qPCR targeting 16S rRNA on total RNA. Error bars indicate the standard deviation of biological duplicates.",
      image_path: "https://p1.itc.cn/q_70/images03/20221106/ef8ae794d16140ee9e76a6c9dd8c6cb2.jpeg"
    },
    {
      number: 2,
      title: "Fig.2: Hierarchical clustering showing the Pearson's correlation of the transcriptome samples.",
      description: "Positive fold changes indicate upregulation in co-cultures, and negative fold changes indicate upregulation in monocultures. Locus tags for genes with Log2 fold change > 2 (or fold change > 4) are labelled. (a) Volcano plot showing significance. (b) Response regulator and putative operon upregulated in the co-cultures. (c) Putative operon upregulated in the monocultures. Fold changes are listed above the respective genes.",
      image_path: "https://www.ceshigo.com/static/v2/img/worksItem1.png"
    }
    ,
    {
      number: 3,
      title: "Fig.3: Hierarchical clustering showing the Pearson's correlation of the transcriptome samples.",
      image_path: "https://puui.qpic.cn/vpic/0/i33285s4ima.png/0"
    }
  ]
};

// 模拟失败日志数组
function generateFailureLog() {
  const logs = [
    `[${randomDate()}] INFO: Task started.`,
    `[${randomDate()}] WARNING: Potential issue detected in data parsing.`,
    `[${randomDate()}] ERROR: Processing failed due to an unexpected error.`, // Simplified error message here
    `[${randomDate()}] DEBUG: Retrying attempt 1...`,
    `[${randomDate()}] ERROR: Retrying failed. Status code: 500.`,
    `[${randomDate()}] INFO: Task terminated after failure.`,
  ];
  // Randomly pick a subset of logs for variety
  const numLogs = Math.floor(Math.random() * logs.length) + 1; // At least one log entry
  return logs.slice(0, numLogs);
}

const analysisData = ref(Array.from({length: 20}).map((_, i) => {
  const status = randomStatus();
  const entityStatus = randomStatus();
  const fulltextStatus = randomStatus();
  const embedding = randomBool();
  const completeness = randomCompleteness();
  const entityJson = JSON.stringify({
    abstract: 'Healthy adults have robust individual differences in neuroanatomy and cognitive ability not captured by demographics or gross morphology.',
    entities: [
      {text: 'neuroanatomy', type: '解剖学术语'},
      {text: 'cognitive ability', type: '功能指标'},
      {text: 'gross morphology', type: '解剖学术语'}
    ]
  });
  // 全文识别样例
  const fulltextResult = fulltextStatus === 'done' ? fulltextSample : undefined;
  // 模拟失败日志数组
  const failureLogs = (fulltextStatus === 'fail' || entityStatus === 'fail' || status === 'fail')
    ? generateFailureLog()
    : [];

  // 为第一个"完成"的条目设置特定的摘要和解读
  let originalAbstract = '';
  let interpretedAbstract = `本研究通过大模型自动解读，发现${titles[i]}具有重要意义。`;
  if (i === 0 && status === 'done') { // 假设第一个完成的条目
    originalAbstract = 'Despite advances in our understanding of risk, development, immunologic control, and treatment options for lung cancer, it remains the leading cause of cancer death. Tobacco smoking remains the predominant risk factor for lung cancer development. Nontobacco risk factors include environmental and occupational exposures, chronic lung disease, lung infections, and lifestyle factors. Because tobacco remains the leading risk factor for lung cancer, disease prevention is focused on smoking avoidance and cessation. Other prevention measures include healthy diet choices and maintaining a physically active lifestyle. Future work should focus on smoking cessation campaigns and better understanding disease development and treatment strategies in nonsmokers.';
    interpretedAbstract = '肺癌尽管在风险认知、疾病发展、免疫控制和治疗方面已有一定进展，依然是癌症死亡的主要原因。吸烟仍是导致肺癌的最重要风险因素，因此预防策略主要集中在避免吸烟和帮助人们戒烟。同时，研究也指出了其他非吸烟因素（如环境暴露、慢性肺病等）对肺癌风险的影响，并提倡健康饮食和积极生活方式作为辅助预防手段。未来的研究重点应该放在强化戒烟干预措施，同时深入探索非吸烟人群中肺癌的发生机制和治疗策略。整段强调了吸烟控制在肺癌防治中的关键作用，同时指出需要扩大研究视野，关注非吸烟相关肺癌的挑战。';
  }

  return {
    title: titles[i],
    pmid: randomPMID(),
    pmcid: randomPMCID(),
    doi: randomDOI(),
    abstractStatus: status,
    originalAbstract: originalAbstract,
    abstractResult: interpretedAbstract,
    llmVersion: 'GPT-4.0',
    llmTime: randomDate(),
    entityStatus,
    entity: entityJson,
    entityFailReason: entityStatus==='fail' ? '实体模型服务超时' : '',
    entityLog: failureLogs,
    embedding,
    fulltextStatus,
    fulltextResult,
    fulltextFailReason: fulltextStatus==='fail' ? '全文解析服务异常' : '',
    fulltextLog: failureLogs,
    abstractFailReason: status==='fail' ? '摘要解读模型异常' : '',
    abstractLog: failureLogs,
    completeness
  };
}));

// 弹窗状态
const dialog = reactive({
  abstract: false,
  entity: false,
  fulltext: false,
  fail: false
});
const currentRow = ref(null);
const failInfo = ref(null);
const entityJsonStr = ref('');
const entityPreviewHtml = ref('');
const activeFigureIndex = ref(0);
const carouselRef = ref();
const currentFigure = computed(() => {
  const figs = currentRow.value?.fulltextResult?.figures || [];
  return figs[activeFigureIndex.value] || {};
});

// 检索过滤
const filteredList = computed(() => {
  return analysisData.value.filter(row => {
    if (query.title && !row.title.includes(query.title)) return false;
    if (query.pmid && !row.pmid.includes(query.pmid)) return false;
    if (query.pmcid && !row.pmcid.includes(query.pmcid)) return false;
    if (query.doi && !row.doi.includes(query.doi)) return false;
    if (query.entity && !(row.entity && row.entity.includes(query.entity))) return false;
    if (query.completeness !== undefined) {
      if (query.completeness === 100 && row.completeness !== 100) return false;
      if (query.completeness !== 100 && row.completeness <= query.completeness) return false;
    }
    return true;
  });
});

function handleQuery() {}
function resetQuery() {
  query.title = '';
  query.pmid = '';
  query.pmcid = '';
  query.doi = '';
  query.entity = '';
  query.completeness = undefined;
}

// 摘要解读弹窗
function showAbstract(row) {
  currentRow.value = row;
  dialog.abstract = true;
}
function saveAbstract() {
  ElMessage.success('已保存摘要解读内容');
  dialog.abstract = false;
}
function deleteAbstract() {
  if (currentRow.value) {
    currentRow.value.abstractResult = '';
    ElMessage.success('已删除摘要解读内容');
    dialog.abstract = false;
  }
}

// 实体识别弹窗
function showEntity(row) {
  currentRow.value = row;
  entityJsonStr.value = JSON.stringify(JSON.parse(row.entity), null, 2);
  // 生成高亮HTML，按类型区分颜色（仅改文字颜色，不加背景色）
  try {
    const obj = JSON.parse(row.entity);
    let abs = obj.abstract || '';
    (obj.entities || []).forEach(e => {
      const color = entityTypeColor[e.type] || entityTypeColor['其他'];
      const reg = new RegExp(e.text, 'g');
      abs = abs.replace(reg, `<span class='entity-highlight' style='color:${color};'>${e.text}</span>`);
    });
    entityPreviewHtml.value = abs;
  } catch {
    entityPreviewHtml.value = '解析失败';
  }
  dialog.entity = true;
}

// 全文识别弹窗
function showFulltext(row) {
  currentRow.value = row;
  dialog.fulltext = true;
}
function downloadFulltext(row) {
  const content = JSON.stringify(row.fulltextResult, null, 2);
  const blob = new Blob([content], {type: 'application/json'});
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `${row.title}_全文识别结果.zip`;
  a.click();
  URL.revokeObjectURL(url);
}

// 失败详情弹窗
function showFail(row, type) {
  let reason = '未知错误';
  let log = []; // 日志改为数组
  if (type === 'abstract') {
    reason = row.abstractFailReason || '摘要解读失败';
    log = row.abstractLog || []; // 确保是数组
  } else if (type === 'entity') {
    reason = row.entityFailReason || '实体识别失败';
    log = row.entityLog || []; // 确保是数组
  } else if (type === 'fulltext') {
    reason = row.fulltextFailReason || '全文识别失败';
    log = row.fulltextLog || []; // 确保是数组
  }
  failInfo.value = { reason, log, type, row };
  dialog.fail = true;
}

// 重试操作（模拟）
function retry(type, row) {
  row[type + 'Status'] = 'analyzing';
  ElMessage.info('已重新提交分析任务');
  dialog.fail = false;
}

// 获取图片路径（本地图片）
function getDocImg(image_path, idx) {
  // 取文件名如 1.png、2.png ...
  const match = image_path.match(/(\d+\.png)$/);
  if (match) {
    // vite/webpack会自动处理静态资源
    return new URL(`../assets/images/doc/${match[1]}`, import.meta.url).href;
  }
  // fallback: 用原始url
  return image_path;
}
function getDocImgName(image_path, idx) {
  const match = image_path.match(/(\d+\.png)$/);
  return match ? match[1] : `图片${idx+1}`;
}
// el-table动态列prop
function getColProp(idx) {
  return `col${idx}`;
}

function onCarouselChange(idx) {
  activeFigureIndex.value = idx;
}

// 每次弹窗打开时重置索引
watch(() => dialog.fulltext, (val) => {
  if (val) {
    nextTick(() => {
      activeFigureIndex.value = 0;
      if (carouselRef.value) carouselRef.value.setActiveItem(0);
    });
  }
});

// 实体分组映射
const entityTypeMap = computed(() => {
  try {
    const obj = JSON.parse(currentRow.value?.entity || '{}');
    const map = {};
    (obj.entities || []).forEach(e => {
      if (!map[e.type]) map[e.type] = [];
      if (!map[e.type].includes(e.text)) map[e.type].push(e.text);
    });
    return map;
  } catch {
    return {};
  }
});

// 获取状态对应的文本
function getStatusText(status) {
  switch (status) {
    case 'done': return '完成';
    case 'analyzing': return '分析中';
    case 'todo': return '待分析';
    case 'fail': return '失败';
    default: return '';
  }
}

// 获取状态对应的el-tag类型（颜色）
function getStatusTagType(status) {
  switch (status) {
    case 'done': return 'success'; // 绿色
    case 'analyzing': return 'warning'; // 橙色
    case 'todo': return 'info'; // 灰色
    case 'fail': return 'danger'; // 红色
    default: return '';
  }
}
</script>

<style scoped>
.app-container {
  padding: 16px;
}
.mb16 {
  margin-bottom: 16px;
}
.mt16 {
  margin-top: 16px;
}
.mb8 {
  margin-bottom: 8px;
}
.entity-preview {
  background: #f8f8f8;
  padding: 12px;
  border-radius: 4px;
  min-height: 48px;
  font-size: 14px;
  word-break: break-all;
}
.entity-highlight {
  font-weight: bold;
}
.dialog-footer {
  text-align: right;
}
.mr8 {
  margin-right: 8px;
}
.fulltext-section {
  background: #f8f8f8;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  font-size: 16px;
  word-break: break-all;
}
.fulltext-content {
  max-height: 600px;
  overflow-y: auto;
  padding-right: 8px;
}
.fulltext-content::-webkit-scrollbar {
  width: 6px;
}
.fulltext-content::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 3px;
}
.fulltext-content::-webkit-scrollbar-track {
  background-color: #f8f8f8;
}
.fulltext-paragraph {
  margin-bottom: 14px;
  line-height: 1.8;
  text-align: justify;
}
.fulltext-images {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}
.fulltext-figure {
  text-align: center;
  margin-right: 16px;
}
.figure-title {
  font-size: 13px;
  color: #888;
  margin-top: 4px;
}
.fulltext-table-block {
  margin-bottom: 16px;
}
.table-title {
  font-weight: bold;
  margin-bottom: 4px;
}
.table-note {
  color: #888;
  font-size: 14px;
  margin-top: 2px;
}
.carousel-img-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 240px;
}
.carousel-img {
  max-width: 320px;
  max-height: 180px;
  border-radius: 6px;
  box-shadow: 0 2px 8px #eee;
  margin-bottom: 8px;
}
.carousel-img-title {
  font-size: 14px;
  color: #333;
  font-weight: bold;
  margin-bottom: 2px;
}
.carousel-img-desc {
  font-size: 13px;
  color: #888;
  margin-bottom: 2px;
}
.carousel-legend {
  text-align: center;
  margin-top: 12px;
  margin-bottom: 8px;
}
.carousel-legend-title {
  font-size: 16px;
  font-weight: bold;
  color: #222;
  margin-bottom: 4px;
}
.carousel-legend-desc {
  font-size: 14px;
  color: #666;
}
.entity-group-list {
  background: #fafbfc;
  border-radius: 6px;
  padding: 16px 16px 8px 16px;
  margin-top: 16px;
  margin-bottom: 8px;
  font-size: 15px;
}
.entity-group-row {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.entity-group-title {
  font-weight: bold;
  margin-right: 12px;
  color: #666;
  min-width: 90px;
  display: inline-block;
}
.entity-group-word {
  margin-right: 16px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s;
}
.fail-detail-section {
  margin-bottom: 12px;
}
.fail-label {
  font-weight: bold;
  margin-bottom: 4px;
  color: #555;
}
.fail-content {
  background: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  word-break: break-all;
}
.fail-log {
  color: #c0392b;
  word-break: break-all;
}
.fail-log-item {
  margin-bottom: 4px; /* 控制每条日志的间距 */
}
.abstract-original-preview {
  background: #f8f8f8;
  padding: 12px;
  border-radius: 4px;
  min-height: 48px;
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  word-break: break-all;
}
.metadata-container {
  margin-bottom: 20px;
}
.metadata-block {
  padding: 8px 0;
}
.metadata-block.publication-info {
  font-size: 14px;
  color: #555;
}
.metadata-block.title {
  font-size: 18px;
  font-weight: bold;
  line-height: 1.3;
}
.metadata-block.authors {
  font-size: 16px;
  color: #333;
}
.metadata-block.identifiers {
  display: flex;
  gap: 20px;
  font-size: 16px;
  color: #333;
  margin-bottom: 16px;
  line-height: 1.5;
  flex-direction: row;
  align-items: center;
}

.metadata-block.keywords {
  font-size: 14px;
  margin-bottom: 4px;
  word-break: break-all;
}

.mb8.mt16 {
  margin-top: 24px;
}
</style>
