<template>
    <div class="app-container">
      <!-- 搜索区域 -->
      <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
        <el-form-item label="传递编号" prop="deliveryId">
          <el-input
            v-model="queryParams.deliveryId"
            placeholder="请输入传递编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="请求人" prop="requester">
          <el-input
            v-model="queryParams.requester"
            placeholder="请输入请求人"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="请求时间" style="width: 308px">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 工具栏 -->
      <el-row :gutter="10" class="mb8">
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 表格 -->
      <el-table v-loading="loading" :data="deliveryList">
        <el-table-column label="传递编号" align="center" prop="deliveryId" />
        <el-table-column label="文献标题" align="center" prop="title" :show-overflow-tooltip="true">
          <template #default="scope">
            <el-link type="primary" :underline="false" :href="`https://www.biosino.org/plosp/${scope.row.literatureId}`" target="_blank">{{ scope.row.title }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="请求人" align="center" prop="requester" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-tag
              :type="scope.row.status === '0' ? 'warning' : scope.row.status === '1' ? 'primary' : scope.row.status === '2' ? 'success' : 'danger'"
            >
              {{ statusFormat(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="请求时间" align="center" prop="requestTime" width="180" />
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 查看日志对话框 -->
      <el-dialog title="传递日志" v-model="logDialogVisible" width="600px" append-to-body>
        <el-timeline>
          <el-timeline-item
            v-for="(log, index) in logList"
            :key="index"
            :timestamp="log.time"
            :type="log.type"
          >
            {{ log.content }}
          </el-timeline-item>
        </el-timeline>
      </el-dialog>
    </div>
  </template>

  <script setup>
  import { ref, reactive, toRefs, onMounted } from 'vue';
  import { ElMessage } from 'element-plus';
  import Pagination from '@/components/Pagination';
  import RightToolbar from '@/components/RightToolbar';

  // 显示搜索条件
  const showSearch = ref(true);
  // 加载状态
  const loading = ref(false);
  // 总条数
  const total = ref(0);
  // 日期范围
  const dateRange = ref([]);
  // 日志对话框
  const logDialogVisible = ref(false);
  // 日志列表
  const logList = ref([]);
  // 文献传递列表
  const deliveryList = ref([]);

  // 状态选项
  const statusOptions = [
    { value: '0', label: '等待中' },
    { value: '1', label: '传递中' },
    { value: '2', label: '传递完成' },
    { value: '3', label: '传递失败' }
  ];

  // 查询参数
  const data = reactive({
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      deliveryId: undefined,
      requester: undefined,
      status: undefined,
      requestTime: undefined
    }
  });

  const { queryParams } = toRefs(data);

  // 模拟数据
  const mockDeliveryData = [
    {
      deliveryId: 'T20240501001',
      literatureId: 'LIT20240501001',
      title: '基于深度学习的自然语言处理技术研究',
      requester: '张三',
      status: '0',
      requestTime: '2024-05-01 09:30:45'
    },
    {
      deliveryId: 'T20240501002',
      literatureId: 'LIT20240501002',
      title: '人工智能在医疗领域的应用与发展趋势',
      requester: '李四',
      status: '1',
      requestTime: '2024-05-01 10:15:22'
    },
    {
      deliveryId: 'T20240501003',
      literatureId: 'LIT20240501003',
      title: '区块链技术在供应链管理中的应用研究',
      requester: '王五',
      status: '2',
      requestTime: '2024-05-01 11:05:37'
    },
    {
      deliveryId: 'T20240501004',
      literatureId: 'LIT20240501004',
      title: '5G网络技术在智慧城市建设中的关键作用',
      requester: '赵六',
      status: '3',
      requestTime: '2024-05-01 13:45:18'
    },
    {
      deliveryId: 'T20240502001',
      literatureId: 'LIT20240502001',
      title: '量子计算技术发展现状与未来展望',
      requester: '钱七',
      status: '0',
      requestTime: '2024-05-02 08:30:12'
    },
    {
      deliveryId: 'T20240502002',
      literatureId: 'LIT20240502002',
      title: '大数据分析在金融风险管理中的应用',
      requester: '孙八',
      status: '2',
      requestTime: '2024-05-02 09:45:33'
    },
    {
      deliveryId: 'T20240502003',
      literatureId: 'LIT20240502003',
      title: '云计算架构下的数据安全与隐私保护',
      requester: '周九',
      status: '1',
      requestTime: '2024-05-02 14:20:45'
    },
    {
      deliveryId: 'T20240503001',
      literatureId: 'LIT20240503001',
      title: '物联网技术在智能家居中的应用研究',
      requester: '吴十',
      status: '2',
      requestTime: '2024-05-03 10:10:25'
    },
    {
      deliveryId: 'T20240503002',
      literatureId: 'LIT20240503002',
      title: '边缘计算在工业互联网中的实践与探索',
      requester: '郑十一',
      status: '3',
      requestTime: '2024-05-03 15:35:40'
    },
    {
      deliveryId: 'T20240504001',
      literatureId: 'LIT20240504001',
      title: '人机交互技术的发展历程与未来趋势',
      requester: '王五',
      status: '0',
      requestTime: '2024-05-04 09:25:18'
    }
  ];

  // 格式化状态显示
  function statusFormat(row) {
    return statusOptions.find(item => item.value === row.status)?.label || '';
  }

  /** 查询文献传递列表 */
  function getList() {
    loading.value = true;

    // 模拟API请求延迟
    setTimeout(() => {
      // 模拟过滤逻辑
      let filteredList = [...mockDeliveryData];

      if (queryParams.value.deliveryId) {
        filteredList = filteredList.filter(item =>
          item.deliveryId.includes(queryParams.value.deliveryId)
        );
      }

      if (queryParams.value.requester) {
        filteredList = filteredList.filter(item =>
          item.requester.includes(queryParams.value.requester)
        );
      }

      if (queryParams.value.status) {
        filteredList = filteredList.filter(item =>
          item.status === queryParams.value.status
        );
      }

      if (dateRange.value && dateRange.value.length === 2) {
        const startDate = new Date(dateRange.value[0]);
        const endDate = new Date(dateRange.value[1]);
        filteredList = filteredList.filter(item => {
          const itemDate = new Date(item.requestTime);
          return itemDate >= startDate && itemDate <= endDate;
        });
      }

      deliveryList.value = filteredList;
      total.value = filteredList.length;
      loading.value = false;
    }, 300);
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = [];
    queryParams.value = {
      pageNum: 1,
      pageSize: 10,
      deliveryId: undefined,
      requester: undefined,
      status: undefined,
      requestTime: undefined
    };
    getList();
  }


  // 页面加载时获取列表数据
  onMounted(() => {
    getList();
  });
  </script>
