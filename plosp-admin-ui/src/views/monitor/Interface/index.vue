<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
      <el-form-item label="接口方法名" prop="methodName">
        <el-input
          v-model="queryParams.methodName"
          placeholder="请输入接口方法名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="请求方法" prop="requestMethod">
        <el-select
          v-model="queryParams.requestMethod"
          placeholder="请选择请求方法"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in requestMethodOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="请求时间" style="width: 308px">
        <el-date-picker
          v-model="requestTimeRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="interfaceLogList">
      <el-table-column label="接口方法名" align="center" prop="methodName" />
      <el-table-column label="请求参数" align="center" prop="requestParams" width="250">
        <template #default="scope">
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="scope.row.requestParams"
            placement="top-start"
          >
            <div class="ellipsis-text">{{ scope.row.requestParams }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="请求方法" align="center" prop="requestMethod" width="100">
        <template #default="scope">
          <el-tag
            :type="scope.row.requestMethod === 'GET' ? 'success' : scope.row.requestMethod === 'POST' ? 'primary' : scope.row.requestMethod === 'PUT' ? 'warning' : scope.row.requestMethod === 'DELETE' ? 'danger' : 'info'"
          >
            {{ scope.row.requestMethod }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="请求IP" align="center" prop="requestIp" width="130" />
      <el-table-column label="用户ID" align="center" prop="userId" width="100" />
      <el-table-column label="请求URL" align="center" prop="requestUrl" width="250">
        <template #default="scope">
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="scope.row.requestUrl"
            placement="top-start"
          >
            <div class="ellipsis-text">{{ scope.row.requestUrl }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="返回结果" align="center" prop="responseResult" width="250">
        <template #default="scope">
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="scope.row.responseResult"
            placement="top-start"
          >
            <div class="ellipsis-text">{{ scope.row.responseResult }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag
            :type="scope.row.status === '成功' ? 'success' : 'danger'"
          >
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="请求时间" align="center" prop="requestTime" width="160" />
      <el-table-column label="接口耗时(ms)" align="center" prop="duration" width="120" />
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted } from 'vue';
import Pagination from '@/components/Pagination';
import RightToolbar from '@/components/RightToolbar';

// 显示搜索条件
const showSearch = ref(true);
// 加载状态
const loading = ref(false);
// 总条数
const total = ref(0);
// 请求时间范围
const requestTimeRange = ref([]);
// 接口日志列表
const interfaceLogList = ref([]);

// 请求方法选项
const requestMethodOptions = [
  { value: 'GET', label: 'GET' },
  { value: 'POST', label: 'POST' },
  { value: 'PUT', label: 'PUT' },
  { value: 'DELETE', label: 'DELETE' },
  { value: 'PATCH', label: 'PATCH' }
];

// 状态选项
const statusOptions = [
  { value: '成功', label: '成功' },
  { value: '失败', label: '失败' }
];

// 查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    methodName: undefined,
    requestMethod: undefined,
    requestIp: undefined,
    userId: undefined,
    status: undefined,
    requestTimeStart: undefined,
    requestTimeEnd: undefined
  }
});

const { queryParams } = toRefs(data);

// 模拟接口日志数据
const mockInterfaceLogData = [
  {
    id: '1',
    methodName: 'getLiteratureList',
    requestParams: '{"pageNum":1,"pageSize":10,"keyword":"人工智能"}',
    requestMethod: 'GET',
    requestIp: '*************',
    userId: 'user001',
    requestUrl: '/api/literature/list',
    responseResult: '{"code":200,"msg":"操作成功","data":{"total":156,"rows":[...]}}',
    status: '成功',
    requestTime: '2024-05-01 09:30:45',
    duration: 125
  },
  {
    id: '2',
    methodName: 'addLiterature',
    requestParams: '{"title":"人工智能在医疗领域的应用","authors":"张三,李四","journal":"科学通报"}',
    requestMethod: 'POST',
    requestIp: '************',
    userId: 'user002',
    requestUrl: '/api/literature/add',
    responseResult: '{"code":200,"msg":"添加成功","data":{"id":"33649033"}}',
    status: '成功',
    requestTime: '2024-05-01 10:15:22',
    duration: 230
  },
  {
    id: '3',
    methodName: 'updateLiterature',
    requestParams: '{"id":"33649030","title":"基于深度学习的自然语言处理关键技术研究"}',
    requestMethod: 'PUT',
    requestIp: '*******',
    userId: 'user003',
    requestUrl: '/api/literature/update',
    responseResult: '{"code":200,"msg":"更新成功"}',
    status: '成功',
    requestTime: '2024-05-01 11:05:37',
    duration: 185
  },
  {
    id: '4',
    methodName: 'deleteLiterature',
    requestParams: '{"id":"33649029"}',
    requestMethod: 'DELETE',
    requestIp: '************',
    userId: 'user004',
    requestUrl: '/api/literature/delete/33649029',
    responseResult: '{"code":200,"msg":"删除成功"}',
    status: '成功',
    requestTime: '2024-05-02 08:30:12',
    duration: 150
  },
  {
    id: '5',
    methodName: 'getLiteratureDetail',
    requestParams: '{"id":"33649031"}',
    requestMethod: 'GET',
    requestIp: '*************',
    userId: 'user005',
    requestUrl: '/api/literature/detail/33649031',
    responseResult: '{"code":200,"msg":"操作成功","data":{...}}',
    status: '成功',
    requestTime: '2024-05-02 13:45:18',
    duration: 95
  },
  {
    id: '6',
    methodName: 'uploadLiterature',
    requestParams: '{"file":"literature.pdf","type":"paper"}',
    requestMethod: 'POST',
    requestIp: '************',
    userId: 'user006',
    requestUrl: '/api/literature/upload',
    responseResult: '{"code":500,"msg":"文件格式不支持"}',
    status: '失败',
    requestTime: '2024-05-03 10:10:25',
    duration: 320
  },
  {
    id: '7',
    methodName: 'searchLiterature',
    requestParams: '{"keyword":"区块链","year":"2023","journal":"计算机科学"}',
    requestMethod: 'GET',
    requestIp: '***********',
    userId: 'user007',
    requestUrl: '/api/literature/search',
    responseResult: '{"code":200,"msg":"操作成功","data":{"total":45,"rows":[...]}}',
    status: '成功',
    requestTime: '2024-05-03 14:25:18',
    duration: 210
  },
  {
    id: '8',
    methodName: 'exportLiterature',
    requestParams: '{"ids":["33649030","33649031","33649032"],"format":"excel"}',
    requestMethod: 'POST',
    requestIp: '*************',
    userId: 'user008',
    requestUrl: '/api/literature/export',
    responseResult: '{"code":200,"msg":"导出成功","data":{"url":"/download/literature_20240504.xlsx"}}',
    status: '成功',
    requestTime: '2024-05-04 09:35:40',
    duration: 450
  },
  {
    id: '9',
    methodName: 'submitDefect',
    requestParams: '{"literatureId":"33649032","defectType":"作者","content":"作者信息有误"}',
    requestMethod: 'POST',
    requestIp: '*************',
    userId: 'user009',
    requestUrl: '/api/defect/submit',
    responseResult: '{"code":200,"msg":"提交成功","data":{"id":"7"}}',
    status: '成功',
    requestTime: '2024-05-04 16:20:33',
    duration: 135
  },
  {
    id: '10',
    methodName: 'processDefect',
    requestParams: '{"id":"5","status":"1","processor":"admin"}',
    requestMethod: 'PUT',
    requestIp: '**********',
    userId: 'admin',
    requestUrl: '/api/defect/process',
    responseResult: '{"code":500,"msg":"参数错误"}',
    status: '失败',
    requestTime: '2024-05-05 11:15:27',
    duration: 95
  },
  {
    id: '11',
    methodName: 'getDefectList',
    requestParams: '{"pageNum":1,"pageSize":10,"status":"0"}',
    requestMethod: 'GET',
    requestIp: '*************',
    userId: 'admin',
    requestUrl: '/api/defect/list',
    responseResult: '{"code":200,"msg":"操作成功","data":{"total":3,"rows":[...]}}',
    status: '成功',
    requestTime: '2024-05-05 15:40:12',
    duration: 110
  },
  {
    id: '12',
    methodName: 'getUserInfo',
    requestParams: '{"userId":"user001"}',
    requestMethod: 'GET',
    requestIp: '*************',
    userId: 'admin',
    requestUrl: '/api/user/info/user001',
    responseResult: '{"code":200,"msg":"操作成功","data":{...}}',
    status: '成功',
    requestTime: '2024-05-06 08:55:39',
    duration: 85
  }
];

/** 查询接口日志列表 */
function getList() {
  loading.value = true;

  // 模拟API请求延迟
  setTimeout(() => {
    // 模拟过滤逻辑
    let filteredList = [...mockInterfaceLogData];

    if (queryParams.value.methodName) {
      filteredList = filteredList.filter(item =>
        item.methodName.includes(queryParams.value.methodName)
      );
    }

    if (queryParams.value.requestMethod) {
      filteredList = filteredList.filter(item =>
        item.requestMethod === queryParams.value.requestMethod
      );
    }

    if (queryParams.value.requestIp) {
      filteredList = filteredList.filter(item =>
        item.requestIp.includes(queryParams.value.requestIp)
      );
    }

    if (queryParams.value.userId) {
      filteredList = filteredList.filter(item =>
        item.userId.includes(queryParams.value.userId)
      );
    }

    if (queryParams.value.status) {
      filteredList = filteredList.filter(item =>
        item.status === queryParams.value.status
      );
    }

    if (requestTimeRange.value && requestTimeRange.value.length === 2) {
      const startDate = new Date(requestTimeRange.value[0]);
      const endDate = new Date(requestTimeRange.value[1]);
      endDate.setHours(23, 59, 59, 999); // 设置为当天结束时间

      filteredList = filteredList.filter(item => {
        const itemDate = new Date(item.requestTime);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    // 模拟分页
    const start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
    const end = start + queryParams.value.pageSize;
    interfaceLogList.value = filteredList.slice(start, end);
    total.value = filteredList.length;
    loading.value = false;
  }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  requestTimeRange.value = [];
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    methodName: undefined,
    requestMethod: undefined,
    requestIp: undefined,
    userId: undefined,
    status: undefined,
    requestTimeStart: undefined,
    requestTimeEnd: undefined
  };
  getList();
}

// 页面加载时获取列表数据
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 10px;
  padding-bottom: 40px;
}

.mb8 {
  margin-bottom: 8px;
}

.ellipsis-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 230px;
}
</style>
