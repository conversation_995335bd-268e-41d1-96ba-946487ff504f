<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
      <el-form-item label="文献ID" prop="literatureId">
        <el-input
          v-model="queryParams.literatureId"
          placeholder="请输入文献ID"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入用户名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="IP地址" prop="ip">
        <el-input
          v-model="queryParams.ip"
          placeholder="请输入IP地址"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="国家" prop="country">
        <el-input
          v-model="queryParams.country"
          placeholder="请输入国家"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="访问时间" style="width: 308px">
        <el-date-picker
          v-model="accessTimeRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="accessLogList">
      <el-table-column label="文献ID" align="center" prop="literatureId" />
      <el-table-column label="访问时间" align="center" prop="accessTime" width="160" />
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column label="IP地址" align="center" prop="ip" />
      <el-table-column label="国家" align="center" prop="country" />
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted } from 'vue';
import Pagination from '@/components/Pagination';
import RightToolbar from '@/components/RightToolbar';

// 显示搜索条件
const showSearch = ref(true);
// 加载状态
const loading = ref(false);
// 总条数
const total = ref(0);
// 访问时间范围
const accessTimeRange = ref([]);
// 访问日志列表
const accessLogList = ref([]);

// 查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    literatureId: undefined,
    username: undefined,
    ip: undefined,
    country: undefined,
    accessTimeStart: undefined,
    accessTimeEnd: undefined
  }
});

const { queryParams } = toRefs(data);

// 模拟访问日志数据
const mockAccessLogData = [
  {
    id: '1',
    literatureId: '33649030',
    accessTime: '2024-05-01 09:30:45',
    username: 'user001',
    ip: '*************',
    country: '中国'
  },
  {
    id: '2',
    literatureId: '33649031',
    accessTime: '2024-05-01 10:15:22',
    username: 'user002',
    ip: '************',
    country: '美国'
  },
  {
    id: '3',
    literatureId: '33649032',
    accessTime: '2024-05-01 11:05:37',
    username: 'user003',
    ip: '*******',
    country: '美国'
  },
  {
    id: '4',
    literatureId: '33649030',
    accessTime: '2024-05-02 08:30:12',
    username: 'user004',
    ip: '************',
    country: '中国'
  },
  {
    id: '5',
    literatureId: '33649031',
    accessTime: '2024-05-02 13:45:18',
    username: 'user005',
    ip: '*************',
    country: '英国'
  },
  {
    id: '6',
    literatureId: '33649032',
    accessTime: '2024-05-03 10:10:25',
    username: 'user006',
    ip: '************',
    country: '加拿大'
  },
  {
    id: '7',
    literatureId: '33649030',
    accessTime: '2024-05-03 14:25:18',
    username: 'user007',
    ip: '***********',
    country: '德国'
  },
  {
    id: '8',
    literatureId: '33649031',
    accessTime: '2024-05-04 09:35:40',
    username: 'user008',
    ip: '*************',
    country: '法国'
  },
  {
    id: '9',
    literatureId: '33649032',
    accessTime: '2024-05-04 16:20:33',
    username: 'user009',
    ip: '*************',
    country: '日本'
  },
  {
    id: '10',
    literatureId: '33649030',
    accessTime: '2024-05-05 11:15:27',
    username: 'user010',
    ip: '**********',
    country: '韩国'
  },
  {
    id: '11',
    literatureId: '33649031',
    accessTime: '2024-05-05 15:40:12',
    username: 'user011',
    ip: '*************',
    country: '澳大利亚'
  },
  {
    id: '12',
    literatureId: '33649032',
    accessTime: '2024-05-06 08:55:39',
    username: 'user012',
    ip: '*************',
    country: '新加坡'
  }
];

/** 查询访问日志列表 */
function getList() {
  loading.value = true;

  // 模拟API请求延迟
  setTimeout(() => {
    // 模拟过滤逻辑
    let filteredList = [...mockAccessLogData];

    if (queryParams.value.literatureId) {
      filteredList = filteredList.filter(item =>
        item.literatureId.includes(queryParams.value.literatureId)
      );
    }

    if (queryParams.value.username) {
      filteredList = filteredList.filter(item =>
        item.username.includes(queryParams.value.username)
      );
    }

    if (queryParams.value.ip) {
      filteredList = filteredList.filter(item =>
        item.ip.includes(queryParams.value.ip)
      );
    }

    if (queryParams.value.country) {
      filteredList = filteredList.filter(item =>
        item.country.includes(queryParams.value.country)
      );
    }

    if (accessTimeRange.value && accessTimeRange.value.length === 2) {
      const startDate = new Date(accessTimeRange.value[0]);
      const endDate = new Date(accessTimeRange.value[1]);
      endDate.setHours(23, 59, 59, 999); // 设置为当天结束时间

      filteredList = filteredList.filter(item => {
        const itemDate = new Date(item.accessTime);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    // 模拟分页
    const start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
    const end = start + queryParams.value.pageSize;
    accessLogList.value = filteredList.slice(start, end);
    total.value = filteredList.length;
    loading.value = false;
  }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  accessTimeRange.value = [];
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    literatureId: undefined,
    username: undefined,
    ip: undefined,
    country: undefined,
    accessTimeStart: undefined,
    accessTimeEnd: undefined
  };
  getList();
}

// 页面加载时获取列表数据
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 10px;
  padding-bottom: 40px;
}

.mb8 {
  margin-bottom: 8px;
}
</style>
