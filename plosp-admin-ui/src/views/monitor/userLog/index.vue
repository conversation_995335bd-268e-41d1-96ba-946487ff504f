<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form v-show="showSearch" ref="queryRef" :model="queryParams" :inline="true">
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入用户名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="IP地址" prop="ip">
        <el-input
          v-model="queryParams.ip"
          placeholder="请输入IP地址"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="国家" prop="country">
        <el-input
          v-model="queryParams.country"
          placeholder="请输入国家"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="登录时间" style="width: 308px">
        <el-date-picker
          v-model="loginTimeRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格 -->
    <el-table v-loading="loading" :data="userLogList">
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="用户名" align="center" prop="username" />
      <el-table-column label="IP地址" align="center" prop="ip" />
      <el-table-column label="国家" align="center" prop="country"  />
      <el-table-column label="浏览器" align="center" prop="browser">
        <template #default="scope">
          <div class="browser-info">
            <el-icon v-if="scope.row.browser.includes('Chrome')"><ChromeFilled /></el-icon>
            <el-icon v-else-if="scope.row.browser.includes('Firefox')"><ElementPlus /></el-icon>
            <el-icon v-else-if="scope.row.browser.includes('Safari')"><Apple /></el-icon>
            <el-icon v-else-if="scope.row.browser.includes('Edge')"><Connection /></el-icon>
            <el-icon v-else><Monitor /></el-icon>
            <span class="browser-name">{{ scope.row.browser }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="登录时间" align="center" prop="loginTime"  />

    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, onMounted } from 'vue';
import { ChromeFilled, ElementPlus, Apple, Connection, Monitor } from '@element-plus/icons-vue';
import Pagination from '@/components/Pagination';
import RightToolbar from '@/components/RightToolbar';

// 显示搜索条件
const showSearch = ref(true);
// 加载状态
const loading = ref(false);
// 总条数
const total = ref(0);
// 登录时间范围
const loginTimeRange = ref([]);
// 用户日志列表
const userLogList = ref([]);

// 浏览器选项
const browserOptions = [
  { value: 'Chrome', label: 'Chrome' },
  { value: 'Firefox', label: 'Firefox' },
  { value: 'Safari', label: 'Safari' },
  { value: 'Edge', label: 'Edge' },
  { value: 'IE', label: 'IE' }
];

// 查询参数
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    username: undefined,
    ip: undefined,
    country: undefined,
    browser: undefined,
    loginTimeStart: undefined,
    loginTimeEnd: undefined
  }
});

const { queryParams } = toRefs(data);

// 模拟用户登录日志数据
const mockUserLogData = [
  {
    id: '1',
    userId: 'user001',
    username: '张三',
    loginTime: '2024-05-01 09:30:45',
    ip: '*************',
    country: '中国',
    browser: 'Chrome 114.0.5735.199'
  },
  {
    id: '2',
    userId: 'user002',
    username: '李四',
    loginTime: '2024-05-01 10:15:22',
    ip: '************',
    country: '美国',
    browser: 'Firefox 115.0'
  },
  {
    id: '3',
    userId: 'user003',
    username: '王五',
    loginTime: '2024-05-01 11:05:37',
    ip: '*******',
    country: '美国',
    browser: 'Safari 16.5'
  },
  {
    id: '4',
    userId: 'user004',
    username: '赵六',
    loginTime: '2024-05-02 08:30:12',
    ip: '************',
    country: '中国',
    browser: 'Edge 114.0.1823.58'
  },
  {
    id: '5',
    userId: 'user005',
    username: '钱七',
    loginTime: '2024-05-02 13:45:18',
    ip: '*************',
    country: '英国',
    browser: 'Chrome 114.0.5735.199'
  },
  {
    id: '6',
    userId: 'user006',
    username: '孙八',
    loginTime: '2024-05-03 10:10:25',
    ip: '************',
    country: '加拿大',
    browser: 'Firefox 115.0'
  },
  {
    id: '7',
    userId: 'user007',
    username: '周九',
    loginTime: '2024-05-03 14:25:18',
    ip: '***********',
    country: '德国',
    browser: 'Chrome 114.0.5735.199'
  },
  {
    id: '8',
    userId: 'user008',
    username: '吴十',
    loginTime: '2024-05-04 09:35:40',
    ip: '*************',
    country: '法国',
    browser: 'Safari 16.5'
  },
  {
    id: '9',
    userId: 'user009',
    username: '郑十一',
    loginTime: '2024-05-04 16:20:33',
    ip: '*************',
    country: '日本',
    browser: 'Edge 114.0.1823.58'
  },
  {
    id: '10',
    userId: 'user010',
    username: '王十二',
    loginTime: '2024-05-05 11:15:27',
    ip: '**********',
    country: '韩国',
    browser: 'Chrome 114.0.5735.199'
  },
  {
    id: '11',
    userId: 'user011',
    username: '李十三',
    loginTime: '2024-05-05 15:40:12',
    ip: '*************',
    country: '澳大利亚',
    browser: 'Firefox 115.0'
  },
  {
    id: '12',
    userId: 'user012',
    username: '张十四',
    loginTime: '2024-05-06 08:55:39',
    ip: '*************',
    country: '新加坡',
    browser: 'Chrome 114.0.5735.199'
  },
  {
    id: '13',
    userId: 'user013',
    username: '刘十五',
    loginTime: '2024-05-06 13:20:45',
    ip: '***********',
    country: '中国',
    browser: 'IE 11.0'
  },
  {
    id: '14',
    userId: 'user014',
    username: '陈十六',
    loginTime: '2024-05-07 09:10:22',
    ip: '**************',
    country: '中国',
    browser: 'Chrome 114.0.5735.199'
  },
  {
    id: '15',
    userId: 'user015',
    username: '杨十七',
    loginTime: '2024-05-07 14:35:18',
    ip: '************',
    country: '美国',
    browser: 'Safari 16.5'
  }
];

/** 查询用户登录日志列表 */
function getList() {
  loading.value = true;

  // 模拟API请求延迟
  setTimeout(() => {
    // 模拟过滤逻辑
    let filteredList = [...mockUserLogData];

    if (queryParams.value.userId) {
      filteredList = filteredList.filter(item =>
        item.userId.includes(queryParams.value.userId)
      );
    }

    if (queryParams.value.username) {
      filteredList = filteredList.filter(item =>
        item.username.includes(queryParams.value.username)
      );
    }

    if (queryParams.value.ip) {
      filteredList = filteredList.filter(item =>
        item.ip.includes(queryParams.value.ip)
      );
    }

    if (queryParams.value.country) {
      filteredList = filteredList.filter(item =>
        item.country.includes(queryParams.value.country)
      );
    }

    if (queryParams.value.browser) {
      filteredList = filteredList.filter(item =>
        item.browser.includes(queryParams.value.browser)
      );
    }

    if (loginTimeRange.value && loginTimeRange.value.length === 2) {
      const startDate = new Date(loginTimeRange.value[0]);
      const endDate = new Date(loginTimeRange.value[1]);
      endDate.setHours(23, 59, 59, 999); // 设置为当天结束时间

      filteredList = filteredList.filter(item => {
        const itemDate = new Date(item.loginTime);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    // 模拟分页
    const start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
    const end = start + queryParams.value.pageSize;
    userLogList.value = filteredList.slice(start, end);
    total.value = filteredList.length;
    loading.value = false;
  }, 300);
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  loginTimeRange.value = [];
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    userId: undefined,
    username: undefined,
    ip: undefined,
    country: undefined,
    browser: undefined,
    loginTimeStart: undefined,
    loginTimeEnd: undefined
  };
  getList();
}

// 页面加载时获取列表数据
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 10px;
  padding-bottom: 40px;
}

.mb8 {
  margin-bottom: 8px;
}

.browser-info {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-icon {
    margin-right: 5px;
    font-size: 16px;
  }

  .browser-name {
    display: inline-block;
    max-width: 180px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
