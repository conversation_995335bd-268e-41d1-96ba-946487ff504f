<template>
  <div class="app-container">
    <div class="monitor-content">
      <!-- 左侧节点列表 -->
      <div class="node-list">
        <!-- 左侧搜索框 -->
        <div class="list-search">
          <el-input
            v-model="searchKeyword"
            placeholder="节点名称"
            prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div class="node-items-container">
          <div
            v-for="node in filteredNodes"
            :key="node.nodeId"
            class="node-item"
            :class="{
              'is-active': currentNode && node.nodeId === currentNode.nodeId,
            }"
            @click="selectNode(node)"
          >
            <div class="node-name">{{ node.nodeName }}</div>
            <div class="node-group">{{ node.groupName }}</div>
            <div class="node-bottom-info">
              <div class="node-type">
                <span
                  class="type-badge"
                  :class="'node-type-' + node.nodeType"
                  >{{ getNodeTypeText(node.nodeType) }}</span
                >
              </div>
              <div class="node-status">
                <span
                  class="status-dot"
                  :class="getStatusClass(node.status)"
                ></span>
                <span class="status-text">{{
                  getStatusText(node.status)
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧详细信息 -->
      <div v-if="currentNode" class="node-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <div class="section-title">基本信息</div>
          <div class="basic-info">
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">节点名称</div>
                <div class="info-value node-title">
                  {{ currentNode.nodeName }}
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">分组</div>
                <div class="info-value node-group">
                  {{ currentNode.groupName }}
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">节点类型</div>
                <div class="info-value">
                  <el-tag :type="getTagType(currentNode.nodeType)">{{
                    getNodeTypeText(currentNode.nodeType)
                  }}</el-tag>
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">运行状态</div>
                <div class="info-value">
                  <span
                    class="status-badge"
                    :class="getStatusClass(currentNode.status)"
                  >
                    {{ getStatusText(currentNode.status) }}
                  </span>
                </div>
              </div>
              <div class="info-item">
                <div class="info-label">IP</div>
                <div class="info-value">{{ currentNode.ip }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">更新时间</div>
                <div class="info-value">
                  {{ formatDateTime(currentNode.lastHandshakeTime) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 资源消耗 -->
        <div class="detail-section">
          <div class="section-title">资源消耗</div>
          <div class="resource-info">
            <div class="resource-item">
              <el-progress
                type="dashboard"
                :percentage="currentNode.cpuUsage"
                :color="getResourceColor(currentNode.cpuUsage)"
                :stroke-width="10"
              />
              <div class="resource-detail">
                <div class="usage-info">
                  <span class="usage-label">已用</span>
                  <span class="usage-value"
                    >{{ currentNode.cpuUsed }} /
                    {{ currentNode.cpuTotal }} core</span
                  >
                </div>
              </div>
              <div class="resource-name">CPU 使用率</div>
            </div>

            <div class="resource-item">
              <el-progress
                type="dashboard"
                :percentage="currentNode.memoryUsage"
                :color="getResourceColor(currentNode.memoryUsage)"
                :stroke-width="10"
              />
              <div class="resource-detail">
                <div class="usage-info">
                  <span class="usage-label">已用</span>
                  <span class="usage-value"
                    >{{ currentNode.memoryUsed }} /
                    {{ currentNode.memoryTotal }} GB</span
                  >
                </div>
              </div>
              <div class="resource-name">内存使用率</div>
            </div>

            <div class="resource-item">
              <el-progress
                type="dashboard"
                :percentage="currentNode.diskUsage"
                :color="getResourceColor(currentNode.diskUsage)"
                :stroke-width="10"
              />
              <div class="resource-detail">
                <div class="usage-info">
                  <span class="usage-label">已用</span>
                  <span class="usage-value"
                    >{{ currentNode.diskUsed }} /
                    {{ currentNode.diskTotal }} GB</span
                  >
                </div>
              </div>
              <div class="resource-name">磁盘使用率</div>
            </div>
          </div>
        </div>

        <!-- 监控指标 -->
        <div class="detail-section">
          <div class="section-title">
            <span>监控指标</span>
            <!--            <el-select v-model="timeRange" class="time-range-selector" size="small">
              <el-option label="最近 1 小时" value="1" />
              <el-option label="最近 6 小时" value="6" />
              <el-option label="最近 12 小时" value="12" />
              <el-option label="最近 24 小时" value="24" />
            </el-select>-->
          </div>
          <div class="metrics-chart">
            <div class="metrics-header">
              <div class="metric-item" style="color: #e6a23c">
                <div class="metric-value">{{ currentNode.totalTasks }}</div>
                <div class="metric-name">总数</div>
              </div>
              <div class="metric-item" style="color: #67c23a">
                <div class="metric-value">{{ currentNode.completedTasks }}</div>
                <div class="metric-name">完成数</div>
              </div>
              <div class="metric-item" style="color: #f56c6c">
                <div class="metric-value">{{ currentNode.failedTasks }}</div>
                <div class="metric-name">失败数</div>
              </div>
              <div class="metric-item" style="color: #409eff">
                <div class="metric-value">{{ currentNode.runningTasks }}</div>
                <div class="metric-name">执行中</div>
              </div>
            </div>
            <div ref="chartRef" class="chart-container"></div>
          </div>
        </div>
      </div>

      <!-- 未选择节点时的提示 -->
      <div v-else class="node-detail empty-detail">
        <el-empty description="请选择一个节点查看详细信息" />
      </div>
    </div>
  </div>
</template>

<script setup name="NodeMonitor">
  import { ref, onMounted, watch, nextTick } from 'vue';
  import { Search } from '@element-plus/icons-vue';
  import * as echarts from 'echarts';

  const searchKeyword = ref('');
  const currentNode = ref(null);
  const timeRange = ref('1');
  let chart = null;

  // 记录总节点数
  const totalNodes = ref(0);

  // 模拟节点数据
  const nodeList = ref([
    {
      nodeId: '1',
      nodeName: 'demo-dev-master-01',
      nodeType: '1', // 1:批次 2:源刊 3:高校
      status: '0', // 0:运行中 1:断开 2:异常
      groupName: '文献采集组1',
      ip: '************',
      lastHandshakeTime: new Date('2022-09-14 10:05:00'),
      cpuUsage: 25,
      cpuUsed: '1',
      cpuTotal: '4',
      memoryUsage: 73,
      memoryUsed: '5.6',
      memoryTotal: '8',
      diskUsage: 9,
      diskUsed: '10.47',
      diskTotal: '100',
      totalTasks: 86,
      completedTasks: 45,
      failedTasks: 12,
      runningTasks: 29,
    },
    {
      nodeId: '2',
      nodeName: 'demo-dev-master-02',
      nodeType: '1',
      status: '1', // 断开
      groupName: '文献采集组1',
      ip: '************',
      lastHandshakeTime: new Date('2022-09-13 15:30:00'),
      cpuUsage: 0,
      cpuUsed: '0',
      cpuTotal: '4',
      memoryUsage: 0,
      memoryUsed: '0',
      memoryTotal: '8',
      diskUsage: 0,
      diskUsed: '0',
      diskTotal: '40',
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      runningTasks: 0,
    },
    {
      nodeId: '3',
      nodeName: 'demo-dev-master-03',
      nodeType: '1',
      status: '0', // 运行中
      groupName: '文献采集组2',
      ip: '************',
      lastHandshakeTime: new Date('2022-09-14 10:09:00'),
      cpuUsage: 53,
      cpuUsed: '2.1',
      cpuTotal: '4',
      memoryUsage: 68,
      memoryUsed: '5.2',
      memoryTotal: '7.64',
      diskUsage: 35,
      diskUsed: '14.2',
      diskTotal: '40',
      totalTasks: 92,
      completedTasks: 52,
      failedTasks: 8,
      runningTasks: 32,
    },
    {
      nodeId: '4',
      nodeName: 'demo-dev-worker-01',
      nodeType: '2',
      status: '0', // 运行中
      groupName: '文献采集组4',
      ip: '************',
      lastHandshakeTime: new Date('2022-09-14 10:08:00'),
      cpuUsage: 35,
      cpuUsed: '1.4',
      cpuTotal: '4',
      memoryUsage: 45,
      memoryUsed: '3.6',
      memoryTotal: '8',
      diskUsage: 22,
      diskUsed: '8.8',
      diskTotal: '40',
      totalTasks: 75,
      completedTasks: 50,
      failedTasks: 5,
      runningTasks: 20,
    },
    {
      nodeId: '5',
      nodeName: 'demo-dev-worker-02',
      nodeType: '2',
      status: '0', // 运行中
      groupName: '文献采集组4',
      ip: '************',
      lastHandshakeTime: new Date('2022-09-14 10:00:00'),
      cpuUsage: 42,
      cpuUsed: '1.7',
      cpuTotal: '4',
      memoryUsage: 62,
      memoryUsed: '5.0',
      memoryTotal: '8',
      diskUsage: 31,
      diskUsed: '12.4',
      diskTotal: '40',
      totalTasks: 68,
      completedTasks: 38,
      failedTasks: 6,
      runningTasks: 24,
    },
    {
      nodeId: '6',
      nodeName: 'demo-dev-worker-03',
      nodeType: '3',
      status: '0', // 运行中
      groupName: '文献采集组5',
      ip: '************',
      lastHandshakeTime: new Date('2022-09-14 09:55:00'),
      cpuUsage: 28,
      cpuUsed: '1.1',
      cpuTotal: '4',
      memoryUsage: 40,
      memoryUsed: '3.2',
      memoryTotal: '8',
      diskUsage: 18,
      diskUsed: '7.2',
      diskTotal: '40',
      totalTasks: 57,
      completedTasks: 41,
      failedTasks: 4,
      runningTasks: 12,
    },
    {
      nodeId: '7',
      nodeName: 'demo-dev-worker-04',
      nodeType: '3',
      status: '0', // 运行中
      groupName: '文献采集组5',
      ip: '************',
      lastHandshakeTime: new Date('2022-09-14 09:50:00'),
      cpuUsage: 32,
      cpuUsed: '1.3',
      cpuTotal: '4',
      memoryUsage: 55,
      memoryUsed: '4.4',
      memoryTotal: '8',
      diskUsage: 25,
      diskUsed: '10.0',
      diskTotal: '40',
      totalTasks: 64,
      completedTasks: 42,
      failedTasks: 5,
      runningTasks: 17,
    },
  ]);

  // 获取过滤后的节点列表
  const filteredNodes = ref([]);

  // 处理搜索和分组过滤
  const handleSearch = () => {
    let result = [...nodeList.value];

    // 根据关键字过滤
    if (searchKeyword.value) {
      result = result.filter(node =>
        node.nodeName.toLowerCase().includes(searchKeyword.value.toLowerCase()),
      );
    }

    // 更新节点总数
    totalNodes.value = result.length;

    // 显示所有过滤后的节点
    filteredNodes.value = result;

    // 如果当前选中的节点不在过滤结果中，取消选中
    if (
      currentNode.value &&
      !result.some(node => node.nodeId === currentNode.value.nodeId)
    ) {
      currentNode.value = null;
    }

    // 如果未选中节点但有过滤结果，自动选择第一个
    if (!currentNode.value && filteredNodes.value.length > 0) {
      currentNode.value = filteredNodes.value[0];
      initChart();
    }
  };

  // 选择节点
  const selectNode = node => {
    currentNode.value = node;
    initChart();
  };

  // 获取状态类名
  const getStatusClass = status => {
    switch (status) {
      case '0':
        return 'status-running';
      case '1':
        return 'status-disconnected';
      case '2':
        return 'status-error';
      default:
        return 'status-unknown';
    }
  };

  // 获取状态文本
  const getStatusText = status => {
    switch (status) {
      case '0':
        return '运行中';
      case '1':
        return '断开';
      case '2':
        return '异常';
      default:
        return '未知';
    }
  };

  // 获取节点类型文本
  const getNodeTypeText = type => {
    switch (type) {
      case '1':
        return '批次';
      case '2':
        return '源刊';
      case '3':
        return '高校';
      default:
        return '未知类型';
    }
  };

  // 获取标签类型
  const getTagType = type => {
    switch (type) {
      case '1':
        return ''; // 默认蓝色
      case '2':
        return 'success';
      case '3':
        return 'warning';
      default:
        return 'info';
    }
  };

  // 格式化日期时间
  const formatDateTime = date => {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hour = String(d.getHours()).padStart(2, '0');
    const minute = String(d.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hour}:${minute}`;
  };

  // 获取资源使用率颜色
  const getResourceColor = percentage => {
    if (percentage < 60) return '#67C23A';
    if (percentage < 80) return '#E6A23C';
    return '#F56C6C';
  };

  // 图表引用
  const chartRef = ref(null);

  // 初始化图表
  const initChart = () => {
    if (!currentNode.value) return;

    // 确保DOM已渲染
    nextTick(() => {
      if (!chartRef.value) return;

      // 如果已存在图表实例，销毁它
      if (chart) {
        chart.dispose();
      }

      // 创建新图表
      chart = echarts.init(chartRef.value);

      // 模拟时间数据
      const hours = [];
      const now = new Date();
      for (let i = 24; i > 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 60 * 1000);
        hours.push(`${String(time.getHours()).padStart(2, '0')}:00`);
      }

      // 模拟任务数据
      const generateRandomData = baseValue => {
        return hours.map(
          () => Math.floor(Math.random() * baseValue * 0.5) + baseValue * 0.5,
        );
      };

      const totalData = generateRandomData(currentNode.value.totalTasks);
      const completedData = generateRandomData(
        currentNode.value.completedTasks,
      );
      const failedData = generateRandomData(currentNode.value.failedTasks);
      const runningData = generateRandomData(currentNode.value.runningTasks);

      // 设置图表选项
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          data: ['总数', '完成数', '失败数', '执行中'],
          bottom: 0,
          icon: 'rect',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 12,
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: hours,
          axisLabel: {
            fontSize: 10,
            interval: 2,
          },
          axisLine: {
            lineStyle: {
              color: '#ddd',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eee',
            },
          },
        },
        series: [
          {
            name: '总数',
            type: 'line',
            smooth: true,
            data: totalData,
            itemStyle: {
              color: '#F56C6C',
            },
            lineStyle: {
              width: 2,
            },
            symbol: 'circle',
            symbolSize: 6,
          },
          {
            name: '完成数',
            type: 'line',
            smooth: true,
            data: completedData,
            itemStyle: {
              color: '#67C23A',
            },
            lineStyle: {
              width: 2,
            },
            symbol: 'circle',
            symbolSize: 6,
          },
          {
            name: '失败数',
            type: 'line',
            smooth: true,
            data: failedData,
            itemStyle: {
              color: '#E6A23C',
            },
            lineStyle: {
              width: 2,
            },
            symbol: 'circle',
            symbolSize: 6,
          },
          {
            name: '执行中',
            type: 'line',
            smooth: true,
            data: runningData,
            itemStyle: {
              color: '#409EFF',
            },
            lineStyle: {
              width: 2,
            },
            symbol: 'circle',
            symbolSize: 6,
          },
        ],
      };

      // 设置图表
      chart.setOption(option);

      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        chart.resize();
      });
    });
  };

  // 监听搜索条件变化
  watch(searchKeyword, () => {
    handleSearch();
  });

  // 监听时间范围变化
  watch(timeRange, () => {
    initChart();
  });

  // 页面加载完成
  onMounted(() => {
    // 初始化加载数据
    handleSearch();
  });
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 0;
    background-color: #f5f7fa;
  }

  .monitor-content {
    display: flex;
    height: 100vh;

    .node-list {
      width: 300px;
      background-color: #fff;
      border-right: 1px solid #eee;
      display: flex;
      flex-direction: column;
      height: 100%;

      .list-search {
        padding: 16px;
        border-bottom: 1px solid #eee;
        flex-shrink: 0;
      }

      .node-items-container {
        flex: 1;
        overflow-y: auto;

        .node-item {
          padding: 16px;
          border-bottom: 1px solid #f0f0f0;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            background-color: #f5f7fa;
          }

          &.is-active {
            background-color: #ecf5ff;
            border-left: 3px solid #409eff;
          }

          .node-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
            color: #303133;
          }

          .node-group {
            font-size: 12px;
            color: #409eff;
            margin-bottom: 8px;
          }

          .node-bottom-info {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .node-type {
              .type-badge {
                padding: 4px 10px;
                border-radius: 12px;
                font-size: 13px;
                color: #fff;

                &.node-type-1 {
                  background-color: #409eff;
                }

                &.node-type-2 {
                  background-color: #67c23a;
                }

                &.node-type-3 {
                  background-color: #e6a23c;
                }
              }
            }

            .node-status {
              display: flex;
              align-items: center;
              font-size: 12px;
              color: #909399;
              margin-left: 8px;

              .status-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-right: 6px;
              }

              .status-running {
                background-color: #67c23a;
              }

              .status-disconnected {
                background-color: #f56c6c;
              }

              .status-error {
                background-color: #e6a23c;
              }

              .status-unknown {
                background-color: #909399;
              }
            }
          }
        }
      }
    }

    .node-detail {
      flex: 1;
      padding: 16px;
      overflow-y: auto;

      &.empty-detail {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .detail-section {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
        margin-bottom: 16px;
        overflow: hidden;

        .section-title {
          padding: 12px 16px;
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          border-bottom: 1px solid #f0f0f0;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .time-range-selector {
            width: 120px;
          }
        }

        .basic-info {
          padding: 16px;

          .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;

            .info-item {
              .info-label {
                font-size: 16px;
                color: #666;
                margin-bottom: 4px;
              }

              .info-value {
                font-size: 14px;
                color: #606266;

                &.node-title {
                  font-size: 16px;
                  font-weight: 500;
                  color: #f56c6c;
                }

                &.node-group {
                  font-size: 16px;
                  color: #409eff;
                }

                .status-badge {
                  display: inline-flex;
                  align-items: center;
                  padding: 4px 10px;
                  border-radius: 12px;
                  font-size: 13px;

                  &.status-running {
                    background-color: rgba(103, 194, 58, 0.1);
                    color: #67c23a;
                  }

                  &.status-disconnected {
                    background-color: rgba(245, 108, 108, 0.1);
                    color: #f56c6c;
                  }

                  &.status-error {
                    background-color: rgba(230, 162, 60, 0.1);
                    color: #e6a23c;
                  }

                  .status-dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    margin-right: 6px;
                  }
                }
              }
            }
          }
        }

        .resource-info {
          padding: 20px;
          display: flex;
          flex-wrap: wrap;

          .resource-item {
            flex: 1;
            min-width: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;

            .resource-detail {
              margin-top: 16px;
              text-align: center;

              .usage-info {
                margin-bottom: 4px;
                line-height: 1.2;

                .usage-label {
                  font-size: 12px;
                  color: #909399;
                  margin-right: 8px;
                }

                .usage-value {
                  font-size: 14px;
                  color: #606266;
                }
              }
            }

            .resource-name {
              margin-top: 10px;
              font-size: 14px;
              color: #606266;
            }
          }
        }

        .metrics-chart {
          padding: 0 16px 16px;

          .metrics-header {
            display: flex;
            padding: 20px 0;

            .metric-item {
              flex: 1;
              text-align: center;

              .metric-value {
                font-size: 24px;
                font-weight: 600;
                margin-bottom: 4px;
              }

              .metric-name {
                font-size: 14px;
              }
            }
          }

          .chart-container {
            height: 280px;
            width: 100%;
          }
        }
      }
    }
  }
</style>
