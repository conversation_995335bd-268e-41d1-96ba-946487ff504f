<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="任务号" prop="taskId">
          <el-input
              v-model="queryParams.taskId"
              placeholder="请输入任务号"
              clearable
              class="search-input"
          />
        </el-form-item>
        <el-form-item label="任务名称" prop="taskName">
          <el-input
              v-model="queryParams.taskName"
              placeholder="请输入任务名称"
              clearable
              class="search-input"
          />
        </el-form-item>
        <el-form-item label="发布时间" prop="createTime">
          <el-date-picker
              v-model="queryParams.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              class="search-date"
          />
        </el-form-item>
        <el-form-item label="发布人" prop="creator">
          <el-select
              v-model="queryParams.creator"
              placeholder="请选择发布人"
              clearable
              class="search-input"
          >
            <el-option
                v-for="creator in creatorOptions"
                :key="creator"
                :label="creator"
                :value="creator"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 系统状态信息 -->
    <div class="system-status-info">
      <div class="status-item">
        <span class="status-label">系统下载中任务数：</span>
        <span class="status-value">124</span>
      </div>
      <div class="status-item">
        <span class="status-label">待下载任务数：</span>
        <span class="status-value">54</span>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-container">
      <el-empty v-if="taskList.length === 0" description="暂无数据"/>

      <div v-else class="task-list">
        <div v-for="task in taskList" :key="task.taskId" class="task-card">
          <!-- 任务卡片头部 -->
          <div class="task-header">
            <div class="task-title">
              <el-tooltip :content="task.taskId" placement="top">
                <span class="task-id">{{ task.taskId }}</span>
              </el-tooltip>
              <span class="task-name">{{ task.taskName }}</span>
            </div>
            <div class="task-actions">
              <el-button type="primary" link @click="handleViewDetail(task)">查看详情</el-button>
              <el-button type="success" link @click="handleViewLog(task)">查看日志</el-button>
              <el-button
                  v-if="task.status !== '已完成'"
                  :type="task.status === '已暂停' ? 'success' : 'warning'"
                  link
                  @click="handleTaskControl(task)"
              >
                {{ task.status === '已暂停' ? '恢复任务' : '暂停任务' }}
              </el-button>
              <el-button type="info" link @click="handleDownloadResult(task)">下载结果</el-button>
              <el-popconfirm
                  title="确定要删除该任务吗？删除后无法恢复。"
                  @confirm="handleDeleteTask(task)"
              >
                <template #reference>
                  <el-button type="danger" link>删除</el-button>
                </template>
              </el-popconfirm>
            </div>
          </div>

          <!-- 任务卡片内容 -->
          <div class="task-content">
            <div class="task-info-row">
              <div class="task-info-item">
                <span class="info-label">文献总数：</span>
                <span class="info-value">{{ task.totalCount }}</span>
              </div>
              <div class="task-info-item">
                <span class="info-label">状态：</span>
                <el-tag :type="getStatusType(task.status)">{{ task.status }}</el-tag>
              </div>
              <div class="task-info-item">
                <span class="info-label">发布人：</span>
                <span class="info-value">{{ task.creator }}</span>
              </div>
              <div class="task-info-item">
                <span class="info-label">发布时间：</span>
                <span class="info-value">{{ task.createTime }}</span>
              </div>
            </div>

            <div class="task-info-row">
              <div class="task-info-item">
                <span class="info-label">最后更新：</span>
                <span class="info-value">{{ task.updateTime }}</span>
              </div>
              <div class="task-info-item">
                <span class="info-label">优先级：</span>
                <el-tag :type="getPriorityType(task.priority)" size="small">
                  {{ getPriorityText(task.priority) }}
                </el-tag>
              </div>
              <div class="task-info-item">
                <span class="info-label">测试任务：</span>
                <el-tag :type="task.isTest ? 'warning' : 'info'" size="small">
                  {{ task.isTest ? '是' : '否' }}
                </el-tag>
              </div>
              <div class="task-info-item">
                <span class="info-label">节点类型：</span>
                <div class="node-types">
                  <el-tag v-if="task.nodeTypes.includes('1')" type="primary" size="small">批次</el-tag>
                  <el-tag v-if="task.nodeTypes.includes('2')" type="success" size="small">源刊</el-tag>
                  <el-tag v-if="task.nodeTypes.includes('3')" type="warning" size="small">高校</el-tag>
                </div>
              </div>
            </div>

            <!-- 下载情况 -->
            <div class="download-status">
              <div class="download-header">
                <span class="info-label">下载情况：</span>
                <el-progress
                    :percentage="getCompletionRate(task)"
                    :status="getProgressStatus(task.status)"
                />
              </div>
              <div class="download-items">
                <div class="download-item">
                  <span class="item-label">存量库:</span>
                  <span class="item-value">{{ task.stockCount }}</span>
                </div>
                <div class="download-item">
                  <span class="item-label">已下载:</span>
                  <span class="item-value">{{ task.downloadedCount }}</span>
                </div>
                <div class="download-item">
                  <span class="item-label">下载失败:</span>
                  <span class="item-value">{{ task.failedCount }}</span>
                </div>
                <div class="download-item">
                  <span class="item-label">库中不存在:</span>
                  <span class="item-value">{{ task.notExistCount }}</span>
                </div>
                <div class="download-item">
                  <span class="item-label">待下载:</span>
                  <span class="item-value">{{ task.pendingCount }}</span>
                </div>
              </div>
            </div>

            <!-- 站点分组 -->
            <div class="site-group">
              <span class="info-label">站点分组：</span>
              <span class="info-value">{{ task.siteGroup || '不限' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 任务详情弹窗 -->
    <el-dialog v-model="detailDialog.visible" title="任务详情"  width="80%" append-to-body>
      <div class="detail-header">
        <div class="detail-search">
          <div class="search-controls">
            <el-input
              v-model="detailDialog.searchLiteratureId"
              placeholder="请输入文献ID"
              clearable
              style="width: 200px; margin-right: 10px;"
            />
            <el-select
              v-model="detailDialog.searchStatus"
              placeholder="选择状态"
              clearable
              style="width: 200px; margin-right: 10px;"
            >
              <el-option label="等待站点执行" value="waiting"/>
              <el-option label="正在执行" value="executing"/>
              <el-option label="执行失败" value="failed"/>
              <el-option label="存量库" value="stock"/>
              <el-option label="自动下载成功" value="success"/>
            </el-select>
            <el-button type="primary" @click="filterDetailList">搜索</el-button>
            <el-button @click="resetDetailFilter">重置</el-button>
          </div>

          <div class="detail-actions">
            <el-button type="danger" @click="handleDownloadFailedList">下载失败列表</el-button>
            <el-button type="success" @click="handleDownloadSuccessList">下载成功列表</el-button>
          </div>
        </div>
      </div>

      <el-table :data="filteredDetailList" style="width: 100%;margin-top: 10px" max-height="500">
        <el-table-column prop="literatureId" label="文献ID" width="120"/>
        <el-table-column prop="title" label="文献标题" min-width="180" :show-overflow-tooltip="true"/>
        <el-table-column prop="taskType" label="任务类型" width="120">
          <template #default="scope">
            <el-tag :type="getTaskTypeTag(scope.row.taskType)">{{ scope.row.taskType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="executeLog" label="执行情况" min-width="250" :show-overflow-tooltip="true">
          <template #default="scope">
            <div v-for="(log, index) in scope.row.executeLog" :key="index" class="execute-log-item">
              {{ log }}
            </div>
            <el-button v-if="scope.row.executeLog && scope.row.executeLog.length > 1" link type="primary"
                       @click="viewFullLog(scope.row)">
              查看完整日志
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="downloadStatus" label="下载状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.downloadStatus === '完成' ? 'success' : 'danger'">
              {{ scope.row.downloadStatus }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 日志弹窗 -->
    <el-dialog v-model="logDialog.visible" title="任务日志" append-to-body>
      <div class="log-content">
        <div v-for="(log, index) in logDialog.logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </el-dialog>

    <!-- 执行日志详情弹窗 -->
    <el-dialog v-model="fullLogDialog.visible" title="执行详情" width="700px" append-to-body>
      <div class="full-log-content">
        <div v-for="(log, index) in fullLogDialog.logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import {computed, onMounted, reactive, ref} from 'vue';
import {ElMessage} from 'element-plus';

// 查询参数
const queryParams = reactive({
  taskId: '',
  taskName: '',
  creator: '',
  dateRange: [],
  pageNum: 1,
  pageSize: 10
});

// 总数量
const total = ref(0);

// 任务列表
const taskList = ref([]);

// 任务详情弹窗
const detailDialog = reactive({
  visible: false,
  taskId: '',
  detailList: [],
  searchLiteratureId: '',
  searchStatus: '',
  filteredList: []
});

// 日志弹窗
const logDialog = reactive({
  visible: false,
  taskId: '',
  logs: []
});

// 执行日志详情弹窗
const fullLogDialog = reactive({
  visible: false,
  logs: []
});

// 获取所有发布人选项
const creatorOptions = computed(() => {
  const creators = new Set();
  mockTaskList.forEach(task => {
    if (task.creator) {
      creators.add(task.creator);
    }
  });
  return [...creators];
});

// 过滤后的详情列表
const filteredDetailList = computed(() => {
  if (!detailDialog.searchLiteratureId && !detailDialog.searchStatus) {
    return detailDialog.detailList;
  }

  return detailDialog.detailList.filter(item => {
    const matchLiteratureId = !detailDialog.searchLiteratureId ||
        item.literatureId.toLowerCase().includes(detailDialog.searchLiteratureId.toLowerCase());

    const matchStatus = !detailDialog.searchStatus ||
        (detailDialog.searchStatus === 'waiting' && item.taskType === '等待站点执行') ||
        (detailDialog.searchStatus === 'executing' && item.taskType === '正在执行') ||
        (detailDialog.searchStatus === 'failed' && item.taskType === '执行失败') ||
        (detailDialog.searchStatus === 'stock' && item.taskType === '存量库') ||
        (detailDialog.searchStatus === 'success' && item.taskType === '自动下载成功');

    return matchLiteratureId && matchStatus;
  });
});

// 模拟数据
const mockTaskList = [
  {
    taskId: 'D25ENPJDrofygMJf',
    taskName: '细胞免疫学文献集',
    totalCount: 230,
    stockCount: 50,
    downloadedCount: 120,
    failedCount: 20,
    notExistCount: 5,
    pendingCount: 35,
    status: '分配完成',
    creator: 'admin',
    createTime: '2025-05-10 14:22:47',
    updateTime: '2025-05-14 10:42:47',
    priority: 3,
    isTest: false,
    nodeTypes: ['1', '2'],
    siteGroup: '健康所分组'
  },
  {
    taskId: 'D25EMUtOhw7HYeOq',
    taskName: '神经科学领域综述文献',
    totalCount: 150,
    stockCount: 30,
    downloadedCount: 80,
    failedCount: 10,
    notExistCount: 8,
    pendingCount: 22,
    status: '已完成',
    creator: 'zhang.san',
    createTime: '2025-05-08 09:15:32',
    updateTime: '2025-05-13 16:30:11',
    priority: 2,
    isTest: false,
    nodeTypes: ['1', '2', '3'],
    siteGroup: '北京节点组'
  },
  {
    taskId: 'D25ELPwTcx6FzNio',
    taskName: '测试任务-心脏病学文献',
    totalCount: 80,
    stockCount: 15,
    downloadedCount: 25,
    failedCount: 15,
    notExistCount: 5,
    pendingCount: 20,
    status: '已暂停',
    creator: 'li.si',
    createTime: '2025-05-12 11:30:45',
    updateTime: '2025-05-14 08:20:33',
    priority: 1,
    isTest: true,
    nodeTypes: ['2'],
    siteGroup: null
  },
  {
    taskId: 'D25EKQrSbv5ExMhn',
    taskName: '肿瘤学研究进展',
    totalCount: 300,
    stockCount: 60,
    downloadedCount: 0,
    failedCount: 0,
    notExistCount: 0,
    pendingCount: 240,
    status: '待分配',
    creator: 'wang.wu',
    createTime: '2025-05-14 08:10:20',
    updateTime: '2025-05-14 08:10:20',
    priority: 89,
    isTest: false,
    nodeTypes: ['1', '3'],
    siteGroup: '上海高校组'
  },
  {
    taskId: 'D25EJRqRau4DwLgm',
    taskName: '药理学前沿综述',
    totalCount: 120,
    stockCount: 0,
    downloadedCount: 0,
    failedCount: 20,
    notExistCount: 10,
    pendingCount: 90,
    status: '分配失败',
    creator: 'admin',
    createTime: '2025-05-13 14:55:12',
    updateTime: '2025-05-14 07:30:25',
    priority: 2,
    isTest: false,
    nodeTypes: ['1', '2', '3'],
    siteGroup: null
  }
];

// 模拟任务详情数据
const mockDetailData = {
  'D25ENPJDrofygMJf': [
    {
      literatureId: '39817484',
      title: 'Immune Cell Profiling in Cancer Therapy Response',
      taskType: '执行失败',
      executeLog: [
        '[2025-05-14 10:38:37]   执行失败   21=BDP-ANT07-wangsisi(源刊节点)',
        '[2025-05-14 10:42:47]   执行失败   23=BDP-ANT03-lingyunchao（源刊）(源刊节点)'
      ],
      downloadStatus: '失败'
    },
    {
      literatureId: '39815672',
      title: 'T Cell Receptor Signaling in Autoimmunity',
      taskType: '自动下载成功',
      executeLog: [
        '[2025-05-14 09:15:22]   下载成功   18=BDP-ANT02-zhangming(批次节点)'
      ],
      downloadStatus: '完成'
    },
    {
      literatureId: 'PMC7654321',
      title: 'Cytokine Responses in Viral Infections',
      taskType: '存量库',
      executeLog: [
        '[2025-05-14 08:30:15]   已存在于存量库中'
      ],
      downloadStatus: '完成'
    }
  ],
  'D25EMUtOhw7HYeOq': [
    {
      literatureId: '39810234',
      title: 'Neural Circuit Mechanisms of Working Memory',
      taskType: '自动下载成功',
      executeLog: [
        '[2025-05-13 14:22:10]   下载成功   25=BDP-ANT05-liuwei(源刊节点)'
      ],
      downloadStatus: '完成'
    },
    {
      literatureId: '39809876',
      title: 'Synaptic Plasticity in Learning and Memory',
      taskType: '自动下载成功',
      executeLog: [
        '[2025-05-13 13:45:30]   下载成功   19=BDP-ANT03-chenli(批次节点)'
      ],
      downloadStatus: '完成'
    }
  ],
  'D25ELPwTcx6FzNio': [
    {
      literatureId: '39805432',
      title: 'Cardiac Remodeling After Myocardial Infarction',
      taskType: '等待站点执行',
      executeLog: [],
      downloadStatus: '失败'
    },
    {
      literatureId: '39804321',
      title: 'Mechanisms of Heart Failure Development',
      taskType: '正在执行',
      executeLog: [
        '[2025-05-14 08:15:22]   正在执行   29=BDP-ANT09-wangyan(源刊节点)'
      ],
      downloadStatus: '失败'
    }
  ]
};

// 模拟任务日志数据
const mockLogData = {
  'D25ENPJDrofygMJf': [
    '[2025-05-14 14:44:24] 站点 26, 传递文献 failed: D25ENPJDrofygMJf 39817484',
    '[2025-05-14 14:44:21] 站点 36, 传递文献 failed: D25ENPJDrofygMJf 39817484',
    '[2025-05-14 14:42:21] 站点 36 启动脚本下载 jur_5720.pyscript TaskId: D25ENPJDrofygMJf PMID: 39817484',
    '[2025-05-14 14:42:21] 站点 26 启动脚本下载 jur_5720.pyscript TaskId: D25ENPJDrofygMJf PMID: 39817484',
    '[2025-05-14 14:42:21] 站点 36 获取到 TaskID: D25ENPJDrofygMJf PMID： 39817484 传递任务，开始执行传递任务**********',
    '[2025-05-14 14:42:21] 站点 26 获取到 TaskID: D25ENPJDrofygMJf PMID： 39817484 传递任务，开始执行传递任务**********',
    '[2025-05-14 10:42:47] 站点 23 下载文献失败: D25ENPJDrofygMJf 39817484',
    '[2025-05-14 10:38:37] 站点 21 下载文献失败: D25ENPJDrofygMJf 39817484',
    '[2025-05-14 09:15:22] 站点 18 下载文献成功: D25ENPJDrofygMJf 39815672',
    '[2025-05-14 08:30:15] 文献存在于存量库: D25ENPJDrofygMJf PMC7654321'
  ]
};

// 初始查询任务列表
const getTaskList = () => {
  // 在实际应用中，这里应该调用API获取数据
  const filteredList = mockTaskList.filter(task => {
    const matchTaskId = !queryParams.taskId || task.taskId.includes(queryParams.taskId);
    const matchTaskName = !queryParams.taskName || task.taskName.includes(queryParams.taskName);
    const matchCreator = !queryParams.creator || task.creator.includes(queryParams.creator);

    // 日期范围过滤
    let matchDate = true;
    if (queryParams.dateRange && queryParams.dateRange.length === 2) {
      const startDate = new Date(queryParams.dateRange[0]);
      const endDate = new Date(queryParams.dateRange[1]);
      endDate.setHours(23, 59, 59, 999); // 将结束日期设置为当天的最后一刻

      const createDate = new Date(task.createTime);
      matchDate = createDate >= startDate && createDate <= endDate;
    }

    return matchTaskId && matchTaskName && matchCreator && matchDate;
  });

  total.value = filteredList.length;

  // 模拟分页
  const startIndex = (queryParams.pageNum - 1) * queryParams.pageSize;
  const endIndex = startIndex + queryParams.pageSize;
  taskList.value = filteredList.slice(startIndex, endIndex);
};

// 查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  getTaskList();
};

// 重置
const resetQuery = () => {
  queryParams.taskId = '';
  queryParams.taskName = '';
  queryParams.creator = '';
  queryParams.dateRange = [];
  handleQuery();
};

// 分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size;
  getTaskList();
};

// 页码变化
const handleCurrentChange = (page) => {
  queryParams.pageNum = page;
  getTaskList();
};

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case '已完成':
      return 'success';
    case '已暂停':
      return 'info';
    case '分配失败':
      return 'danger';
    case '待分配':
      return 'warning';
    case '分配中':
      return '';
    case '分配完成':
      return 'success';
    default:
      return '';
  }
};

// 获取优先级类型
const getPriorityType = (priority) => {
  if (priority === 3) return 'danger';
  if (priority === 2) return '';
  if (priority >= 70) return 'warning';
  return 'info';
};

// 获取优先级文本
const getPriorityText = (priority) => {
  if (priority === 3) return '高';
  if (priority === 2) return '普通';
  return priority.toString();
};

// 获取完成率
const getCompletionRate = (task) => {
  const {totalCount, pendingCount} = task;
  if (totalCount === 0) return 0;
  if (pendingCount === 0) return 100; // 如果没有待下载的，进度为100%
  return Math.round((totalCount - pendingCount) * 100 / totalCount);
};

// 获取进度状态
const getProgressStatus = (status) => {
  if (status === '已完成') return 'success';
  if (status === '已暂停' || status === '分配失败') return 'exception';
  return '';
};

// 获取任务类型标签样式
const getTaskTypeTag = (type) => {
  switch (type) {
    case '自动下载成功':
      return 'success';
    case '存量库':
      return 'info';
    case '正在执行':
      return 'warning';
    case '执行失败':
      return 'danger';
    case '等待站点执行':
      return '';
    default:
      return '';
  }
};

// 处理查看详情
const handleViewDetail = (task) => {
  detailDialog.taskId = task.taskId;
  // 模拟从API获取详情数据
  detailDialog.detailList = mockDetailData[task.taskId] || [];
  detailDialog.searchLiteratureId = '';
  detailDialog.searchStatus = '';
  detailDialog.visible = true;
};

// 处理下载结果
const handleDownloadResult = (task) => {
  ElMessage.success('数据下载链接已经发送到您的邮箱');
};

// 处理查看日志
const handleViewLog = (task) => {
  logDialog.taskId = task.taskId;
  // 模拟从API获取日志数据
  logDialog.logs = mockLogData[task.taskId] || [];
  logDialog.visible = true;
};

// 处理任务控制（暂停/恢复）
const handleTaskControl = (task) => {
  const action = task.status === '已暂停' ? '恢复' : '暂停';
  // 在实际应用中，这里应该调用API暂停或恢复任务
  ElMessage.success(`${action}任务成功`);

  // 模拟更新状态
  if (task.status === '已暂停') {
    task.status = '分配完成';
  } else {
    task.status = '已暂停';
  }
};

// 处理删除任务
const handleDeleteTask = (task) => {
  // 在实际应用中，这里应该调用API删除任务
  ElMessage.success('删除任务成功');
  taskList.value = taskList.value.filter(item => item.taskId !== task.taskId);
  total.value--;
};

// 过滤详情列表
const filterDetailList = () => {
  // 通过computed属性自动过滤
};

// 重置详情过滤
const resetDetailFilter = () => {
  detailDialog.searchLiteratureId = '';
  detailDialog.searchStatus = '';
};

// 查看完整执行日志
const viewFullLog = (row) => {
  fullLogDialog.logs = row.executeLog;
  fullLogDialog.visible = true;
};

// 处理下载失败列表
const handleDownloadFailedList = () => {
  const failedList = detailDialog.detailList.filter(item => item.downloadStatus === '失败');
  if (failedList.length === 0) {
    ElMessage.warning('当前没有下载失败的文献');
    return;
  }

  // 在实际应用中，这里应该调用API下载失败列表
  ElMessage.success(`已生成${failedList.length}条失败记录的下载文件，链接已发送到您的邮箱`);
};

// 处理下载成功列表
const handleDownloadSuccessList = () => {
  const successList = detailDialog.detailList.filter(item => item.downloadStatus === '完成');
  if (successList.length === 0) {
    ElMessage.warning('当前没有下载成功的文献');
    return;
  }

  // 在实际应用中，这里应该调用API下载成功列表
  ElMessage.success(`已生成${successList.length}条成功记录的下载文件，链接已发送到您的邮箱`);
};

onMounted(() => {
  getTaskList();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  padding-bottom: 40px;
}

.search-container {
  padding: 20px;
  /*background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);*/
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.search-input {
  width: 220px;
}

.search-date {
  width: 360px;
}

.task-list-container {
  /*  background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    padding: 0 0 20px 0;*/
}

.task-list {
  padding: 20px;
}

.task-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
  border: 1px solid #ebeef5;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.task-title {
  display: flex;
  align-items: center;

  .task-id {
    font-family: monospace;
    background-color: #ebeef5;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 14px;
    margin-right: 10px;
    color: #606266;
  }

  .task-name {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }
}

.task-actions {
  display: flex;
  gap: 10px;
}

.task-content {
  padding: 20px;
}

.task-info-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.task-info-item {
  min-width: 25%;
  margin-bottom: 10px;
  display: flex;
  align-items: center;

  .info-label {
    color: #909399;
    margin-right: 5px;
  }

  .info-value {
    color: #606266;
  }

  .node-types {
    display: flex;
    gap: 5px;
  }
}

.download-status {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.download-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  .info-label {
    margin-right: 10px;
    color: #909399;
  }

  .el-progress {
    flex: 1;
  }
}

.download-items {
  display: flex;
  flex-wrap: wrap;
  gap: 10px 30px;
}

.download-item {
  display: flex;
  align-items: center;

  .item-label {
    color: #909399;
    margin-right: 5px;
  }

  .item-value {
    margin-top: 3px;
    color: #606266;
    font-weight: 500;
  }
}

.site-group {
  display: flex;
  align-items: center;

  .info-label {
    color: #909399;
    margin-right: 5px;
  }

  .info-value {
    color: #606266;
  }
}

.pagination-container {
  padding: 0 20px;
  display: flex;
  justify-content: flex-end;
}

.log-content, .full-log-content {
  height: 500px;
  overflow-y: auto;
  font-family: monospace;
  background-color: #292929;
  color: #f0f0f0;
  padding: 15px;
  border-radius: 4px;
}

.log-item {
  line-height: 1.6;
  padding: 3px 0;
}

.execute-log-item {
  line-height: 1.4;
  margin-bottom: 4px;
  font-size: 12px;
  color: #606266;
}

.detail-header {
  margin-bottom: 15px;

  .detail-search {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .search-controls {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
    }

    .detail-actions {
      display: flex;
      gap: 10px;
    }
  }
}

.system-status-info {
  padding: 10px 20px;
  margin: 0 20px;
  background-color: #f2f6fc;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 40px;

  .status-item {
    display: flex;
    align-items: center;

    .status-label {
      color: #606266;
      font-size: 14px;
    }

    .status-value {
      color: #409EFF;
      font-weight: 600;
      font-size: 16px;
      margin-left: 5px;
    }
  }
}
</style>
