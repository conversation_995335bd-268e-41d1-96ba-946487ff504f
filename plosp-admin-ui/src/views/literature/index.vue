<template>
  <div class="app-container">
    <!-- 搜索和工具栏 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="PMID" prop="pmid">
        <el-input type="number" v-model="queryParams.pmid" placeholder="请输入PMID" clearable class="search-input" @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="PMCID" prop="pmcId">
        <el-input type="number" v-model="queryParams.pmcId" placeholder="请输入PMCID" clearable class="search-input" @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="自定义ID" prop="customId">
        <el-input type="number" v-model="queryParams.customId" placeholder="请输入自定义ID" clearable class="search-input" @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="DOI" prop="doi">
        <el-input v-model="queryParams.doi" placeholder="请输入DOI" clearable class="search-input" @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="文献标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入文献标题" clearable class="search-input" @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="期刊名称" prop="journalName">
        <el-input v-model="queryParams.journalName" placeholder="请输入期刊名称" clearable class="search-input" @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="卷" prop="volume">
        <el-input v-model="queryParams.volume" placeholder="请输入卷号" clearable class="search-input" @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="发表时间" prop="publishedTime">
        <el-date-picker
            v-model="dateRange"
            class="search-input"
            style="width: 240px;"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作工具栏 -->
    <el-row :gutter="10" class="mb8 mt10">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Upload" @click="handleImport">导入文献题录</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Upload" @click="handleBatchUpload">批量上传附件</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Download" @click="handleExport" :disabled="multiple">
          导出附件{{ ids.length > 0 ? `(${ids.length})` : '' }}
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Select" @click="handleSelectAll">{{ isAllSelected ? '取消全选' : '全选' }}</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getArticleList"></right-toolbar>
    </el-row>

    <!-- 文献列表 -->
    <div class="article-list" v-loading="loading">
      <el-empty v-if="articleList.length === 0" description="暂无数据"/>
      <div v-else>
        <div class="article-items">
          <div class="article-item" v-for="(item, index) in articleList" :key="index">
            <div class="item-left">
              <el-checkbox v-model="item.selected" @change="handleSelectionChange"/>
              <div class="source-tags">
                <el-tag v-if="item.source && item.source.includes('PubMed')" type="primary">PubMed</el-tag>
                <el-tag v-if="item.source && item.source.includes('PMC')" type="success">PMC</el-tag>
                <el-tag v-if="item.source && item.source.includes('BioRxiv')" type="warning">BioRxiv</el-tag>
                <el-tag v-if="item.source && item.source.includes('MedRxiv')" type="info">MedRxiv</el-tag>
              </div>
            </div>
            <div class="item-content">
              <div class="title-row">
                <div v-if="item.existPdf" class="pdf-link" @click="handleDownloadPdf(item)">
                  <el-icon>
                    <Document/>
                  </el-icon>
                  <span>PDF</span>
                </div>
                <div class="item-title" @click="handleView(item)" v-html="item.title"></div>
              </div>
              <div class="item-authors">{{ formatAuthors(item.author) }}</div>
              <div class="item-info">
                <span class="journal">{{ item.journalName }}</span>
                <span class="date">{{ item.year }}{{
                    item.volume ? '; ' + item.volume : ''
                  }}{{ item.issue ? '(' + item.issue + ')' : '' }}:{{ item.page || '' }}</span>
                <div class="item-ids">
                  <span class="id-item" v-if="item.pmid">
                    <span class="id-label">PMID: </span>
                    <span class="id-value"><a :href="'https://pubmed.ncbi.nlm.nih.gov/' + item.pmid" target="_blank">{{
                        item.pmid
                      }}</a></span>
                  </span>
                  <span class="id-item" v-if="item.pmcId">
                    <span class="id-label">PMCID: </span>
                    <span class="id-value"><a :href="'https://pmc.ncbi.nlm.nih.gov/articles/PMC' + item.pmcId" target="_blank">PMC{{
                        item.pmcId
                      }}</a></span>
                  </span>
                  <span class="id-item" v-if="item.doi">
                    <span class="id-label">DOI: </span>
                    <span class="id-value"><a :href="'https://doi.org/' + item.doi" target="_blank">{{
                        item.doi
                      }}</a></span>
                  </span>
                </div>
              </div>
            </div>
            <div class="item-right">
              <div class="action-buttons">
                <el-button type="success" link icon="Edit" @click="handleEdit(item)">编辑</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页组件 -->
        <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getArticleList"
        />
      </div>
    </div>

    <!-- 文献详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="文献详情" append-to-body>
      <el-descriptions :column="2" border direction="vertical">
        <!-- 标题 -->
        <el-descriptions-item label="标题" :span="2">{{ detailData.title || '-' }}</el-descriptions-item>

        <!-- 标识符 - 每行两个项目 -->
        <el-descriptions-item label="PMID">{{ detailData.pmid || '-' }}</el-descriptions-item>
        <el-descriptions-item label="PMCID">{{ detailData.pmcId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="DOI">{{ detailData.doi || '-' }}</el-descriptions-item>
        <el-descriptions-item label="自定义ID">{{ detailData.customId || '-' }}</el-descriptions-item>

        <!-- 基本信息 -->
        <el-descriptions-item label="作者" :span="2">
          {{ formatArticleAuthors(detailData.authorInfo) || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="单位" :span="2">
          {{ formatAffiliations(detailData.affiliation) || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="期刊" :span="2">{{ detailData.journalName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="发表信息" :span="2">
          {{ detailData.publishedYear || '-' }}
          {{ detailData.volume ? ',' + detailData.volume : '' }}
          {{ detailData.issue ? '(' + detailData.issue + ')' : '' }}
          {{ detailData.page ? ':' + detailData.page : '' }}
        </el-descriptions-item>

        <!-- ISSN信息 - 每行两个项目 -->
        <el-descriptions-item label="ISSN Print">{{ detailData.issnPrint || '-' }}</el-descriptions-item>
        <el-descriptions-item label="ISSN Electronic">{{ detailData.issnElectronic || '-' }}</el-descriptions-item>

        <!-- 摘要 - 占一整行 -->
        <el-descriptions-item label="摘要" :span="2">
          <div v-html="detailData.articleAbstract || '暂无摘要'"></div>
        </el-descriptions-item>

        <!-- 系统信息 - 每行两个项目 -->
        <el-descriptions-item label="入库时间">{{ detailData.createTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="最后修改时间">{{ detailData.updateTime || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 文献编辑弹窗 -->
    <el-dialog v-model="editDialogVisible" title="编辑文献" width="80vw" append-to-body>
      <el-form ref="editFormRef" :model="editDialogForm" label-width="120px" :rules="rules">

        <!-- 标题 -->
        <el-form-item label="标题" prop="title">
          <el-input v-model="editDialogForm.title" type="text" placeholder="请输入文献标题"/>
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="PMID" prop="pmid">
              <el-input v-model="editDialogForm.pmid" type="number" placeholder="请输入PMID"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="PMCID" prop="pmcId">
              <el-input v-model="editDialogForm.pmcId" type="number" placeholder="请输入PMCID"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">

            <el-form-item label="DOI" prop="doi">
              <el-input v-model="editDialogForm.doi" placeholder="请输入DOI"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="自定义ID" prop="customId">
              <el-input v-model="editDialogForm.customId" type="number" placeholder="请输入自定义ID"/>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 标识符区域 -->
        <el-row>
          <el-col :span="12">

            <el-form-item label="ISSN Print" prop="issnPrint">
              <el-input v-model="editDialogForm.issnPrint" placeholder="ISSN Print" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="ISSN Electronic" prop="issnElectronic">
              <el-input v-model="editDialogForm.issnElectronic" placeholder="ISSN Electronic" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="期刊" prop="journalName" class="full-width">
          <el-select
              v-model="editDialogForm.journalId"
              v-loadMore="handleLoadMore"
              clearable
              filterable
              remote
              reserve-keyword
              placeholder="Please select Journal"
              style="width: 100%"
              class="select-v2 w-100"
              :remote-method="
                (queryString, cb) => {
                  remoteSelect(queryString);
                }
              "
              :popper-class="'page-select'"
              :popper-class-name="'page-select'"
              :loading="selectLoading"
              @visible-change="handleVisibleChange"
          >
            <el-option
                :key="'key-1'"
                :label="editDialogForm.journalName"
                :value="editDialogForm.journalId"
            >
            </el-option>
            <el-option
                v-for="(item, idx) in selectOptions"
                :key="item.id"
                :label="item.title"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发表年" prop="publishedYear" class="full-width">
          <div class="publish-info-row">
            <el-input v-model="editDialogForm.publishedYear" type="number" placeholder="年份" style="width: 20%"/>
            卷
            <el-input v-model="editDialogForm.volume" placeholder="volume" style="width: 20%"/>
            期
            <el-input v-model="editDialogForm.issue" placeholder="issue" style="width: 20%"/>
            页码
            <el-input v-model="editDialogForm.page" placeholder="page" style="width: 20%"/>
          </div>
        </el-form-item>
        <el-form-item label="单位" prop="organizations" class="full-width">
          <el-table :data="editDialogForm.organizations" border max-height="500">
            <el-table-column label="单位名称">
              <template #default="scope">
                <el-input v-model="scope.row.name" placeholder="请输入组织名称"/>
              </template>
            </el-table-column>
            <el-table-column width="100" align="right">
              <template #default="scope">
                <el-button type="primary" circle icon="Plus" size="small" @click="addOrganization(scope.$index)"/>
                <el-button
                    v-if="editDialogForm.organizations.length > 1"
                    type="danger"
                    circle
                    icon="Delete"
                    size="small"
                    @click="removeOrganization(scope.$index)"
                />
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="作者" prop="authorList" class="full-width">
          <el-table :data="editDialogForm.authorList" border style="width: 100%" max-height="500">
            <el-table-column width="200">
              <template #header>
                <span>姓氏 <span style="color: #f56c6c;">*</span></span>
              </template>
              <template #default="scope">
                <el-input v-model="scope.row.lastname" placeholder="姓氏（必填）"/>
              </template>
            </el-table-column>
            <el-table-column width="200">
              <template #header>
                <span>名字 <span style="color: #f56c6c;">*</span></span>
              </template>
              <template #default="scope">
                <el-input v-model="scope.row.forename" placeholder="名字（必填）"/>
              </template>
            </el-table-column>
            <el-table-column label="邮箱" width="200">
              <template #default="scope">
                <el-input v-model="scope.row.email" placeholder="邮箱"/>
              </template>
            </el-table-column>
            <el-table-column label="单位">
              <template #default="scope">
                <el-select
                    v-model="scope.row.organizationIds"
                    multiple
                    collapse-tags-tooltip
                    :max-collapse-tags="2"
                    filterable
                    placeholder="请选择单位"
                    style="width: 100%"
                    popper-class="org-select-dropdown"
                >
                  <el-option
                      v-for="org in editDialogForm.organizations"
                      :key="org.id"
                      :label="org.name"
                      :value="org.id"
                  >

                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column width="100" align="right">
              <template #default="scope">
                <el-button type="primary" circle icon="Plus" size="small" @click="addAuthor(scope.$index)"/>
                <el-button
                    v-if="editDialogForm.authorList.length > 1"
                    type="danger"
                    circle
                    icon="Delete"
                    size="small"
                    @click="removeAuthor(scope.$index)"
                />
              </template>
            </el-table-column>
          </el-table>
          </el-form-item>
        <el-form-item label="摘要" prop="articleAbstract" width="100%">
          <Editor v-model="editDialogForm.articleAbstract" :min-height="160"/>
          </el-form-item>
        <!-- PDF文件上传 -->
        <el-form-item label="PDF文件">
          <el-upload
              ref="pdfUploadRef"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :data="{ docId: editDialogForm.id, fileType: 'PDF' }"
              :auto-upload="false"
              :on-change="(file) => handleFileChange(file, 'PDF')"
              :on-success="(response) => handleUploadSuccess(response, 'PDF')"
              :on-error="handleUploadError"
              :before-remove="(file) => handleBeforeFileRemove(file, 'PDF')"
              :on-preview="handleFilePreview"
              :limit="1"
              accept=".pdf"
              :file-list="pdfFileList"
          >
            <template #trigger>
              <el-button type="primary" size="small">选择PDF文件</el-button>
            </template>
            <el-button v-if="hasPdfToUpload" type="success" size="small" @click="submitUpload('pdfUploadRef')" style="margin-left: 10px;">
              上传
            </el-button>
          </el-upload>
        </el-form-item>

        <!-- 补充文件上传 -->
        <el-form-item label="SUPP文件">
          <el-upload
              ref="suppUploadRef"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :data="{ docId: editDialogForm.id, fileType: 'SUPP' }"
              :auto-upload="false"
              :on-change="(file) => handleFileChange(file, 'SUPP')"
              :on-success="(response) => handleUploadSuccess(response, 'SUPP')"
              :on-error="handleUploadError"
              :before-remove="(file) => handleBeforeFileRemove(file, 'SUPP')"
              :on-preview="handleFilePreview"
              multiple
              :file-list="suppFileList"
          >
            <template #trigger>
              <el-button type="primary" size="small">选择补充文件</el-button>
            </template>
            <el-button v-if="hasSuppToUpload" type="success" size="small" @click="submitUpload('suppUploadRef')" style="margin-left: 10px;">
              上传
            </el-button>
          </el-upload>
        </el-form-item>


      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitEdit">保存</el-button>
          <el-button @click="cancelEdit">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量上传附件对话框 -->
    <el-dialog
        v-model="upload.open"
        :title="upload.title"
        width="400px"
        append-to-body
    >
      <el-upload
          ref="uploadRef"
          :limit="10"
          accept=".zip"
          :headers="upload.headers"
          :action="upload.url"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <el-icon class="el-icon--upload">
          <upload-filled/>
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>格式说明：请将文件整理到如下目录层级中：[PMID_xxx]|[PMCID_xxx]|[CUSTOMID_xxx]/[PDF]|[SUPP],然后将整理好的多个目录压缩成zip文件上传</span>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Article">
import {Document, UploadFilled} from '@element-plus/icons-vue';
import Editor from '@/components/Editor/index.vue';
import {deleteAttachment, getArticle, listArticle, updateArticle} from "@/api/article/article.js";
import {listJournal} from "@/api/article/journal.js";
import {debounce, isStrBlank} from "@/utils/index.js";
import {getToken} from '@/utils/auth';
import {computed} from 'vue';
import {useRouter} from 'vue-router';
import {downloadUseForm} from "@/utils/download.js";


const router = useRouter();
const {proxy} = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 文献表格数据
const articleList = ref([]);
// 日期范围
const dateRange = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  pmid: null,
  pmcId: null,
  customId: null,
  doi: null,
  title: undefined,
  journalName: null,
  volume: undefined,
});

// 文献详情弹窗
const detailDialogVisible = ref(false);
const detailData = ref({});

// 文件上传相关数据
const pdfFileList = ref([]);
const suppFileList = ref([]);

// 上传配置
const uploadUrl = import.meta.env.VITE_APP_BASE_API + '/article/uploadAttachment';
const uploadHeaders = {
  Authorization: 'Bearer ' + getToken()
};

// 批量上传附件参数
const upload = reactive({
  // 是否显示弹出层
  open: false,
  // 弹出层标题
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: {Authorization: 'Bearer ' + getToken()},
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + '/article/batchUploadArticleAttachment'
});

// 判断是否有未上传的文件（修改为计算属性）
const hasPdfToUpload = computed(() => {
  return pdfFileList.value.some(file => file.status !== 'success');
});

const hasSuppToUpload = computed(() => {
  return suppFileList.value.some(file => file.status !== 'success');
});

// 全选状态计算属性
const isAllSelected = computed(() => {
  return articleList.value.length > 0 && articleList.value.every(item => item.selected);
});


// 生成随机ID
function generateRandomId() {
  return 'org_' + Math.random().toString(36).substr(2, 9);
}

// 文献编辑弹窗
const editDialogVisible = ref(false);
const editDialogForm = ref({
  id: null,
  title: null,
  pmid: null,
  pmcId: null,
  doi: null,
  customId: null,
  issnPrint: null,
  issnElectronic: null,
  authors: [],
  affiliation: null,
  journalId: '',
  journalName: '',
  publishedYear: null,
  publishedMonth: null,
  publishedDay: null,
  volume: null,
  issue: null,
  page: null,
  articleAbstract: null,
  hasPdf: false,
  source: [],
  organizations: [],
  authorList: [],
});

// 表单校验规则
const rules = {
  title: [{required: true, message: '请输入文献标题', trigger: 'blur'}],
  journalName: [{required: true, message: '请输入期刊名称', trigger: 'blur'}]
};

/** 查询文献列表 */
function getArticleList() {
  loading.value = true;
  listArticle(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    // 为每个文章项添加selected属性
    articleList.value = response.rows.map(item => ({
      ...item,
      selected: false
    }));
    total.value = response.total;
    loading.value = false;
    // 重置选中状态
    handleSelectionChange();
  }).catch(error => {
    console.error("获取文献列表失败:", error);
    loading.value = false;
    proxy.$modal.msgError("获取文献列表失败");
  });
}

// 格式化作者名称
function formatAuthors(authors) {
  if (!authors || authors.length === 0) return '-';
  if (authors.length <= 3) return authors.join(', ');
  return `${authors[0]}, ${authors[1]}, ${authors[2]}, et al.`;
}


// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1;
  getArticleList();
}

// 重置按钮操作
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryForm");
  queryParams.value.pageNum = 1;
  getArticleList();
}

// 多选框选中数据
function handleSelectionChange() {
  ids.value = articleList.value.filter(item => item.selected).map(item => item.id);
  single.value = ids.value.length !== 1;
  multiple.value = !ids.value.length;
}

// 全选/取消全选操作
function handleSelectAll() {
  const shouldSelectAll = !isAllSelected.value;
  articleList.value.forEach(item => {
    item.selected = shouldSelectAll;
  });
  handleSelectionChange();
}


/** 查看按钮操作 - 修改为打开详情弹窗 */
function handleView(row) {
  detailDialogVisible.value = true;

  // 使用API获取文献详情
  getArticle(row.id).then(response => {
    detailData.value = response.data;
  }).catch(() => {
    // 如果API调用失败，使用传入的行数据作为备选
    detailData.value = row;
  });
}

/** 下载PDF操作 */
function handleDownloadPdf(row) {
  downloadUseForm(`/article/file/downloadPdf?id=${row.id}`);
  proxy.$modal.msgSuccess(`开始下载PDF文件`);
}

/** 导入文献题录操作 */
function handleImport() {
  router.push('/literature/import');
}

/** 批量上传附件操作 */
function handleBatchUpload() {
  upload.title = "批量上传附件";
  upload.open = true;
}

/**
 * 文件上传中处理
 */
function handleFileUploadProgress() {
  upload.isUploading = true;
}

/**
 * 文件上传成功处理
 */
function handleFileSuccess(response, file, fileList) {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs['uploadRef'].handleRemove(file);

  if (response.data && response.data.length !== 0) {
    let messages = response.data.map(it => it.message);
    proxy.$modal.alertError(
        `<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>${messages.join('<br>')}</div>`,
        true
    );
    getArticleList();
  }
}

/**
 * 提交上传文件
 */
function submitFileForm() {
  proxy.$refs['uploadRef'].submit();
}

/** 导出附件操作 */
function handleExport() {
  if (ids.value.length === 0) {
    proxy.$modal.msgError('请先选择要导出附件的文献');
    return;
  }

  if (ids.value.length > 100) {
    proxy.$modal.msgError('最多只能选择100条文献进行导出');
    return;
  }

  proxy.$modal.confirm(`是否确认导出选中的${ids.value.length}条文献的附件?`).then(() => {
    // 使用POST请求发送ids数组
    downloadUseForm('/article/downloadArticleAttachmentByIds', { ids: ids.value }, 'post');
    proxy.$modal.msgSuccess(`开始导出${ids.value.length}条文献的附件`);
  }).catch(() => {
    // 用户取消操作
  });
}

// 处理文件选择变化
function handleFileChange(file, fileType) {
  // 文件选择后更新对应的文件列表，但不自动上传
  console.log(`${fileType} 文件已选择:`, file.name);

  // 确保新选择的文件状态不是success，并且文件列表中不存在该文件
  if (file.status !== 'success') {
    // 直接使用file对象，而不是file.raw
    const fileItem = {
      name: file.name,
      uid: file.uid,
      status: 'ready',
      raw: file.raw
    };

    if (fileType === 'PDF') {
      // PDF只允许一个文件，直接替换
      pdfFileList.value = [fileItem];
    } else if (fileType === 'SUPP') {
      // SUPP可以有多个文件，添加到列表中
      if (!suppFileList.value.some(f => f.uid === file.uid)) {
        suppFileList.value.push(fileItem);
      }
    }
  }
}

// 提交上传
function submitUpload(refName) {
  if (!editDialogForm.value.id) {
    proxy.$modal.msgError('文献ID不能为空');
    return;
  }
  proxy.$refs[refName].submit();
}

// 上传成功回调
function handleUploadSuccess(response, fileType) {
  if (response.code === 200) {
    proxy.$modal.msgSuccess('上传成功');

    // 更新文件列表为回显状态
    let fileList = response.data.map(it => {
      return {
        name: it.fileName,
        url: `/article/file/download/${it.id}`,
        uid: it.id,
        status: 'success'
      }
    });
    if (fileType === 'PDF') {
      pdfFileList.value = fileList;
    } else if (fileType === 'SUPP') {
      // SUPP文件：移除待上传的文件，添加已上传的文件
      // 先移除状态为ready的文件
      suppFileList.value = suppFileList.value.filter(file => file.status === 'success');
      // 添加新上传的文件
      suppFileList.value.push(...fileList);
    }
  } else {
    proxy.$modal.msgError(response.msg || '上传失败');
  }
}

// 上传失败回调
function handleUploadError(error) {
  console.error('上传失败:', error);
  proxy.$modal.msgError('上传失败，请重试');
}

// 文件删除前确认
function handleBeforeFileRemove(file, fileType) {
  // 如果是已上传的文件（status为success），需要确认删除
  if (file.status === 'success') {
    return proxy.$modal.confirm(`确定要删除该${file.name}文件吗？删除后将无法恢复。`)
        .then(() => {
          // 确认删除后，发送删除请求到后台
          return deleteAttachment(file.uid);
        })
        .then(() => {
          proxy.$modal.msgSuccess('删除成功');
          return true;
        })
        .catch(() => {
          return false; // 取消删除或删除失败
        });
  } else {
    // 如果是新选择但未上传的文件，直接删除
    return true;
  }
}

// 处理作者和组织相关的方法
function addOrganization(index) {
  // 添加带有随机ID的组织
  editDialogForm.value.organizations.splice(index + 1, 0, {
    id: generateRandomId(),
    name: ''
  });
}

function removeOrganization(index) {
  // 获取要删除的组织
  const orgToRemove = editDialogForm.value.organizations[index];

  // 从组织列表中删除该组织
  editDialogForm.value.organizations.splice(index, 1);

  // 更新作者的组织关联，移除引用已删除组织的关联
  editDialogForm.value.authorList.forEach(author => {
    if (author.organizationIds) {
      // 移除已删除组织的ID
      author.organizationIds = author.organizationIds.filter(id => id !== orgToRemove.id);
    }
  });
}

function addAuthor(index) {
  editDialogForm.value.authorList.splice(index + 1, 0, {
    lastname: '',
    forename: '',
    email: '',
    organizationIds: []
  });
}

function removeAuthor(index) {
  editDialogForm.value.authorList.splice(index, 1);
}

/** 编辑按钮操作 */
function handleEdit(row) {
  // 先显示loading
  proxy.$modal.loading("正在加载文献详情...");

  // 获取文献详情
  getArticle(row.id).then(response => {
    const data = response.data;

    // 提取组织数据
    let organizations = [];
    let orgId = new Set()
    let authorList = [];

    // 从authorInfo字段加载数据
    if (data.authorInfo) {
      data.authorInfo.forEach(authorInfo => {
        // 提取机构信息
        if (authorInfo.organizations) {
          authorInfo.organizations.forEach(orgName => {
            if (!orgId.has(orgName)) {
              organizations.push({
                id: generateRandomId(),
                name: orgName
              })
              orgId.add(orgName)
            }
          })
        }

        // 构建作者信息
        authorList.push({
          lastname: authorInfo.lastname,
          forename: authorInfo.forename,
          email: authorInfo.email,
          organizationIds: authorInfo.organizations ? authorInfo.organizations.map(orgName => {
            // 找到对应的组织ID
            const org = organizations.find(o => o.name === orgName);
            return org ? org.id : generateRandomId();
          }) : []
        })
      })
    }

    // 确保至少有一个空的组织和作者
    if (organizations.length === 0) {
      organizations.push({
        id: generateRandomId(),
        name: ''
      });
    }

    if (authorList.length === 0) {
      authorList.push({
        lastname: '',
        forename: '',
        email: '',
        organizationIds: []
      });
    }

    editDialogForm.value = {
      id: data.id,
      title: data.title,
      pmid: data.pmid,
      pmcId: data.pmcId,
      doi: data.doi,
      customId: data.customId,
      journalId: data.journalId,
      journalName: data.journalName,
      issnPrint: data.issnPrint,
      issnElectronic: data.issnElectronic,
      publishedYear: data.publishedYear,
      volume: data.volume,
      issue: data.issue,
      page: data.page,
      articleAbstract: data.articleAbstract,
      organizations: organizations,
      authorList: authorList
    };
    // 设置文件回显
    setupFileList(data);

    // 关闭loading，打开弹窗
    proxy.$modal.closeLoading();
    editDialogVisible.value = true;
  }).catch(error => {
    // 关闭loading
    proxy.$modal.closeLoading();
    console.error("获取文献详情失败:", error);
    proxy.$modal.msgError("获取文献详情失败");
  });
}

// 设置文件列表回显
function setupFileList(data) {
  // PDF文件回显
  if (data.pdfFile) {
    pdfFileList.value = [{
      name: data.pdfFile.fileName,
      url: `/article/file/download/${data.pdfFile.id}`,
      uid: data.pdfFile.id,
      status: 'success'
    }];
  } else {
    pdfFileList.value = [];
  }

  // SUPP文件回显
  if (data.suppFiles && data.suppFiles.length > 0) {
    suppFileList.value = data.suppFiles.map(file => ({
      name: file.fileName,
      url: `/article/file/download/${file.id}`,
      uid: file.id,
      status: 'success'
    }));
  } else {
    suppFileList.value = [];
  }
}

let selectQueryParams = ref({
  title: '',
  pageNum: 1,
  pageSize: 200,
});

const selectOptions = ref([]);
const selectLoading = ref(false);

const debounceRemoteSelect = debounce(() => {
  listJournal(selectQueryParams.value)
      .then(response => {
        if (selectQueryParams.value.pageNum === 1) {
          selectOptions.value = [];
        }
        selectOptions.value.push(...response.rows);
      })
      .finally(() => {
        selectLoading.value = false;
      });
}, 300);


function remoteSelect(queryStr) {
  selectQueryParams.value.title = queryStr;
  debounceRemoteSelect();
}

function handleLoadMore() {
  selectQueryParams.value.pageNum += 1;
  remoteSelect(selectQueryParams.value.title);
}

function handleVisibleChange(visible) {
  if (!visible) {
    selectQueryParams.value.pageNum = 1;
    selectOptions.value = [];
  }
}

/** 提交编辑表单 */
function submitEdit() {
  proxy.$refs.editFormRef.validate(valid => {
    if (!valid) return;

    // 过滤空的组织
    const filteredOrganizations = editDialogForm.value.organizations.filter(org =>
        !isStrBlank(org.name)
    );

    // 过滤空的作者并验证必填项
    const filteredAuthorList = editDialogForm.value.authorList.filter(author => {
      const hasLastname = !isStrBlank(author.lastname);
      const hasForename = !isStrBlank(author.forename);
      return hasLastname || hasForename; // 至少有一个不为空
    });

    // 验证作者的姓氏和名字必填项
    const invalidAuthors = filteredAuthorList.filter(author => {
      const hasLastname = !isStrBlank(author.lastname);
      const hasForename = !isStrBlank(author.forename);
      return !hasLastname || !hasForename; // 姓氏和名字都必须填写
    });

    if (invalidAuthors.length > 0) {
      proxy.$modal.msgError('作者的姓氏和名字都是必填项，请完整填写所有作者信息');
      return;
    }

    if (filteredAuthorList.length === 0) {
      proxy.$modal.msgError('至少需要一个有效的作者信息');
      return;
    }

    // 处理提交的数据
    const formData = {...editDialogForm.value};

    let organizations = []
    let orgMap = new Map()

    let authorList = []

    // 使用过滤后的组织数据
    filteredOrganizations.forEach(org => {
      organizations.push(org.name)
      orgMap.set(org.id, org.name)
    })

    // 使用过滤后的作者数据，组装成AuthorDTO格式
    filteredAuthorList.forEach(author => {
      // 过滤掉无效的组织关联（组织已被删除或为空）
      const validOrgIds = author.organizationIds.filter(orgId => orgMap.has(orgId));

      authorList.push({
        forename: author.forename,
        lastname: author.lastname,
        email: isStrBlank(author.email) ? null : author.email,
        organizations: validOrgIds.map(orgId => orgMap.get(orgId))
      })
    })

    const submitData = {
      ...formData,
      organizations,
      authorList
    };

    console.log('提交的数据:');
    console.log(submitData)

    // 调用API保存文献
    updateArticle(submitData).then(response => {
      proxy.$modal.msgSuccess('修改成功');
      editDialogVisible.value = false;
      getArticleList();
    }).catch(error => {
      console.error('保存失败:', error);
      proxy.$modal.msgError('保存失败，请重试');
    });
  });
}

/** 取消编辑 */
function cancelEdit() {
  editDialogVisible.value = false;
  proxy.$refs.editFormRef?.resetFields();
}

// 修改格式化函数 - 支持新的authorInfo格式
function formatArticleAuthors(data) {
  // 优先使用authorInfo字段
  if (data && data.length > 0) {
    // 检查是否是新的authorInfo格式
    return data.map(authorInfo => {
      const lastname = isStrBlank(authorInfo.lastname) ? '' : authorInfo.lastname;
      const forename = isStrBlank(authorInfo.forename) ? '' : authorInfo.forename;
      return `${lastname} ${forename}`.trim();
    }).join(', ');
  }
  return '-';
}

function formatAffiliations(affiliations) {
  if (!affiliations || affiliations.length === 0) return '-';
  return affiliations.join('; ');
}

// 处理文件预览（点击下载）
function handleFilePreview(file) {
  if (file && file.url) {
    downloadUseForm(file.url)
  } else {
    proxy.$modal.msgError('文件信息不完整，无法下载');
  }
}

getArticleList();
</script>

<style lang="scss" scoped>

.search-input {
  width: 240px;
}

.article-list {
  margin-top: 20px;
}

.article-items {
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.article-item {
  padding: 16px;
  display: flex;
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f5f7fa;
  }

  .item-left {
    width: 90px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 16px;

    .source-tags {
      margin-top: 10px;
      display: flex;
      flex-direction: column;
      gap: 5px;

      .tag-pmc {
        background-color: #1890ff;
        color: #fff;
      }

      .tag-biorxiv {
        background-color: #fa8c16;
        color: #fff;
      }

      .tag-medrxiv {
        background-color: #f5222d;
        color: #fff;
      }

      .tag-pubmed {
        background-color: #52c41a;
        color: #fff;
      }
    }
  }

  .item-content {
    flex: 1;
    overflow: hidden;

    .title-row {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 8px;

      .pdf-link {
        color: #409eff;
        display: flex;
        align-items: center;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s;
        white-space: nowrap;

        .el-icon {
          margin-right: 4px;
          font-size: 16px;
        }

        &:hover {
          color: #66b1ff;
          text-decoration: underline;
        }
      }

      .item-title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        cursor: pointer;
        flex: 1;

        &:hover {
          color: #409eff;
        }
      }
    }

    .item-authors {
      font-size: 14px;
      color: #606266;
      margin-bottom: 8px;
    }

    .item-info {
      font-size: 14px;
      color: #909399;

      .journal {
        font-style: italic;
        margin-right: 8px;
      }

      .date {
        margin-right: 8px;
      }

      .item-ids {
        margin-top: 4px;

        .id-item {
          margin-right: 16px;

          .id-label {
            color: #909399;
          }

          .id-value {
            color: #606266;
          }
        }
      }
    }
  }

  .item-right {
    width: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .action-buttons {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 5px;

      .el-button {
        padding: 4px 0;
        margin-left: 0;
      }
    }
  }
}


</style>
