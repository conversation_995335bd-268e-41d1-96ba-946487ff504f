<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-button type="text" icon="ArrowLeft" @click="goBack"
              >返回
            </el-button>
            <span>导入文献题录</span>
          </div>
        </div>
      </template>

      <!-- 重要提示区域 -->
      <div class="warning-box">
        <div class="warning-title">
          <el-icon>
            <Warning />
          </el-icon>
          <span
            >重要提示，上传前请先理解使用本功能的约定，否则可能导致文献数据重复/错误/信息缺失/编号混乱等：</span
          >
        </div>
        <ul class="warning-list">
          <li>
            确保您上传的文献在系统中<strong>没有</strong>被收录，虽然系统做了最基本的重复验证（通过PMID/DOI/PMCID
            或
            文献标题），但有时这些判定是不够的，特别是当您认为上传的数据是没有PMID（PubMed未收录）而自行手动编写了PMID，这时如果标题和原文标题不完全一致，比如中间多了一些不可见字符（空格/换行符等），系统将检测不出来重复，这样可能导致文献数据重复。
          </li>
          <li>
            请先确定您上传的文献是否已被 PubMed/PMC
            收录，如果未被收录您可以自行编订
            <strong>自定义ID</strong> 编号，但如果收录了请
            <strong>务必</strong> 使用 PubMed 指定的 PMID 编号（有PMID/DOI/PMCID
            则 <strong>自定义ID列为空</strong>），否则可能导致数据重复，因为
            PubMed 数据系统会不定期从 PubMed 导入增量数据，会根据 PMID
            将库中的数据进行覆盖。
          </li>
          <li>
            <strong>自定义ID</strong>编号规则说明（均为
            <strong>12</strong>
            位数字，自定义ID位数如果不统一，可能会对后续数据或功能产生影响，请严格遵守本规则约定）：
            <ul class="pmid-rules">
              <li>
                <span class="pmid-range">801000000000 ~ 802000000000</span>
                该区段为 "新冠病毒" 相关文献区段（没有被PubMed
                收录时使用该区段自定义值，如果已经被PubMed，则在PMID列如实填写PMID，自定义ID列为空）,当前手动编号至
                {{ currentMaxCustomId801 || '暂无数据' }}
              </li>
              <li>
                <span class="pmid-range">800000000000 ~ 801000000000</span>
                该区段用于其他没有被PubMed收录时使用(没有填写
                PMID，系统将自动从该区段中分配，<span class="highlight"
                  >如无特殊情况请勿自行指定</span
                >)，当前编号至
                {{ currentMaxCustomId800 || '暂无数据' }}
              </li>
            </ul>
          </li>
          <li>
            本项目提供了很多实用接口，包括 doi/pmid/pmcid
            转换、获取/批量获取文献信息、通过标题查询文献、发布任务
            等等...，您可以通过编程方式调用这些接口，<a
              href="javascript:void(0)"
              >API文档</a
            >。
          </li>
        </ul>
      </div>

      <!-- 上传区域 -->
      <div class="upload-area">
        <div class="upload-btns">
          <el-upload
            ref="uploadRef"
            class="upload-excel"
            action="#"
            :auto-upload="false"
            :on-change="handleExcelChange"
            :limit="1"
            accept=".xlsx, .xls"
          >
            <template #trigger>
              <el-button type="primary">
                <el-icon>
                  <Upload />
                </el-icon>
                选择文件
              </el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                请上传Excel文件，仅支持.xlsx和.xls格式
              </div>
            </template>
          </el-upload>

          <el-button type="success" @click="handleDownloadTemplate">
            <el-icon>
              <Download />
            </el-icon>
            下载模板
          </el-button>
        </div>

        <div class="action-btns">
          <el-button plain @click="handleReset">
            <el-icon>
              <Delete />
            </el-icon>
            重置表格
          </el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <div id="literature-table" ref="hotTableRef" class="hot-table"></div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-area">
        <el-button type="primary" :loading="loading" @click="handleSubmit"
          >提交</el-button
        >
      </div>
    </el-card>
    <result-log
      v-if="resultDialogOpen"
      :log-data="resultDialogData"
      curr-exp-type="Analysis"
      @close="handleResultDialogClose"
    >
    </result-log>
  </div>
</template>

<script setup name="LiteratureImport">
  import { getCurrentInstance, nextTick, onMounted, ref } from 'vue';
  import { Delete, Download, Upload, Warning } from '@element-plus/icons-vue';
  import { registerAllModules } from 'handsontable/registry';
  import { HyperFormula } from 'hyperformula';
  import Handsontable from 'handsontable';
  import 'handsontable/dist/handsontable.full.min.css';
  import * as XLSX from 'xlsx';
  import { useRouter } from 'vue-router';
  import { getMaxCustomId, importArticle } from '@/api/article/article';
  import { isArrEmpty, sleep, trimStr } from '@/utils/index.js';
  import ResultLog from '@/components/ResultLog/index.vue';

  // 导入路由
  const router = useRouter();

  // 注册所有Handsontable模块
  registerAllModules();

  const { proxy } = getCurrentInstance();
  const loading = ref(false);
  const hotTableRef = ref(null);
  const uploadRef = ref(null);
  let hotInstance = ref(null);

  // 当前最大编号
  const currentMaxCustomId800 = ref(null); // 800000000000 ~ 801000000000 区段
  const currentMaxCustomId801 = ref(null); // 801000000000 ~ 802000000000 区段

  // Excel字段定义
  const excelDefine = {
    colHeaders: [
      '自定义ID',
      'PMID',
      'PMCID',
      'DOI',
      'Source',
      'Title',
      'Author',
      'Organization',
      'Keyword',
      'Abstract',
      'Journal Name',
      'Publication Year',
      'Publication Month',
      'Publication Day',
      'Volume',
      'Issue',
      'Page',
      'Language',
    ],
    columns: [
      { type: 'text', name: 'customId' },
      { type: 'text', name: 'pmid' },
      { type: 'text', name: 'pmcId' },
      { type: 'text', name: 'doi' },
      { type: 'text', name: 'source' },
      { type: 'text', name: 'title' },
      { type: 'text', name: 'author' },
      { type: 'text', name: 'organization' },
      { type: 'text', name: 'keyword' },
      { type: 'text', name: 'abstract' },
      { type: 'text', name: 'journalName' },
      { type: 'text', name: 'publishedYear' },
      { type: 'text', name: 'publishedMonth' },
      { type: 'text', name: 'publishedDay' },
      { type: 'text', name: 'volume' },
      { type: 'text', name: 'issue' },
      { type: 'text', name: 'page' },
      { type: 'text', name: 'language' },
    ],
  };

  // 表格列定义
  const columns = [
    { data: 'customId', title: '自定义ID', width: 120, type: 'numeric' },
    { data: 'pmid', title: 'PMID', width: 120, type: 'numeric' },
    { data: 'pmcId', title: 'PMCID', width: 120, type: 'numeric' },
    { data: 'doi', title: 'DOI', width: 140 },
    { data: 'source', title: 'Source', width: 120 },
    { data: 'title', title: 'Title', width: 200 },
    { data: 'author', title: 'Author', width: 150 },
    { data: 'organization', title: 'Organization', width: 180 },
    { data: 'keyword', title: 'Keyword', width: 150 },
    { data: 'abstract', title: 'Abstract', width: 200 },
    { data: 'journalName', title: 'Journal Name', width: 180 },
    {
      data: 'publishedYear',
      title: 'Publication Year',
      width: 130,
      type: 'numeric',
    },
    {
      data: 'publishedMonth',
      title: 'Publication Month',
      width: 130,
      type: 'numeric',
    },
    {
      data: 'publishedDay',
      title: 'Publication Day',
      width: 130,
      type: 'numeric',
    },
    { data: 'volume', title: 'Volume', width: 80 },
    { data: 'issue', title: 'Issue', width: 80 },
    { data: 'page', title: 'Page', width: 100 },
    { data: 'language', title: 'Language', width: 100 },
  ];

  // 初始数据
  const emptyData = Array(15)
    .fill()
    .map(() => ({}));

  // 初始化表格
  const initTable = () => {
    const container = hotTableRef.value;
    if (!container) return;

    const hyperformulaInstance = HyperFormula.buildEmpty({
      licenseKey: 'non-commercial-and-evaluation',
    });

    if (hotInstance.value) {
      hotInstance.value.destroy();
    }

    hotInstance.value = new Handsontable(container, {
      data: emptyData,
      columns: columns,
      colHeaders: columns.map(col => col.title),
      rowHeaders: true,
      width: '100%',
      height: 400,
      licenseKey: 'non-commercial-and-evaluation',
      stretchH: 'all',
      autoColumnSize: true,
      manualColumnResize: true,
      manualRowResize: true,
      contextMenu: true,
      formulas: {
        engine: hyperformulaInstance,
      },
      columnSorting: true,
      filters: true,
      dropdownMenu: true,
      multiColumnSorting: true,
      fixedRowsTop: 0,
      fixedColumnsLeft: 1,
      afterChange: (changes, source) => {
        if (source === 'loadData') {
          return;
        }
        // 可以在这里处理数据变更
      },
    });
  };

  // 选中首行指定列的单元格
  function doSelectHeadCell(i) {
    hotInstance.value.selectCell(0, i < 0 ? 0 : i);
  }

  /** 删除空行 */
  function deleteEmptyRow() {
    let datas = hotInstance.value.getData();
    const len = datas.length;
    if (len === 0) {
      return false;
    }
    const emptyRowIndex = [];
    for (let i = 0; i < len; i++) {
      let row = datas[i];
      let isEmpty = true;
      if (row && row.length > 0) {
        let colCount = row.length;
        for (let j = 0; j < colCount; j++) {
          if (trimStr(row[j])) {
            isEmpty = false;
            break;
          }
        }
      }
      if (isEmpty) {
        emptyRowIndex.push(i);
      }
    }
    if (!isArrEmpty(emptyRowIndex)) {
      // 对行号进行降序排序，避免删除后的行号变化导致的问题
      const sortedRows = emptyRowIndex.sort((a, b) => b - a);
      sortedRows.forEach(row => {
        hotInstance.value.alter('remove_row', row);
      });
    }
  }

  /** 删除空行，校验表格数据，并选中第一个校验失败的单元格 */
  function validatePromise() {
    return new Promise((resolve, reject) => {
      // 先删除空行
      deleteEmptyRow();
      // 然后调用校验器
      hotInstance.value.validateCells(valid => {
        if (!valid) {
          const cells = hotInstance.value.getCellsMeta();
          if (cells) {
            // cells.reverse();
            // 选中验证出错的项目
            (async () => {
              for (let item of cells) {
                if (!item.valid) {
                  doSelectHeadCell(0);
                  await sleep(100);
                  hotInstance.value.selectCell(item.row, item.col);
                  break; // 直接跳出循环
                }
              }
            })();
          }
          // hotInstance.value.setCellMeta(2, 1, 'valid', false);
          // 校验失败
          resolve(false);
        } else {
          resolve(true);
        }
      });
    });
  }

  // 解析Excel表头
  const parseSheetHeader = sheet => {
    let sheetRange = XLSX.utils.decode_range(sheet['!ref']);
    let headers = [];
    let headerMappings = [];

    // 先获取所有Excel列的表头
    for (
      let sheetColumnIndex = sheetRange.s.c;
      sheetColumnIndex <= sheetRange.e.c;
      ++sheetColumnIndex
    ) {
      let address = XLSX.utils.encode_cell({
        r: sheetRange.s.r,
        c: sheetColumnIndex,
      });
      let cellInfo = sheet[address];
      if (!cellInfo) {
        headerMappings.push(null);
        continue;
      }

      // 获取Excel中的表头名称
      let headName = cellInfo.v.trim();
      let currentIndex = excelDefine.colHeaders.findIndex(h => h === headName);

      if (currentIndex === -1) {
        console.info('字段' + headName + '未定义，将跳过此列');
        headerMappings.push(null); // 标记为无效列
      } else {
        headerMappings.push(excelDefine.columns[currentIndex].name);
      }
    }

    return headerMappings;
  };

  // 处理Excel文件上传
  const handleExcelChange = async file => {
    if (!file) return;

    try {
      proxy.$modal.msg('正在解析Excel文件...');

      // 前端解析Excel文件
      const reader = new FileReader();

      reader.onload = function (e) {
        try {
          const data = e.target.result;
          const workbook = XLSX.read(data, { type: 'binary' });

          // 查找第一个工作表
          const firstSheetName = workbook.SheetNames[0];
          const sheet = workbook.Sheets[firstSheetName];

          if (!sheet) {
            proxy.$modal.msgError('Excel文件中没有找到有效工作表');
            clearUploadComponent();
            return;
          }

          // 解析表头
          const headerMappings = parseSheetHeader(sheet);

          // 解析数据行
          const jsonOpts = {
            header: headerMappings,
            range: 1,
            defval: null,
          };
          const rows = XLSX.utils.sheet_to_json(sheet, jsonOpts);

          if (rows.length === 0) {
            proxy.$modal.msgWarning('Excel文件中没有有效数据');
            clearUploadComponent();
            return;
          }

          // 转换为表格数据格式
          const tableData = rows.map(row => {
            // 过滤掉null的表头字段和未定义的列
            const validRow = {};
            Object.keys(row).forEach(key => {
              if (key !== null && key !== undefined) {
                validRow[key] = row[key];
              }
            });
            return validRow;
          });

          // 更新表格数据
          if (hotInstance.value) {
            hotInstance.value.loadData(tableData);
          }

          proxy.$modal.msgSuccess(
            `Excel文件解析成功，共${tableData.length}条记录`,
          );

          // 清空上传组件状态，允许重新选择文件
          clearUploadComponent();
        } catch (error) {
          proxy.$modal.msgError(
            'Excel文件解析失败：' + (error.message || '格式错误'),
          );
          console.error('Excel解析错误:', error);
          clearUploadComponent();
        }
      };

      reader.readAsBinaryString(file.raw);
    } catch (error) {
      proxy.$modal.msgError(
        'Excel文件解析失败：' + (error.message || '未知错误'),
      );
      console.error('Excel解析错误:', error);
      clearUploadComponent();
    }
  };

  // 清空上传组件状态
  const clearUploadComponent = () => {
    if (uploadRef.value) {
      uploadRef.value.clearFiles();
    }
  };

  // 下载模板
  const handleDownloadTemplate = () => {
    try {
      // 创建一个工作簿
      const wb = XLSX.utils.book_new();

      // 创建表头
      const headers = excelDefine.colHeaders;

      // 创建一行空数据作为示例
      const exampleData = [{}];
      headers.forEach(header => {
        exampleData[0][header] = '';
      });

      // 转换为工作表
      const ws = XLSX.utils.json_to_sheet(exampleData, { header: headers });

      // 将工作表添加到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '文献题录');

      // 导出为Excel文件
      XLSX.writeFile(wb, '文献导入模板.xlsx');

      proxy.$modal.msgSuccess('模板下载成功');
    } catch (error) {
      proxy.$modal.msgError('模板下载失败');
      console.error('模板下载失败:', error);
    }
  };

  // 重置表格
  const handleReset = () => {
    proxy.$modal
      .confirm('确定要重置表格吗？所有已填写的数据将被清空', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        if (hotInstance.value) {
          hotInstance.value.loadData(emptyData);
          proxy.$modal.msgSuccess('表格已重置');
        }
      })
      .catch(() => {});
  };

  const resultDialogOpen = ref(false);
  const resultDialogData = ref([]);

  // 提交数据
  const handleSubmit = () => {
    // 获取原始数据
    const rawData = hotInstance.value.getSourceData();

    // 过滤掉没有标题的空行
    const submitData = rawData.filter(item => item.title);

    if (submitData.length === 0) {
      proxy.$modal.msgError('没有数据可提交');
      return;
    }
    console.log(submitData);

    validatePromise().then(valid => {
      if (!valid) {
        // 校验失败
        proxy.$modal.msgWarning('Please correct all invalid cells.');
      } else {
        proxy.$modal
          .confirm(`确定要提交${submitData.length}条文献记录吗？`)
          .then(() => {
            loading.value = true;

            // 调用接口提交数据
            importArticle(submitData)
              .then(response => {
                let data = response.data;
                if (data && data.length > 0) {
                  // 提交失败，展示错误信息，不清空数据
                  resultDialogData.value = data;
                  resultDialogOpen.value = true;
                } else {
                  // 提交成功，显示确认对话框
                  proxy.$modal.confirm(
                    `文献导入成功！共导入${submitData.length}条记录。`,
                    '导入成功',
                    {
                      confirmButtonText: '确定',
                      cancelButtonText: '取消',
                      type: 'success',
                      showCancelButton: false,
                    }
                  ).then(() => {
                    // 用户确认后清空数据
                    if (hotInstance.value) {
                      hotInstance.value.loadData(emptyData);
                    }
                  }).catch(() => {
                    // 用户取消也清空数据（因为导入已经成功）
                    if (hotInstance.value) {
                      hotInstance.value.loadData(emptyData);
                    }
                  });
                }
              })
              .catch(error => {
                console.error('文献导入失败:', error);
                // 网络错误或其他异常，不清空数据，显示错误信息
                proxy.$modal.msgError(
                  '文献导入失败：' + (error.message || '网络错误，请稍后重试')
                );
              })
              .finally(() => {
                loading.value = false;
              });
          });
      }
    });
  };

  // 获取当前最大编号
  const fetchMaxCustomIds = () => {
    // 获取 800000000000 ~ 801000000000 区段的最大编号
    getMaxCustomId(800000000000, 801000000000).then(
      response => (currentMaxCustomId800.value = response.data),
    );
    // 获取 801000000000 ~ 802000000000 区段的最大编号
    getMaxCustomId(801000000000, 802000000000).then(
      response => (currentMaxCustomId801.value = response.data),
    );
  };

  // 处理错误对话框关闭
  const handleResultDialogClose = () => {
    resultDialogOpen.value = false;
    resultDialogData.value = [];
  };

  // 返回上一级页面
  const goBack = () => {
    router.go(-1);
  };

  onMounted(() => {
    nextTick(() => {
      initTable();
      fetchMaxCustomIds();
    });
  });
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 20px;
    padding-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      :deep(.el-button) {
        margin: 0;
        padding: 0;
        height: auto;
        line-height: normal;
        display: inline-flex;
        align-items: center;
      }
    }

    span {
      font-size: 18px;
      font-weight: bold;
      line-height: 1.5;
    }
  }

  .warning-box {
    background-color: #fdf6ec;
    border: 1px solid #faecd8;
    padding: 16px;
    margin-bottom: 20px;
    border-radius: 4px;

    .warning-title {
      display: flex;
      align-items: center;
      color: #e6a23c;
      margin-bottom: 10px;
      font-weight: bold;

      .el-icon {
        font-size: 18px;
        margin-right: 8px;
      }
    }

    .warning-list {
      margin: 0;
      padding-left: 20px;

      li {
        margin-bottom: 10px;
        line-height: 1.5;
      }

      .pmid-rules {
        margin-top: 10px;

        li {
          margin-bottom: 6px;
        }

        .pmid-range {
          font-weight: bold;
          padding: 2px 4px;
          border-radius: 3px;
        }

        .highlight {
          color: #f56c6c;
          font-weight: bold;
        }
      }
    }

    a {
      color: #409eff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .upload-area {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .upload-btns {
      display: flex;
      align-items: flex-start;

      .upload-excel {
      }
    }

    .action-btns {
      display: flex;
      gap: 10px;
    }
  }

  .table-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    margin-bottom: 20px;
    overflow: hidden;

    .hot-table {
      width: 100%;
      height: 400px;
    }
  }

  .submit-area {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    .el-button {
      min-width: 120px;
    }
  }
</style>
