<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>pds</artifactId>
        <groupId>org.biosino.lf.pds</groupId>
        <version>2.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>article2db-svc</artifactId>

    <description>
        数据解析入库服务层
    </description>

    <properties>
        <jsoup.version>1.17.2</jsoup.version>
        <dom4j.version>2.1.4</dom4j.version>
    </properties>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>org.biosino.lf.pds</groupId>
            <artifactId>pds-common</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>org.biosino.lf.pds</groupId>
            <artifactId>pds-framework</artifactId>
        </dependency>

        <!-- Spring Retry -->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <!-- Spring AOP，用于拦截重试操作 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>

        <!-- XML Parsing -->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup.version}</version>
        </dependency>

        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>${dom4j.version}</version>
        </dependency>

        <!-- Postgresql驱动包 -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>

    </dependencies>

</project>