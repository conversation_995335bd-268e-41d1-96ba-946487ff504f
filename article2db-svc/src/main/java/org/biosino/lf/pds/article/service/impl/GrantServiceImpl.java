package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleGrant;
import org.biosino.lf.pds.article.domain.Grant;
import org.biosino.lf.pds.article.mapper.GrantMapper;
import org.biosino.lf.pds.article.service.IArticleGrantService;
import org.biosino.lf.pds.article.service.IGrantService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 资助信息表 服务实现类
 */
@Service
public class GrantServiceImpl extends ServiceImpl<GrantMapper, Grant> implements IGrantService {
    
    private final IArticleGrantService articleGrantService;
    
    public GrantServiceImpl(IArticleGrantService articleGrantService) {
        this.articleGrantService = articleGrantService;
    }
    
    @Override
    public List<Grant> findByGrantIdIn(Collection<String> grantIds) {
        if (CollUtil.isEmpty(grantIds)) {
            return CollUtil.newArrayList();
        }
        Wrapper<Grant> query = Wrappers.<Grant>lambdaQuery().in(Grant::getGrantId, grantIds);
        return this.list(query);
    }

    @Override
    public List<Grant> findByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return CollUtil.newArrayList();
        }
        return this.list(
                Wrappers.<Grant>lambdaQuery()
                        .in(Grant::getId, ids)
        );
    }
    
    @Override
    public boolean removeUnusedByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }
        
        // 查找仍在使用的Grant IDs
        List<ArticleGrant> articleGrants = articleGrantService.list(
                Wrappers.<ArticleGrant>lambdaQuery()
                        .in(ArticleGrant::getGrantId, ids)
        );
        
        if (CollUtil.isEmpty(articleGrants)) {
            // 如果没有引用，则删除所有指定的IDs
            return this.removeByIds(ids);
        } else {
            // 找出未被引用的IDs
            List<Long> usedIds = articleGrants.stream()
                    .map(ArticleGrant::getGrantId)
                    .collect(Collectors.toList());
            
            List<Long> unusedIds = new ArrayList<>(ids);
            unusedIds.removeAll(usedIds);
            
            if (CollUtil.isNotEmpty(unusedIds)) {
                return this.removeByIds(unusedIds);
            }
            return true;
        }
    }
}
