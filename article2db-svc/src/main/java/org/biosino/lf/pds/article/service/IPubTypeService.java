package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.PubType;

import java.util.Collection;
import java.util.List;

/**
 * 出版类型信息表 服务接口
 */
public interface IPubTypeService extends IService<PubType> {
    /**
     * 根据出版类型列表查询出版类型信息
     *
     * @param collect 出版类型列表
     * @return 出版类型信息列表
     */
    List<PubType> findByPubTypeIn(Collection<String> collect);
    
    /**
     * 删除未被引用的出版类型
     * 
     * @param ids 待检查的出版类型ID列表
     * @return 是否删除成功
     */
    boolean removeUnusedByIds(Collection<Long> ids);
}
