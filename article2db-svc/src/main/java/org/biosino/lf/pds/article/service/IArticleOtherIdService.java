package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticleOtherId;

import java.util.List;

/**
 * 文章其他ID关联表 服务接口
 */
public interface IArticleOtherIdService extends IService<ArticleOtherId> {
    /**
     * 根据PMID查询文章其他ID关联信息
     *
     * @param pmid 文章PMID
     * @return 文章其他ID关联信息集合
     */
    List<ArticleOtherId> findByPmid(Long pmid);

    boolean removeByDocId(Long id);
}
