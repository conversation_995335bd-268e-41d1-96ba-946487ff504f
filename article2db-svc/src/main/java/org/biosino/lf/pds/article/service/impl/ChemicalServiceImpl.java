package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleChemical;
import org.biosino.lf.pds.article.domain.Chemical;
import org.biosino.lf.pds.article.mapper.ChemicalMapper;
import org.biosino.lf.pds.article.service.IArticleChemicalService;
import org.biosino.lf.pds.article.service.IChemicalService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 化学物质信息表 服务实现类
 */
@Service
public class ChemicalServiceImpl extends ServiceImpl<ChemicalMapper, Chemical> implements IChemicalService {
    
    private final IArticleChemicalService articleChemicalService;
    
    public ChemicalServiceImpl(IArticleChemicalService articleChemicalService) {
        this.articleChemicalService = articleChemicalService;
    }
    
    @Override
    public List<Chemical> findByRegistryNoIn(Collection<String> registryNos) {
        // 将registryNos中的空字符串和null过滤一遍
        Set<String> collect = registryNos.stream().filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        if (CollUtil.isEmpty(collect)) {
            return CollUtil.newArrayList();
        }
        Wrapper<Chemical> query = Wrappers.<Chemical>lambdaQuery().in(Chemical::getRegistryNo, collect);
        return this.list(query);
    }

    @Override
    public List<Chemical> findByMeshIdIn(Collection<String> meshIds) {
        // 将names中的空字符串和null过滤一遍
        Set<String> collect = meshIds.stream().filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        if (CollUtil.isEmpty(collect)) {
            return CollUtil.newArrayList();
        }
        Wrapper<Chemical> query = Wrappers.<Chemical>lambdaQuery().in(Chemical::getMeshId, collect);
        return this.list(query);
    }
    
    @Override
    public boolean removeUnusedByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }
        
        // 查找仍在使用的Chemical IDs
        List<ArticleChemical> articleChemicals = articleChemicalService.list(
                Wrappers.<ArticleChemical>lambdaQuery()
                        .in(ArticleChemical::getChemicalId, ids)
        );
        
        if (CollUtil.isEmpty(articleChemicals)) {
            // 如果没有引用，则删除所有指定的IDs
            return this.removeByIds(ids);
        } else {
            // 找出未被引用的IDs
            List<Long> usedIds = articleChemicals.stream()
                    .map(ArticleChemical::getChemicalId)
                    .collect(Collectors.toList());
            
            List<Long> unusedIds = new ArrayList<>(ids);
            unusedIds.removeAll(usedIds);
            
            if (CollUtil.isNotEmpty(unusedIds)) {
                return this.removeByIds(unusedIds);
            }
            return true;
        }
    }
}
