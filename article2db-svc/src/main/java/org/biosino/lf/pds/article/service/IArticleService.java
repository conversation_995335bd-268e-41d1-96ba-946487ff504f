package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.Article;
import org.biosino.lf.pds.article.dto.ArticleInfoDTO;

import java.util.List;

/**
 * 文章信息表 服务接口
 *
 * <AUTHOR>
 */
public interface IArticleService extends IService<Article> {

    void saveOne(ArticleInfoDTO dto);

    void merge(Long pubmedArticleId, Long pmcArticleId);

    /**
     * 分页查询文章列表
     * @param lastId 上一页最后一条记录的ID
     * @param pageSize 页面大小
     * @return 文章列表
     */
    List<Article> getArticlesByPage(Long lastId, int pageSize);

    /**
     * 获取最大ID
     * @return 最大ID
     */
    void deletedAllByDocId(String source, Long docId);

    /**
     * 批量更新文章
     * @param articles 文章列表
     * @return 是否成功
     */
    boolean updateBatch(List<Article> articles);
}
