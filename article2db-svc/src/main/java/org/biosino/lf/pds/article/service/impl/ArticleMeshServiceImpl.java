package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleMesh;
import org.biosino.lf.pds.article.mapper.ArticleMeshMapper;
import org.biosino.lf.pds.article.service.IArticleMeshService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文章MeSH关联表 服务实现类
 */
@Service
public class ArticleMeshServiceImpl extends ServiceImpl<ArticleMeshMapper, ArticleMesh> implements IArticleMeshService {
    @Override
    public List<ArticleMesh> findByDocId(Long docId) {
        return this.list(
                Wrappers.<ArticleMesh>lambdaQuery()
                        .eq(ArticleMesh::getDocId, docId)
        );
    }

    @Override
    public boolean removeByDocId(Long docId) {
        return this.remove(
                Wrappers.<ArticleMesh>lambdaQuery()
                        .eq(ArticleMesh::getDocId, docId)
        );
    }
}
