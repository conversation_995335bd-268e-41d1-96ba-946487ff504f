package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleAuthor;
import org.biosino.lf.pds.article.domain.Organization;
import org.biosino.lf.pds.article.mapper.OrganizationMapper;
import org.biosino.lf.pds.article.service.IArticleAuthorService;
import org.biosino.lf.pds.article.service.IOrganizationService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 机构信息表 服务实现类
 */
@Service
public class OrganizationServiceImpl extends ServiceImpl<OrganizationMapper, Organization> implements IOrganizationService {

    private final IArticleAuthorService articleAuthorService;

    public OrganizationServiceImpl(@Lazy IArticleAuthorService articleAuthorService) {
        this.articleAuthorService = articleAuthorService;
    }

    @Override
    public List<Organization> findByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return CollUtil.newArrayList();
        }
        return this.list(
                Wrappers.<Organization>lambdaQuery()
                        .in(Organization::getId, ids)
        );
    }

    @Override
    public boolean removeUnusedByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }

        // 查找仍在使用的Organization IDs
        List<ArticleAuthor> articleAuthors = articleAuthorService.selectByOrgIdIn(ids);

        if (CollUtil.isEmpty(articleAuthors)) {
            // 如果没有引用，则删除所有指定的IDs
            return this.removeByIds(ids);
        } else {
            // 找出未被引用的IDs
            List<Long> allOrgIds = articleAuthors.stream()
                    .flatMap(x -> Optional.ofNullable(x.getOrganizationId()).orElseGet(Collections::emptyList).stream())
                    .collect(Collectors.toList());


            HashSet<Long> unusedIds = new HashSet<>();
            for (Long id : ids) {
                if (!CollUtil.contains(allOrgIds, id)) {
                    unusedIds.add(id);
                }
            }

            if (CollUtil.isNotEmpty(unusedIds)) {
                return this.removeByIds(unusedIds);
            }
            return true;
        }
    }
}
