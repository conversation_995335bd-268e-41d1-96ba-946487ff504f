package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleChemical;
import org.biosino.lf.pds.article.mapper.ArticleChemicalMapper;
import org.biosino.lf.pds.article.service.IArticleChemicalService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文章化学物质关联表 服务实现类
 */
@Service
public class ArticleChemicalServiceImpl extends ServiceImpl<ArticleChemicalMapper, ArticleChemical> implements IArticleChemicalService {
    @Override
    public List<ArticleChemical> findByDocId(Long docId) {
        return this.list(
                Wrappers.<ArticleChemical>lambdaQuery()
                        .eq(ArticleChemical::getDocId, docId)
        );
    }
    
    @Override
    public boolean removeByDocId(Long docId) {
        return this.remove(
                Wrappers.<ArticleChemical>lambdaQuery()
                        .eq(ArticleChemical::getDocId, docId)
        );
    }
}
