package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleSupplMesh;
import org.biosino.lf.pds.article.mapper.ArticleSupplMeshMapper;
import org.biosino.lf.pds.article.service.IArticleSupplMeshService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文章补充MeSH关联表 服务实现类
 */
@Service
public class ArticleSupplMeshServiceImpl extends ServiceImpl<ArticleSupplMeshMapper, ArticleSupplMesh> implements IArticleSupplMeshService {
    @Override
    public List<ArticleSupplMesh> findByDocId(Long docId) {
        return this.list(
                Wrappers.<ArticleSupplMesh>lambdaQuery()
                        .eq(ArticleSupplMesh::getDocId, docId)
        );
    }

    @Override
    public boolean removeByDocId(Long docId) {
        return this.remove(
                Wrappers.<ArticleSupplMesh>lambdaQuery()
                        .eq(ArticleSupplMesh::getDocId, docId)
        );
    }
}
