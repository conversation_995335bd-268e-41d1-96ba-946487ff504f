package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.Publisher;
import org.biosino.lf.pds.article.mapper.PublisherMapper;
import org.biosino.lf.pds.article.service.PublisherService;
import org.biosino.lf.pds.common.enums.JournalSourceTypeEnums;
import org.biosino.lf.pds.common.enums.StatusEnums;
import org.biosino.lf.pds.common.utils.uuid.IdUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;

/**
 * 出版商服务实现类
 */
@Slf4j
@Service
public class PublisherServiceImpl extends ServiceImpl<PublisherMapper, Publisher> implements PublisherService {

    @Override
    public Publisher savePublisher(Publisher publisher) {
        if (publisher == null || publisher.getName() == null) {
            return null;
        }

        // 查询是否存在：按name查询或按alias查询
        Publisher existingPublisher = findExistingPublisher(publisher.getName());

        if (existingPublisher != null) {
            // 更新出版商信息
            if (JournalSourceTypeEnums.system.name().equals(publisher.getSourceType())) {
                publisher.setId(existingPublisher.getId());
                publisher.setUpdateTime(new Date());
                if (CollUtil.isEmpty(publisher.getAlias())) {
                    publisher.setAlias(null);
                }
                updateById(publisher);
            }
            existingPublisher.setInsert(false);
            return existingPublisher;
        } else {
            // 新增出版商信息
            publisher.setId(IdUtils.getSnowflakeNextId());
            publisher.setCreateTime(new Date());
            publisher.setUpdateTime(new Date());
            publisher.setSourceType(JournalSourceTypeEnums.system.name());
            publisher.setStatus(StatusEnums.ENABLE.getCode());

            // 将name也添加到alias中
            if (publisher.getAlias() == null) {
                publisher.setAlias(new ArrayList<>());
            }
            if (!publisher.getAlias().contains(publisher.getName())) {
                publisher.getAlias().add(publisher.getName());
            }
            if (CollUtil.isEmpty(publisher.getAlias())) {
                publisher.setAlias(null);
            }

            save(publisher);
            publisher.setInsert(true);
            return publisher;
        }
    }

    /**
     * 根据名称查找已存在的出版社（按name或alias查询）
     */
    private Publisher findExistingPublisher(String name) {
        // 先按name查询
        LambdaQueryWrapper<Publisher> nameQuery = new LambdaQueryWrapper<>();
        nameQuery.eq(Publisher::getName, name);
        Publisher publisher = this.getOne(nameQuery);

        if (publisher != null) {
            return publisher;
        }

        // 再按alias查询
        LambdaQueryWrapper<Publisher> aliasQuery = new LambdaQueryWrapper<>();
        aliasQuery.apply("alias @> ARRAY[{0}]::text[]", name);
        return this.getOne(aliasQuery);
    }

    @Override
    public void deletedById(Long publisherId) {
        if (publisherId != null) {
            removeById(publisherId);
        }
    }
}
