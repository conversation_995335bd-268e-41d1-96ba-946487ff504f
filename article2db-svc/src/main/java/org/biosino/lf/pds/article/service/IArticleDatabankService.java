package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticleDatabank;

import java.util.List;

/**
 * 文章数据库关联表 服务接口
 */
public interface IArticleDatabankService extends IService<ArticleDatabank> {
    /**
     * 根据文档ID查询文章数据库关联信息
     *
     * @param docId 文档ID
     * @return 文章数据库关联信息集合
     */
    List<ArticleDatabank> findByDocId(Long docId);
    
    /**
     * 根据文档ID删除文章数据库关联信息
     *
     * @param docId 文档ID
     * @return 是否删除成功
     */
    boolean removeByDocId(Long docId);
}
