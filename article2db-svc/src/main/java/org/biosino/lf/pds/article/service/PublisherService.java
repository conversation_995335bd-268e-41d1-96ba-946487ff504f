package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.Publisher;

/**
 * 出版商服务接口
 */
public interface PublisherService extends IService<Publisher> {
    /**
     * 根据文章ID保存或更新出版商信息
     *
     * @param publisher 出版商信息
     * @return 是否成功
     */
    Publisher savePublisher(Publisher publisher);

    void deletedById(Long publisherId);
}
