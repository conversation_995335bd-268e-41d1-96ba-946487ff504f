package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticlePubType;
import org.biosino.lf.pds.article.mapper.ArticlePubTypeMapper;
import org.biosino.lf.pds.article.service.IArticlePubTypeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文章出版类型关联表 服务实现类
 */
@Service
public class ArticlePubTypeServiceImpl extends ServiceImpl<ArticlePubTypeMapper, ArticlePubType> implements IArticlePubTypeService {
    @Override
    public List<ArticlePubType> findByDocId(Long docId) {
        return this.list(
                Wrappers.<ArticlePubType>lambdaQuery()
                        .eq(ArticlePubType::getDocId, docId)
        );
    }

    @Override
    public boolean removeByDocId(Long docId) {
        return this.remove(
                Wrappers.<ArticlePubType>lambdaQuery()
                        .eq(ArticlePubType::getDocId, docId)
        );
    }

    @Override
    public int updateDocIdBatch(Long targetDocId, Long sourceDocId) {
        return baseMapper.updateDocIdBatch(targetDocId, sourceDocId);
    }
}
