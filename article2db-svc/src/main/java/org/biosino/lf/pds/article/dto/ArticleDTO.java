package org.biosino.lf.pds.article.dto;

import lombok.Data;
import org.biosino.lf.pds.article.domain.*;

import java.util.List;

/**
 * <AUTHOR> @date 2025/5/15
 */
@Data
public class ArticleDTO {

    private String docId;

    private Article article;

    private List<ArticleDatabank> articleDatabanks;

    private List<ArticlePubType> articlePubTypes;

    private List<PubType> pubTypes;

    private List<ArticleChemical> articleChemicals;

    private List<Chemical> chemicals;

    private List<ArticleMesh> articleMeshes;

    private List<ArticleSupplMesh> articleSupplMeshes;

    private List<ArticleGene> articleGenes;

    private List<Gene> genes;

    private List<ArticleGrant> articleGrants;

    private List<Grant> grants;

    private List<ArticleOtherId> articleOtherIds;

    private List<Reference> references;

    private List<ArticleAuthor> articleAuthors;
}
