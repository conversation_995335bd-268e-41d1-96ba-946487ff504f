package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.Chemical;

import java.util.Collection;
import java.util.List;

/**
 * 化学物质信息表 服务接口
 */
public interface IChemicalService extends IService<Chemical> {
    /**
     * 根据注册号列表查询化学物质信息
     *
     * @param registryNos 注册号列表
     * @return 化学物质信息列表
     */
    List<Chemical> findByRegistryNoIn(Collection<String> registryNos);

    /**
     * 根据MeSH ID列表查询化学物质信息
     *
     * @param names MeSH ID列表
     * @return 化学物质信息列表
     */
    List<Chemical> findByMeshIdIn(Collection<String> names);
    
    /**
     * 删除未被引用的化学物质
     * 
     * @param ids 待检查的化学物质ID列表
     * @return 是否删除成功
     */
    boolean removeUnusedByIds(Collection<Long> ids);
}
