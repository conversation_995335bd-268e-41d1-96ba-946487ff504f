package org.biosino.lf.pds.article.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleDatabank;
import org.biosino.lf.pds.article.mapper.ArticleDatabankMapper;
import org.biosino.lf.pds.article.service.IArticleDatabankService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文章数据库关联表 服务实现类
 */
@Service
public class ArticleDatabankServiceImpl extends ServiceImpl<ArticleDatabankMapper, ArticleDatabank> implements IArticleDatabankService {
    @Override
    public List<ArticleDatabank> findByDocId(Long docId) {
        return this.list(
                Wrappers.<ArticleDatabank>lambdaQuery()
                        .eq(ArticleDatabank::getDocId, docId)
        );
    }
    
    @Override
    public boolean removeByDocId(Long docId) {
        return this.remove(
                Wrappers.<ArticleDatabank>lambdaQuery()
                        .eq(ArticleDatabank::getDocId, docId)
        );
    }
}
