package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.Gene;

import java.util.Collection;
import java.util.List;

/**
 * 基因信息表 服务接口
 */
public interface IGeneService extends IService<Gene> {
    /**
     * 根据基因符号列表查询基因信息
     *
     * @param geneSymbols 基因符号列表
     * @return 基因信息列表
     */
    List<Gene> findByGeneSymbolIn(Collection<String> geneSymbols);

    /**
     * 根据ID集合查询基因信息
     *
     * @param ids ID集合
     * @return 基因信息列表
     */
    List<Gene> findByIds(Collection<Long> ids);

    /**
     * 删除未被引用的基因
     * 
     * @param ids 待检查的基因ID列表
     * @return 是否删除成功
     */
    boolean removeUnusedByIds(Collection<Long> ids);
}
