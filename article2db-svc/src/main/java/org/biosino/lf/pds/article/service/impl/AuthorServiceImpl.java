package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticleAuthor;
import org.biosino.lf.pds.article.domain.Author;
import org.biosino.lf.pds.article.mapper.AuthorMapper;
import org.biosino.lf.pds.article.service.IArticleAuthorService;
import org.biosino.lf.pds.article.service.IAuthorService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 作者信息表 服务实现类
 */
@Service
public class AuthorServiceImpl extends ServiceImpl<AuthorMapper, Author> implements IAuthorService {

    private final IArticleAuthorService articleAuthorService;
    
    public AuthorServiceImpl(@Lazy IArticleAuthorService articleAuthorService) {
        this.articleAuthorService = articleAuthorService;
    }

    @Override
    public List<Author> findByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return CollUtil.newArrayList();
        }
        return this.list(
                Wrappers.<Author>lambdaQuery()
                        .in(Author::getId, ids)
        );
    }
    
    @Override
    public boolean removeUnusedByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }
        
        // 查找仍在使用的Author IDs
        List<ArticleAuthor> articleAuthors = articleAuthorService.list(
                Wrappers.<ArticleAuthor>lambdaQuery()
                        .in(ArticleAuthor::getAuthorId, ids)
        );
        
        if (CollUtil.isEmpty(articleAuthors)) {
            // 如果没有引用，则删除所有指定的IDs
            return this.removeByIds(ids);
        } else {
            // 找出未被引用的IDs
            List<Long> usedIds = articleAuthors.stream()
                    .map(ArticleAuthor::getAuthorId)
                    .collect(Collectors.toList());
            
            List<Long> unusedIds = new ArrayList<>(ids);
            unusedIds.removeAll(usedIds);
            
            if (CollUtil.isNotEmpty(unusedIds)) {
                return this.removeByIds(unusedIds);
            }
            return true;
        }
    }
}
