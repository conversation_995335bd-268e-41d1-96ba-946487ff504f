package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.biosino.lf.pds.article.domain.Journal;
import org.biosino.lf.pds.article.mapper.JournalMapper;
import org.biosino.lf.pds.article.service.JournalService;
import org.biosino.lf.pds.common.enums.StatusEnums;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.common.utils.uuid.IdUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;

/**
 * 期刊服务实现类
 *
 * <AUTHOR>
 */
@Service
public class JournalServiceImpl extends ServiceImpl<JournalMapper, Journal> implements JournalService {

    @Override
    public Journal saveJournal(Journal journal, Long publisherId) {
        if (journal == null) {
            return null;
        }

        if (StrUtil.isNotBlank(journal.getUniqueNlmId())) {
            journal.setUniqueHistory(CollUtil.newArrayList(journal.getUniqueNlmId()));
        }

        if (StrUtil.isNotBlank(journal.getIssnPrint())) {
            journal.setIssnHistory(CollUtil.newArrayList(journal.getIssnPrint()));
        } else {
            journal.setIssnHistory(new ArrayList<>());
        }
        if (StrUtil.isNotBlank(journal.getIssnElectronic())) {
            journal.getIssnHistory().add(journal.getIssnElectronic());
        }
        if (CollUtil.isNotEmpty(journal.getIssnHistory())) {
            CollUtil.distinct(journal.getIssnHistory());
        }

        // 按照优先级查询期刊：uniqueHistory > issnHistory > isoabbreviation > title
        Journal existingJournal = null;

        // 优先使用uniqueHistory查询
        if (CollUtil.isNotEmpty(journal.getUniqueHistory())) {
            for (String uniqueId : journal.getUniqueHistory()) {
                if (StringUtils.isNotBlank(uniqueId)) {
                    existingJournal = getOne(Wrappers.<Journal>lambdaQuery()
                            .apply("unique_history @> ARRAY[{0}]::text[]", uniqueId)
                            .last("LIMIT 1"));
                    if (existingJournal != null) {
                        break;
                    }
                }
            }
        }

        // 如果uniqueHistory查询不到，使用issnHistory查询
        if (existingJournal == null && CollUtil.isNotEmpty(journal.getIssnHistory())) {
            for (String issn : journal.getIssnHistory()) {
                if (StringUtils.isNotBlank(issn)) {
                    existingJournal = getOne(Wrappers.<Journal>lambdaQuery()
                            .apply("issn_history @> ARRAY[{0}]::text[]", issn)
                            .last("LIMIT 1"));
                    if (existingJournal != null) {
                        break;
                    }
                }
            }
        }

        // 如果issnHistory查询不到，使用issnPrint查询
        if (existingJournal == null && StringUtils.isNotBlank(journal.getIssnPrint())) {
            existingJournal = getOne(new LambdaQueryWrapper<Journal>()
                    .eq(Journal::getIssnPrint, journal.getIssnPrint())
                    .last("LIMIT 1"));
        }

        // 如果issnPrint查询不到，使用issnElectronic查询
        if (existingJournal == null && StringUtils.isNotBlank(journal.getIssnElectronic())) {
            existingJournal = getOne(new LambdaQueryWrapper<Journal>()
                    .eq(Journal::getIssnElectronic, journal.getIssnElectronic())
                    .last("LIMIT 1"));
        }

        // 如果issnHistory查询不到，使用isoabbreviation查询
        if (existingJournal == null && StringUtils.isNotBlank(journal.getIsoabbreviation())) {
            existingJournal = getOne(new LambdaQueryWrapper<Journal>()
                    .eq(Journal::getIsoabbreviation, journal.getIsoabbreviation())
                    .last("LIMIT 1"));
        }

        // 最后使用title查询
        if (existingJournal == null && StringUtils.isNotBlank(journal.getTitle())) {
            existingJournal = getOne(new LambdaQueryWrapper<Journal>()
                    .eq(Journal::getTitle, journal.getTitle())
                    .last("LIMIT 1"));
        }

        // 添加新期刊
        if (existingJournal == null) {
            journal.setId(IdUtils.getSnowflakeNextId());
            journal.setStatus(StatusEnums.ENABLE.getCode());
            if (publisherId != null) {
                journal.setPublisherId(publisherId);
            }
            journal.setCreateTime(new Date());
            journal.setUpdateTime(new Date());

            if (CollUtil.isEmpty(journal.getIssnHistory())) {
                journal.setIssnHistory(null);
            }

            if (CollUtil.isEmpty(journal.getUniqueHistory())) {
                journal.setUniqueHistory(null);
            }

            if (StrUtil.isBlank(journal.getTitle())) {
                throw new ServiceException("期刊标题为空");
            }

            save(journal);
            return journal;
        }

        // 如果以前期刊和出版社没有建立关联关系 则重新建立链接
        if (existingJournal.getPublisherId() == null && publisherId != null) {
            existingJournal.setPublisherId(publisherId);

            if (CollUtil.isEmpty(journal.getIssnHistory())) {
                journal.setIssnHistory(null);
            }

            if (CollUtil.isEmpty(journal.getUniqueHistory())) {
                journal.setUniqueHistory(null);
            }
            updateById(existingJournal);
        }
        return existingJournal;
    }
}
