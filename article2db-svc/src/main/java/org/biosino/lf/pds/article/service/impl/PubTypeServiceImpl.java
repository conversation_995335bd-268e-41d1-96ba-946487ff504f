package org.biosino.lf.pds.article.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.lf.pds.article.domain.ArticlePubType;
import org.biosino.lf.pds.article.domain.PubType;
import org.biosino.lf.pds.article.mapper.PubTypeMapper;
import org.biosino.lf.pds.article.service.IArticlePubTypeService;
import org.biosino.lf.pds.article.service.IPubTypeService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 出版类型信息表 服务实现类
 */
@Service
public class PubTypeServiceImpl extends ServiceImpl<PubTypeMapper, PubType> implements IPubTypeService {
    
    private final IArticlePubTypeService articlePubTypeService;
    
    public PubTypeServiceImpl(IArticlePubTypeService articlePubTypeService) {
        this.articlePubTypeService = articlePubTypeService;
    }
    
    @Override
    public List<PubType> findByPubTypeIn(Collection<String> pubTypes) {
        List<String> collect = pubTypes.stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            return CollUtil.newArrayList();
        }
        Wrapper<PubType> query = Wrappers.<PubType>lambdaQuery().in(PubType::getPubType, collect);
        return this.list(query);
    }
    
    @Override
    public boolean removeUnusedByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return true;
        }
        
        // 查找仍在使用的PubType IDs
        List<ArticlePubType> articlePubTypes = articlePubTypeService.list(
                Wrappers.<ArticlePubType>lambdaQuery()
                        .in(ArticlePubType::getPubtypeId, ids)
        );
        
        if (CollUtil.isEmpty(articlePubTypes)) {
            // 如果没有引用，则删除所有指定的IDs
            return this.removeByIds(ids);
        } else {
            // 找出未被引用的IDs
            List<Long> usedIds = articlePubTypes.stream()
                    .map(ArticlePubType::getPubtypeId)
                    .collect(Collectors.toList());
            
            List<Long> unusedIds = new ArrayList<>(ids);
            unusedIds.removeAll(usedIds);
            
            if (CollUtil.isNotEmpty(unusedIds)) {
                return this.removeByIds(unusedIds);
            }
            return true;
        }
    }
}
