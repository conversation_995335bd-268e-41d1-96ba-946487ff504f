package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticleParse;

import java.util.List;

/**
 * 文献解析元数据表 服务接口
 *
 * <AUTHOR>
 */
public interface IArticleParseService extends IService<ArticleParse> {

    /**
     * 查询未入库的记录
     *
     * @param limit 限制数量
     * @return 未入库的记录列表
     */
    List<ArticleParse> findUnprocessedRecords(int limit);

    /**
     * 更新处理状态
     *
     * @param articleParse 记录
     * @param docId 文档ID
     * @param status 状态 (1:未入库, 2:已入库, 0:失败)
     * @param errorMsg 错误信息
     * @return 是否更新成功
     */
    boolean updateStatus(ArticleParse articleParse, Long docId, Integer status, String errorMsg);

    /**
     * 批量更新doc_id字段（用于文章合并）
     * 将所有匹配源doc_id的记录更新为目标doc_id
     *
     * @param targetDocId 目标文档ID
     * @param sourceDocId 源文档ID
     * @return 更新的记录数
     */
    int updateDocIdBatch(Long targetDocId, Long sourceDocId);
}
