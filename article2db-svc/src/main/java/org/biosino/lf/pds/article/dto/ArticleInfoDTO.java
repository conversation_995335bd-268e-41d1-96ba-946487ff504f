package org.biosino.lf.pds.article.dto;

import lombok.Data;
import org.biosino.lf.pds.article.domain.*;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/5/12
 */
@Data
public class ArticleInfoDTO {

    private Article article;

    private Journal journal;

    private Publisher publisher;

    private Set<ArticleDatabank> articleDatabanks;

    private Set<PubType> pubTypes;

    private Set<Chemical> chemicals;

    private Set<ArticleMesh> articleMeshes;

    private Set<ArticleSupplMesh> articleSupplMeshes;

    private Set<Gene> genes;

    private Set<Grant> grants;

    private Set<ArticleOtherId> articleOtherIds;

    private Set<Reference> references;

    private Set<ArticleAuthor> articleAuthors;
}
