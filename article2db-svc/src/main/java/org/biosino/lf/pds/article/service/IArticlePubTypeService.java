package org.biosino.lf.pds.article.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.biosino.lf.pds.article.domain.ArticlePubType;

import java.util.List;

/**
 * 文章出版类型关联表 服务接口
 */
public interface IArticlePubTypeService extends IService<ArticlePubType> {
    /**
     * 根据文档ID查询文章出版类型关联信息
     *
     * @param docId 文档ID
     * @return 文章出版类型关联信息列表
     */
    List<ArticlePubType> findByDocId(Long docId);

    /**
     * 根据文档ID删除文章出版类型关联信息
     *
     * @param docId 文档ID
     * @return 是否删除成功
     */
    boolean removeByDocId(Long docId);

    /**
     * 批量更新doc_id字段（用于文章合并）
     * 将所有匹配源doc_id的记录更新为目标doc_id
     *
     * @param targetDocId 目标文档ID
     * @param sourceDocId 源文档ID
     * @return 更新的记录数
     */
    int updateDocIdBatch(Long targetDocId, Long sourceDocId);

}
