# 项目相关配置
app:
  # 名称
  name: Pds
  # 版本
  version: 2.0.0
  # 版权年份
  copyrightYear: 2025
  # 获取ip地址开关
  addressEnabled: true
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 500
    max-parameter-count: 20000
    # HTTP表单内容的最大大小（默认2MB），设置为600MB，需要大于Spring的max-request-size(230MB)
    max-http-form-post-size: 600MB
  servlet:
    context-path: /pds-admin
  port: 8081

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 200MB
      # 设置总上传的文件大小
      max-request-size: 230MB
      # 超过阈值才会将文件存入临时目录，反之则直接读入内存
      file-size-threshold: 5MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true

  # 邮件
  mail:
    # 默认编码
    default-encoding: UTF-8
    host: mail.cstnet.cn
    username: <EMAIL>
    password: nXAaY*7H&^Cgu3%q
    port: 994
    properties.mail.smtp:
      ssl:
        enable: true
      socketFactory:
        class: javax.net.ssl.SSLSocketFactory

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: a78fdf789030266672f43b57e303b60epK6vkPKxUJ
  # 令牌有效期（单位分钟）
  expireTime: 180

# MyBatis配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: org.biosino.lf.pds.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  #  configLocation: classpath:mybatis/mybatis-config.xml
  global-config:
    db-config:
      logic-delete-field: delFlag
      logic-delete-value: 1 #逻辑已删除
      logic-not-delete-value: 0 #逻辑未删除
      update-strategy: always
  configuration:
    cache-enabled: true
# PageHelper分页插件
pagehelper:
  helperDialect: postgresql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Springdoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
  group-configs:
    - group: 'default'
      display-name: '测试模块'
      paths-to-match: '/**'
      packages-to-scan: org.biosino.lf.pds.web.controller.tool

# 防止XSS攻击
xss:
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# PDS项目配置
pds:
  # 默认API令牌
  default-api-token: 989e8cD4a70A35f295f25f58d8236c63Sep5wTSE
  # 分配期刊脚本时，一次添加的脚本的最大期刊数量
  maxAddScriptNum: 20000
  # 判断握手超时时间间隔（单位分钟）
  handshakeInterval: 3
  # 完整度优先时，每个节点类型的最大重试数量
  paperMaxRetryTimes: 3