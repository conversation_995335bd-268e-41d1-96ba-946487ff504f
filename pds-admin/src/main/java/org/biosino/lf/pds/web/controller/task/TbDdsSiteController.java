package org.biosino.lf.pds.web.controller.task;

import cn.hutool.core.collection.CollUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.custbean.dto.SiteDTO;
import org.biosino.lf.pds.article.custbean.dto.SiteStatusDTO;
import org.biosino.lf.pds.article.custbean.vo.SiteVO;
import org.biosino.lf.pds.article.custbean.vo.TbDdsScriptlabelVO;
import org.biosino.lf.pds.common.annotation.Log;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.pds.common.enums.BusinessType;
import org.biosino.lf.pds.common.exception.ServiceException;
import org.biosino.lf.pds.task.service.ITbDdsScriptlabelService;
import org.biosino.lf.pds.task.service.ITbDdsSiteService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 节点管理Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/site")
@RequiredArgsConstructor
public class TbDdsSiteController extends BaseController {
    private final ITbDdsSiteService tbDdsSiteService;
    private final ITbDdsScriptlabelService tbDdsScriptlabelService;

    /**
     * 查询节点列表
     */
    @PreAuthorize("@ss.hasAnyPermi('node:index,nodeMonitor:index')")
    @GetMapping("/list")
    public TableDataInfo list(SiteDTO siteDTO) {
        // 节点列表不分页
        List<SiteVO> list = tbDdsSiteService.selectSiteList(siteDTO);
        return getDataTable(list);
    }

    /**
     * 获取节点详细信息
     */
    @PreAuthorize("@ss.hasPermi('node:index')")
    @GetMapping("/get/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(tbDdsSiteService.getSiteById(id));
    }

    /**
     * 新增节点
     */
    @PreAuthorize("@ss.hasPermi('node:index')")
    @Log(title = "节点管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@Validated @RequestBody SiteDTO siteDTO) {
        return tbDdsSiteService.saveSite(siteDTO, getUserId());
    }

    /**
     * 修改节点
     */
    @PreAuthorize("@ss.hasPermi('node:index')")
    @Log(title = "节点管理", businessType = BusinessType.UPDATE)
    @PostMapping("/update")
    public AjaxResult edit(@Validated @RequestBody SiteDTO siteDTO) {
        return tbDdsSiteService.saveSite(siteDTO, getUserId());
    }

    /**
     * 删除节点
     */
    @PreAuthorize("@ss.hasPermi('node:index')")
    @Log(title = "节点管理", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{id}")
    public AjaxResult remove(@PathVariable Integer id) {
        return tbDdsSiteService.deleteSiteById(id);
    }

    /**
     * 修改节点状态
     */
    @PreAuthorize("@ss.hasPermi('node:index')")
    @Log(title = "节点管理", businessType = BusinessType.UPDATE)
    @PostMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SiteStatusDTO statusDTO) {
        return tbDdsSiteService.changeStatus(statusDTO);
    }

    /**
     * 获取所有分组
     */
    @GetMapping("/groups")
    public AjaxResult getGroups() {
        List<String> groups = tbDdsSiteService.selectAllGroups();
        return success(groups);
    }

    /**
     * 根据节点类型获取标签选项
     */
    @GetMapping("/labels")
    public AjaxResult getLabelsByType(String type) {
        List<TbDdsScriptlabelVO> labels = tbDdsScriptlabelService.selectLabelsByType(type);
        return success(labels);
    }

    /**
     * 节点监控详情
     */
    @PreAuthorize("@ss.hasPermi('nodeMonitor:index')")
    @GetMapping("/nodeMonitor/{id}")
    public AjaxResult nodeMonitorInfo(@PathVariable("id") Integer id) {
        if (id == null) {
            throw new ServiceException("节点id不能为空");
        }
        final SiteDTO dto = new SiteDTO();
        dto.setId(id);
        dto.setFindHandshakeFlag(true);
        final List<SiteVO> siteVOS = tbDdsSiteService.selectSiteList(dto);
        if (CollUtil.isEmpty(siteVOS)) {
            throw new ServiceException("节点不存在");
        }
        final SiteVO vo = tbDdsSiteService.addStatInfo(siteVOS.get(0));
        return AjaxResult.success(vo);
    }

    /**
     * 导出节点监控数据
     */
    @PreAuthorize("@ss.hasPermi('nodeMonitor:index')")
    @PostMapping("/exportNodeMonitor")
    @Log(title = "节点监控", businessType = BusinessType.EXPORT)
    public void exportNodeMonitor(HttpServletResponse response) {
        tbDdsSiteService.exportNodeMonitor(response);
    }

    /**
     * 获取站点日志列表
     */
    @PreAuthorize("@ss.hasPermi('node:index')")
    @GetMapping("/logs/{siteId}")
    public AjaxResult getSiteLogList(@PathVariable("siteId") Integer siteId) {
        if (siteId == null) {
            throw new ServiceException("站点ID不能为空");
        }
        return tbDdsSiteService.getSiteLogList(siteId);
    }

    /**
     * 下载站点日志文件
     */
    @PreAuthorize("@ss.hasPermi('node:index')")
    @PostMapping("/downloadLog/{logId}")
    public void downloadSiteLog(@PathVariable("logId") Long logId, HttpServletRequest request, HttpServletResponse response) {
        if (logId == null) {
            throw new ServiceException("日志ID不能为空");
        }
        tbDdsSiteService.downloadSiteLog(logId, request, response);
    }

    /**
     * 刷新站点日志（设置to_refresh_log为1）
     */
    @PreAuthorize("@ss.hasPermi('node:index')")
    @Log(title = "站点日志", businessType = BusinessType.UPDATE)
    @PostMapping("/refreshLog/{siteId}")
    public AjaxResult refreshSiteLog(@PathVariable("siteId") Integer siteId) {
        if (siteId == null) {
            throw new ServiceException("站点ID不能为空");
        }
        return tbDdsSiteService.refreshSiteLog(siteId);
    }

}
