package org.biosino.lf.pds.web.controller.task;

import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.custbean.dto.ScriptDTO;
import org.biosino.lf.pds.article.custbean.vo.ScriptVO;
import org.biosino.lf.pds.common.annotation.Log;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.pds.common.enums.BusinessType;
import org.biosino.lf.pds.task.service.ITbDdsJournalScriptService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 脚本管理
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/script")
public class TbDdsJournalScriptController extends BaseController {
    private final ITbDdsJournalScriptService tbDdsJournalScriptService;

    /**
     * 查询脚本列表
     */
    @GetMapping("/list")
    @PreAuthorize("@ss.hasAnyPermi('script:index,label:index')")
    public TableDataInfo list(ScriptDTO queryDto) {
        return tbDdsJournalScriptService.selectScriptList(queryDto);
    }

    /**
     * 脚本上传
     *
     * @param file 上传的脚本文件
     * @return 上传结果
     */
    @PostMapping("/upload")
    @PreAuthorize("@ss.hasPermi('script:index')")
    @Log(title = "脚本管理", businessType = BusinessType.IMPORT)
    public AjaxResult upload(final MultipartFile file) {
        // 调用服务处理上传
        return tbDdsJournalScriptService.upload(file);
    }

    /**
     * 下载脚本
     *
     * @param scriptId 脚本ID
     * @param response HTTP响应
     */
    @PostMapping("/download/{scriptId}")
    @PreAuthorize("@ss.hasPermi('script:index')")
    @Log(title = "脚本管理", businessType = BusinessType.EXPORT)
    public void download(@PathVariable("scriptId") Integer scriptId, HttpServletResponse response) {
        tbDdsJournalScriptService.download(scriptId, response);
    }

    /**
     * 添加脚本信息
     */
    @PostMapping("/saveScript")
    @PreAuthorize("@ss.hasPermi('script:index')")
    @Log(title = "脚本管理", businessType = BusinessType.INSERT)
    public AjaxResult saveScript(@Validated @RequestBody final ScriptDTO dto) {
        // 调用服务处理上传
        return tbDdsJournalScriptService.saveScript(dto, getUserId());
    }

    /**
     * 删除脚本
     */
    @DeleteMapping("/remove/{scriptId}")
    @PreAuthorize("@ss.hasPermi('script:index')")
    @Log(title = "脚本管理", businessType = BusinessType.DELETE)
    public AjaxResult remove(@PathVariable Integer[] scriptId) {
        return tbDdsJournalScriptService.deleteScript(scriptId);
    }

}
