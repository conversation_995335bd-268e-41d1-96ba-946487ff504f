package org.biosino.lf.pds.web.controller.task;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.biosino.lf.pds.article.custbean.dto.ArticleViewQueryDTO;
import org.biosino.lf.pds.article.custbean.dto.TaskPublishDTO;
import org.biosino.lf.pds.article.custbean.dto.TaskTaskDTO;
import org.biosino.lf.pds.article.custbean.vo.SelectVO;
import org.biosino.lf.pds.article.custbean.vo.TbDdsTaskVO;
import org.biosino.lf.pds.common.annotation.Log;
import org.biosino.lf.pds.common.core.controller.BaseController;
import org.biosino.lf.pds.common.core.domain.AjaxResult;
import org.biosino.lf.pds.common.core.page.PageDomain;
import org.biosino.lf.pds.common.core.page.TableDataInfo;
import org.biosino.lf.pds.common.core.page.TableSupport;
import org.biosino.lf.pds.common.enums.BusinessType;
import org.biosino.lf.pds.common.enums.task.TaskPaperStatusEnum;
import org.biosino.lf.pds.common.enums.task.TaskSourceEnum;
import org.biosino.lf.pds.task.service.ITbDdsTaskService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 文献传递控制层
 *
 * <AUTHOR>
 * @date 2025/6/23
 */
@RestController
@RequestMapping("/task")
@RequiredArgsConstructor
public class TbDdsTaskController extends BaseController {
    private final ITbDdsTaskService tbDdsTaskService;

    /*#################################### 任务发布 ########################################*/

    /**
     * 任务发布
     */
    @PreAuthorize("@ss.hasPermi('task:index')")
    @Log(title = "文献传递任务", businessType = BusinessType.INSERT)
    @PostMapping("/publishTask")
    public AjaxResult publishTask(@Validated @RequestBody TaskPublishDTO dto) {
        dto.setTaskSourceFlag(TaskSourceEnum.PDS.name());
        dto.setTaskUserId(getUserId());
        tbDdsTaskService.publishTask(dto);
        return AjaxResult.success("任务发布成功");
    }

    /**
     * 所有pds节点下拉列表数据
     */
    @PreAuthorize("@ss.hasPermi('task:index')")
    @GetMapping("/allPdsSites")
    public AjaxResult allPdsSites() {
        return AjaxResult.success(tbDdsTaskService.allPdsSites());
    }

    /**
     * 文献校验
     */
    @PreAuthorize("@ss.hasPermi('task:index')")
    @Log(title = "文献校验", businessType = BusinessType.OTHER)
    @PostMapping("/validateLiterature")
    public AjaxResult validateLiterature(MultipartFile file) {
        return AjaxResult.success(tbDdsTaskService.validateLiterature(file));
    }

    /**
     * 下载校验结果文件
     */
    @PreAuthorize("@ss.hasPermi('task:index')")
    @PostMapping("/downloadValidationResult")
    public void downloadValidationResult(@RequestParam String filePath, HttpServletRequest request, HttpServletResponse response) {
        tbDdsTaskService.downloadValidationResult(filePath, request, response);
    }

    /*#################################### 任务跟踪 ########################################*/

    /**
     * 任务跟踪列表
     */
    @PreAuthorize("@ss.hasPermi('taskTrace:index')")
    @GetMapping("/searchTaskTrackList")
    public TbDdsTaskVO searchTaskTrackList(TaskTaskDTO queryDto) {
        final PageDomain pageDomain = TableSupport.buildPageRequest();
        return tbDdsTaskService.searchTaskTrackList(queryDto, pageDomain, getLoginUser());
    }

    /**
     * 所有pds用户列表
     */
    @PreAuthorize("@ss.hasPermi('taskTrace:index')")
    @GetMapping("/allPdsUsers")
    public List<SelectVO> allPdsUsers() {
        return tbDdsTaskService.allPdsUsers();
    }

    /**
     * 所有任务状态列表(包括plosp文献传递用户)
     */
    @PreAuthorize("@ss.hasPermi('taskTrace:index')")
    @GetMapping("/allPaperStatus")
    public List<SelectVO> allPaperStatus() {
        List<SelectVO> list = new ArrayList<>();
        for (TaskPaperStatusEnum value : TaskPaperStatusEnum.values()) {
            SelectVO vo = new SelectVO(value.name(), value.getDescription());
            list.add(vo);
        }
        return list;
    }

    /**
     * 任务跟踪--查看详情
     */
    @PreAuthorize("@ss.hasPermi('taskTrace:index')")
    @GetMapping("/taskArticleViewPage")
    public TableDataInfo taskArticleViewPage(ArticleViewQueryDTO queryDto) {
        return tbDdsTaskService.taskArticleViewPage(queryDto);
    }

    /**
     * 任务跟踪--详情列表--下载成功/失败结果
     */
    @PreAuthorize("@ss.hasPermi('taskTrace:index')")
    @PostMapping("/taskArticleDownloadFile")
    public void taskArticleDownloadFile(ArticleViewQueryDTO queryDto, HttpServletRequest request, HttpServletResponse response) {
        tbDdsTaskService.taskArticleDownloadFile(queryDto, request, response);
    }

    /**
     * 任务跟踪--查看日志
     */
    @PreAuthorize("@ss.hasPermi('taskTrace:index')")
    @GetMapping("/logs/{taskId}")
    public AjaxResult getTaskLogs(@PathVariable String taskId) {
        return AjaxResult.success(tbDdsTaskService.getTaskLogs(taskId));
    }

    /**
     * 任务跟踪--恢复/暂停任务
     */
    @GetMapping("/updateTaskStatus")
    @PreAuthorize("@ss.hasPermi('taskTrace:index')")
    @Log(title = "任务跟踪--恢复/暂停任务", businessType = BusinessType.UPDATE)
    public AjaxResult updateTaskStatus(String taskId, String status) {
        return AjaxResult.success(tbDdsTaskService.updateTaskStatus(taskId, status));
    }

    /**
     * 任务跟踪--下载结果(pdf文件压缩包，邮件发送)
     */
    @GetMapping("/downloadTaskArticleAttachment")
    @PreAuthorize("@ss.hasPermi('taskTrace:index')")
    @Log(title = "任务跟踪--下载结果", businessType = BusinessType.EXPORT)
    public AjaxResult downloadTaskArticleAttachment(String taskId) {
        return AjaxResult.success(tbDdsTaskService.downloadTaskPDF(taskId, getUsername()));
    }

    /**
     * 任务跟踪--删除任务
     */
    @GetMapping("/deleteTask")
    @PreAuthorize("@ss.hasPermi('taskTrace:index')")
    @Log(title = "任务跟踪--任务删除", businessType = BusinessType.DELETE)
    public AjaxResult deleteTask(String taskId) {
        boolean b = tbDdsTaskService.deleteTask(taskId);
        return AjaxResult.success(b);
    }

}
