package org.biosino.lf.pds.generator;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.converts.PostgreSqlTypeConvert;
import com.baomidou.mybatisplus.generator.config.querys.PostgreSqlQuery;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.model.ClassAnnotationAttributes;
import com.baomidou.mybatisplus.generator.query.DefaultQuery;
import org.apache.ibatis.type.JdbcType;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.nio.file.Paths;

/**
 * 代码生成
 *
 * <AUTHOR>
 * @date 2025/3/3
 */
@SpringBootTest
public class MpPlusGen {

    @Test
    public void gen() {
        /*StrategyConfig strategyConfig = new StrategyConfig.Builder()
                .enableCapitalMode() // 开启大写命名
                .enableSkipView() // 开启跳过视图
                .disableSqlFilter() // 禁用 SQL 过滤
                .likeTable(new LikeTable("USER")) // 模糊匹配表名
                .addInclude("t_simple") // 增加表匹配
                .addTablePrefix("t_", "c_") // 增加过滤表前缀
                .addFieldSuffix("_flag") // 增加过滤字段后缀
                .build();*/

        FastAutoGenerator.create("jdbc:postgresql://************:31910/postgres?useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&serverTimezone=Asia/Shanghai&currentSchema=public",
                        "postgres", "Biosino+2025")
                .globalConfig(builder -> builder
                        .author("sw")
                        .outputDir(Paths.get(System.getProperty("user.dir")) + "/src/main/java")
                        .commentDate("yyyy-MM-dd")
                )
                .dataSourceConfig(builder ->
                        // DefaultQuery(元数据查询) SQLQuery(SQL查询)
                        builder.databaseQueryClass(DefaultQuery.class)
                                .typeConvert(new PostgreSqlTypeConvert())
                                .dbQuery(new PostgreSqlQuery())
                                .typeConvertHandler((globalConfig, typeRegistry, metaInfo) -> {
                                    String tableName = metaInfo.getTableName();
                                    String columnName = metaInfo.getColumnName();
                                    if (tableName.equals("tb_dds_task")) {
                                        System.out.println(columnName);
                                    }
                                    // 日期转换为java.util.Date
                                    if (JdbcType.TIMESTAMP == metaInfo.getJdbcType()) {
                                        return DbColumnType.DATE;
                                    }
                                    return typeRegistry.getColumnType(metaInfo);
                                })
                )
                .packageConfig(builder -> builder
                        .parent("org.biosino.lf.pds.mplus")
                        .entity("entity")
                        .mapper("mapper")
                        .service("service")
                        .serviceImpl("service.impl")
                        .xml("mapper.xml")
                )
                // .addTablePrefix("tb_").build()
                .strategyConfig(builder -> builder
                        .entityBuilder().enableLombok(new ClassAnnotationAttributes("@Data", "lombok.Data"))
                        .enableFileOverride().enableFileOverride()
                        .controllerBuilder().enableFileOverride()
                        .mapperBuilder().enableFileOverride()
                        .serviceBuilder().enableFileOverride()
                )
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }


}
