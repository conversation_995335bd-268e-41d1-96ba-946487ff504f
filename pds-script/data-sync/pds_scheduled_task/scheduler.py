#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
定时任务调度器
"""

import time
from datetime import datetime

import schedule

from pds_scheduled_task.config import SCHEDULER_CONFIG
from pds_scheduled_task.utils.email_sender import <PERSON><PERSON><PERSON><PERSON>
from pds_scheduled_task.utils.logger import get_logger
from pds_scheduled_task.utils.log_monitor import LogMonitor
from run_spider import SpiderRunner

logger = get_logger()


class TaskScheduler:
    """定时任务调度器"""

    def __init__(self):
        self.email_sender = EmailSender()
        self.spider_runner = SpiderRunner()
        self.log_monitor = LogMonitor()
        self.running_spiders = set()
        self.is_running = False

    def run_spider_task(self, spider_name='data_spider'):
        """运行爬虫任务"""
        if spider_name in self.running_spiders:
            logger.warning(f"爬虫 {spider_name} 正在运行中，跳过本次执行")
            return

        try:
            self.running_spiders.add(spider_name)
            logger.info(f"开始执行定时爬虫任务: {spider_name}")

            self._run_spider_with_cleanup(spider_name)

        except Exception as e:
            self.running_spiders.discard(spider_name)
            error_msg = f"启动爬虫任务失败: {str(e)}"
            logger.error(error_msg)

            # 发送错误邮件
            self.email_sender.send_error_notification(
                error_message=error_msg,
                spider_name=spider_name,
                additional_info={
                    'Error Type': type(e).__name__,
                    'Timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'Running Spiders': list(self.running_spiders)
                }
            )

            raise

    def _run_spider_with_cleanup(self, spider_name):
        """运行爬虫并清理资源"""
        try:
            self.spider_runner.run_spider(spider_name)
            logger.info(f"定时爬虫任务完成: {spider_name}")
        except Exception as e:
            error_msg = f"定时爬虫任务执行失败: {str(e)}"
            logger.error(error_msg)

            # 发送错误邮件
            self.email_sender.send_error_notification(
                error_message=error_msg,
                spider_name=spider_name,
                additional_info={
                    'Error Type': type(e).__name__,
                    'Timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            )

            raise
        finally:
            # 清理运行状态
            self.running_spiders.discard(spider_name)

    def check_daily_logs(self):
        """检查当日日志"""
        try:
            logger.info("开始执行日志监控检查")
            self.log_monitor.check_logs_for_date()
            logger.info("日志监控检查完成")
        except Exception as e:
            logger.error(f"日志监控检查失败: {e}")

    def setup_schedule(self):
        """设置定时任务"""
        # task_schedule_time = SCHEDULER_CONFIG['task_schedule_time']
        log_schedule_time = SCHEDULER_CONFIG['log_schedule_time']

        # 添加任务爬取 - 每隔3小时执行一次
        schedule.every(3).minutes.do(
            self.run_spider_task, 'data_spider'
        )
        logger.info(f"爬虫任务定时任务已设置: 每隔3小时运行一次")

        # 添加日志监控任务 - 每天检查一次
        schedule.every().day.at(log_schedule_time).do(self.check_daily_logs)

        logger.info(f"日志信息检查定时任务已设置: 指定时间: 每天 {log_schedule_time} 运行一次")

    def start(self):
        """启动调度器"""
        logger.info("启动定时任务调度器")
        self.is_running = True

        # 设置定时任务
        self.setup_schedule()

        # 发送启动通知
        self.email_sender.send_success_notification(
            'Task Scheduler',
            {
                'status': 'Started',
                'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        )

        try:
            # 运行调度循环
            while self.is_running:
                schedule.run_pending()
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("接收到停止信号")
        finally:
            self.stop()

    def stop(self):
        """停止调度器"""
        logger.info("停止定时任务调度器")
        self.is_running = False

        # 发送停止通知
        self.email_sender.send_success_notification(
            'Task Scheduler',
            {
                'status': 'Stopped',
                'stop_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'final_running_spiders': list(self.running_spiders)
            }
        )


def main():
    """主函数"""
    scheduler = TaskScheduler()

    try:
        scheduler.start()
    except Exception as e:
        print(f"调度器运行失败: {e}")


if __name__ == '__main__':
    main()
