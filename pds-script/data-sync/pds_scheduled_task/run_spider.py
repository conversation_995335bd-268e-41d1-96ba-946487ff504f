#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
爬虫运行脚本
"""

import subprocess
import sys
from datetime import datetime

from pds_scheduled_task.utils.database import DatabaseManager
from pds_scheduled_task.utils.email_sender import EmailSender
from pds_scheduled_task.utils.logger import get_logger

logger = get_logger()


class SpiderRunner:
    """爬虫运行器"""

    def __init__(self):
        self.email_sender = EmailSender()
        self.db_manager = DatabaseManager()

    def run_spider(self, spider_name='data_spider'):
        """运行指定的爬虫"""
        try:
            logger.info(f"开始运行爬虫: {spider_name}")

            subprocess.run(["scrapy", "crawl", spider_name])

            logger.info(f"爬虫 {spider_name} 运行完成")

        except Exception as e:
            error_msg = f"运行爬虫 {spider_name} 时发生错误: {str(e)}"
            logger.error(error_msg)

            # 发送错误邮件
            self.email_sender.send_error_notification(
                error_message=error_msg,
                spider_name=spider_name,
                additional_info={
                    'Error Type': type(e).__name__,
                    'Timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            )

            raise


def main():
    """主函数"""
    # if len(sys.argv) > 1:
    #     spider_name = sys.argv[1]
    # else:
    #     spider_name = 'data_spider'
    spider_name = 'data_spider'
    runner = SpiderRunner()

    try:
        runner.run_spider(spider_name)
        logger.info(f"爬虫 {spider_name} 运行成功")
    except Exception as e:
        logger.error(f"爬虫 {spider_name} 运行失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
