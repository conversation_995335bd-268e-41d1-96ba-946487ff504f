#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
配置文件
包含数据库、邮件、日志等配置信息
"""

import os
from datetime import datetime

# 数据库配置
DATABASE_CONFIG = {
    "host": "************",
    "port": 31910,
    "dbname": "postgres",
    "user": "postgres",
    "password": "Biosino+2025",
    "options": "-c search_path=public"
}

# 数据表配置
TABLE_CONFIG = {
    'summary_table': 'tb_dds_article_parse',
    'content_table': 'tb_dds_article_xml',
    'log_table': 'tb_dds_scheduler_logs'
}

# 邮件配置
EMAIL_CONFIG = {
    'smtp_server': 'smtp.163.com',  # 根据实际邮箱服务商修改
    'smtp_port': 25,
    'sender_email': '<EMAIL>',  # 发送方邮箱
    'sender_password': 'DPcqkidi5FZQfMCb',  # 邮箱应用密码
    'recipients': [  # 接收错误通知的邮箱列表
        '<EMAIL>',
        '<EMAIL>'
    ]
}

# 日志配置
LOG_CONFIG = {
    'log_dir': 'logs',
    'log_level': 'INFO',
    'max_bytes': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
    'error_threshold_num':'10', # ERROR日志超过10条发送邮件
    'warning_threshold_num':'50', # WARNING日志超过50条发送邮件
}

# 爬虫配置
SPIDER_CONFIG = {
    'target_databases': ['pubmed', 'pmc'],
    'date_start': '2025/04/27',
    'date_end': '2025/09/20',
    'max_retry_times': 2,  # 最大重试次数
    'ncbi_api_key': '58c4311b01ac794248c6fd117e4b5181f708'
}

# 定时任务配置
SCHEDULER_CONFIG = {
    # 'task_schedule_time': '15:10',  # 定时任务指定时间
    'log_schedule_time': '23:00' # 日志定时扫描任务指定时间
}


# 创建必要的目录
def ensure_directories():
    """确保必要的目录存在"""
    log_dir = LOG_CONFIG['log_dir']
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)


# 初始化目录
ensure_directories()
