#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志配置工具
"""

import logging
import logging.handlers
from .postgres_log_handler import PostgreSQLLogHandler
import os
from datetime import datetime

from ..config import LOG_CONFIG


def setup_logger(name: str = None, log_file: str = None) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件名（可选，如果不指定则使用配置中的默认文件名）
    
    Returns:
        配置好的日志记录器
    """
    logger_name = name or __name__
    logger = logging.getLogger(logger_name)

    # 如果已经配置过，直接返回
    if logger.handlers:
        return logger

    logger.setLevel(getattr(logging, LOG_CONFIG['log_level']))

    # 创建日志目录
    log_dir = LOG_CONFIG['log_dir']
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 日志文件路径
    if not log_file:
        log_file = f"pds_scheduler_{datetime.now().strftime('%Y%m%d')}.log"
    log_file_path = os.path.join(log_dir, log_file)

    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 创建文件处理器（支持日志轮转）
    file_handler = logging.handlers.RotatingFileHandler(
        log_file_path,
        maxBytes=LOG_CONFIG['max_bytes'],
        backupCount=LOG_CONFIG['backup_count'],
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, LOG_CONFIG['log_level']))
    file_handler.setFormatter(formatter)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 添加处理器到日志记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # 添加PostgreSQL处理器
    try:
        postgres_handler = PostgreSQLLogHandler()
        postgres_handler.setLevel(logging.WARNING)  # 只记录WARNING和ERROR
        logger.addHandler(postgres_handler)
    except Exception as e:
        logger.warning(f"无法添加PostgreSQL日志处理器: {e}")

    return logger


def get_spider_logger(spider_name: str) -> logging.Logger:
    """
    获取爬虫专用日志记录器
    
    Args:
        spider_name: 爬虫名称
    
    Returns:
        爬虫专用日志记录器
    """
    log_file = f"{spider_name}_{datetime.now().strftime('%Y%m%d')}.log"
    return setup_logger(f"spider_{spider_name}", log_file)


def get_scheduler_logger() -> logging.Logger:
    """
    获取调度器日志记录器
    
    Returns:
        调度器日志记录器
    """
    log_file = f"scheduler_{datetime.now().strftime('%Y%m%d')}.log"
    return setup_logger("scheduler", log_file)


def get_logger() -> logging.Logger:
    """
    获取单一所有调度器日志记录器

    Returns:
        调度器日志记录器
    """
    log_file = f"pds_scheduler_{datetime.now().strftime('%Y%m%d')}.log"
    return setup_logger("pds_scheduler", log_file)


class LoggerMixin:
    """日志记录器混入类"""

    @property
    def logger(self):
        """获取日志记录器"""
        if not hasattr(self, '_logger'):
            class_name = self.__class__.__name__
            self._logger = setup_logger(class_name)
        return self._logger


# 创建默认日志记录器
default_logger = setup_logger()


def log_exception(logger: logging.Logger, message: str = "发生异常"):
    """
    记录异常信息的装饰器
    
    Args:
        logger: 日志记录器
        message: 异常消息前缀
    """

    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.exception(f"{message}: {str(e)}")
                raise

        return wrapper

    return decorator
