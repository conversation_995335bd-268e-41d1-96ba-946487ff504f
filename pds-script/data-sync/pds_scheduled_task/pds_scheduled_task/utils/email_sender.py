#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
邮件发送工具类
"""

import smtplib
from datetime import datetime
from email.header import Header
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.text import MIMEText
from typing import List

from .logger import get_logger
from ..config import EMAIL_CONFIG

logger = get_logger()


class EmailSender:
    """邮件发送类"""

    def __init__(self):
        self.smtp_server = EMAIL_CONFIG['smtp_server']
        self.smtp_port = EMAIL_CONFIG['smtp_port']
        self.sender_email = EMAIL_CONFIG['sender_email']
        self.sender_password = EMAIL_CONFIG['sender_password']
        self.recipients = EMAIL_CONFIG['recipients']

    def send_error_notification(self, error_message: str, spider_name: str = None,
                                additional_info: dict = None) -> bool:
        """发送错误通知邮件"""
        try:
            subject = f"爬虫任务错误通知 - {spider_name or 'Unknown Spider'}"

            # 构建邮件内容
            body = self._build_error_email_body(error_message, spider_name, additional_info)

            return self._send_email(subject, body, self.recipients)

        except Exception as e:
            logger.error(f"发送错误通知邮件失败: {e}")
            return False

    def send_success_notification(self, spider_name: str, stats: dict) -> bool:
        """发送成功通知邮件"""
        try:
            subject = f"爬虫任务通知 - {spider_name}"

            # 构建邮件内容
            body = self._build_success_email_body(spider_name, stats)

            return self._send_email(subject, body, self.recipients)

        except Exception as e:
            logger.error(f"发送成功通知邮件失败: {e}")
            return False

    def _build_error_email_body(self, error_message: str, spider_name: str = None,
                                additional_info: dict = None) -> str:
        """构建错误邮件内容"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        body = f"""
        <html>
        <body>
            <h2 style="color: red;">爬虫任务错误通知</h2>
            <p><strong>时间:</strong> {current_time}</p>
            <p><strong>爬虫名称:</strong> {spider_name or 'Unknown'}</p>
            <p><strong>错误信息:</strong></p>
            <pre style="background-color: #f5f5f5; padding: 10px; border-radius: 5px;">
{error_message}
            </pre>
        """

        if additional_info:
            body += "<p><strong>附加信息:</strong></p><ul>"
            for key, value in additional_info.items():
                body += f"<li><strong>{key}:</strong> {value}</li>"
            body += "</ul>"

        body += """
            <p>请及时检查并处理相关问题。</p>
            <hr>
            <p><small>此邮件由爬虫监控系统自动发送</small></p>
        </body>
        </html>
        """

        return body

    def _build_success_email_body(self, spider_name: str, stats: dict) -> str:
        """构建成功邮件内容"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        body = f"""
        <html>
        <body>
            <h2 style="color: green;">爬虫任务通知</h2>
            <p><strong>时间:</strong> {current_time}</p>
            <p><strong>爬虫名称:</strong> {spider_name}</p>
            <p><strong>执行统计:</strong></p>
            <ul>
        """

        for key, value in stats.items():
            body += f"<li><strong>{key}:</strong> {value}</li>"

        body += """
            </ul>
            <hr>
            <p><small>此邮件由爬虫监控系统自动发送</small></p>
        </body>
        </html>
        """

        return body

    def _send_email(self, subject: str, body: str, recipients: List[str]) -> bool:
        """发送邮件"""
        try:
            # 创建邮件对象
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = Header(subject, 'utf-8')

            # 添加邮件内容
            msg.attach(MIMEText(body, 'html', 'utf-8'))

            # 连接SMTP服务器并发送邮件
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()  # 启用TLS加密
                server.login(self.sender_email, self.sender_password)
                server.send_message(msg)

            logger.info(f"邮件发送成功，收件人: {', '.join(recipients)}")
            return True

        except Exception as e:
            logger.error(f"邮件发送失败: {e}")
            return False

    def test_email_connection(self) -> bool:
        """测试邮件连接"""
        try:
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.sender_email, self.sender_password)
            logger.info("邮件服务器连接测试成功")
            return True
        except Exception as e:
            logger.error(f"邮件服务器连接测试失败: {e}")
            return False
