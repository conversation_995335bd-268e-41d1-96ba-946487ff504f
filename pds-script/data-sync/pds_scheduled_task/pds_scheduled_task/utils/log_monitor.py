#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
日志监控工具
"""

from datetime import datetime, timedelta
from .database import DatabaseManager
from .email_sender import EmailSender
from .logger import get_logger
from ..config import TABLE_CONFIG
from ..config import LOG_CONFIG
logger = get_logger()

class LogMonitor:
    """日志监控类"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.email_sender = EmailSender()
        self.log_table = TABLE_CONFIG['log_table']
        
        # 阈值配置
        self.thresholds = {
            'ERROR': LOG_CONFIG['error_threshold_num'],    # ERROR日志超过*条发送邮件
            'WARNING': LOG_CONFIG['warning_threshold_num']  # WARNING日志超过*条发送邮件
        }
    
    def check_logs_for_date(self, check_date=None):
        """检查指定日期的日志数量"""
        if check_date is None:
            check_date = datetime.now().date()
            
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cur:
                    # 统计各级别日志数量
                    stats_sql = f"""
                    SELECT log_level, COUNT(*) as count
                    FROM {self.log_table}
                    WHERE create_date = %s
                    GROUP BY log_level
                    """
                    
                    cur.execute(stats_sql, (check_date,))
                    results = cur.fetchall()

                    if results:
                        log_stats = {level: count for level, count in results}

                        # 检查是否超过阈值
                        alerts = []
                        for level, threshold in self.thresholds.items():
                            count = log_stats.get(level, 0)
                            if int(count) > int(threshold):
                                alerts.append({
                                    'level': level,
                                    'count': count,
                                    'threshold': threshold,
                                    'date': check_date
                                })
                    
                        # 如果有告警，发送邮件
                        if alerts:
                            self._send_log_alert_email(alerts, log_stats, check_date)
                    
                    logger.info(f"[log_monitor.py] 日志监控完成 - 日期: {check_date}, 统计: {log_stats}")
                    return log_stats
                    
        except Exception as e:
            logger.error(f"日志监控检查失败: {e}")
            return {}
    
    def _send_log_alert_email(self, alerts, log_stats, check_date):
        """发送日志告警邮件"""
        try:
            subject = f"系统日志告警 - {check_date}"
            
            body = f"""
            <html>
            <body>
                <h2 style="color: orange;">系统日志告警通知</h2>
                <p><strong>检查日期:</strong> {check_date}</p>
                <p><strong>告警详情:</strong></p>
                <table border="1" style="border-collapse: collapse;">
                    <tr>
                        <th>日志级别</th>
                        <th>实际数量</th>
                        <th>阈值</th>
                        <th>状态</th>
                    </tr>
            """
            
            for alert in alerts:
                body += f"""
                    <tr style="background-color: #ffebee;">
                        <td>{alert['level']}</td>
                        <td>{alert['count']}</td>
                        <td>{alert['threshold']}</td>
                        <td style="color: red;">超出阈值</td>
                    </tr>
                """
            
            body += """
                </table>
                <p><strong>完整统计:</strong></p>
                <ul>
            """
            
            for level, count in log_stats.items():
                body += f"<li>{level}: {count} 条</li>"
            
            body += """
                </ul>
                <p>请及时检查系统运行状况。</p>
                <hr>
                <p><small>此邮件由PDS日志监控系统自动发送</small></p>
            </body>
            </html>
            """
            
            self.email_sender._send_email(subject, body, self.email_sender.recipients)
            logger.info(f"[log_monitor.py] 日志告警邮件发送成功")
            
        except Exception as e:
            logger.error(f"[log_monitor.py] 发送日志告警邮件失败: {e}")
    
    def get_recent_errors(self, hours=24, limit=100):
        """获取最近的错误日志"""
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cur:
                    since_time = datetime.now() - timedelta(hours=hours)
                    
                    query_sql = f"""
                    SELECT log_time, log_level, logger_name, message, spider_name, error_type
                    FROM {self.log_table}
                    WHERE log_time >= %s AND log_level IN ('ERROR', 'WARNING')
                    ORDER BY log_time DESC
                    LIMIT %s
                    """
                    
                    cur.execute(query_sql, (since_time, limit))
                    return cur.fetchall()
                    
        except Exception as e:
            logger.error(f"[log_monitor.py] 获取最近错误日志失败: {e}")
            return []