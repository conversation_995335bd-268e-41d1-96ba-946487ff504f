#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
PostgreSQL日志处理器
"""

import logging
import traceback
from datetime import datetime
import psycopg
from ..config import DATABASE_CONFIG
from ..config import TABLE_CONFIG

class PostgreSQLLogHandler(logging.Handler):
    """PostgreSQL日志处理器"""
    
    def __init__(self):
        super().__init__()
        self.conn_params = DATABASE_CONFIG
        self.log_table = TABLE_CONFIG['log_table']
        
    def emit(self, record):
        """发送日志记录到PostgreSQL"""
        # 只处理ERROR和WARNING级别的日志
        if record.levelno < logging.WARNING:
            return
            
        try:
            with psycopg.connect(**self.conn_params) as conn:
                with conn.cursor() as cur:
                    # 提取额外信息
                    spider_name = getattr(record, 'spider_name', None)
                    error_type = getattr(record, 'error_type', None)
                    stack_trace = None
                    
                    # 如果是异常记录，获取堆栈跟踪
                    if record.exc_info:
                        stack_trace = ''.join(traceback.format_exception(*record.exc_info))
                        if not error_type:
                            error_type = record.exc_info[0].__name__
                    
                    insert_sql = f"""
                    INSERT INTO {self.log_table} (
                        log_time, log_level, logger_name, module_name, 
                        function_name, line_number, message, spider_name,
                        error_type, stack_trace
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                    """
                    
                    cur.execute(insert_sql, (
                        datetime.fromtimestamp(record.created),
                        record.levelname,
                        record.name,
                        record.module,
                        record.funcName,
                        record.lineno,
                        record.getMessage(),
                        spider_name,
                        error_type,
                        stack_trace
                    ))
                    conn.commit()
                    
        except Exception as e:
            # 避免日志处理器本身的错误影响主程序
            print(f"PostgreSQL日志处理器错误: {e}")