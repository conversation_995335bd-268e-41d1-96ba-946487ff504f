#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库操作工具类
"""

import psycopg

from ..config import DATABASE_CONFIG, TABLE_CONFIG
from .logger import get_logger

logger = get_logger()

class DatabaseManager:
    """数据库管理类"""

    def __init__(self):
        self.conn_params = DATABASE_CONFIG
        self.summary_table = TABLE_CONFIG['summary_table']
        self.content_table = TABLE_CONFIG['content_table']
        self.log_table = TABLE_CONFIG['log_table']
        self._ensure_tables()

    def get_connection(self):
        """获取数据库连接"""
        try:
            conn = psycopg.connect(**self.conn_params)
            return conn
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise Exception("程序已终止")

    def _ensure_tables(self):
        """确保数据表存在"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # 创建摘要表
                    create_summary_sql = f"""
                    CREATE TABLE IF NOT EXISTS {self.summary_table} (
                         id BIGINT PRIMARY KEY NOT NULL,
                         doc_id BIGINT, 
                         file_name VARCHAR(600) NOT NULL,
                         local_path VARCHAR(255) NOT NULL,
                         content_type VARCHAR(30) NOT NULL,
                         source_id VARCHAR(255) NOT NULL,
                         source VARCHAR(30) NOT NULL,
                         status INTEGER NOT NULL,
                         version INTEGER NOT NULL,
                         file_md5 VARCHAR(32) UNIQUE NOT NULL,
                         create_time TIMESTAMPTZ NOT NULL,
                         update_time TIMESTAMPTZ,
                         error_msg TEXT
                    );
                    """

                    # 创建内容表
                    create_content_sql = f"""
                    CREATE TABLE IF NOT EXISTS {self.content_table} (
                         id BIGINT PRIMARY KEY,
                         file_data BYTEA NOT NULL
                    );
                    """

                    # 创建日志表
                    create_log_table_sql = f"""
                    CREATE TABLE IF NOT EXISTS {self.log_table} (
                        id SERIAL PRIMARY KEY,
                        log_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        log_level VARCHAR(20) NOT NULL,
                        logger_name VARCHAR(100),
                        module_name VARCHAR(100),
                        function_name VARCHAR(100),
                        line_number INTEGER,
                        message TEXT NOT NULL,
                        spider_name VARCHAR(50),
                        error_type VARCHAR(100),
                        stack_trace TEXT,
                        create_date DATE DEFAULT CURRENT_DATE
                    );
                    
                    CREATE INDEX IF NOT EXISTS idx_system_logs_date_level 
                    ON {self.log_table}(create_date, log_level);
                    """

                    cur.execute(create_summary_sql)
                    cur.execute(create_content_sql)
                    cur.execute(create_log_table_sql)
                    conn.commit()
                    logger.info("数据表检查/创建完成，可以继续执行")
        except Exception as e:
            logger.error(f"[database.py] 检查/创建数据表失败: {e}")
            raise Exception("程序已终止")

    def insert_data(self, summary_data, content_data):
        """插入数据到数据库"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # 插入摘要数据
                    insert_summary_sql = f"""
                    INSERT INTO {self.summary_table} (
                        id, file_name, local_path, content_type, 
                        source_id, source, status, version, file_md5, create_time,
                        update_time
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                    """

                    cur.execute(insert_summary_sql, summary_data)

                    # 插入内容数据
                    insert_content_sql = f"""
                    INSERT INTO {self.content_table} (
                        id, file_data
                    ) VALUES (
                        %s, %s
                    )
                    """

                    cur.execute(insert_content_sql, content_data)
                    conn.commit()

        except Exception as e:
            logger.error(f"[database.py] 插入数据失败: {e} \n插入失败的数据是 {summary_data}")

    def find_md5_exists(self, data_md5):
        """ 判断md5的值是否存在 """
        try:
            select_md5_sql = f"""
            SELECT EXISTS (
                SELECT 1 FROM {self.summary_table} 
                WHERE file_md5 = %s LIMIT 1
            );
            """

            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(select_md5_sql, (data_md5,))
                    exists = cur.fetchone()[0]
                    return exists

        except Exception as e:
            logger.error(f"[database.py] 检查MD5是否存在时出错: {e}, md5数据是: {data_md5}")
            return False

    def check_pmid_version(self, pmid, new_version):
        """
        检查PMID在数据库中是否存在，如果存在则比较版本号
        返回:
            -1: PMID不存在
            0: 版本相等，需要更新
            1: 新版本大于库中版本，需要更新
            2: 新版本小于库中版本，忽略
        """
        try:
            select_sql = f"""
            SELECT id, version FROM {self.summary_table}
            WHERE source_id = %s AND source = 'Pubmed';
            """

            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(select_sql, (pmid,))
                    result = cur.fetchone()

                    if result:
                        db_id, db_version = result
                        # 将版本转换为整数进行比较
                        try:
                            db_version = int(db_version)
                            new_version = int(new_version)
                        except ValueError:
                            # 如果版本不是有效整数，则默认为相等
                            logger.warning(f"无效的版本号格式: db_version={db_version}, new_version={new_version}")
                            return 0, db_id

                        if new_version > db_version:
                            return 1, db_id  # 新版本大于库中版本
                        elif new_version < db_version:
                            return 2, db_id  # 新版本小于库中版本
                        else:
                            return 1, db_id  # 版本相等
                    else:
                        return -1, None  # PMID不存在
        except Exception as e:
            logger.error(f"[database.py] 检查PMID版本时出错: {e}, PMID={pmid}, version={new_version}")
            return -1, None

    def check_pmc_exists(self, pmcid, new_version):
        """
        检查PMID在数据库中是否存在，如果存在则比较版本号
        返回:
            -1: PMID不存在
            0: 版本相等，需要更新
            1: 新版本大于库中版本，需要更新
            2: 新版本小于库中版本，忽略
        """
        try:
            select_sql = f"""
            SELECT id, version FROM {self.summary_table}
            WHERE source_id = %s AND source = 'PMC';
            """

            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(select_sql, (pmcid,))
                    result = cur.fetchone()

                    if result:
                        db_id, db_version = result
                        # 将版本转换为整数进行比较
                        try:
                            db_version = int(db_version)
                            new_version = int(new_version)
                        except ValueError:
                            # 如果版本不是有效整数，则默认为相等
                            logger.warning(f"无效的版本号格式: db_version={db_version}, new_version={new_version}")
                            return 0, db_id

                        if new_version > db_version:
                            return 1, db_id  # 新版本大于库中版本
                        elif new_version < db_version:
                            return 2, db_id  # 新版本小于库中版本
                        else:
                            return 1, db_id  # 版本相等
                    else:
                        return -1, None  # PMID不存在
        except Exception as e:
            logger.error(f"[database.py] 检查PMCID版本时出错: {e}, PMCID={pmcid}, version={new_version}")
            return -1, None

    def update_existing_record(self, db_id, summary_data, content_data):
        """
        更新已存在的记录
        """
        try:
            # 提取必要的数据
            _, file_name, local_path, content_type, source_id, source, status, version, file_md5, _, update_time = summary_data
            _, binary_data = content_data

            # 更新摘要信息
            update_summary_sql = f"""
            UPDATE {self.summary_table}
            SET file_name = %s,
                local_path = %s,
                content_type = %s,
                status = %s,
                version = %s,
                file_md5 = %s,
                update_time = %s
            WHERE id = %s;
            """

            # 更新内容信息
            update_content_sql = f"""
            UPDATE {self.content_table}
            SET file_data = %s
            WHERE id = %s;
            """

            with self.get_connection() as conn:
                with conn.cursor() as cur:
                    # 更新摘要表
                    cur.execute(update_summary_sql, (
                        file_name, local_path, content_type, status, version, file_md5, update_time, db_id
                    ))

                    # 更新内容表
                    cur.execute(update_content_sql, (binary_data, db_id))

                    conn.commit()
                    logger.debug(f"[database.py] 成功更新记录，ID: {db_id}")
                    return True
        except Exception as e:
            logger.error(f"[database.py] 更新记录时出错: {e}, id={db_id}")
            conn.rollback()
            return False
