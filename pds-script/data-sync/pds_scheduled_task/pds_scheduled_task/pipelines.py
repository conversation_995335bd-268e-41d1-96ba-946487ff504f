# Define your item pipelines here
#
# Don't forget to add your pipeline to the ITEM_PIPELINES setting
# See: https://docs.scrapy.org/en/latest/topics/item-pipeline.html

import hashlib
import xml.etree.ElementTree as ET
from datetime import datetime, timezone
from io import String<PERSON>

from bs4 import BeautifulSoup, Tag
from itemadapter import ItemAdapter

from .utils.database import DatabaseManager
from .utils.email_sender import EmailSender
from .utils.logger import get_logger
from .utils.snow_flake import SnowFlake

logger = get_logger()


class DuplicatesPipeline:
    """去重管道"""

    def __init__(self):
        self.db_manager = DatabaseManager()

    def get_dict_data(self, dom, name):
        """
        安全地获取数据，避免keyErro异常
        """
        if dom is not None:
            data = dom.get(name, None)
            if data:
                return data
            else:
                return None
        else:
            return None

    def process_item(self, item, spider):
        try:
            adapter = ItemAdapter(item)

            # 生成数据哈希值
            xml_content = self.get_dict_data(adapter, "xml_content")
            if xml_content:
                data_md5 = hashlib.md5(xml_content).hexdigest()
                # 检查是否重复
                if self.db_manager.find_md5_exists(data_md5):
                    logger.info(f"[pipelines.py] md5已存在，跳过该条数据，ID: {adapter.get('data_id')}")
                else:
                    return item
            else:
                return None
        except Exception as e:
            logger.error(f"[pipelines.py] 去重数据出错: {e}")
            return None


class PostgreSQLPipeline:
    """PostgreSQL存储管道"""

    def __init__(self):
        self.db_manager = DatabaseManager()
        self.email_sender = EmailSender()
        self.snowflake = SnowFlake(worker_id=1, datacenter_id=1)

    def get_dict_data(self, dom, name):
        """
        安全地获取数据，避免keyErro异常
        """
        if dom is not None:
            data = dom.get(name, None)
            if data:
                return data
            else:
                return None
        else:
            return None

    def get_text_data(self, dom, dom_path):
        """
        获取xml的文本值
        """
        element = dom.select_one(dom_path)
        if element:
            dom_text = element.text.strip()
            return dom_text
        else:
            return None

    def get_pubmed_data(self, data):
        """
        解析pubmed 的 xml文件内容
        """
        pmid = None
        version_value = 1
        if data is not None and data:
            try:
                soup = BeautifulSoup(data, 'xml')

                medline_citation_data = soup.select_one("MedlineCitation")
                if medline_citation_data:
                    # 查找 PMID 标签
                    pmid_tag = soup.find('PMID')
                    if isinstance(pmid_tag, Tag) and pmid_tag.get('Version'):
                        version_value = pmid_tag.get('Version', 1)

                    pmid = self.get_text_data(medline_citation_data, "PMID")

                return pmid, int(version_value)
            except Exception as e:
                logger.error(f"[pipelines.py] 解析pubmed里XML数据出错: {e}")
                return None, None

    def get_pmc_data(self, data):
        """
        解析 pmcid 的 xml文件内容
        """
        pmcid = None
        version_value = 1
        if data is not None and data:
            try:
                soup = BeautifulSoup(data, 'xml')

                article_meta_data = soup.select_one("article-meta")
                if article_meta_data:
                    # 查找 PMCID 标签
                    pmcid = self.get_text_data(article_meta_data, 'article-id[pub-id-type="pmcid"]')

                    version_value = self.get_text_data(article_meta_data,
                                                       'article-version[article-version-type="pmc-version"]')
                    pmcid = pmcid.replace("PMC", "")
                return pmcid, int(version_value)
            except Exception as e:
                logger.error(f"[pipelines.py] 解析pmc里XML数据出错: {e}")
                return None, None

    def process_pubmed_xml(self, xml_content):
        """处理单个pubmed XML文件并返回处理结果"""
        data_md5 = hashlib.md5(xml_content).hexdigest()
        if self.db_manager.find_md5_exists(data_md5):
            return False

        pmid, version_value = self.get_pubmed_data(xml_content)
        if not pmid:
            logger.warning("xml里未解析出来pmid, 不进行入库")
            return False
        try:
            # 检查PMID是否存在及版本情况
            version_status, existing_id = self.db_manager.check_pmid_version(pmid, version_value)

            if version_status == 2:
                # 新版本小于库中版本，忽略
                return False

            snow_flake_id = self.snowflake.get_id()

            # 如果是更新现有记录
            if version_status == 1 and existing_id:
                id = existing_id  # 使用已存在的ID
            else:
                id = snow_flake_id  # 使用新生成的ID

            utc_now = datetime.now(timezone.utc)

            file_name = f"{pmid}.xml"
            local_path = f"{pmid}.xml"  # 相对路径
            content_type = "xml"
            source_id = pmid
            source = "Pubmed"
            status = 1
            version = version_value
            file_md5 = data_md5
            create_time = utc_now
            update_time = utc_now

            # 返回处理结果
            summary_data = (id, file_name, local_path, content_type, source_id,
                            source, status, version, file_md5, create_time, update_time)
            content_data = (id, xml_content)

            if summary_data and content_data:
                # 如果是更新现有记录
                if version_status == 1 and existing_id:
                    self.db_manager.update_existing_record(existing_id, summary_data, content_data)
                else:
                    # 直接插入新记录
                    self.db_manager.insert_data(summary_data, content_data)

        except Exception as e:
            error_msg = f"[pipelines.py] 对应pmid: {pmid}, pubmed数据保存数据到PostgreSQL失败: {str(e)}"
            logger.error(error_msg)

    def process_pmc_xml(self, xml_content):
        """处理单个pmc XML文件并返回处理结果"""
        data_md5 = hashlib.md5(xml_content).hexdigest()
        if self.db_manager.find_md5_exists(data_md5):
            return False

        pmcid, version_value = self.get_pmc_data(xml_content)
        if not pmcid:
            logger.warning("xml里未解析出来pmcid, 不进行入库")
            return False
        try:
            # 检查PMCID是否存在及版本情况
            version_status, existing_id = self.db_manager.check_pmc_exists(pmcid, version_value)

            if version_status == 2:
                # 新版本小于库中版本，忽略
                return False

            snow_flake_id = self.snowflake.get_id()

            # 如果是更新现有记录
            if version_status == 1 and existing_id:
                id = existing_id  # 使用已存在的ID
            else:
                id = snow_flake_id  # 使用新生成的ID

            utc_now = datetime.now(timezone.utc)

            file_name = f"{pmcid}.xml"
            local_path = f"{pmcid}.xml"  # 相对路径
            content_type = "xml"
            source_id = pmcid
            source = "PMC"
            status = 1
            version = version_value
            file_md5 = data_md5
            create_time = utc_now
            update_time = utc_now

            # 返回处理结果
            summary_data = (id, file_name, local_path, content_type, source_id,
                            source, status, version, file_md5, create_time, update_time)
            content_data = (id, xml_content)

            if summary_data and content_data:
                if version_status == 1 and existing_id:
                    self.db_manager.update_existing_record(existing_id, summary_data, content_data)
                else:
                    self.db_manager.insert_data(summary_data, content_data)

        except Exception as e:
            error_msg = f"[pipelines.py] 对应pmcid: {pmcid}, pmc数据保存数据到PostgreSQL失败: {str(e)}"
            logger.error(error_msg)

    def splite_big_xml(self, xml_str, database_name, tag_name):
        try:
            xml_file = StringIO(xml_str)

            # 打开 XML 文件并创建迭代器
            context = ET.iterparse(xml_file, events=('start', 'end'))

            # 初始化一个堆栈用于跟踪打开的<...>元素
            xml_stack = []

            # 遍历每个元素的开始和结束事件
            for event, elem in context:
                # 如果是<...>元素的开始事件
                if event == 'start' and elem.tag == tag_name:
                    # 将当前<...>元素推入堆栈
                    xml_stack.append(elem)

                # 如果是</...>元素的结束事件
                elif event == 'end' and elem.tag == tag_name:
                    # 获取最近的打开的<...>元素
                    xml_elem = xml_stack[-1]

                    # 创建一个新的 ElementTree 对象，用于存储当前<...>内容
                    xml_elem_tree = ET.ElementTree(xml_elem)

                    xml_str = ET.tostring(xml_elem_tree.getroot(), encoding='utf-8').decode('utf-8')
                    xml_bytes = xml_str.encode('utf-8')

                    if database_name == "pubmed":
                        self.process_pubmed_xml(xml_bytes)
                    elif database_name == "pmc":
                        self.process_pmc_xml(xml_bytes)

                    # 清除当前<...>元素，释放内存
                    xml_elem.clear()

                    # 将当前<...>元素从堆栈中弹出
                    xml_stack.pop()
        except Exception as e:
            logger.error(f"分割xml出现问题: {e}")

    def process_item(self, item, spider):
        try:
            if not item:
                return
            adapter = ItemAdapter(item)

            xml_content = self.get_dict_data(adapter, "xml_content")
            database_name = self.get_dict_data(adapter, "target_database")
            xml_str = xml_content.decode('utf-8')
            if database_name == "pubmed":
                self.splite_big_xml(xml_str, database_name, "PubmedArticle")
            elif database_name == "pmc":
                self.splite_big_xml(xml_str, database_name, "article")

        except Exception as e:
            error_msg = f"[pipelines.py] 保存数据到PostgreSQL失败: {str(e)}"
            logger.error(error_msg)
