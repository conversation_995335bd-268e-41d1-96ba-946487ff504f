#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据爬取爬虫
"""

import json
import time
from datetime import datetime, timedelta

import scrapy

from ..config import SPIDER_CONFIG
from ..utils.email_sender import EmailSender
from ..utils.logger import get_logger

logger = get_logger()


class DataSpider(scrapy.Spider):
    """数据爬取爬虫"""

    name = 'data_spider'
    allowed_domains = ['eutils.ncbi.nlm.nih.gov']

    def __init__(self, *args, **kwargs):
        super(DataSpider, self).__init__(*args, **kwargs)
        self.email_sender = EmailSender()
        self.start_time = datetime.now()
        self.ncbi_api_key = SPIDER_CONFIG['ncbi_api_key']
        self.target_database_list = SPIDER_CONFIG['target_databases']  # 'pubmed', 'pmc'
        self.max_retry_times = SPIDER_CONFIG['max_retry_times']
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'items_scraped': 0
        }

    def get_dict_data(self, dom, name):
        """
        安全地获取数据，避免keyErro异常
        """
        if dom is not None:
            data = dom.get(name, None)
            if data:
                return data
            else:
                return None
        else:
            return None

    def start_requests(self):
        """生成初始请求 - 先获取ID列表"""
        date_start = SPIDER_CONFIG['date_start']
        date_end = SPIDER_CONFIG['date_end']
        logger.info(f"当前配置里初始日期范围: {date_start} - {date_end} 的数据")
        if date_start and date_end:
            # 解析日期字符串
            start_date = datetime.strptime(date_start, '%Y/%m/%d')
            end_date = datetime.strptime(date_end, '%Y/%m/%d')

            # 按天遍历日期范围
            current_date = start_date
            logger.info(f"将处理{current_date} - {end_date} 的数据")
        else:
            # 获取当前时间
            now = datetime.now()
            current_hour = now.hour

            # 判断是否在凌晨12点到3点之间
            if 0 <= current_hour < 3:
                # 凌晨12点到3点：遍历前一天和今天
                start_date = now - timedelta(days=1)
                end_date = now
                logger.info(f"当前时间 {now.strftime('%H:%M')}，将处理前一天和今天的数据")
            else:
                # 其他时间：只遍历今天
                start_date = now
                end_date = now
                logger.info(f"当前时间 {now.strftime('%H:%M')}，将处理今天的数据")
            # 按天遍历日期范围
            current_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)

        while current_date <= end_date:
            date_str = current_date.strftime('%Y/%m/%d')
            logger.info(f"正准备读 {date_str} 的数据")
            for target_database in self.target_database_list:
                self.stats['total_requests'] += 1
                # 构建获取ID列表的请求
                # https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=pmc&mindate=2024/01/01&maxdate=2024/11/31&retmode=json&retmax=100000&datetype=mdat
                url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi"
                form_data = {
                    "db": target_database,
                    "mindate": date_str,
                    "maxdate": date_str,
                    "retmode": "json",
                    "retmax": "10000000",
                    "datetype": "mdat"
                }

                yield scrapy.FormRequest(
                    url=url,
                    formdata=form_data,
                    callback=self.parse_id_list,
                    errback=self.handle_error,
                    meta={'target_database': target_database},
                    dont_filter=True
                )
            # 移动到下一天
            current_date += timedelta(days=1)

    def parse_id_list(self, response):
        """解析ID列表响应"""
        try:
            target_database = response.meta['target_database']
            # 状态码检查
            if not (200 <= response.status < 300):
                logger.error(f"获取ID列表失败, url: {response.url}，状态码: {response.status}")
                return None

            data = response.text
            data_json = json.loads(data)
            esearchresult_data = self.get_dict_data(data_json, "esearchresult")

            if esearchresult_data:
                count = self.get_dict_data(esearchresult_data, "count")
                id_list = self.get_dict_data(esearchresult_data, "idlist")

                if id_list and count != "0":
                    logger.info(f"数据库 {target_database} 预计入库数量：{count}")

                    batch_size = 100  # 每100个发送一次请求
                    for i in range(0, len(id_list), batch_size):
                        batch_ids = id_list[i:i + batch_size]
                        batch_id_string = ','.join(batch_ids)
                        logger.info(f"数据库 {target_database} 批次 {i // batch_size + 1}，ID数量: {len(batch_ids)}")
                        yield self.create_xml_request(target_database, batch_id_string)
                else:
                    logger.info(f"数据库 {target_database} 数量：{count}，无数据入库")

        except Exception as e:
            logger.error(f"[data_spider.py] 解析数据的ID列表出现问题: {e}")
            self.stats['failed_requests'] += 1

    def create_xml_request(self, target_database, id_string):
        """创建获取XML数据的请求"""
        try:
            xml_url = f"https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db={target_database}&id={id_string}&rettype=xml&retmode=xml&api_key={self.ncbi_api_key}"

            return scrapy.Request(
                url=xml_url,
                callback=self.parse_xml_data,
                errback=self.handle_error,
                meta={
                    'target_database': target_database,
                    'id_string': id_string,
                    'batch_size': len(id_string.split(','))  # 记录批次大小
                },
                dont_filter=True
            )
        except Exception as e:
            logger.error(f"[data_spider.py] 创建XML请求出现问题: {e}")
            return None

    def parse_xml_data(self, response):
        """解析XML数据响应"""
        try:
            target_database = response.meta['target_database']
            id_string = response.meta['id_string']
            batch_size = response.meta.get('batch_size', 1)

            # 状态码检查
            if not (200 <= response.status < 300):
                logger.error(f"[data_spider.py] 获取XML数据失败, url: {response.url}，状态码: {response.status}")
                return None

            xml_content = response.body

            if xml_content:
                self.stats['successful_requests'] += 1

                # 返回处理后的数据项
                yield {
                    'target_database': target_database,
                    'xml_content': xml_content,
                    'batch_size': batch_size,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                logger.warning(f"[data_spider.py] 获取到空的XML数据，ID: {id_string}")

        except Exception as e:
            logger.error(f"[data_spider.py] 解析XML数据出现问题: {e}")
            self.stats['failed_requests'] += 1

    def handle_error(self, failure):
        """处理请求错误"""
        self.stats['failed_requests'] += 1

        # 添加重试机制
        request = failure.request
        retry_times = request.meta.get('retry_times', 0)

        if retry_times < self.max_retry_times:
            retry_times += 1
            request.meta['retry_times'] = retry_times

            # 延迟时间：2^retry_times 秒（指数退避）
            delay = 2 * retry_times
            time.sleep(delay)
            logger.warning(f"请求失败，{delay}秒后进行第{retry_times}次重试: {request.url}")

            # 创建延迟重试请求
            yield scrapy.Request(
                url=request.url,
                callback=request.callback,
                errback=self.handle_error,
                meta=request.meta,
                dont_filter=True,
                priority=request.priority - retry_times  # 降低优先级
            )
        else:
            # 如果是XML请求失败且有批量ID，尝试单个获取
            if 'id_string' in request.meta and ',' in request.meta['id_string']:
                target_database = request.meta['target_database']
                id_string = request.meta['id_string']
                id_list = id_string.split(',')

                logger.info(f"该响应批量请求失败{self.max_retry_times}次，开始单个获取 {len(id_list)} 个ID的XML数据")

                # 为每个ID创建单独的请求
                for single_id in id_list:
                    yield self.create_xml_request(target_database, single_id)
            else:
                # 单个ID请求失败，打印错误信息
                url = request.url
                error_msg = f"[data_spider.py] 请求最终失败: {str(failure.value)}, URL: {url}"
                logger.error(error_msg)

    def closed(self, reason):
        """爬虫关闭时的处理"""
        end_time = datetime.now()
        duration = end_time - self.start_time

        # 更新统计信息
        self.stats['interval_time'] = str(duration)
        self.stats['end_reason'] = reason
        self.stats['start_time'] = self.start_time.strftime('%Y-%m-%d %H:%M:%S')
        self.stats['end_time'] = end_time.strftime('%Y-%m-%d %H:%M:%S')

        logger.info(f"爬虫 {self.name} 执行完成，原因: {reason}")
        logger.info(f"执行统计: {self.stats}")

        # 发送完成通知邮件
        if reason == 'finished':
            pass
            # self.email_sender.send_success_notification(self.name, self.stats)
        else:
            # 如果不是正常完成，发送错误通知
            self.email_sender.send_error_notification(
                error_message=f"爬虫异常结束，原因: {reason}",
                spider_name=self.name,
                additional_info=self.stats
            )
