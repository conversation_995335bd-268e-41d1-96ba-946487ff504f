#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
项目启动脚本
"""

import argparse
import sys

from run_spider import <PERSON><PERSON>unner
from scheduler import TaskScheduler
from pds_scheduled_task.utils.database import DatabaseManager
from pds_scheduled_task.utils.email_sender import EmailSender
from pds_scheduled_task.utils.logger import get_logger

logger = get_logger()


def test_connections():
    """测试各种连接"""

    logger.info("=" * 50)
    logger.info("测试系统连接...")
    logger.info("=" * 50)

    # 测试数据库连接
    try:
        db_manager = DatabaseManager()
        with db_manager.get_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT version()")
                version = cur.fetchone()[0]
                logger.info(f"数据库连接成功: {version}")
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False

    # 测试邮件连接
    try:
        email_sender = EmailSender()
        if email_sender.test_email_connection():
            logger.info("邮件服务器连接成功")
        else:
            logger.error("邮件服务器连接失败")
            return False
    except Exception as e:
        logger.error(f"邮件服务器连接异常: {e}")
        return False

    logger.info("=" * 50)
    return True


def run_single_spider():
    """运行单次爬虫"""
    runner = SpiderRunner()
    runner.run_spider('data_spider')


def run_scheduler():
    """运行定时调度器"""
    scheduler = TaskScheduler()
    scheduler.start()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PDS定时爬虫任务系统')
    parser.add_argument('command', choices=['test', 'spider', 'scheduler'],
                        help='执行的命令')

    args = parser.parse_args()

    if args.command == 'test':
        # 测试连接
        if test_connections():
            logger.info("所有连接测试完成")
        else:
            logger.error("连接测试失败，请检查配置")
            sys.exit(1)

    elif args.command == 'spider':
        # 测试连接
        if test_connections():
            logger.info("所有连接测试完成")
        else:
            logger.error("连接测试失败，请检查配置")
            sys.exit(1)

        # 运行单次爬虫
        logger.info("运行单次爬虫任务...")
        try:
            run_single_spider()
            logger.info("爬虫任务完成")
        except Exception as e:
            logger.error(f"爬虫任务失败: {e}")
            sys.exit(1)

    elif args.command == 'scheduler':
        # 测试连接
        if test_connections():
            logger.info("所有连接测试完成")
        else:
            logger.error("连接测试失败，请检查配置")
            sys.exit(1)

        # 运行定时调度器
        logger.info("启动定时调度器...")
        try:
            run_scheduler()
        except KeyboardInterrupt:
            logger.error("调度器已停止")
        except Exception as e:
            logger.error(f"调度器运行失败: {e}")
            sys.exit(1)


if __name__ == '__main__':
    main()
    # run_scheduler()

