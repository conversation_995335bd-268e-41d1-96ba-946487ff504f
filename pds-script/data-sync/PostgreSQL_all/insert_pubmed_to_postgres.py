#!/usr/bin/env python
# -- coding: utf-8 --
# __author__ = "Xiong Gang"
# create_time: 2025/5/20 下午3:35

import gzip
import hashlib
import logging
import os
import threading
import time
import xml.etree.ElementTree as ET
from datetime import datetime, timezone
from time import strftime, gmtime
from typing import Optional

import psycopg
from bs4 import BeautifulSoup, Tag


def init_default_logger(log_file_name: str, curr_logger: Optional[logging.Logger] = None) -> logging.Logger:
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    if not log_file_name.endswith('.log'):
        log_file_name = f'{log_file_name}.log'

    log_output_path = os.path.join(log_dir, log_file_name)

    if curr_logger is None:
        curr_logger = logging.getLogger(log_file_name)

    curr_logger.setLevel(logging.INFO)
    curr_logger.propagate = False  # 防止日志消息被传递到父记录器
    # 创建控制台处理器并设置格式
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    console_handler.setFormatter(formatter)
    curr_logger.addHandler(console_handler)

    # 创建文件处理器并设置格式 (用于提供日志报告)
    file_handler = logging.FileHandler(log_output_path, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    curr_logger.addHandler(file_handler)
    return curr_logger


# 初始化日志
log_current_time = datetime.now()
log_formatted_time = log_current_time.strftime("%Y%m%d%H%M%S")
logger = init_default_logger(f'insert_file_{log_formatted_time}.log')


class SnowFlake:
    """
    雪花算法实现类

    雪花算法是一种分布式ID生成算法，能够生成全局唯一的ID。
    ID结构：1位符号位 + 41位时间戳 + 5位数据中心ID + 5位工作机器ID + 12位序列号

    参数:
        worker_id: 工作机器ID (0-31)
        datacenter_id: 数据中心ID (0-31)
        sequence: 起始序列号 (默认为0)
    """
    def __init__(self, worker_id, datacenter_id, sequence=0):
        # 各部分位数
        self.worker_id_bits = 5  # 工作机器ID位数
        self.datacenter_id_bits = 5  # 数据中心ID位数
        self.sequence_bits = 12  # 序列号位数

        # 各部分最大值
        self.max_worker_id = -1 ^ (-1 << self.worker_id_bits)  # 工作机器ID最大值（31）
        self.max_datacenter_id = -1 ^ (-1 << self.datacenter_id_bits)  # 数据中心ID最大值（31）
        self.max_sequence = -1 ^ (-1 << self.sequence_bits)  # 序列号最大值（4095）

        # 验证参数合法性
        if worker_id > self.max_worker_id or worker_id < 0:
            raise ValueError(f"worker_id 超出范围 0 到 {self.max_worker_id}")
        if datacenter_id > self.max_datacenter_id or datacenter_id < 0:
            raise ValueError(f"datacenter_id 超出范围 0 到 {self.max_datacenter_id}")

        # 保存ID
        self.worker_id = worker_id
        self.datacenter_id = datacenter_id
        self.sequence = sequence

        # 位移计算
        self.worker_id_shift = self.sequence_bits  # 工作机器ID左移位数（12）
        self.datacenter_id_shift = self.sequence_bits + self.worker_id_bits  # 数据中心ID左移位数（17）
        self.timestamp_shift = self.sequence_bits + self.worker_id_bits + self.datacenter_id_bits  # 时间戳左移位数（22）

        # 开始时间戳（2021-01-01 00:00:00 的毫秒级时间戳）
        self.twepoch = 1609459200000

        # 上次生成ID的时间戳
        self.last_timestamp = -1

        # 线程锁，保证并发安全
        self.lock = threading.Lock()

    def _next_millis(self, last_timestamp):
        """
        获取下一毫秒时间戳

        参数:
            last_timestamp: 上次生成ID的时间戳

        返回:
            下一毫秒的时间戳
        """
        timestamp = self._get_time()
        while timestamp <= last_timestamp:
            timestamp = self._get_time()
        return timestamp

    def _get_time(self):
        """
        获取当前毫秒时间戳

        返回:
            当前毫秒时间戳
        """
        return int(time.time() * 1000)

    def get_id(self):
        """
        生成下一个ID

        返回:
            生成的ID
        """
        with self.lock:
            timestamp = self._get_time()

            # 如果当前时间小于上次生成ID的时间，说明系统时钟回退，抛出异常
            if timestamp < self.last_timestamp:
                raise Exception(f"时钟回拨异常，拒绝生成ID，当前时间: {timestamp}，上次时间: {self.last_timestamp}")

            # 如果是同一毫秒内，则递增序列号
            if timestamp == self.last_timestamp:
                self.sequence = (self.sequence + 1) & self.max_sequence
                # 如果序列号溢出，则等待下一毫秒
                if self.sequence == 0:
                    timestamp = self._next_millis(self.last_timestamp)
            else:
                # 不是同一毫秒，序列号重置为0
                self.sequence = 0

            # 保存本次的时间戳
            self.last_timestamp = timestamp

            # 生成并返回ID
            return ((timestamp - self.twepoch) << self.timestamp_shift) | \
                (self.datacenter_id << self.datacenter_id_shift) | \
                (self.worker_id << self.worker_id_shift) | \
                self.sequence


class FileProcess:
    def __init__(self, file_path, conn_params, summary_table, content_table, tag_name, num_threads):
        self.file_path = file_path
        self.snowflake = SnowFlake(worker_id=2, datacenter_id=2)
        self.conn_params = conn_params
        self.summary_table = summary_table
        self.content_table = content_table
        self.tag_name = tag_name
        self.num_threads = num_threads
        self.stop_event = threading.Event()  # 用于通知线程停止

    def create_summary_table(self, conn):
        try:
            # 创建表的SQL语句
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.summary_table} (
                 id BIGINT PRIMARY KEY NOT NULL,
                 doc_id BIGINT, 
                 file_name VARCHAR(600) NOT NULL,
                 local_path VARCHAR(255) NOT NULL,
                 content_type VARCHAR(30) NOT NULL,
                 source_id VARCHAR(255) NOT NULL,
                 source VARCHAR(30) NOT NULL,
                 status INTEGER NOT NULL,
                 version INTEGER NOT NULL,
                 file_md5 VARCHAR(32) UNIQUE NOT NULL,
                 create_time TIMESTAMPTZ NOT NULL,
                 update_time TIMESTAMPTZ,
                 error_msg TEXT
            );
            """

            with conn.cursor() as cur:
                cur.execute(create_table_sql)
                conn.commit()
                return True
        except Exception as e:
            logger.error(e)
            conn.rollback()
            return False

    def create_content_table(self, conn):
        try:
            # 创建表的SQL语句
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.content_table} (
                 id BIGINT PRIMARY KEY,
                 file_data BYTEA NOT NULL
            );
            """

            with conn.cursor() as cur:
                cur.execute(create_table_sql)
                conn.commit()
                return True
        except Exception as e:
            logger.error(e)
            conn.rollback()
            return False

    def insert_summary_information_to_postgres(self, conn, data):
        try:
            insert_sql = f"""
            INSERT INTO {self.summary_table} (
                id, file_name, local_path, content_type, 
                source_id, source, status, version, file_md5, create_time,
                update_time
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """

            with conn.cursor() as cur:
                cur.execute(insert_sql, data)
                conn.commit()
                logger.debug(f"成功插入摘要数据，ID: {data[0]}")
        except Exception as e:
            logger.error(f"插入摘要数据失败: {e}")
            conn.rollback()

    def insert_file_content_to_postgres(self, conn, data):
        try:
            insert_sql = f"""
            INSERT INTO {self.content_table} (
                id, file_data
            ) VALUES (
                %s, %s
            )
            """

            with conn.cursor() as cur:
                cur.execute(insert_sql, data)
                conn.commit()
                logger.debug(f"成功插入内容数据，ID: {data[0]}")
        except Exception as e:
            logger.error(f"插入内容数据失败: {e}")
            conn.rollback()

    def find_md5_exists(self, conn, data_md5):
        """ 判断md5的值是否存在 """
        try:
            select_md5_sql = f"""
            SELECT EXISTS (
                SELECT 1 FROM {self.summary_table}
                WHERE file_md5 = %s LIMIT 1
            );
            """

            with conn.cursor() as cur:
                cur.execute(select_md5_sql, (data_md5,))
                exists = cur.fetchone()[0]
                return exists

        except Exception as e:
            logger.error(f"检查MD5是否存在时出错: {e}, md5数据是: {data_md5}")
            return False

    def check_pmid_version(self, conn, pmid, new_version):
        """
        检查PMID在数据库中是否存在，如果存在则比较版本号
        返回: 
            -1: PMID不存在
            0: 版本相等，需要更新
            1: 新版本大于库中版本，需要更新
            2: 新版本小于库中版本，忽略
        """
        try:
            select_sql = f"""
            SELECT id, version FROM {self.summary_table}
            WHERE source_id = %s AND source = 'Pubmed';
            """

            with conn.cursor() as cur:
                cur.execute(select_sql, (pmid,))
                result = cur.fetchone()

                if result:
                    db_id, db_version = result
                    # 将版本转换为整数进行比较
                    try:
                        db_version = int(db_version)
                        new_version = int(new_version)
                    except ValueError:
                        # 如果版本不是有效整数，则默认为相等
                        logger.warning(f"无效的版本号格式: db_version={db_version}, new_version={new_version}")
                        return 0, db_id

                    if new_version > db_version:
                        return 1, db_id  # 新版本大于库中版本
                    elif new_version < db_version:
                        return 2, db_id  # 新版本小于库中版本
                    else:
                        return 1, db_id  # 版本相等
                else:
                    return -1, None  # PMID不存在
        except Exception as e:
            logger.error(f"检查PMID版本时出错: {e}, PMID={pmid}, version={new_version}")
            return -1, None

    def update_existing_record(self, conn, db_id, summary_data, content_data):
        """
        更新已存在的记录
        """
        try:
            # 提取必要的数据
            _, file_name, local_path, content_type, source_id, source, status, version, file_md5, _, update_time = summary_data
            _, binary_data = content_data

            # 更新摘要信息
            update_summary_sql = f"""
            UPDATE {self.summary_table}
            SET file_name = %s,
                local_path = %s,
                content_type = %s,
                status = %s,
                version = %s,
                file_md5 = %s,
                update_time = %s
            WHERE id = %s;
            """

            # 更新内容信息
            update_content_sql = f"""
            UPDATE {self.content_table}
            SET file_data = %s
            WHERE id = %s;
            """

            with conn.cursor() as cur:
                # 更新摘要表
                cur.execute(update_summary_sql, (
                    file_name, local_path, content_type, status, version, file_md5, update_time, db_id
                ))

                # 更新内容表
                cur.execute(update_content_sql, (binary_data, db_id))

                conn.commit()
                logger.debug(f"成功更新记录，ID: {db_id}")
                return True
        except Exception as e:
            logger.error(f"更新记录时出错: {e}, id={db_id}")
            conn.rollback()
            return False

    def create_all_table(self, conn):
        flag = self.create_summary_table(conn)
        if not flag:
            raise Exception("该注释信息数据表创建失败或检查不存在，程序终止")

        flag2 = self.create_content_table(conn)
        if not flag2:
            raise Exception("该文件信息数据表创建失败或检查不存在，程序终止")

    def get_text_data(self, dom, dom_path):
        """
        获取xml的文本值
        """
        element = dom.select_one(dom_path)
        if element:
            dom_text = element.text.strip()
            return dom_text
        else:
            return None

    def get_pubmed_data(self, data):
        """
        解析xml文件内容
        """
        pmid = None
        version_value = 1
        if data is not None and data:
            try:
                soup = BeautifulSoup(data, 'xml')

                medline_citation_data = soup.select_one("MedlineCitation")
                if medline_citation_data:
                    # 查找 PMID 标签
                    pmid_tag = soup.find('PMID')
                    if isinstance(pmid_tag, Tag) and pmid_tag.get('Version'):
                        version_value = pmid_tag.get('Version', 1)

                    pmid = self.get_text_data(medline_citation_data, "PMID")

                return pmid, version_value
            except Exception as e:
                logger.error(f"解析XML数据出错: {e}")
                return None, None

    def process_xml_data(self, xml_str, full_path, conn):
        """处理单个XML数据并返回处理结果"""
        pmid, version_value = self.get_pubmed_data(xml_str)

        if not pmid:
            return False

        try:
            # 读取原始二进制
            binary_data = xml_str.encode('utf-8')
            data_md5 = hashlib.md5(binary_data).hexdigest()

            # 检查MD5是否已存在
            if self.find_md5_exists(conn, data_md5):
                return False

            # 检查PMID是否存在及版本情况
            version_status, existing_id = self.check_pmid_version(conn, pmid, version_value)

            if version_status == 2:
                # 新版本小于库中版本，忽略
                return False

            snow_flake_id = self.snowflake.get_id()
            xml_file_name = f"{pmid}.xml"  # 纯文件名

            utc_now = datetime.now(timezone.utc)

            # 如果是更新现有记录
            if version_status == 1 and existing_id:
                id = existing_id  # 使用已存在的ID
            else:
                id = snow_flake_id  # 使用新生成的ID

            file_name = xml_file_name
            local_path = full_path
            content_type = "xml"
            source_id = pmid
            source = "Pubmed"
            status = 1
            version = version_value
            file_md5 = data_md5
            create_time = utc_now
            update_time = utc_now

            # 准备数据
            summary_data = (id, file_name, local_path, content_type, source_id,
                            source, status, version, file_md5, create_time, update_time)
            content_data = (id, binary_data)

            # 如果是更新现有记录
            if version_status == 1 and existing_id:
                self.update_existing_record(conn, existing_id, summary_data, content_data)
            else:
                # 直接插入新记录
                self.insert_summary_information_to_postgres(conn, summary_data)
                self.insert_file_content_to_postgres(conn, content_data)

            return True

        except Exception as e:
            logger.error(f"处理XML数据时出错: {e}")
            return False

    def file_processor_worker(self, file_list, thread_id):
        """文件处理线程的工作函数"""
        logger.info(f"线程 {thread_id} 启动，处理 {len(file_list)} 个文件")

        # 每个线程创建自己的数据库连接
        with psycopg.connect(**self.conn_params) as conn:
            # 确保表已创建
            if thread_id == 0:  # 只让一个线程创建表
                self.create_all_table(conn)

            local_processed = 0

            for filename in file_list:
                if self.stop_event.is_set():
                    break

                try:
                    # 判断文件是否以.xml.gz结尾
                    if filename.endswith('.xml.gz'):
                        logger.info(f"线程 {thread_id} 正在处理 {filename}")
                        # 拼接完整路径
                        full_path = os.path.join(self.file_path, filename)

                        # 打开文件并读取内容
                        with gzip.open(full_path, 'rb') as file_obj:
                            if file_obj:
                                try:
                                    # 打开 XML 文件并创建迭代器
                                    context = ET.iterparse(file_obj, events=('start', 'end'))

                                    # 初始化一个堆栈用于跟踪打开的元素
                                    xml_stack = []

                                    # 遍历每个元素的开始和结束事件
                                    for event, elem in context:
                                        if self.stop_event.is_set():
                                            break

                                        # 如果是元素的开始事件
                                        if event == 'start' and elem.tag == self.tag_name:
                                            # 将当前元素推入堆栈
                                            xml_stack.append(elem)

                                        # 如果是元素的结束事件
                                        elif event == 'end' and elem.tag == self.tag_name and len(xml_stack) > 0:
                                            # 获取最近的打开的元素
                                            xml_elem = xml_stack.pop()

                                            try:
                                                # 将元素转换为XML字符串
                                                xml_str = ET.tostring(xml_elem, encoding='utf-8').decode('utf-8')

                                                # 处理XML数据 - 直接处理，不再批量收集
                                                self.process_xml_data(xml_str, full_path, conn)
                                                local_processed += 1

                                                if local_processed % 10000 == 0:
                                                    logger.info(f"{thread_id} 已经处理 {local_processed} 个XML文件")

                                            except Exception as e:
                                                logger.error(f"处理XML元素时出错: {e}")
                                            finally:
                                                # 清除当前元素，释放内存
                                                xml_elem.clear()
                                except Exception as e:
                                    logger.error(f"解析XML文件时出错: {e}")
                except Exception as e:
                    logger.error(f"线程 {thread_id} 处理文件 {filename} 时出错: {e}")

            logger.info(f"线程 {thread_id} 处理了 {local_processed} 条记录")
        logger.info(f"线程 {thread_id} 完成处理")

    def process_pubmed_xml_gz_files_multi_threaded(self):
        """使用多线程处理XML.gz文件"""
        temps = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        logger.info(f"程序开始时间: {temps} {type(temps)}")
        time_start = time.time()  # 记录开始时间
        time.sleep(0.1)
        ######################################################################

        # 获取目录下所有XML.gz文件的路径
        file_list = [f for f in os.listdir(self.file_path) if f.endswith('.xml.gz')]

        if not file_list:
            logger.error(f"目录 {self.file_path} 中没有XML.gz文件")
            return

        total_files = len(file_list)
        logger.info(f"找到 {total_files} 个XML.gz文件，使用 {self.num_threads} 个线程处理")

        # 将文件列表分割成多个部分，每个线程处理一部分
        files_per_thread = len(file_list) // self.num_threads
        file_chunks = []

        for i in range(self.num_threads):
            start_idx = i * files_per_thread
            end_idx = start_idx + files_per_thread if i < self.num_threads - 1 else len(file_list)
            file_chunks.append(file_list[start_idx:end_idx])

        # 启动文件处理线程
        file_threads = []
        for i in range(self.num_threads):
            thread = threading.Thread(
                target=self.file_processor_worker,
                args=(file_chunks[i], i)
            )
            thread.daemon = True
            thread.start()
            file_threads.append(thread)

        # 等待所有文件处理线程完成
        for thread in file_threads:
            thread.join()

        ######################################################################
        logger.info("多线程存储数据已结束")
        temps2 = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        logger.info(f"程序结束时间: {temps2} {type(temps2)}")

        time_end = time.time()  # 记录结束时间
        run_time = time_end - time_start  # 计算的时间差为程序的执行时间，单位为秒/s
        run_time = strftime("%H:%M:%S", gmtime(run_time))
        logger.info(f'运行时间为: {run_time}')

    def process_all(self):
        self.process_pubmed_xml_gz_files_multi_threaded()


def __run():
    file_path = "./data"

    conn_params = {
        "host": "************",
        "port": 31910,
        "dbname": "postgres",
        "user": "postgres",
        "password": "Biosino+2025",
        "options": "-c search_path=public"
    }

    summary_table = "tb_dds_article_parse"
    content_table = "tb_dds_article_xml"
    tag_name = "PubmedArticle"
    num_threads = 12  # 线程数，可以根据CPU核心数调整

    obj = FileProcess(file_path, conn_params, summary_table, content_table, tag_name, num_threads)
    obj.process_all()


if __name__ == "__main__":
    __run()
