#!/usr/bin/env python
# -- coding: utf-8 --
# __author__ = "Xiong Gang"
# create_time: 2025/5/30 上午10:52
import logging
import os
import threading
import time
from datetime import datetime, timezone
from urllib import parse

import psycopg
import pymongo


class SaveDataToMongoDB:
    """
    mongodb里存入数据
    """

    def __init__(self, url, db, col):
        self.url = url
        self.db = db
        self.col = col

    def init_mongodb(self):
        """
        初始化数据库连接
        """
        client = pymongo.MongoClient(self.url)
        database = client[self.db]
        collection = database[self.col]
        return collection


def init_default_logger(log_file_name: str, curr_logger: logging.Logger = None) -> logging.Logger:
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    if not log_file_name.endswith('.log'):
        log_file_name = f'{log_file_name}.log'

    log_output_path = os.path.join(log_dir, log_file_name)

    if curr_logger is None:
        curr_logger = logging.getLogger(log_file_name)

    curr_logger.setLevel(logging.INFO)
    curr_logger.propagate = False  # 防止日志消息被传递到父记录器
    # 创建控制台处理器并设置格式
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    console_handler.setFormatter(formatter)
    curr_logger.addHandler(console_handler)

    # 创建文件处理器并设置格式 (用于提供日志报告)
    file_handler = logging.FileHandler(log_output_path, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    curr_logger.addHandler(file_handler)
    return curr_logger


# 初始化日志
log_current_time = datetime.now()
log_formatted_time = log_current_time.strftime("%Y%m%d%H%M%S")
logger = init_default_logger(f'insert_file_{log_formatted_time}.log')


class Snowflake:
    """ 雪花算法 """

    def __init__(self, worker_id, datacenter_id, sequence=0):
        # 分配各部分位数（可根据需要调整）
        self.worker_id_bits = 5
        self.datacenter_id_bits = 5
        self.sequence_bits = 12

        # 计算最大值
        self.max_worker_id = -1 ^ (-1 << self.worker_id_bits)
        self.max_datacenter_id = -1 ^ (-1 << self.datacenter_id_bits)
        if worker_id > self.max_worker_id or worker_id < 0:
            raise ValueError("worker_id 超出范围 0 到 %d" % self.max_worker_id)
        if datacenter_id > self.max_datacenter_id or datacenter_id < 0:
            raise ValueError("datacenter_id 超出范围 0 到 %d" % self.max_datacenter_id)

        self.worker_id = worker_id
        self.datacenter_id = datacenter_id
        self.sequence = sequence

        # 位移位数计算
        self.worker_id_shift = self.sequence_bits
        self.datacenter_id_shift = self.sequence_bits + self.worker_id_bits
        self.timestamp_left_shift = self.sequence_bits + self.worker_id_bits + self.datacenter_id_bits
        self.sequence_mask = -1 ^ (-1 << self.sequence_bits)
        # twepoch 为自定义纪元，这里用2021-01-01 00:00:00
        self.twepoch = 1609459200000

        self.lock = threading.Lock()
        self.last_timestamp = -1

    def _til_next_millis(self, last_timestamp):
        """等待下一毫秒"""
        timestamp = int(time.time() * 1000)
        while timestamp <= last_timestamp:
            timestamp = int(time.time() * 1000)
        return timestamp

    def get_id(self):
        """生成下一个 ID"""
        with self.lock:
            timestamp = int(time.time() * 1000)
            if timestamp < self.last_timestamp:
                raise Exception("时钟回拨错误. 当前时间 {} 小于上一次时间 {}".format(timestamp, self.last_timestamp))

            if self.last_timestamp == timestamp:
                # 同一毫秒内，序列号递增
                self.sequence = (self.sequence + 1) & self.sequence_mask
                if self.sequence == 0:
                    # 序列号用完，等待下一毫秒
                    timestamp = self._til_next_millis(self.last_timestamp)
            else:
                # 新的毫秒，序列号归零
                self.sequence = 0

            self.last_timestamp = timestamp
            # 生成 ID，各部分按照位移拼接
            new_id = ((timestamp - self.twepoch) << self.timestamp_left_shift) | \
                     (self.datacenter_id << self.datacenter_id_shift) | \
                     (self.worker_id << self.worker_id_shift) | \
                     self.sequence
            return new_id


class DataProcess:
    def __init__(self, publisher_mongo_collection, journal_mongo_collection, journal_table, publisher_table,
                 conn_params):
        self.publisher_mongo_collection = publisher_mongo_collection
        self.journal_mongo_collection = journal_mongo_collection
        self.journal_table = journal_table
        self.publisher_table = publisher_table
        self.snowflake = Snowflake(worker_id=2, datacenter_id=2)
        self.conn_params = conn_params

    def get_dict_data(self, dom, name):
        """
        安全地获取数据，避免keyErro异常
        :param dom: xml的dom树
        :param name: 字段名
        :return: 字段值
        """
        if dom is not None:
            data = dom.get(name, None)
            if data:
                return data
            else:
                return None
        else:
            return None

    def create_journal_table(self, conn):
        try:
            # 创建表的SQL语句
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.journal_table} (
                 id BIGINT PRIMARY KEY NOT NULL,
                 publisher_id BIGINT, 
                 unique_nlm_id VARCHAR(300),
                 issn_print VARCHAR(300),
                 issn_electronic VARCHAR(300),
                 title VARCHAR(300),
                 isoabbreviation VARCHAR(300),
                 pmcabbreviation VARCHAR(300),
                 jcrabbreviation VARCHAR(300),
                 issn_history TEXT[],
                 unique_history TEXT[],
                 medline_ta VARCHAR(300),
                 status INTEGER NOT NULL,
                 create_time TIMESTAMPTZ NOT NULL,
                 update_time TIMESTAMPTZ NOT NULL,
                 source TEXT[],
                 source_type TEXT,
                 script_id BIGINT
            );
            """

            with conn.cursor() as cur:
                cur.execute(create_table_sql)
                conn.commit()
                return True
        except Exception as e:
            logger.error(e)
            conn.rollback()
            return False

    def insert_journal_to_postgres(self, conn, data):
        try:
            insert_sql = f"""
            INSERT INTO {self.journal_table} (
                id, publisher_id, unique_nlm_id, issn_print, issn_electronic, title, isoabbreviation,
                pmcabbreviation, jcrabbreviation, issn_history, unique_history,
                status, create_time, update_time, source, source_type
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """

            with conn.cursor() as cur:
                cur.execute(insert_sql, data)
                conn.commit()
        except Exception as e:
            logger.error(f"问题是: {e}, 数据是: {data}")
            conn.rollback()

    def find_publisher_id(self, conn, publisher_name):
        try:
            select_publisher_sql = f"""
            SELECT id FROM {self.publisher_table} WHERE name = %s
            """

            with conn.cursor() as cur:
                cur.execute(select_publisher_sql, (publisher_name,))
                exists = cur.fetchone()[0]
                return exists

        except Exception as e:
            logger.error(f"问题是: {e}, 出版社名称是: {publisher_name}")

    def get_postgres_id(self, conn, publisher_name):
        publisher_postgresql_id = None
        publisher_dict = self.publisher_mongo_collection.find_one({"alias_publisher_name": publisher_name})
        if publisher_dict:
            new_publisher = self.get_dict_data(publisher_dict, "publisher_name")
            if new_publisher:
                publisher_postgresql_id = self.find_publisher_id(conn, new_publisher)
        return publisher_postgresql_id

    def process_journal_data(self, journal_data, conn):
        publisher_postgresql_id = None
        snow_flake_id = self.snowflake.get_id()
        utc_now = datetime.now(timezone.utc)

        unique_nlm_id = self.get_dict_data(journal_data, "unique_nlm_id")
        journal_title = self.get_dict_data(journal_data, "title")
        journal_issn_print = self.get_dict_data(journal_data, "issn_print")
        journal_issn_electronic = self.get_dict_data(journal_data, "issn_electronic")
        jcr_abbreviation = self.get_dict_data(journal_data, "jcr_abbreviation")
        pubmed_iso_abbreviation = self.get_dict_data(journal_data, "pubmed_iso_abbreviation")
        pmc_abbreviation = self.get_dict_data(journal_data, "pmc_abbreviation")
        pubmed_publisher = self.get_dict_data(journal_data, "pubmed_publisher")
        pmc_publisher = self.get_dict_data(journal_data, "pmc_publisher")
        jcr_publisher = self.get_dict_data(journal_data, "jcr_publisher")
        source = self.get_dict_data(journal_data, "source")
        issn_history = self.get_dict_data(journal_data, "issn_history")
        unique_history = self.get_dict_data(journal_data, "unique_history")

        if jcr_publisher and publisher_postgresql_id is None:
            publisher_postgresql_id = self.get_postgres_id(conn, jcr_publisher)
            print(f"采用jcr的出版社: {publisher_postgresql_id}", flush=True)
        if pmc_publisher and publisher_postgresql_id is None:
            publisher_postgresql_id = self.get_postgres_id(conn, pmc_publisher)
            print(f"采用pmc的出版社: {publisher_postgresql_id}", flush=True)
        if pubmed_publisher and publisher_postgresql_id is None:
            publisher_postgresql_id = self.get_postgres_id(conn, pubmed_publisher)
            print(f"采用pubmed的出版社: {publisher_postgresql_id}", flush=True)

        journal_id = snow_flake_id
        publisher_id = publisher_postgresql_id
        issn_print = journal_issn_print
        issn_electronic = journal_issn_electronic
        title = journal_title
        jcrabbreviation = jcr_abbreviation
        isoabbreviation = pubmed_iso_abbreviation
        pmcabbreviation = pmc_abbreviation
        status = 0
        create_time = utc_now
        update_time = utc_now
        source_type = "custom"

        journal_data = (
            journal_id, publisher_id, unique_nlm_id,issn_print, issn_electronic, title,
            isoabbreviation, pmcabbreviation, jcrabbreviation,issn_history,unique_history,
            status, create_time, update_time, source, source_type)
        self.insert_journal_to_postgres(conn, journal_data)

    def process_all(self):
        journal_dict_list = self.journal_mongo_collection.find({}, no_cursor_timeout=True)

        with psycopg.connect(**self.conn_params) as conn:
            flag = self.create_journal_table(conn)
            if not flag:
                raise Exception("该注释信息数据表创建失败或检查不存在，程序终止")

            for journal_data in journal_dict_list:
                self.process_journal_data(journal_data, conn)


def __run():
    user = parse.quote_plus("ndd")
    passwd = parse.quote_plus("ndd@2023")

    mongo_host = f'mongodb://{user}:{passwd}@************:32608/ndd-xg'
    db_name = 'ndd-xg'
    journal_col_name = "all_final_journal_202507022"
    publisher_col_name = "all_final_publisher_20250715"

    conn_params = {
        "host": "************",
        "port": 31910,
        "dbname": "postgres",
        "user": "postgres",
        "password": "Biosino+2025",
        "options": "-c search_path=public"
    }

    journal_table = "tb_dds_journal_250722"
    publisher_table = "tb_dds_publisher_250722"

    journal_mongo_object = SaveDataToMongoDB(mongo_host, db_name, journal_col_name)
    journal_mongo_collection = journal_mongo_object.init_mongodb()

    publisher_mongo_object = SaveDataToMongoDB(mongo_host, db_name, publisher_col_name)
    publisher_mongo_collection = publisher_mongo_object.init_mongodb()

    obj = DataProcess(publisher_mongo_collection, journal_mongo_collection, journal_table, publisher_table, conn_params)
    obj.process_all()

    print("代码结束", flush=True)


if __name__ == '__main__':
    __run()
