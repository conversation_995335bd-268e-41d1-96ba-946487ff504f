#!/usr/bin/env python
# -- coding: utf-8 --
# __author__ = "Xiong Gang"
# create_time: 2025/5/30 上午10:52
import logging
import os
import threading
import time
from datetime import datetime, timezone
from urllib import parse

import psycopg
import pymongo


class SaveDataToMongoDB:
    """
    mongodb里存入数据
    """

    def __init__(self, url, db, col):
        self.url = url
        self.db = db
        self.col = col

    def init_mongodb(self):
        """
        初始化数据库连接
        """
        client = pymongo.MongoClient(self.url)
        database = client[self.db]
        collection = database[self.col]
        return collection


def init_default_logger(log_file_name: str, curr_logger: logging.Logger = None) -> logging.Logger:
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    if not log_file_name.endswith('.log'):
        log_file_name = f'{log_file_name}.log'

    log_output_path = os.path.join(log_dir, log_file_name)

    if curr_logger is None:
        curr_logger = logging.getLogger(log_file_name)

    curr_logger.setLevel(logging.INFO)
    curr_logger.propagate = False  # 防止日志消息被传递到父记录器
    # 创建控制台处理器并设置格式
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    console_handler.setFormatter(formatter)
    curr_logger.addHandler(console_handler)

    # 创建文件处理器并设置格式 (用于提供日志报告)
    file_handler = logging.FileHandler(log_output_path, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    curr_logger.addHandler(file_handler)
    return curr_logger


# 初始化日志
log_current_time = datetime.now()
log_formatted_time = log_current_time.strftime("%Y%m%d%H%M%S")
logger = init_default_logger(f'insert_file_{log_formatted_time}.log')


class Snowflake:
    """ 雪花算法 """

    def __init__(self, worker_id, datacenter_id, sequence=0):
        # 分配各部分位数（可根据需要调整）
        self.worker_id_bits = 5
        self.datacenter_id_bits = 5
        self.sequence_bits = 12

        # 计算最大值
        self.max_worker_id = -1 ^ (-1 << self.worker_id_bits)
        self.max_datacenter_id = -1 ^ (-1 << self.datacenter_id_bits)
        if worker_id > self.max_worker_id or worker_id < 0:
            raise ValueError("worker_id 超出范围 0 到 %d" % self.max_worker_id)
        if datacenter_id > self.max_datacenter_id or datacenter_id < 0:
            raise ValueError("datacenter_id 超出范围 0 到 %d" % self.max_datacenter_id)

        self.worker_id = worker_id
        self.datacenter_id = datacenter_id
        self.sequence = sequence

        # 位移位数计算
        self.worker_id_shift = self.sequence_bits
        self.datacenter_id_shift = self.sequence_bits + self.worker_id_bits
        self.timestamp_left_shift = self.sequence_bits + self.worker_id_bits + self.datacenter_id_bits
        self.sequence_mask = -1 ^ (-1 << self.sequence_bits)
        # twepoch 为自定义纪元，这里用2021-01-01 00:00:00
        self.twepoch = 1609459200000

        self.lock = threading.Lock()
        self.last_timestamp = -1

    def _til_next_millis(self, last_timestamp):
        """等待下一毫秒"""
        timestamp = int(time.time() * 1000)
        while timestamp <= last_timestamp:
            timestamp = int(time.time() * 1000)
        return timestamp

    def get_id(self):
        """生成下一个 ID"""
        with self.lock:
            timestamp = int(time.time() * 1000)
            if timestamp < self.last_timestamp:
                raise Exception("时钟回拨错误. 当前时间 {} 小于上一次时间 {}".format(timestamp, self.last_timestamp))

            if self.last_timestamp == timestamp:
                # 同一毫秒内，序列号递增
                self.sequence = (self.sequence + 1) & self.sequence_mask
                if self.sequence == 0:
                    # 序列号用完，等待下一毫秒
                    timestamp = self._til_next_millis(self.last_timestamp)
            else:
                # 新的毫秒，序列号归零
                self.sequence = 0

            self.last_timestamp = timestamp
            # 生成 ID，各部分按照位移拼接
            new_id = ((timestamp - self.twepoch) << self.timestamp_left_shift) | \
                     (self.datacenter_id << self.datacenter_id_shift) | \
                     (self.worker_id << self.worker_id_shift) | \
                     self.sequence
            return new_id


class DataProcess:
    def __init__(self, publisher_mongo_collection, target_publisher_name, conn_params):
        self.publisher_mongo_collection = publisher_mongo_collection
        self.target_publisher_name = target_publisher_name
        self.snowflake = Snowflake(worker_id=1, datacenter_id=1)
        self.conn_params = conn_params

    def get_dict_data(self, dom, name):
        """
        安全地获取数据，避免keyErro异常
        :param dom: xml的dom树
        :param name: 字段名
        :return: 字段值
        """
        if dom is not None:
            data = dom.get(name, None)
            if data:
                return data
            else:
                return None
        else:
            return None

    def create_publisher_table(self, conn):
        try:
            # 创建表的SQL语句
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.target_publisher_name} (
                 id BIGINT PRIMARY KEY NOT NULL,
                 name VARCHAR(300) UNIQUE NOT NULL,
                 alias TEXT[],
                 ioc VARCHAR(300),
                 source_type VARCHAR(50),
                 status INTEGER NOT NULL,
                 update_time TIMESTAMPTZ NOT NULL,
                 create_time TIMESTAMPTZ NOT NULL
            );
            """

            with conn.cursor() as cur:
                cur.execute(create_table_sql)
                conn.commit()
                return True
        except Exception as e:
            logger.error(e)
            conn.rollback()
            return False

    def insert_publisher_to_postgres(self, conn, data):
        try:
            insert_sql = f"""
            INSERT INTO {self.target_publisher_name} (
                id, name, alias, ioc, source_type, status, update_time, create_time
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s
            )
            """

            with conn.cursor() as cur:
                cur.execute(insert_sql, data)
                conn.commit()
        except Exception as e:
            logger.error(f"问题是: {e}, 数据是: {data}")
            conn.rollback()

    def process_publisher_data(self, publisher_data, conn):
        snow_flake_id = self.snowflake.get_id()
        utc_now = datetime.now(timezone.utc)

        publisher_name = self.get_dict_data(publisher_data, 'publisher_name')
        publisher_ioc = self.get_dict_data(publisher_data, 'publisher_ioc')
        alias_publisher_name = self.get_dict_data(publisher_data, 'alias_publisher_name')

        publisher_id = snow_flake_id
        name = publisher_name
        alias = alias_publisher_name
        ioc = publisher_ioc
        source_type = "custom"
        status = 0
        update_time = utc_now
        create_time = utc_now

        publisher_data = (publisher_id, name, alias, ioc, source_type, status, update_time, create_time)
        self.insert_publisher_to_postgres(conn, publisher_data)

    def process_all(self):
        pubmed_publisher_list = list()
        publisher_data_dict_list = self.publisher_mongo_collection.find({})
        for publisher_data_dict in publisher_data_dict_list:
            new_data_dict = dict()
            pubmed_publisher = self.get_dict_data(publisher_data_dict, "publisher_name")
            pubmed_ioc = self.get_dict_data(publisher_data_dict, "jcr_publisher_address")
            alias_publisher_name = self.get_dict_data(publisher_data_dict, 'alias_publisher_name')
            if pubmed_publisher and pubmed_publisher not in pubmed_publisher_list:
                new_data_dict["publisher_name"] = pubmed_publisher
                new_data_dict["publisher_ioc"] = pubmed_ioc
                new_data_dict["alias_publisher_name"] = alias_publisher_name
                pubmed_publisher_list.append(new_data_dict)

        with psycopg.connect(**self.conn_params) as conn:
            # flag = self.create_publisher_table(conn)
            # if not flag:
            #     raise Exception("该注释信息数据表创建失败或检查不存在，程序终止")

            for publisher_data in pubmed_publisher_list:
                self.process_publisher_data(publisher_data, conn)


def __run():
    user = parse.quote_plus("ndd")
    passwd = parse.quote_plus("ndd@2023")

    mongo_host = f'mongodb://{user}:{passwd}@************:32608/ndd-xg'
    db_name = 'ndd-xg'
    publisher_col_name = "all_final_publisher_20250715"

    conn_params = {
        "host": "************",
        "port": 31910,
        "dbname": "postgres",
        "user": "postgres",
        "password": "Biosino+2025",
        "options": "-c search_path=public"
    }

    target_publisher_name = "tb_dds_publisher_250722"

    publisher_mongo_object = SaveDataToMongoDB(mongo_host, db_name, publisher_col_name)
    publisher_mongo_collection = publisher_mongo_object.init_mongodb()

    obj = DataProcess(publisher_mongo_collection, target_publisher_name, conn_params)
    obj.process_all()

    print("代码结束", flush=True)


if __name__ == '__main__':
    __run()
