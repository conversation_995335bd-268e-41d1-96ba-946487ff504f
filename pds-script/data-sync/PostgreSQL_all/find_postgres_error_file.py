#!/usr/bin/env python
# -- coding: utf-8 --
# __author__ = "Xiong Gang"
# create_time: 2024/6/12 上午9:54

import hashlib
import logging
import os
import queue
import threading
import time
import xml.etree.ElementTree as ET
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timezone
from io import StringIO
from time import strftime, gmtime
from typing import Optional

import psycopg
from bs4 import BeautifulSoup, Tag


def init_default_logger(log_file_name: str, curr_logger: Optional[logging.Logger] = None) -> logging.Logger:
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    if not log_file_name.endswith('.log'):
        log_file_name = f'{log_file_name}.log'

    log_output_path = os.path.join(log_dir, log_file_name)

    if curr_logger is None:
        curr_logger = logging.getLogger(log_file_name)

    curr_logger.setLevel(logging.INFO)
    curr_logger.propagate = False  # 防止日志消息被传递到父记录器
    # 创建控制台处理器并设置格式
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    console_handler.setFormatter(formatter)
    curr_logger.addHandler(console_handler)

    # 创建文件处理器并设置格式 (用于提供日志报告)
    file_handler = logging.FileHandler(log_output_path, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    curr_logger.addHandler(file_handler)
    return curr_logger


# 初始化日志
log_current_time = datetime.now()
log_formatted_time = log_current_time.strftime("%Y%m%d%H%M%S")
logger = init_default_logger(f'insert_pubmed_file_{log_formatted_time}.log')
logger2 = init_default_logger(f'pubmed_pmid_{log_formatted_time}.log')


class SnowFlake:
    """
    雪花算法实现类

    雪花算法是一种分布式ID生成算法，能够生成全局唯一的ID。
    ID结构：1位符号位 + 41位时间戳 + 5位数据中心ID + 5位工作机器ID + 12位序列号

    参数:
        worker_id: 工作机器ID (0-31)
        datacenter_id: 数据中心ID (0-31)
        sequence: 起始序列号 (默认为0)
    """

    def __init__(self, worker_id, datacenter_id, sequence=0):
        # 各部分位数
        self.worker_id_bits = 5  # 工作机器ID位数
        self.datacenter_id_bits = 5  # 数据中心ID位数
        self.sequence_bits = 12  # 序列号位数

        # 各部分最大值
        self.max_worker_id = -1 ^ (-1 << self.worker_id_bits)  # 工作机器ID最大值（31）
        self.max_datacenter_id = -1 ^ (-1 << self.datacenter_id_bits)  # 数据中心ID最大值（31）
        self.max_sequence = -1 ^ (-1 << self.sequence_bits)  # 序列号最大值（4095）

        # 验证参数合法性
        if worker_id > self.max_worker_id or worker_id < 0:
            raise ValueError(f"worker_id 超出范围 0 到 {self.max_worker_id}")
        if datacenter_id > self.max_datacenter_id or datacenter_id < 0:
            raise ValueError(f"datacenter_id 超出范围 0 到 {self.max_datacenter_id}")

        # 保存ID
        self.worker_id = worker_id
        self.datacenter_id = datacenter_id
        self.sequence = sequence

        # 位移计算
        self.worker_id_shift = self.sequence_bits  # 工作机器ID左移位数（12）
        self.datacenter_id_shift = self.sequence_bits + self.worker_id_bits  # 数据中心ID左移位数（17）
        self.timestamp_shift = self.sequence_bits + self.worker_id_bits + self.datacenter_id_bits  # 时间戳左移位数（22）

        # 开始时间戳（2021-01-01 00:00:00 的毫秒级时间戳）
        self.twepoch = 1609459200000

        # 上次生成ID的时间戳
        self.last_timestamp = -1

        # 线程锁，保证并发安全
        self.lock = threading.Lock()

    def _next_millis(self, last_timestamp):
        """
        获取下一毫秒时间戳

        参数:
            last_timestamp: 上次生成ID的时间戳

        返回:
            下一毫秒的时间戳
        """
        timestamp = self._get_time()
        while timestamp <= last_timestamp:
            timestamp = self._get_time()
        return timestamp

    def _get_time(self):
        """
        获取当前毫秒时间戳

        返回:
            当前毫秒时间戳
        """
        return int(time.time() * 1000)

    def get_id(self):
        """
        生成下一个ID

        返回:
            生成的ID
        """
        with self.lock:
            timestamp = self._get_time()

            # 如果当前时间小于上次生成ID的时间，说明系统时钟回退，抛出异常
            if timestamp < self.last_timestamp:
                raise Exception(f"时钟回拨异常，拒绝生成ID，当前时间: {timestamp}，上次时间: {self.last_timestamp}")

            # 如果是同一毫秒内，则递增序列号
            if timestamp == self.last_timestamp:
                self.sequence = (self.sequence + 1) & self.max_sequence
                # 如果序列号溢出，则等待下一毫秒
                if self.sequence == 0:
                    timestamp = self._next_millis(self.last_timestamp)
            else:
                # 不是同一毫秒，序列号重置为0
                self.sequence = 0

            # 保存本次的时间戳
            self.last_timestamp = timestamp

            # 生成并返回ID
            return ((timestamp - self.twepoch) << self.timestamp_shift) | \
                (self.datacenter_id << self.datacenter_id_shift) | \
                (self.worker_id << self.worker_id_shift) | \
                self.sequence


class QueryTask:
    def __init__(self, conn_params, summary_table, content_table, num_workers):
        self.snowflake = SnowFlake(worker_id=1, datacenter_id=1)
        self.conn_params = conn_params
        self.summary_table = summary_table
        self.content_table = content_table
        self.tag_name = "PubmedArticle"

        self.num_workers = num_workers
        self.data_queue = queue.Queue(maxsize=10000)
        self.lock = threading.Lock()

    def get_dict_data(self, dom, name):
        """
        安全地获取数据，避免keyErro异常
        :param dom: xml的dom树
        :param name: 字段名
        :return: 字段值
        """
        if dom is not None:
            data = dom.get(name, None)
            if data:
                return data
            else:
                return None
        else:
            return None

    def insert_summary_information_to_postgres(self, conn, data):
        try:
            insert_sql = f"""
            INSERT INTO {self.summary_table} (
                id, file_name, local_path, content_type, 
                source_id, source, status, version, file_md5, create_time,
                update_time
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            )
            """

            with conn.cursor() as cur:
                cur.execute(insert_sql, data)
                conn.commit()
        except Exception as e:
            logger.error(f"插入摘要数据失败: {e}")
            conn.rollback()

    def insert_file_content_to_postgres(self, conn, data):
        try:
            insert_sql = f"""
            INSERT INTO {self.content_table} (
                id, file_data
            ) VALUES (
                %s, %s
            )
            """

            with conn.cursor() as cur:
                cur.execute(insert_sql, data)
                conn.commit()
        except Exception as e:
            logger.error(f"插入内容数据失败: {e}")
            conn.rollback()

    def delete_content_data_by_id(self, conn, data_id):
        try:
            delete_sql = f"""
            DELETE FROM {self.content_table} WHERE id = %s
            """

            with conn.cursor() as cur:
                cur.execute(delete_sql, (data_id,))
                conn.commit()
        except Exception as e:
            logger.error(f"删除{data_id}的数据失败: {e}")
            conn.rollback()

    def delete_summary_data_by_id(self, conn, data_id):
        try:
            delete_sql = f"""
            DELETE FROM {self.summary_table} WHERE id = %s
            """

            with conn.cursor() as cur:
                cur.execute(delete_sql, (data_id,))
                conn.commit()
        except Exception as e:
            logger.error(f"删除{data_id}的数据失败: {e}")
            conn.rollback()

    def find_md5_exists(self, conn, data_md5):
        """ 判断md5的值是否存在 """
        try:
            select_md5_sql = f"""
            SELECT EXISTS (
                SELECT 1 FROM {self.summary_table} 
                WHERE file_md5 = %s LIMIT 1
            );
            """

            with conn.cursor() as cur:
                cur.execute(select_md5_sql, (data_md5,))
                exists = cur.fetchone()[0]
                return exists

        except Exception as e:
            logger.error(f"检查MD5是否存在时出错: {e}, md5数据是: {data_md5}")
            return False

    def check_pmid_version(self, conn, pmid, new_version):
        """
        检查PMID在数据库中是否存在，如果存在则比较版本号
        返回:
            -1: PMID不存在
            0: 版本相等，需要更新
            1: 新版本大于库中版本，需要更新
            2: 新版本小于库中版本，忽略
        """
        try:
            select_sql = f"""
            SELECT id, version FROM {self.summary_table}
            WHERE source_id = %s AND source = 'Pubmed';
            """

            with conn.cursor() as cur:
                cur.execute(select_sql, (pmid,))
                result = cur.fetchone()

                if result:
                    db_id, db_version = result
                    # 将版本转换为整数进行比较
                    try:
                        db_version = int(db_version)
                        new_version = int(new_version)
                    except ValueError:
                        # 如果版本不是有效整数，则默认为相等
                        logger.warning(f"无效的版本号格式: db_version={db_version}, new_version={new_version}")
                        return 0, db_id

                    if new_version > db_version:
                        return 1, db_id  # 新版本大于库中版本
                    elif new_version < db_version:
                        return 2, db_id  # 新版本小于库中版本
                    else:
                        return 1, db_id  # 版本相等
                else:
                    return -1, None  # PMID不存在
        except Exception as e:
            logger.error(f"检查PMID版本时出错: {e}, PMID={pmid}, version={new_version}")
            return -1, None

    def find_content_data_by_id(self, snow_flak_id, conn):
        try:
            select_md5_sql = f"""
            SELECT file_data FROM {self.content_table} WHERE id = %s
            """

            with conn.cursor() as cur:
                cur.execute(select_md5_sql, (snow_flak_id,))
                result = cur.fetchone()[0]
                if result:
                    file_data = result
                return file_data

        except Exception as e:
            logger.error(f"查找xml原始数据时出错: {e}, snow_flak_id 数据是: {snow_flak_id}")

    def update_existing_record(self, conn, db_id, summary_data, content_data):
        """
        更新已存在的记录
        """
        try:
            # 提取必要的数据
            _, file_name, local_path, content_type, source_id, source, status, version, file_md5, _, update_time = summary_data
            _, binary_data = content_data

            # 更新摘要信息
            update_summary_sql = f"""
            UPDATE {self.summary_table}
            SET file_name = %s,
                local_path = %s,
                content_type = %s,
                status = %s,
                version = %s,
                file_md5 = %s,
                update_time = %s
            WHERE id = %s;
            """

            # 更新内容信息
            update_content_sql = f"""
            UPDATE {self.content_table}
            SET file_data = %s
            WHERE id = %s;
            """

            with conn.cursor() as cur:
                # 更新摘要表
                cur.execute(update_summary_sql, (
                    file_name, local_path, content_type, status, version, file_md5, update_time, db_id
                ))

                # 更新内容表
                cur.execute(update_content_sql, (binary_data, db_id))

                conn.commit()
                logger.debug(f"成功更新记录，ID: {db_id}")
                return True
        except Exception as e:
            logger.error(f"更新记录时出错: {e}, id={db_id}")
            conn.rollback()
            return False

    def get_text_data(self, dom, dom_path):
        """
        获取xml的文本值
        """
        element = dom.select_one(dom_path)
        if element:
            dom_text = element.text.strip()
            return dom_text
        else:
            return None

    def get_pubmed_data(self, data):
        """
        解析xml文件内容
        """
        pmid = None
        version_value = 1
        if data is not None and data:
            try:
                soup = BeautifulSoup(data, 'xml')

                medline_citation_data = soup.select_one("MedlineCitation")
                if medline_citation_data:
                    # 查找 PMID 标签
                    pmid_tag = soup.find('PMID')
                    if isinstance(pmid_tag, Tag) and pmid_tag.get('Version'):
                        version_value = pmid_tag.get('Version', 1)

                    pmid = self.get_text_data(medline_citation_data, "PMID")

                return pmid, version_value
            except Exception as e:
                logger.error(f"解析XML数据出错: {e}")
                return None, None

    def process_xml_file(self, xml_str, conn):
        """处理单个XML文件并返回处理结果"""
        try:
            pmid, version_value = self.get_pubmed_data(xml_str)
            logger2.info(f"{pmid}\t{version_value}")
            xml_bytes = xml_str.encode('utf-8')
            data_md5 = hashlib.md5(xml_bytes).hexdigest()

            # 检查MD5是否已存在
            if self.find_md5_exists(conn, data_md5):
                return None

            # 检查PMID是否存在及版本情况
            version_status, existing_id = self.check_pmid_version(conn, pmid, version_value)

            if version_status == 2:
                # 新版本小于库中版本，忽略
                return False

            snow_flake_id = self.snowflake.get_id()

            # 如果是更新现有记录
            if version_status == 1 and existing_id:
                id = existing_id  # 使用已存在的ID
            else:
                id = snow_flake_id  # 使用新生成的ID

            utc_now = datetime.now(timezone.utc)

            file_name = f"{pmid}.xml"
            local_path = f"{pmid}.xml"  # 相对路径
            content_type = "xml"
            source_id = pmid
            source = "Pubmed"
            status = 1
            version = version_value
            file_md5 = data_md5
            create_time = utc_now
            update_time = utc_now

            # 返回处理结果
            summary_data = (id, file_name, local_path, content_type, source_id,
                            source, status, version, file_md5, create_time, update_time)
            content_data = (id, xml_bytes)

            if summary_data and content_data:
                # 如果是更新现有记录
                if version_status == 1 and existing_id:
                    self.update_existing_record(conn, existing_id, summary_data, content_data)
                else:
                    # 直接插入新记录
                    self.insert_summary_information_to_postgres(conn, summary_data)
                    self.insert_file_content_to_postgres(conn, content_data)

        except Exception as e:
            return None

    def has_multiple_paired_tags_bs(self, xml_data):
        """
        使用BeautifulSoup检查XML中是否存在多个成对的目标标签
        :param xml_data: XML字节数据
        :param target_tag: 要检测的标签名称
        :param debug: 是否启用调试模式
        :return: 是否存在多个成对的标签
        """
        if not xml_data:
            return False, 0

        try:
            # 使用lxml-xml解析器获取高性能
            soup = BeautifulSoup(xml_data, 'xml')

            # 查找所有目标标签
            tags = soup.find_all(self.tag_name)
            tag_count = len(tags)

            # 如果标签少于2个，不可能有多个成对标签
            if tag_count < 2:
                return False, tag_count

            return tag_count >= 2, tag_count
        except Exception as e:
            logging.error(f"XML解析失败: {e}")

    def splite_big_xml(self, xml_str, conn):
        try:
            xml_file = StringIO(xml_str)

            # 打开 XML 文件并创建迭代器
            context = ET.iterparse(xml_file, events=('start', 'end'))

            # 初始化一个堆栈用于跟踪打开的<...>元素
            xml_stack = []

            # 遍历每个元素的开始和结束事件
            for event, elem in context:
                # 如果是<...>元素的开始事件
                if event == 'start' and elem.tag == self.tag_name:
                    # 将当前<...>元素推入堆栈
                    xml_stack.append(elem)

                # 如果是</...>元素的结束事件
                elif event == 'end' and elem.tag == self.tag_name:
                    # 获取最近的打开的<...>元素
                    xml_elem = xml_stack[-1]

                    # 创建一个新的 ElementTree 对象，用于存储当前<...>内容
                    xml_elem_tree = ET.ElementTree(xml_elem)

                    xml_str = ET.tostring(xml_elem_tree.getroot(), encoding='utf-8').decode('utf-8')

                    self.process_xml_file(xml_str, conn)

                    # 清除当前<...>元素，释放内存
                    xml_elem.clear()

                    # 将当前<...>元素从堆栈中弹出
                    xml_stack.pop()
        except Exception as e:
            logger.error(f"分割xml出现问题: {e}")

    def delete_data_by_id(self, data_id, conn):
        self.delete_summary_data_by_id(conn, data_id)

        self.delete_content_data_by_id(conn, data_id)

    def process_pubmed_data(self, data_id, xml_data, conn):

        self.delete_data_by_id(data_id, conn)

        self.splite_big_xml(xml_data, conn)

    def find_data(self):
        logger.info("文件查找线程启动")
        total_files = 0
        with psycopg.connect(**self.conn_params) as conn:
            with conn.cursor() as cur:
                select_sql = f"SELECT * FROM {self.summary_table} WHERE status = '0'"
                cur.execute(select_sql)
                data_list = cur.fetchall()
                column_names = [desc[0] for desc in cur.description]

                # 使用列表推导式转换
                dict_list = [
                    dict(zip(column_names, row))
                    for row in data_list
                ]

                for row in dict_list:
                    try:
                        data_dict = dict()
                        error_msg = self.get_dict_data(row, "error_msg")
                        id = self.get_dict_data(row, "id")
                        source_id = self.get_dict_data(row, "source_id")

                        if error_msg and "XML中存在" in error_msg:
                            data_dict['snow_flak_id'] = id
                            data_dict['data_id'] = int(source_id)
                            self.data_queue.put(data_dict)
                            total_files = total_files + 1
                    except Exception as e:
                        logger.error(f"遍历数据表时，数据出现问题: {e}")
        logger.info(f"文件查找完成，总共找到 {total_files} 个XML文件")
        self.data_queue.put(None)
        logger.info(f"已处理完所有的数据")

    def worker(self):
        """
        将 旧数据集数据 放到新数据集里去找，如果没找到就是有问题

        """
        self.is_running = True
        while self.is_running:
            try:
                data = self.data_queue.get_nowait()
            except queue.Empty:
                with self.lock:
                    if not self.is_running:
                        logger.info("接收到结束信号, 退出工作线程")
                        break
                time.sleep(0.1)  # 短暂睡眠，避免CPU占用过高
                continue
            if data:
                try:
                    with psycopg.connect(**self.conn_params) as conn:
                        data_id = self.get_dict_data(data, "data_id")
                        snow_flak_id = self.get_dict_data(data, "snow_flak_id")
                        logger.info(f"[源pmid]正在处理 {data_id}")

                        xml_data = self.find_content_data_by_id(snow_flak_id, conn)
                        if xml_data:
                            xml_str = xml_data.decode('utf-8')
                            self.process_pubmed_data(snow_flak_id, xml_str, conn)

                except Exception as e:
                    logger.error(f"数据库写入出错: {e}")
            elif data is None:  # 使用 None 作为队列结束的信号
                logger.info("数据任务，接收到结束信号, 退出工作线程")
                with self.lock:
                    self.is_running = False
                self.data_queue.task_done()
                continue
            self.data_queue.task_done()

    # 使用线程池并发处理数据
    def process_concurrently(self):
        temps = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        logger.info(f"程序开始时间: {temps}")
        time_start = time.time()  # 记录开始时间
        ######################################################################

        # 主要代码
        # 初始化线程池
        all_workers_num = self.num_workers + 1
        with ThreadPoolExecutor(max_workers=all_workers_num) as executor:
            logger.info("已提交任务到线程池")
            # 提交解析的任务到线程池
            executor.submit(self.find_data)
            logger.info("创建多条工作线程，进行数据处理")
            # 创建工作线程
            for _ in range(self.num_workers):
                executor.submit(self.worker)

        # 等待队列被处理完毕
        self.data_queue.join()

        ######################################################################
        logger.info("多线程存储数据已结束")
        temps2 = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        logger.info(f"程序结束时间: {temps2}")

        time_end = time.time()  # 记录结束时间
        run_time = time_end - time_start  # 计算的时间差为程序的执行时间，单位为秒/s
        run_time = strftime("%H:%M:%S", gmtime(run_time))
        logger.info(f'运行时间为: {run_time}')


def __run():

    conn_params = {
        "host": "************",
        "port": 31910,
        "dbname": "postgres",
        "user": "postgres",
        "password": "Biosino+2025",
        "options": "-c search_path=public"
    }
    summary_table = "tb_dds_article_parse"
    content_table = "tb_dds_article_xml"
    num_workers = 10 # 线程数，可以根据CPU核心数调整

    all_obj = QueryTask(conn_params, summary_table, content_table, num_workers)
    all_obj.process_concurrently()


if __name__ == '__main__':
    __run()
