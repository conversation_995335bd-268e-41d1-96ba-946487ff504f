#!/usr/bin/env python
# -- coding: utf-8 --
# __author__ = "Xiong Gang"
# create_time: 2025/7/1 下午4:59

import logging
import os
from datetime import datetime
from typing import Optional

import psycopg


def init_default_logger(log_file_name: str, curr_logger: Optional[logging.Logger] = None) -> logging.Logger:
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    if not log_file_name.endswith('.log'):
        log_file_name = f'{log_file_name}.log'

    log_output_path = os.path.join(log_dir, log_file_name)

    if curr_logger is None:
        curr_logger = logging.getLogger(log_file_name)

    curr_logger.setLevel(logging.INFO)
    curr_logger.propagate = False  # 防止日志消息被传递到父记录器
    # 创建控制台处理器并设置格式
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    console_handler.setFormatter(formatter)
    curr_logger.addHandler(console_handler)

    # 创建文件处理器并设置格式 (用于提供日志报告)
    file_handler = logging.FileHandler(log_output_path, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    curr_logger.addHandler(file_handler)
    return curr_logger


# 初始化日志
log_current_time = datetime.now()
log_formatted_time = log_current_time.strftime("%Y%m%d%H%M%S")
logger = init_default_logger(f'delete_file_{log_formatted_time}.log')


class FileProcess:
    def __init__(self, conn_params, main_table, related_table, related_id, batch_size):
        self.conn_params = conn_params
        self.batch_size = batch_size
        self.main_table = main_table
        self.related_table = related_table  # 需要同步删除的关联表名
        self.id_column = related_id  # 主键列名

    def delete_pmc_data_from_postgres(self, conn):
        try:
            # 获取需要删除的总记录数
            with conn.cursor() as cur:
                cur.execute(f"SELECT COUNT(*) FROM {self.main_table} WHERE source = 'PMC'")
                total_records = cur.fetchone()[0]
                
            logger.info(f"需要删除的总记录数: {total_records}")
            
            if total_records == 0:
                logger.info("没有找到需要删除的PMC数据")
                return
                
            # 分批处理删除
            offset = 0
            total_deleted = 0
            
            while offset < total_records:
                # 获取当前批次要删除的ID
                with conn.cursor() as cur:
                    cur.execute(
                        f"SELECT {self.id_column} FROM {self.main_table} "
                        f"WHERE source = 'PMC' "
                        f"ORDER BY {self.id_column} "
                        f"LIMIT {self.batch_size} OFFSET {offset}"
                    )
                    ids = [row[0] for row in cur.fetchall()]
                
                if not ids:
                    break
                    
                # 使用获取的ID删除关联表中的数据
                with conn.cursor() as cur:
                    placeholders = ','.join(['%s'] * len(ids))
                    related_delete_sql = f"""
                    DELETE FROM {self.related_table} 
                    WHERE {self.id_column} IN ({placeholders})
                    """
                    cur.execute(related_delete_sql, ids)
                    related_deleted = cur.rowcount
                    logger.info(f"已从关联表 {self.related_table} 删除 {related_deleted} 条记录")
                
                # 删除主表中的数据
                with conn.cursor() as cur:
                    main_delete_sql = f"""
                    DELETE FROM {self.main_table} 
                    WHERE {self.id_column} IN ({placeholders})
                    """
                    cur.execute(main_delete_sql, ids)
                    batch_deleted = cur.rowcount
                    
                conn.commit()
                total_deleted += batch_deleted
                logger.info(f"已删除 {total_deleted}/{total_records} 条记录 ({(total_deleted/total_records*100):.2f}%)")
                
                # 如果本批次删除的记录数小于批次大小，说明已经删除完毕
                if batch_deleted < self.batch_size:
                    break
                    
                # 不需要增加offset，因为我们每次都是删除最前面的记录
                
        except Exception as e:
            logger.error(f"删除数据时出错: {e}")
            conn.rollback()
            raise

    def delete_pmc_data(self):
        """
        处理PMC数据的删除
        """
        with psycopg.connect(**self.conn_params) as conn:
            logger.info("开始删除PMC数据")
            self.delete_pmc_data_from_postgres(conn)
            logger.info("PMC数据删除完成")

    def process_all(self):
        self.delete_pmc_data()


def __run():
    conn_params = {
        "host": "************",
        "port": 31910,
        "dbname": "postgres",
        "user": "postgres",
        "password": "Biosino+2025",
        "options": "-c search_path=public"
    }
    main_table = "tb_dds_article_parse"
    related_table = "tb_dds_article_xml"
    related_id = "id"
    batch_size = 10000

    obj = FileProcess(conn_params, main_table, related_table, related_id, batch_size)
    obj.process_all()


if __name__ == "__main__":
    __run()
