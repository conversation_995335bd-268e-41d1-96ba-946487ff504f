#!/usr/bin/env python
# -- coding: utf-8 --
# __author__ = "Xiong Gang"
# create_time: 2024/6/12 上午9:54

import logging
import os
import threading
import time
from datetime import datetime, timezone
from typing import Optional
from urllib import parse

import numpy as np
import pandas as pd
import psycopg
import pymongo


def init_default_logger(log_file_name: str, curr_logger: Optional[logging.Logger] = None) -> logging.Logger:
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    if not log_file_name.endswith('.log'):
        log_file_name = f'{log_file_name}.log'

    log_output_path = os.path.join(log_dir, log_file_name)

    if curr_logger is None:
        curr_logger = logging.getLogger(log_file_name)

    curr_logger.setLevel(logging.INFO)
    curr_logger.propagate = False  # 防止日志消息被传递到父记录器
    # 创建控制台处理器并设置格式
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    console_handler.setFormatter(formatter)
    curr_logger.addHandler(console_handler)

    # 创建文件处理器并设置格式 (用于提供日志报告)
    file_handler = logging.FileHandler(log_output_path, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    curr_logger.addHandler(file_handler)
    return curr_logger


# 初始化日志
log_current_time = datetime.now()
log_formatted_time = log_current_time.strftime("%Y%m%d%H%M%S")
logger = init_default_logger(f'insert_pubmed_file_{log_formatted_time}.log')
logger2 = init_default_logger(f'pubmed_pmid_{log_formatted_time}.log')


class SnowFlake:
    """
    雪花算法实现类

    雪花算法是一种分布式ID生成算法，能够生成全局唯一的ID。
    ID结构：1位符号位 + 41位时间戳 + 5位数据中心ID + 5位工作机器ID + 12位序列号

    参数:
        worker_id: 工作机器ID (0-31)
        datacenter_id: 数据中心ID (0-31)
        sequence: 起始序列号 (默认为0)
    """

    def __init__(self, worker_id, datacenter_id, sequence=0):
        # 各部分位数
        self.worker_id_bits = 5  # 工作机器ID位数
        self.datacenter_id_bits = 5  # 数据中心ID位数
        self.sequence_bits = 12  # 序列号位数

        # 各部分最大值
        self.max_worker_id = -1 ^ (-1 << self.worker_id_bits)  # 工作机器ID最大值（31）
        self.max_datacenter_id = -1 ^ (-1 << self.datacenter_id_bits)  # 数据中心ID最大值（31）
        self.max_sequence = -1 ^ (-1 << self.sequence_bits)  # 序列号最大值（4095）

        # 验证参数合法性
        if worker_id > self.max_worker_id or worker_id < 0:
            raise ValueError(f"worker_id 超出范围 0 到 {self.max_worker_id}")
        if datacenter_id > self.max_datacenter_id or datacenter_id < 0:
            raise ValueError(f"datacenter_id 超出范围 0 到 {self.max_datacenter_id}")

        # 保存ID
        self.worker_id = worker_id
        self.datacenter_id = datacenter_id
        self.sequence = sequence

        # 位移计算
        self.worker_id_shift = self.sequence_bits  # 工作机器ID左移位数（12）
        self.datacenter_id_shift = self.sequence_bits + self.worker_id_bits  # 数据中心ID左移位数（17）
        self.timestamp_shift = self.sequence_bits + self.worker_id_bits + self.datacenter_id_bits  # 时间戳左移位数（22）

        # 开始时间戳（2021-01-01 00:00:00 的毫秒级时间戳）
        self.twepoch = 1609459200000

        # 上次生成ID的时间戳
        self.last_timestamp = -1

        # 线程锁，保证并发安全
        self.lock = threading.Lock()

    def _next_millis(self, last_timestamp):
        """
        获取下一毫秒时间戳

        参数:
            last_timestamp: 上次生成ID的时间戳

        返回:
            下一毫秒的时间戳
        """
        timestamp = self._get_time()
        while timestamp <= last_timestamp:
            timestamp = self._get_time()
        return timestamp

    def _get_time(self):
        """
        获取当前毫秒时间戳

        返回:
            当前毫秒时间戳
        """
        return int(time.time() * 1000)

    def get_id(self):
        """
        生成下一个ID

        返回:
            生成的ID
        """
        with self.lock:
            timestamp = self._get_time()

            # 如果当前时间小于上次生成ID的时间，说明系统时钟回退，抛出异常
            if timestamp < self.last_timestamp:
                raise Exception(f"时钟回拨异常，拒绝生成ID，当前时间: {timestamp}，上次时间: {self.last_timestamp}")

            # 如果是同一毫秒内，则递增序列号
            if timestamp == self.last_timestamp:
                self.sequence = (self.sequence + 1) & self.max_sequence
                # 如果序列号溢出，则等待下一毫秒
                if self.sequence == 0:
                    timestamp = self._next_millis(self.last_timestamp)
            else:
                # 不是同一毫秒，序列号重置为0
                self.sequence = 0

            # 保存本次的时间戳
            self.last_timestamp = timestamp

            # 生成并返回ID
            return ((timestamp - self.twepoch) << self.timestamp_shift) | \
                (self.datacenter_id << self.datacenter_id_shift) | \
                (self.worker_id << self.worker_id_shift) | \
                self.sequence


class SaveDataToMongoDB:
    """
    mongodb里存入数据
    """

    def __init__(self, url, db, col):
        self.url = url
        self.db = db
        self.col = col

    def init_mongodb(self):
        """
        初始化数据库连接
        """
        client = pymongo.MongoClient(self.url)
        database = client[self.db]
        collection = database[self.col]
        return collection


class DataProcess:
    def __init__(self, conn_params, jcr_impact_factor_table, zky_impact_factor_table, journal_table,
                 jcr_mongo_collection, zky_file_path):
        self.snowflake = SnowFlake(worker_id=3, datacenter_id=3)
        self.conn_params = conn_params
        self.journal_table = journal_table
        self.jcr_impact_factor_table = jcr_impact_factor_table
        self.zky_impact_factor_table = zky_impact_factor_table
        self.jcr_mongo_collection = jcr_mongo_collection
        self.zky_file_path = zky_file_path

    def get_dict_data(self, dom, name):
        """
        安全地获取数据，避免keyErro异常
        :param dom: xml的dom树
        :param name: 字段名
        :return: 字段值
        """
        if dom is not None:
            data = dom.get(name, None)
            if data and data!="N/A":
                return data
            else:
                return None
        else:
            return None

    def create_jcr_impact_factor_table(self, conn):
        try:
            # 创建表的SQL语句
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.jcr_impact_factor_table} (
                 id BIGINT PRIMARY KEY NOT NULL,
                 journal_id BIGINT NOT NULL,
                 year VARCHAR(300),
                 impact_factor VARCHAR(50),
                 jcr_quartile VARCHAR(50),
                 create_time TIMESTAMPTZ NOT NULL,
                 update_time TIMESTAMPTZ NOT NULL
            );
            """

            with conn.cursor() as cur:
                cur.execute(create_table_sql)
                conn.commit()
                return True
        except Exception as e:
            logger.error(e)
            conn.rollback()
            return False

    def create_zky_impact_factor_table(self, conn):
        try:
            # 创建表的SQL语句
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.zky_impact_factor_table} (
                 id BIGINT PRIMARY KEY NOT NULL,
                 journal_id BIGINT NOT NULL,
                 top TEXT,
                 year VARCHAR(300),
                 large_category VARCHAR(300),
                 large_category_section INTEGER,
                 subclass_1 VARCHAR(300),
                 subclass_1_section INTEGER,
                 subclass_2 VARCHAR(300),
                 subclass_2_section INTEGER,
                 subclass_3 VARCHAR(300),
                 subclass_3_section INTEGER,
                 subclass_4 VARCHAR(300),
                 subclass_4_section INTEGER,
                 subclass_5 VARCHAR(300),
                 subclass_5_section INTEGER,
                 subclass_6 VARCHAR(300),
                 subclass_6_section INTEGER,
                 create_time TIMESTAMPTZ NOT NULL,
                 update_time TIMESTAMPTZ NOT NULL
            );
            """

            with conn.cursor() as cur:
                cur.execute(create_table_sql)
                conn.commit()
                return True
        except Exception as e:
            logger.error(e)
            conn.rollback()
            return False

    def insert_jcr_impact_factor_to_postgres(self, conn, data):
        try:
            insert_sql = f"""
            INSERT INTO {self.jcr_impact_factor_table} (
                id, journal_id, year, impact_factor, 
                jcr_quartile, create_time, update_time
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s
            )
            """

            with conn.cursor() as cur:
                cur.execute(insert_sql, data)
                conn.commit()
        except Exception as e:
            logger.error(f"JCR 插入摘要数据失败: {e}")
            conn.rollback()

    def insert_zky_impact_factor_to_postgres(self, conn, data):
        try:
            insert_sql = f"""
            INSERT INTO {self.zky_impact_factor_table} (
                id, journal_id, top, year, large_category, large_category_section,
                subclass_1, subclass_1_section, 
                subclass_2, subclass_2_section,
                subclass_3, subclass_3_section,
                subclass_4, subclass_4_section,
                subclass_5, subclass_5_section,
                subclass_6, subclass_6_section,
                create_time, update_time
            ) VALUES (
                {','.join(['%s'] * 20)}
            )
            """

            with conn.cursor() as cur:
                cur.execute(insert_sql, data)
                conn.commit()
        except Exception as e:
            logger.error(f"ZKY 插入内容数据失败: {e}")
            logger.error(f"{data}")
            conn.rollback()

    def find_journal_id_by_issn(self, conn, issn_list):
        try:
            select_publisher_sql = f"""
                    SELECT id FROM {self.journal_table} WHERE issn_history && %s
                    """

            with conn.cursor() as cur:
                cur.execute(select_publisher_sql, (issn_list,))
                exists = cur.fetchone()
                if exists:
                    return exists[0]
                else:
                    return None

        except Exception as e:
            logger.error(f"问题是: {e}, issn列表是: {issn_list}")

    def get_text_data(self, dom, dom_path):
        """
        获取xml的文本值
        """
        element = dom.select_one(dom_path)
        if element:
            dom_text = element.text.strip()
            return dom_text
        else:
            return None

    def remove_none_values(self, dict_list):
        """
        清除每个字典的None值
        """
        for dictionary in dict_list:
            keys_to_remove = []
            for key, value in dictionary.items():
                if value is None:
                    keys_to_remove.append(key)
            for key in keys_to_remove:
                del dictionary[key]
        return dict_list

    def get_name_cn(self, str):
        """
        分割字符串，获取数据
        ONCOLOGY 肿瘤学
        """
        result = dict()
        new_str = str.strip()
        if not str:
            result['name'] = ''
            result['name_en'] = ''
            result['name_cn'] = ''
            return result
        index = 0
        for s in new_str.encode('utf-8').decode('utf-8'):
            if u'\u4e00' <= s <= u'\u9fff':
                break
            else:
                index += 1
        result['name'] = new_str
        result['name_en'] = new_str[0:index - 1]
        result['name_cn'] = new_str[index: len(str)]
        return result

    def check_data(self, data):
        new_data = int(np.nan_to_num(data))
        if new_data:
            return new_data
        else:
            return None

    def insert_jcr_to_postgressql(self, conn):
        jcr_dict_list = self.jcr_mongo_collection.find({})
        for jcr_dict in list(jcr_dict_list):
            issn_list = list()
            issn = self.get_dict_data(jcr_dict, "issn")
            eissn = self.get_dict_data(jcr_dict, "eissn")
            jif2019 = self.get_dict_data(jcr_dict, "jif2019")
            quartile = self.get_dict_data(jcr_dict, "quartile")
            jcr_year = self.get_dict_data(jcr_dict, "jcrYear")

            if issn and issn != 'N/A':
                issn_list.append(issn)
            if eissn and eissn != 'N/A':
                issn_list.append(eissn)
            if issn or eissn:
                journal_id = self.find_journal_id_by_issn(conn, issn_list)
                if journal_id:
                    snow_flake_id = self.snowflake.get_id()
                    utc_now = datetime.now(timezone.utc)

                    jcr_id = snow_flake_id
                    year = jcr_year
                    impact_factor = jif2019
                    jcr_quartile = quartile
                    create_time = utc_now
                    update_time = utc_now

                    jcr_data = (jcr_id, journal_id, year, impact_factor, jcr_quartile, create_time, update_time)

                    self.insert_jcr_impact_factor_to_postgres(conn, jcr_data)

        logger.info("JCR数据入库完毕")

    def insert_zky_to_postgressql(self, conn):
        zky_df = pd.read_csv(self.zky_file_path)
        zky_df = zky_df.dropna(how='all')  # 删除缺失值（na）的行
        zky_df = zky_df.where(zky_df.notnull(), None)  # 将非空数据保留，空数据用None替换
        zky_data_dict_list = zky_df.to_dict('records')  # 将 Pandas 数据帧转换为字典列表
        zky_clean_data_dict_list = self.remove_none_values(zky_data_dict_list)
        for zky_clean_data_dict in zky_clean_data_dict_list:
            subclass_1_en, subclass_1_cn, subclass_2_en, subclass_2_cn, subclass_3_en, subclass_3_cn, subclass_4_en, subclass_4_cn, subclass_5_en, subclass_5_cn, subclass_6_en, subclass_6_cn = [None] * 12
            zky_year = self.get_dict_data(zky_clean_data_dict, "年份")
            issn = self.get_dict_data(zky_clean_data_dict, "ISSN")
            large_category = self.get_dict_data(zky_clean_data_dict, "大类")
            large_category_section = self.get_dict_data(zky_clean_data_dict, "大类分区")
            large_category_section = self.check_data(large_category_section)
            top = self.get_dict_data(zky_clean_data_dict, "Top")
            subclass_1 = self.get_dict_data(zky_clean_data_dict, "小类1")
            if subclass_1 is not None:
                subclass_1_dict = self.get_name_cn(subclass_1)
                subclass_1 = subclass_1_dict['name']
                subclass_1_en = subclass_1_dict['name_en']
                subclass_1_cn = subclass_1_dict['name_cn']
            subclass_1_section = self.get_dict_data(zky_clean_data_dict, "小类1分区")
            subclass_1_section = self.check_data(subclass_1_section)
            subclass_2 = self.get_dict_data(zky_clean_data_dict, "小类2")
            if subclass_2 is not None:
                subclass_2_dict = self.get_name_cn(subclass_2)
                subclass_2 = subclass_2_dict['name']
                subclass_2_en = subclass_2_dict['name_en']
                subclass_2_cn = subclass_2_dict['name_cn']
            subclass_2_section = self.get_dict_data(zky_clean_data_dict, "小类2分区")
            subclass_2_section = self.check_data(subclass_2_section)
            subclass_3 = self.get_dict_data(zky_clean_data_dict, "小类3")
            if subclass_3 is not None:
                subclass_3_dict = self.get_name_cn(subclass_3)
                subclass_3 = subclass_3_dict['name']
                subclass_3_en = subclass_3_dict['name_en']
                subclass_3_cn = subclass_3_dict['name_cn']
            subclass_3_section = self.get_dict_data(zky_clean_data_dict, "小类3分区")
            subclass_3_section = self.check_data(subclass_3_section)
            subclass_4 = self.get_dict_data(zky_clean_data_dict, "小类4")
            if subclass_4 is not None:
                subclass_4_dict = self.get_name_cn(subclass_4)
                subclass_4 = subclass_4_dict['name']
                subclass_4_en = subclass_4_dict['name_en']
                subclass_4_cn = subclass_4_dict['name_cn']
            subclass_4_section = self.get_dict_data(zky_clean_data_dict, "小类4分区")
            subclass_4_section = self.check_data(subclass_4_section)
            subclass_5 = self.get_dict_data(zky_clean_data_dict, "小类5")
            if subclass_5 is not None:
                subclass_5_dict = self.get_name_cn(subclass_5)
                subclass_5 = subclass_5_dict['name']
                subclass_5_en = subclass_5_dict['name_en']
                subclass_5_cn = subclass_5_dict['name_cn']
            subclass_5_section = self.get_dict_data(zky_clean_data_dict, "小类5分区")
            subclass_5_section = self.check_data(subclass_5_section)
            subclass_6 = self.get_dict_data(zky_clean_data_dict, "小类6")
            if subclass_6 is not None:
                subclass_6_dict = self.get_name_cn(subclass_6)
                subclass_6 = subclass_6_dict['name']
                subclass_6_en = subclass_6_dict['name_en']
                subclass_6_cn = subclass_6_dict['name_cn']
            subclass_6_section = self.get_dict_data(zky_clean_data_dict, "小类6分区")
            subclass_6_section = self.check_data(subclass_6_section)
            if issn and issn != 'N/A':
                issn_list = [issn]
                journal_id = self.find_journal_id_by_issn(conn, issn_list)
                if journal_id:
                    snow_flake_id = self.snowflake.get_id()
                    utc_now = datetime.now(timezone.utc)

                    zky_id = snow_flake_id
                    create_time = utc_now
                    update_time = utc_now

                    zky_data = (zky_id, journal_id, top, zky_year, large_category, large_category_section,
                                subclass_1, subclass_1_section,
                                subclass_2, subclass_2_section,
                                subclass_3, subclass_3_section,
                                subclass_4, subclass_4_section,
                                subclass_5, subclass_5_section,
                                subclass_6, subclass_6_section,
                                create_time, update_time)

                    self.insert_zky_impact_factor_to_postgres(conn, zky_data)
        logger.info("zky数据入库完毕")

    def check_postgressql_table(self, conn):
        self.create_jcr_impact_factor_table(conn)

        self.create_zky_impact_factor_table(conn)

    def process_all(self):
        with psycopg.connect(**self.conn_params) as conn:
            #self.check_postgressql_table(conn)

            self.insert_jcr_to_postgressql(conn)

            #self.insert_zky_to_postgressql(conn)


def __run():
    # 保存到mongodb里
    # 定义一个数据库入库对象

    conn_params = {
        "host": "************",
        "port": 31910,
        "dbname": "postgres",
        "user": "postgres",
        "password": "Biosino+2025",
        "options": "-c search_path=public"
    }

    zky_file_path = "./data/FQBJCR2025-UTF8.csv"

    user = parse.quote_plus("ndd")
    passwd = parse.quote_plus("ndd@2023")

    mongo_host = f'mongodb://{user}:{passwd}@************:32608/ndd-xg'
    db_name = 'ndd-xg'
    jcr_col_name = f'JCR2023_copy1'
    journal_table = "tb_dds_journal"

    jcr_impact_factor_table = "tb_dds_if_year"
    zky_impact_factor_table = "tb_dds_zky_section"

    mongo_object = SaveDataToMongoDB(mongo_host, db_name, jcr_col_name)
    jcr_mongo_collection = mongo_object.init_mongodb()

    all_obj = DataProcess(conn_params, jcr_impact_factor_table, zky_impact_factor_table, journal_table,
                          jcr_mongo_collection, zky_file_path)
    all_obj.process_all()


if __name__ == '__main__':
    __run()
