import json
import threading
import time
import xml.etree.ElementTree as ET
from datetime import datetime, timezone

import psycopg


class SnowFlake:
    """
    雪花算法实现类

    雪花算法是一种分布式ID生成算法，能够生成全局唯一的ID。
    ID结构：1位符号位 + 41位时间戳 + 5位数据中心ID + 5位工作机器ID + 12位序列号

    参数:
        worker_id: 工作机器ID (0-31)
        datacenter_id: 数据中心ID (0-31)
        sequence: 起始序列号 (默认为0)
    """

    def __init__(self, worker_id, datacenter_id, sequence=0):
        # 各部分位数
        self.worker_id_bits = 5  # 工作机器ID位数
        self.datacenter_id_bits = 5  # 数据中心ID位数
        self.sequence_bits = 12  # 序列号位数

        # 各部分最大值
        self.max_worker_id = -1 ^ (-1 << self.worker_id_bits)  # 工作机器ID最大值（31）
        self.max_datacenter_id = -1 ^ (-1 << self.datacenter_id_bits)  # 数据中心ID最大值（31）
        self.max_sequence = -1 ^ (-1 << self.sequence_bits)  # 序列号最大值（4095）

        # 验证参数合法性
        if worker_id > self.max_worker_id or worker_id < 0:
            raise ValueError(f"worker_id 超出范围 0 到 {self.max_worker_id}")
        if datacenter_id > self.max_datacenter_id or datacenter_id < 0:
            raise ValueError(f"datacenter_id 超出范围 0 到 {self.max_datacenter_id}")

        # 保存ID
        self.worker_id = worker_id
        self.datacenter_id = datacenter_id
        self.sequence = sequence

        # 位移计算
        self.worker_id_shift = self.sequence_bits  # 工作机器ID左移位数（12）
        self.datacenter_id_shift = self.sequence_bits + self.worker_id_bits  # 数据中心ID左移位数（17）
        self.timestamp_shift = self.sequence_bits + self.worker_id_bits + self.datacenter_id_bits  # 时间戳左移位数（22）

        # 开始时间戳（2021-01-01 00:00:00 的毫秒级时间戳）
        self.twepoch = 1609459200000

        # 上次生成ID的时间戳
        self.last_timestamp = -1

        # 线程锁，保证并发安全
        self.lock = threading.Lock()

    def _next_millis(self, last_timestamp):
        """
        获取下一毫秒时间戳

        参数:
            last_timestamp: 上次生成ID的时间戳

        返回:
            下一毫秒的时间戳
        """
        timestamp = self._get_time()
        while timestamp <= last_timestamp:
            timestamp = self._get_time()
        return timestamp

    def _get_time(self):
        """
        获取当前毫秒时间戳

        返回:
            当前毫秒时间戳
        """
        return int(time.time() * 1000)

    def get_id(self):
        """
        生成下一个ID

        返回:
            生成的ID
        """
        with self.lock:
            timestamp = self._get_time()

            # 如果当前时间小于上次生成ID的时间，说明系统时钟回退，抛出异常
            if timestamp < self.last_timestamp:
                raise Exception(f"时钟回拨异常，拒绝生成ID，当前时间: {timestamp}，上次时间: {self.last_timestamp}")

            # 如果是同一毫秒内，则递增序列号
            if timestamp == self.last_timestamp:
                self.sequence = (self.sequence + 1) & self.max_sequence
                # 如果序列号溢出，则等待下一毫秒
                if self.sequence == 0:
                    timestamp = self._next_millis(self.last_timestamp)
            else:
                # 不是同一毫秒，序列号重置为0
                self.sequence = 0

            # 保存本次的时间戳
            self.last_timestamp = timestamp

            # 生成并返回ID
            return ((timestamp - self.twepoch) << self.timestamp_shift) | \
                (self.datacenter_id << self.datacenter_id_shift) | \
                (self.worker_id << self.worker_id_shift) | \
                self.sequence


class DataAnalysis:
    """
    解析xml数据
    """

    def __init__(self, conn_params, desc_file_path, qual_file_path, supp_file_path, mesh_table):
        self.conn_params = conn_params
        self.snowflake = SnowFlake(worker_id=4, datacenter_id=4)
        self.desc_file_path = desc_file_path
        self.qual_file_path = qual_file_path
        self.supp_file_path = supp_file_path
        self.mesh_table = mesh_table

    def get_dict_data(self, dom, name):
        """
        安全地获取数据，避免keyErro异常
        :param dom: xml的dom树
        :param name: 字段名
        :return: 字段值
        """
        if dom is not None:
            data = dom.get(name, None)
            if data and data != "N/A":
                return data
            else:
                return None
        else:
            return None

    def create_mesh_table(self, conn):
        try:
            # 创建表的SQL语句
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.mesh_table} (
                 id BIGINT PRIMARY KEY NOT NULL,
                 mesh_ui VARCHAR(300),
                 mesh_name VARCHAR(300),
                 mesh_type VARCHAR(300),
                 tree_numbers TEXT,
                 date_revised VARCHAR(300),
                 terms TEXT,
                 parent_info TEXT,
                 child_info TEXT,
                 create_time TIMESTAMPTZ NOT NULL,
                 update_time TIMESTAMPTZ NOT NULL
            );
            """

            with conn.cursor() as cur:
                cur.execute(create_table_sql)
                conn.commit()
                return True
        except Exception as e:
            conn.rollback()
            return False

    def insert_mesh_to_postgres(self, conn, data):
        try:
            insert_sql = f"""
            INSERT INTO {self.mesh_table} (
                id, mesh_ui, mesh_name, mesh_type, tree_numbers, date_revised, terms, parent_info, child_info, create_time, update_time
            ) VALUES (
                {','.join(['%s'] * 11)}
            )
            """

            with conn.cursor() as cur:
                cur.execute(insert_sql, data)
                conn.commit()
        except Exception as e:
            print(f"JCR 插入摘要数据失败: {e}")
            conn.rollback()

    def get_desc_dict_data(self, xml_root) -> dict:
        """
        解析并获取主题词文件中XML相关的数据
        :param desc_root: 该词的根元素
        :return: desc_dict: 字典数据
        """
        # DescriptorName  描述符的名称（表示主题词的核心概念或实体）
        descriptor_name = xml_root.find("DescriptorName").find("String")

        # TreeNumbers 描述符在MeSH主题词树中的位置（由一系列数字组成）(列表数据)
        tree_numbers = list()
        tree_number_list = xml_root.find("TreeNumberList")
        if tree_number_list is not None:
            for tree_number in tree_number_list:
                if tree_number is not None:
                    tree_numbers.append(tree_number.text)

        # DateEstablished 主题词的创建时间
        date_established = self.get_date_time(xml_root, "DateEstablished")

        # ScopeNote  描述符的范围说明，对主题词的含义、适用范围等进行解释和说明
        # <Concept PreferredConceptYN="Y"> 首选概念
        scope_note = None
        concept_list = xml_root.find("ConceptList")
        if concept_list is not None:
            for concept in concept_list.iter("Concept"):
                # 优先挑选 具有首选概念的数据
                if concept.get("PreferredConceptYN") == "Y":
                    # 检索 scope_note
                    scope_note = concept.find("ScopeNote")

        # DateOfEntry 概念或术语添加到MeSH数据库的时间（先有相关概念和术语，再有主题词）
        date_of_entry = self.get_date_time(xml_root, "DateCreated")

        # Annotation 描述符的注释，提供与主题词相关的额外信息
        annotation = xml_root.find("Annotation")

        # PublicMeSHNote 描述符的公共注释，提供与主题词相关的一般性信息；
        public_mesh_note = xml_root.find("PublicMeSHNote")

        # HistoryNote 描述符的历史注释，提供与主题词历史相关的信息
        history_note = xml_root.find("HistoryNote")

        # RevisionDate 描述符的修订时间，表示该主题词最后一次被修订的时间
        date_revised = self.get_date_time(xml_root, "DateRevised")

        # Terms 描述符的术语列表，包含了与描述符相关的所有术语
        terms_list, dms_synonym_list = self.get_term_list(xml_root)

        # DescriptorID 描述符的唯一标识符，等同于 dms_id
        descriptor_ui = xml_root.find("DescriptorUI")

        # 返回注释信息
        desc_note = self.get_note(scope_note, annotation)

        # 获取dms相关的字典数据
        desc_dict = self.get_dms_data(xml_root, dms_synonym_list, descriptor_name, descriptor_ui, desc_note)

        if descriptor_name is not None:
            desc_dict['mesh_name'] = descriptor_name.text
        if tree_numbers is not None and bool(tree_numbers):
            desc_dict['tree_numbers'] = tree_numbers
        if date_established is not None:
            desc_dict['date_established'] = date_established
        if scope_note is not None:
            desc_dict['scope_note'] = scope_note.text.strip()
        if date_of_entry is not None:
            desc_dict['date_of_entry'] = date_of_entry
        if annotation is not None:
            desc_dict['annotation'] = annotation.text
        if public_mesh_note is not None:
            desc_dict['public_mesh_note'] = public_mesh_note.text.strip()
        if history_note is not None:
            desc_dict['history_note'] = history_note.text.strip()
        if date_revised is not None:
            desc_dict['date_revised'] = date_revised
        if terms_list is not None and bool(terms_list):
            desc_dict['terms'] = terms_list
        if descriptor_ui is not None:
            desc_dict['mesh_ui'] = descriptor_ui.text
        desc_dict['mesh_type'] = "descriptor"

        return desc_dict

    def get_qual_dict_data(self, xml_root) -> dict:
        """
        解析并获取限定词文件中XML相关的数据
        :param qual_root: 该词的根元素
        :return: qual_dict: 字典数据
        """
        # QualifierName  限定词的名称
        qualifier_name = xml_root.find("QualifierName").find("String")

        # TreeNumbers 限定词在MeSH限定词树中的位置（由一系列数字组成）(列表数据)
        tree_numbers = list()
        tree_number_list = xml_root.find("TreeNumberList")
        if tree_number_list is not None:
            for tree_number in tree_number_list:
                if tree_number is not None:
                    tree_numbers.append(tree_number.text)

        # DateEstablished 限定词的创建时间
        date_established = self.get_date_time(xml_root, "DateEstablished")

        # ScopeNote  限定词的范围说明，对限定词的含义、适用范围等进行解释和说明
        # <Concept PreferredConceptYN="Y"> 首选概念
        scope_note = None
        abbreviation = None
        entryVersion = None
        concept_list = xml_root.find("ConceptList")
        if concept_list is not None:
            for concept in concept_list.iter("Concept"):
                # 优先挑选 具有首选概念的数据
                if concept.get("PreferredConceptYN") == "Y":
                    # 检索 scope_note
                    scope_note = concept.find("ScopeNote")
                    term_list = concept.find("TermList")
                    if term_list is not None:
                        for term in term_list:
                            if term.get("ConceptPreferredTermYN") == "Y" and term.get(
                                    "IsPermutedTermYN") == "N" and term.get("RecordPreferredTermYN") == "Y":
                                abbreviation = term.find("Abbreviation")
                                entryVersion = term.find("EntryVersion")

        # DateOfEntry 概念或术语添加到MeSH数据库的时间（先有相关概念和术语，再有主题词）
        date_of_entry = self.get_date_time(xml_root, "DateCreated")

        # Annotation 限定词的注释，提供与主题词相关的额外信息
        annotation = xml_root.find("Annotation")

        # HistoryNote 限定词的历史注释，提供历史相关的信息
        history_note = xml_root.find("HistoryNote")

        # RevisionDate 限定词的修订时间，表示该主题词最后一次被修订的时间
        date_revised = self.get_date_time(xml_root, "DateRevised")

        # Terms 限定词的术语列表，包含了与描述符相关的所有术语
        terms_list, dms_synonym_list = self.get_term_list(xml_root)

        # QualifierUI 限定词的唯一标识符，等同于 dms_id
        qualifier_ui = xml_root.find("QualifierUI")

        # 返回注释信息
        qual_note = self.get_note(scope_note, annotation)

        # 获取dms相关的字典数据
        qual_dict = self.get_dms_data(xml_root, dms_synonym_list, qualifier_name, qualifier_ui, qual_note)

        if qualifier_name is not None:
            qual_dict['mesh_name'] = qualifier_name.text
        if abbreviation is not None:
            qual_dict['abbreviation'] = abbreviation.text
        if entryVersion is not None:
            qual_dict['entry_version'] = entryVersion.text
        if tree_numbers is not None and bool(tree_numbers):
            qual_dict['tree_numbers'] = tree_numbers
        if date_established is not None:
            qual_dict['date_established'] = date_established
        if scope_note is not None:
            qual_dict['scope_note'] = scope_note.text.strip()
        if date_of_entry is not None:
            qual_dict['date_of_entry'] = date_of_entry
        if annotation is not None:
            qual_dict['annotation'] = annotation.text.strip()
        if history_note is not None:
            qual_dict['history_note'] = history_note.text.strip()
        if date_revised is not None:
            qual_dict['date_revised'] = date_revised
        if terms_list is not None and bool(terms_list):
            qual_dict['terms'] = terms_list
        if qualifier_ui is not None:
            qual_dict['mesh_ui'] = qualifier_ui.text
        qual_dict['mesh_type'] = "qualifier"

        return qual_dict

    def get_supp_dict_data(self, xml_root) -> dict:
        """
        解析并获取补充记录文件中XML相关的数据
        :param supp_root: 该词的根元素
        :return: supp_dict: 字典数据
        """
        # SupplementalRecordName  补充记录的名称
        supp_name = xml_root.find("SupplementalRecordName").find("String")

        # DateOfEntry 概念或术语添加到MeSH数据库的时间（先有相关概念和术语，再有主题词）
        date_of_entry = self.get_date_time(xml_root, "DateCreated")

        # Note 提供有关该物质的信息，特别是其生物特性的自由文本叙述
        note = xml_root.find("Note")

        # RevisionDate 补充记录的修订时间，表示该记录最后一次被修订的时间
        date_revised = self.get_date_time(xml_root, "DateRevised")

        # Terms 补充记录的术语列表，包含了相关的所有术语
        terms_list, dms_synonym_list = self.get_term_list(xml_root)

        # SupplementalRecordUI 补充记录的唯一标识符，等同于 dms_id
        supp_ui = xml_root.find("SupplementalRecordUI")

        supp_note = None
        if note is not None:
            supp_note = note.text.strip()

        # 获取dms相关的字典数据
        supp_dict = self.get_dms_data(xml_root, dms_synonym_list, supp_name, supp_ui, supp_note)

        if note is not None:
            supp_dict['supp_note'] = note.text.strip()
        if supp_name is not None:
            supp_dict['mesh_name'] = supp_name.text.strip()
        if date_of_entry is not None:
            supp_dict['date_of_entry'] = date_of_entry
        if date_revised is not None:
            supp_dict['date_revised'] = date_revised
        if terms_list is not None and bool(terms_list):
            supp_dict['terms'] = terms_list
        if supp_ui is not None:
            supp_dict['mesh_ui'] = supp_ui.text
        supp_dict['mesh_type'] = "supplemental"

        return supp_dict

    def get_dms_data(self, xml_root, dms_synonym_list, dms_name, dms_id, dms_note) -> dict:
        """
        获取dms相关的字典数据
        :param xml_root: 该词的根元素
        :param dms_synonym_list: 该词的同义词列表
        :param dms_name: 该词的名称
        :param dms_id: 该词的id
        :param dms_note: 该词的注释信息
        :param parent_taxid_list: 父级的taxid信息列表
        :return: xml_dict: 返回dms字典数据
        """
        xml_dict = dict()
        dms_id_list = list()
        # dms_synonym_extend 扩展的同义词列表，包含了相关的所有同义词
        dms_synonym_extend_list = self.get_dms_synonym_extend(xml_root)

        # dms_synonym 同义词列表，包含了与描述符相关的常用同义词
        if dms_synonym_list is not None and bool(dms_synonym_list):
            xml_dict['dms_synonym'] = dms_synonym_list
        # dms_synonym_extend 包含了 所有术语数据
        if dms_synonym_extend_list is not None and bool(dms_synonym_extend_list):
            xml_dict['dms_synonym_extend'] = dms_synonym_extend_list
        if dms_id is not None:
            dms_id_dict = dict()
            dms_id_dict['db'] = 'MeSH'
            dms_id_dict['id'] = dms_id.text
            dms_id_list.append(dms_id_dict)
            xml_dict['dms_ids'] = dms_id_list
            xml_dict['dms_ids_extend'] = dms_id_list
            xml_dict['dms_id'] = dms_id.text
        if dms_name is not None:
            xml_dict['dms_name'] = dms_name.text
        if dms_note is not None:
            xml_dict['dms_description'] = dms_note
        return xml_dict

    def get_dms_synonym_extend(self, xml_root) -> list:
        """
        获取扩展的同义词列表，包含了相关的所有同义词
        """
        dms_synonym_extend_list = list()
        concept_list = xml_root.find("ConceptList")
        if concept_list is not None:
            for concept in concept_list.iter("Concept"):
                # 找到所有概念下的术语的相关数据
                term_list = concept.find("TermList")
                if term_list is not None:
                    for term in term_list:
                        # 获得所有的术语名称
                        dms_synonym = term.find("String")
                        if dms_synonym is not None:
                            dms_synonym_extend_list.append(dms_synonym.text)
        return dms_synonym_extend_list

    def get_term_list(self, xml_root):
        """
        Terms 描述符的术语列表，包含了与描述符相关的所有术语
        :param xml_root: 该词的根元素
        :return terms_list: 术语列表
        :return dms_synonym_list: 同义词名称列表
        """
        terms_list = list()
        dms_synonym_list = list()
        concept_list = xml_root.find("ConceptList")
        if concept_list is not None:
            for concept in concept_list.iter("Concept"):
                term_list = concept.find("TermList")
                if term_list is not None:
                    for term in term_list:
                        terms = dict()
                        if term.get("IsPermutedTermYN") == "N":
                            term_ui = term.find("TermUI")
                            term_string = term.find("String")
                            if term_ui is not None and term_string is not None:
                                terms["term_ui"] = term_ui.text
                                terms["String"] = term_string.text
                            if term_string is not None:
                                dms_synonym_list.append(term_string.text)
                            terms_list.append(terms)
        return terms_list, dms_synonym_list

    def get_date_time(self, xml_root, keyword):
        """
        处理并获得该xml中的相关时间
        """
        date = None
        try:
            date_data = xml_root.find(keyword)
            if date_data is not None:
                date_year = date_data.find("Year").text
                date_month = date_data.find("Month").text
                date_day = date_data.find("Day").text
                date = date_year + '-' + date_month + '-' + date_day
            else:
                return date
        except AttributeError as e:
            print("日期数据查找失败：", e)
        return date

    def get_note(self, scope_note, annotation):
        """
        获取比较后的注释信息
        :param scope_note: 描述符的范围说明
        :param annotation: 描述符的注释，提供与主题词相关的额外信息
        :return: 注释数据
        """
        if scope_note is None and annotation is None:
            return None
        if scope_note is None:
            return annotation.text.strip()
        return scope_note.text.strip()

    def process_desc_data(self):
        dom_tree = ET.parse(self.desc_file_path)
        dom_root = dom_tree.getroot()

        num = 0
        dom_list = list()
        dom_record_list = dom_root.findall("DescriptorRecord")
        if dom_record_list is not None:
            for dom_record in dom_record_list:
                num = num + 1
                # 定义一个解析对象
                dom_dict = self.get_desc_dict_data(dom_record)
                dom_list.append(dom_dict)

        final_data = GlobalModification(dom_list, "mesh_ui")
        dom_list = final_data.set_dms_parent_and_child()

        return dom_list

    def process_qual_data(self):
        dom_tree = ET.parse(self.qual_file_path)
        dom_root = dom_tree.getroot()

        num = 0
        dom_list = list()
        dom_record_list = dom_root.findall("QualifierRecord")
        if dom_record_list is not None:
            for dom_record in dom_record_list:
                num = num + 1

                dom_dict = self.get_qual_dict_data(dom_record)
                dom_list.append(dom_dict)

        final_data = GlobalModification(dom_list, "mesh_ui")
        dom_list = final_data.set_dms_parent_and_child()

        return dom_list

    def process_supp_data(self):
        dom_tree = ET.parse(self.supp_file_path)
        dom_root = dom_tree.getroot()

        num = 0
        dom_list = list()
        dom_record_list = dom_root.findall("SupplementalRecord")
        if dom_record_list is not None:
            for dom_record in dom_record_list:
                num = num + 1
                # 定义一个解析对象
                dom_dict = self.get_supp_dict_data(dom_record)
                dom_list.append(dom_dict)

        return dom_list

    def insert_data_to_postgresql(self, desc_list, qual_list, supp_list):
        all_list = list()
        if desc_list:
            all_list.extend(desc_list)
        if qual_list:
            all_list.extend(qual_list)
        if supp_list:
            all_list.extend(supp_list)
        with psycopg.connect(**self.conn_params) as conn:
            self.create_mesh_table(conn)
            for data_dict in all_list:
                child_list, parent_list = None, None
                mesh_ui = self.get_dict_data(data_dict, "mesh_ui")
                mesh_name = self.get_dict_data(data_dict, "mesh_name")
                mesh_type = self.get_dict_data(data_dict, "mesh_type")
                tree_numbers = self.get_dict_data(data_dict, "tree_numbers")
                date_revised = self.get_dict_data(data_dict, "date_revised")
                terms = self.get_dict_data(data_dict, "terms")
                parent_info = self.get_dict_data(data_dict, "parent_info")
                child_info = self.get_dict_data(data_dict, "child_info")

                terms_str = json.dumps(terms)

                snow_flake_id = self.snowflake.get_id()
                utc_now = datetime.now(timezone.utc)

                mesh_id = snow_flake_id

                create_time = utc_now
                update_time = utc_now

                mesh_data = (mesh_id, mesh_ui, mesh_name, mesh_type, tree_numbers, date_revised, terms_str, parent_info, child_info, create_time,
                             update_time)

                self.insert_mesh_to_postgres(conn, mesh_data)

    def process_all(self):
        desc_list = self.process_desc_data()

        qual_list = self.process_qual_data()

        supp_list = self.process_supp_data()

        self.insert_data_to_postgresql(desc_list, qual_list, supp_list)


class GlobalModification:
    """
    对解析完后的所有数据针对性地进行增删改
    """

    def __init__(self, dom_list, keyword):
        self.dom_list = dom_list
        self.keyword = keyword

    def set_dms_parent_and_child(self):
        """
        找出 该词 的父级节点和子级节点并赋值
        :return:
        """
        print("开始处理父级节点及重新赋值", flush=True)
        dom_len = len(self.dom_list)
        print(f"即将处理的数据有{dom_len}条", flush=True)
        if dom_len > 5000:
            print("时间可能会很长，请耐心等待.......", flush=True)
        num1 = 0
        num2 = 0
        # 处理父级节点
        for dom in self.dom_list:
            if "tree_numbers" in dom:
                child_number_list = dom["tree_numbers"]
                parent_id_list = list()
                if child_number_list is not None:
                    # 循环遍历 tree_numbers 数据
                    for child_number in child_number_list:
                        # 找到父级节点，截取的父级树位置数据
                        end_index = child_number.rfind('.')
                        parent_tree_number = child_number[:end_index]
                        for y in self.dom_list:
                            if "tree_numbers" in y:
                                parent_ui = y[self.keyword]
                                parent_number_list = y["tree_numbers"]
                                # 如果配对成功，说明该节点是父级节点
                                if parent_number_list is not None and parent_tree_number in parent_number_list:
                                    # 判断是否已有数据
                                    if parent_ui not in parent_id_list and parent_ui != ' ':
                                        parent_id_list.append(parent_ui)
                if parent_id_list and all(not str(elem).isspace() for elem in parent_id_list):
                    dom["parent_info"] = parent_id_list
                    num1 = num1 + 1
                    self.count_number(num1)
        print("父级节点处理完毕！！", flush=True)

        print("开始处理子级节点及重新赋值", flush=True)
        if dom_len > 5000:
            print("时间可能也会很长，请耐心等待.......", flush=True)
        # 处理子级节点
        for dom in self.dom_list:
            parent_ui = dom[self.keyword]
            child_id_list = list()
            for y in self.dom_list:
                if "parent_info" in y:
                    child_ui = y[self.keyword]
                    parent_id_list = y["parent_info"]
                    if parent_ui in parent_id_list:
                        child_id_list.append(child_ui)
            if child_id_list and all(not str(elem).isspace() for elem in child_id_list):
                dom["child_info"] = child_id_list
                num2 = num2 + 1
                self.count_number(num2)
        print("子级节点处理完毕", flush=True)
        return self.dom_list

    def count_number(self, num):
        """
        判断处理的数据条数
        :param num: 当前数据个数
        """
        if num % 100 == 0:
            discuss = num / 100
            total = int(discuss * 100)
            print(f"已经处理了{total}条数据", flush=True)


def __run():
    desc_file_path = "./data/desc2025.xml"
    qual_file_path = "./data/qual2025.xml"
    supp_file_path = "./data/supp2025.xml"

    conn_params = {
        "host": "************",
        "port": 31910,
        "dbname": "postgres",
        "user": "postgres",
        "password": "Biosino+2025",
        "options": "-c search_path=public"
    }
    mesh_table = "tb_dds_mesh_20250811"

    data_obj = DataAnalysis(conn_params, desc_file_path, qual_file_path, supp_file_path, mesh_table)
    data_obj.process_all()


if __name__ == '__main__':
    __run()
