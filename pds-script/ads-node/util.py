# coding=utf8
__author__ = 'yhju'

import json
import logging
import os
import re
import shutil
import sys
from hashlib import md5

import psutil

logger = logging.getLogger()


def program_root_dir():
    """
    获取项目运行根目录
    :return:
    """
    root_dir = "."
    if getattr(sys, "frozen", False):
        root_dir = os.path.dirname(sys.executable)
    elif __file__:
        root_dir = os.path.dirname(os.path.realpath(__file__))
    return root_dir


def get_site_config() -> dict:
    """解析站点配置文件"""
    file = os.path.join(program_root_dir(), "conf/site.json")
    if not os.path.exists(file):
        raise ValueError("站点配置文件不存在，Path: {}".format(file))

    site_config = None
    with open(file, "r", encoding="UTF-8") as f:
        site_config = json.loads(f.read(), encoding="UTF-8")

    # 验证参数
    if site_config is None:
        raise ValueError("未加载到站点配置信息 ./conf/site.json")

    if not dict_has_value(site_config, "siteId"):
        raise ValueError("站点配置信息中未配置 siteId")
    if not dict_has_value(site_config, "apiPath"):
        raise ValueError("站点配置信息中未配置 apiPath")

    # PDS Api 配置项
    if not dict_has_value(site_config, "apiTimeout") or int(site_config["apiTimeout"]) < 1:
        site_config["apiTimeout"] = 30
    if not dict_has_value(site_config, "apiEncoding"):
        site_config["apiEncoding"] = "UTF-8"
    if not dict_has_value(site_config, "handshakeIntervalTime") or int(site_config["handshakeIntervalTime"]) < 1:
        site_config["handshakeIntervalTime"] = 60

    return site_config


def update_site_config(param: dict) -> dict:
    config = get_site_config()
    if param is None or len(param) == 0:
        return config
    config_file = os.path.join(program_root_dir(), "conf/site.json")
    with open(config_file, 'w', encoding='utf-8') as file:
        for k, v in param.items():
            if k not in config.keys():
                continue
            if v is None:
                continue
            if isinstance(v, (str,)) and str(v).strip() == "":
                continue
            config[k] = v

        file.write(json.dumps(config, ensure_ascii=False, indent=2))
        return config


def get_selenium_config() -> dict:
    """解析站点配置文件"""
    file = os.path.join(program_root_dir(), "conf/selenium.json")
    if not os.path.exists(file):
        raise ValueError("selenium配置文件不存在，Path: {}".format(file))

    site_config = None
    with open(file, "r", encoding="UTF-8") as f:
        site_config = json.loads(f.read(), encoding="UTF-8")

    # 验证参数
    if site_config is None:
        raise ValueError("未加载到selenium配置信息 {}".format(file))

    if not dict_has_value(site_config, "driver_path"):
        raise ValueError("selenium配置信息中，未配置驱动路径 driver_path")
    else:
        driver_path = site_config["driver_path"]
        if not os.path.exists(driver_path) or not os.path.isfile(driver_path):
            raise ValueError("selenium配置信息中，驱动文件路径driver_path配置错误 {}".format(driver_path))

    if not dict_has_value(site_config, "version_main"):
        raise ValueError("selenium配置信息中未配置浏览器主版本号 version_main")
    else:
        version_main = int(site_config["version_main"])
        if version_main < 96:
            raise ValueError("selenium配置信息中浏览器主版本号 version_main 最低为96")

    return site_config


def dict_has_value(d: dict, key: str) -> bool:
    """字典中是否有该 key 值，is not null and string != '' """
    if d is None or key is None or key not in d.keys():
        return False
    if d[key] is None:
        return False
    if isinstance(d[key], str) and str(d[key]).strip() == "":
        return False
    return True


def empty_str(s: str) -> bool:
    """判断字符串是否未空"""
    if s is None:
        return True
    if s.strip() is None:
        return True
    return False


def md5_file(file_path: str) -> str:
    """ 计算文件的MD5值 """

    if not os.path.exists(file_path):
        raise FileNotFoundError(file_path)

    m = md5()

    with open(file_path, "rb") as fh:
        chunk = fh.read(8096)
        while chunk:
            m.update(chunk)
            chunk = fh.read(8096)
    return m.hexdigest()


def norm_file_path(path: str):
    """将路径表转化，所有分隔符 用 / 表示，同时提供 db 和 ftp 使用"""
    if path is None or path.strip() == "":
        return "/"
    path = os.path.normpath(path)
    return re.sub(re.compile(r"/+|\\+", re.S), "/", path)


def remove_result_dir(result_dir_path: str):
    """清理结果文件"""
    try:
        if result_dir_path is None:
            return
        if os.path.exists(result_dir_path):
            shutil.rmtree(result_dir_path, ignore_errors=True)

        zip_file_path = "{}.zip".format(result_dir_path)
        if os.path.exists(zip_file_path):
            os.remove(zip_file_path)

        md5_file_path = "{}.md5".format(result_dir_path)
        if os.path.exists(md5_file_path):
            os.remove(md5_file_path)
    except BaseException as e:
        logger.exception(e)


def check_result_file(result_dir_path: str) -> bool:
    """
    验证文件是否已执行完成
    :return:
    """
    if result_dir_path is None:
        return False
    # 没有输出路径，直接出错
    if not os.path.exists(result_dir_path):
        logger.error("未找到结果文件夹 {}".format(result_dir_path))
        return False

    # 定义文件没找到或者为空，直接失败
    define_file_path = os.path.join(result_dir_path, "attach_note.txt")
    if not os.path.exists(define_file_path) or os.path.getsize(define_file_path) == 0:
        logger.error("未下载到结果，未找到文件{}".format(define_file_path))
        return False

    files = os.listdir(result_dir_path)
    if len(files) < 2:
        logger.error("结果文件夹中没有找到下载的文件 {}".format(result_dir_path))
        return False
    return True


def system_info() -> dict:
    """
    获取 系统 的 Cpu、内存、[当前]脚本运行的磁盘 信息
    :return:
    """
    return {"cpu": cpu_info(), "memory": memory_info(), "disk": disk_info()}


def disk_info() -> dict:
    """
    获取 本程序运行目录的 磁盘信息
    :return:
    """
    disk = {"total": -1, "used": -1, "free": -1, "percent": -1}
    try:
        # 磁盘信息
        disk_tmp = psutil.disk_usage(program_root_dir())
        disk["total"] = disk_tmp.total
        disk["used"] = disk_tmp.used
        disk["free"] = disk_tmp.free
        disk["percent"] = disk_tmp.percent
    except BaseException as e:
        logger.exception(e)
    return disk


def memory_info() -> dict:
    """
    内存信息
    :return:
    """
    memory = {"total": -1, "available": -1, "used": -1, "free": -1, "percent": -1}
    try:
        # 内存信息
        mem = psutil.virtual_memory()
        memory["total"] = mem.total
        memory["available"] = mem.available
        memory["used"] = mem.used
        memory["free"] = mem.free
        memory["percent"] = mem.percent
    except BaseException as e:
        logger.exception(e)
    return memory


def cpu_info() -> dict:
    """
    获取cpu信息
    :return:
    """
    cpu_info = {"logical_count": -1, "physical_count": -1, "freq_current": -1, "freq_min": -1, "freq_max": -1}

    try:
        logical_count = psutil.cpu_count()
        physical_count = psutil.cpu_count(logical=False)
        cpu_info["logical_count"] = logical_count if logical_count else -1
        cpu_info["physical_count"] = physical_count if physical_count else -1

        cpu_freq = psutil.cpu_freq()
        cpu_info["freq_current"] = cpu_freq.current
        cpu_info["freq_min"] = cpu_freq.min
        cpu_info["freq_max"] = cpu_freq.max
    except BaseException as e:
        logger.exception(e)
    return cpu_info


if __name__ == '__main__':
    print(md5_file('/Users/<USER>/workspace/pds/scripts/adsnode1/bak/sit_03_ncbi.py'))
