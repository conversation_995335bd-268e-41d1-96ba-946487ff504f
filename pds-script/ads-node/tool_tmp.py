# coding=utf8
__author__ = 'yhju'
import json
import os

import util

"""
临时数据处理
"""

if __name__ == "__main__":
    bak_dir = os.path.join(util.program_root_dir(), "bak")
    result = list()
    for file_name in os.listdir(bak_dir):
        if not file_name.endswith(".pyscript"):
            continue
        result.append(
            {
                "scriptServicePath": "script/" + file_name,
                "scriptMD5": util.md5_file(os.path.join(bak_dir, file_name))
            }
        )
    with open(os.path.join(bak_dir, "test.json"), "w", encoding="utf8") as f:
        f.write(json.dumps(result))
