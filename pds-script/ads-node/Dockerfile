FROM    registry.cn-hangzhou.aliyuncs.com/samjoy_public/python:3.8.20-bookworm

COPY    / /worker

ENV     TZ=Asia/Shanghai
ENV     DEBIAN_FRONTEND=noninteractive

# 配置APT源 - 自动判断配置方式，注：从debian 12（bookworm）开始使用DEB822配置debian.sources，debian 12之前使用/etc/apt/sources.list
RUN     if [ -f /etc/apt/sources.list.d/debian.sources ]; then \
            echo "Found debian.sources (DEB822 format), updating mirrors..."; \
            cp /etc/apt/sources.list.d/debian.sources /etc/apt/sources.list.d/debian.sources.bak; \
            sed -i 's|deb.debian.org|mirrors.cernet.edu.cn|g' /etc/apt/sources.list.d/debian.sources; \
            sed -i 's|security.debian.org/debian-security|mirrors.cernet.edu.cn/debian-security|g' /etc/apt/sources.list.d/debian.sources; \
        elif [ -f /etc/apt/sources.list ]; then \
            echo "Found traditional sources.list, updating mirrors..."; \
            cp /etc/apt/sources.list /etc/apt/sources.list.bak; \
            sed -i 's|deb.debian.org|mirrors.cernet.edu.cn|g' /etc/apt/sources.list; \
            sed -i 's|security.debian.org/debian-security|mirrors.cernet.edu.cn/debian-security|g' /etc/apt/sources.list; \
        else \
            echo "Creating new sources.list with mirror configuration..."; \
            echo "# 中科院教育网镜像源配置" > /etc/apt/sources.list; \
            echo "deb https://mirrors.cernet.edu.cn/debian/ bookworm main contrib non-free non-free-firmware" >> /etc/apt/sources.list; \
            echo "deb https://mirrors.cernet.edu.cn/debian/ bookworm-updates main contrib non-free non-free-firmware" >> /etc/apt/sources.list; \
            echo "deb https://mirrors.cernet.edu.cn/debian/ bookworm-backports main contrib non-free non-free-firmware" >> /etc/apt/sources.list; \
            echo "deb https://mirrors.cernet.edu.cn/debian-security bookworm-security main contrib non-free non-free-firmware" >> /etc/apt/sources.list; \
        fi \
        && echo "APT sources configuration completed" \
        && echo "Current APT sources:" \
        && cat /etc/apt/sources.list* 2>/dev/null | head -10

# 安装必要的工具
RUN     apt-get update && apt-get install -y wget unzip

# 查看chromedriver列表：https://registry.npmmirror.com/binary.html?path=chrome-for-testing
# 安装指定版本的Chrome和匹配的ChromeDriver
RUN     CHROME_VERSION="139.0.7258.66" \
        && CHROME_DEB="google-chrome-stable_${CHROME_VERSION}-1_amd64.deb" \
        && EXPECTED_MD5="84cfdfd93b061a21601627b75ba00790" \
        && echo "Installing Chrome version: $CHROME_VERSION" \
        && echo "Chrome package: $CHROME_DEB" \
        && echo "Expected MD5: $EXPECTED_MD5" \
        && DOWNLOAD_SUCCESS=false \
        && echo "Trying to download Chrome from Google official source..." \
        && if curl -fsSL --connect-timeout 30 --max-time 300 "https://dl.google.com/linux/chrome/deb/pool/main/g/google-chrome-stable/$CHROME_DEB" -o "/tmp/$CHROME_DEB"; then \
            echo "Download from Google official source completed, verifying MD5..." && \
            ACTUAL_MD5=$(md5sum "/tmp/$CHROME_DEB" | cut -d' ' -f1) && \
            echo "Actual MD5: $ACTUAL_MD5" && \
            if [ "$ACTUAL_MD5" = "$EXPECTED_MD5" ]; then \
                echo "MD5 verification passed for Google official source" && \
                DOWNLOAD_SUCCESS=true; \
            else \
                echo "MD5 verification failed for Google official source, removing file..." && \
                rm -f "/tmp/$CHROME_DEB"; \
            fi; \
        else \
            echo "Download from Google official source failed"; \
        fi \
        && if [ "$DOWNLOAD_SUCCESS" = "false" ]; then \
            echo "Trying to download Chrome from mirror.cs.uchicago.edu..." && \
            if curl -fsSL --connect-timeout 30 --max-time 300 "https://mirror.cs.uchicago.edu/google-chrome/pool/main/g/google-chrome-stable/$CHROME_DEB" -o "/tmp/$CHROME_DEB"; then \
                echo "Download from backup mirror completed, verifying MD5..." && \
                ACTUAL_MD5=$(md5sum "/tmp/$CHROME_DEB" | cut -d' ' -f1) && \
                echo "Actual MD5: $ACTUAL_MD5" && \
                if [ "$ACTUAL_MD5" = "$EXPECTED_MD5" ]; then \
                    echo "MD5 verification passed for backup mirror" && \
                    DOWNLOAD_SUCCESS=true; \
                else \
                    echo "MD5 verification failed for backup mirror, removing file..." && \
                    rm -f "/tmp/$CHROME_DEB"; \
                fi; \
            else \
                echo "Download from backup mirror failed"; \
            fi; \
        fi \
        && if [ "$DOWNLOAD_SUCCESS" = "true" ] && [ -f "/tmp/$CHROME_DEB" ]; then \
            echo "Chrome package downloaded and verified successfully, installing..." && \
            apt-get install -y "/tmp/$CHROME_DEB" && \
            rm -f "/tmp/$CHROME_DEB"; \
        else \
            echo "Failed to download or verify Chrome package, installation failed"; \
            exit 1; \
        fi \
        && echo "Chrome installed, now downloading ChromeDriver..." \
        && CHROMEDRIVER_URL="https://registry.npmmirror.com/-/binary/chrome-for-testing/$CHROME_VERSION/linux64/chromedriver-linux64.zip" \
        && echo "Trying to download ChromeDriver from: $CHROMEDRIVER_URL" \
        && (curl -fsSL --connect-timeout 30 --max-time 300 "$CHROMEDRIVER_URL" -o /tmp/chromedriver.zip || \
            (echo "NPM mirror failed, trying Google official source..." && \
             curl -fsSL --connect-timeout 30 --max-time 300 "https://storage.googleapis.com/chrome-for-testing-public/$CHROME_VERSION/linux64/chromedriver-linux64.zip" -o /tmp/chromedriver.zip) || \
            (echo "Both sources failed, using pre-downloaded chromedriver..." && \
             cp /worker/chromedriver /usr/local/bin/chromedriver && chmod +x /usr/local/bin/chromedriver && touch /tmp/skip_unzip)) \
        && if [ ! -f /tmp/skip_unzip ]; then \
            unzip /tmp/chromedriver.zip -d /tmp/ && \
            mv /tmp/chromedriver-linux64/chromedriver /usr/local/bin/chromedriver && \
            chmod +x /usr/local/bin/chromedriver; \
        fi \
        && rm -rf /tmp/chromedriver* /tmp/skip_unzip \
        && apt-get autoclean && rm -rf /var/lib/apt/lists/* \
        && google-chrome --version && chromedriver --version

# 全局配置pip国内镜像源
RUN     mkdir -p /etc && \
        echo "[global]" > /etc/pip.conf && \
        echo "index-url = https://mirrors.cernet.edu.cn/pypi/web/simple" >> /etc/pip.conf && \
        echo "trusted-host = mirrors.cernet.edu.cn" >> /etc/pip.conf

# 升级pip并安装Python依赖，其中包含playwright
RUN     pip3 install --upgrade pip \
        && pip3 install -r /worker/requirements.txt

# 安装playwright及对应chromium，并安装相关依赖（必须使用--with-deps）
# 配置playwright国内镜像，添加镜像地址环境变量
ENV     PLAYWRIGHT_DOWNLOAD_HOST=https://npmmirror.com/mirrors/playwright
RUN     playwright install --with-deps chromium \
        && apt-get autoclean \
        && apt-get autoremove -y \
        && rm -rf /var/lib/apt/lists/* \
        && rm -rf /tmp/* /var/tmp/*

WORKDIR   /worker/
ENV     siteId=0

CMD     ["sh", "-c", "python3 start.py --siteId=${siteId}"]
