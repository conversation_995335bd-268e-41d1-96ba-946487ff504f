#!/bin/bash
SITE_ID=0
CONTAINER_NAME="ads-node"

# 检查容器是否存在
if ! podman ps -a --format '{{.Names}}' | grep -q "${CONTAINER_NAME}$"; then
  echo "[$(date)] ${CONTAINER_NAME} is not running. Starting..."
  podman run --name ${CONTAINER_NAME} --restart on-failure:5 -m 300m --cpu-shares 512 -d registry.cn-hangzhou.aliyuncs.com/aliyun-public/adsnode-v3:1 python3 start.py --siteId ${SITE_ID}
  exit 1
fi

# 检查容器是否运行
if ! podman ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
  echo "[$(date)] ${CONTAINER_NAME} is not running. Restarting..."
  podman rm ${CONTAINER_NAME} && podman run --name ${CONTAINER_NAME} --restart on-failure:5 -m 300m --cpu-shares 512 -d registry.cn-hangzhou.aliyuncs.com/aliyun-public/adsnode-v3:1 python3 start.py --siteId ${SITE_ID}
  exit 1
fi

echo "[$(date)] ${CONTAINER_NAME} is running."
