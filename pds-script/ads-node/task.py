# coding=utf8
__author__ = 'sw'

import logging
import random
import threading
import time
from concurrent.futures import ThreadPoolExecutor

import api
import db
import util
from task_executor import TaskExecutor

logger = logging.getLogger()


class TaskInfo:
    """任务信息类"""

    def __init__(self, task_data):
        self.task_data = task_data
        self.task_id = task_data.get('taskId', 'Unknown')
        self.doc_id = task_data.get('docId', 'Unknown')
        self.pmid = task_data.get('pmid', '')
        self.used_script_ids = set()  # 已尝试过的脚本ID集合
        self.is_success = False  # 是否已成功下载
        self.is_processing = False  # 是否正在被处理
        self.processing_script_id = None  # 当前处理该任务的脚本ID
        self.result_dir_path = None
        self.lock = threading.Lock()

    def add_used_script_id(self, script_id):
        """添加已使用的脚本ID"""
        with self.lock:
            self.used_script_ids.add(script_id)

    def is_script_used(self, script_id):
        """检查脚本ID是否已被使用"""
        with self.lock:
            return script_id in self.used_script_ids

    def mark_success(self):
        """标记任务成功"""
        with self.lock:
            self.is_success = True
            self.is_processing = False
            self.processing_script_id = None

    def mark_processing(self, script_id):
        """标记任务正在被处理"""
        with self.lock:
            if self.is_processing:
                return False  # 已经在被处理
            self.is_processing = True
            self.processing_script_id = script_id
            self.used_script_ids.add(script_id)
            return True

    def mark_processing_failed(self):
        """标记任务处理失败"""
        with self.lock:
            self.is_processing = False
            self.processing_script_id = None

    def is_all_scripts_used(self, all_script_ids):
        """检查是否所有脚本都已尝试过"""
        with self.lock:
            return self.used_script_ids >= set(all_script_ids)


class TaskProcessor(threading.Thread, TaskExecutor):
    """
    主运行类，任务调度线程
    1. 使用_script_context.__get_site_script_module获取节点脚本信息
    2. 创建全局任务信息池task_infos（最大5个）
    3. 每个线程对应一个脚本ID，从任务池中获取未被当前脚本处理过的任务
    4. 限制最大脚本线程数为6个，总线程数不超过7个（6个脚本线程+1个任务池线程）
    """

    def __init__(self, site_id: int, db_file: db.DbFile, api_invoker: api.ApiInvoker):
        threading.Thread.__init__(self, daemon=True)
        TaskExecutor.__init__(self, site_id, db_file, api_invoker)
        self.__stop = False

        # 获取站点脚本信息
        self.__script_infos = []  # 存储脚本信息 [(name, md5, module, script_id), ...]
        self.__script_ids = []  # 存储脚本ID列表
        self.__load_script_infos()

        # 全局任务信息池（最大5个）
        self.__task_infos = {}  # {doc_id: TaskInfo}
        self.__task_infos_lock = threading.Lock()
        self.__max_task_pool_size = 5

        # 线程池：每个线程对应一个脚本ID + 1个任务池管理线程
        # 限制最大脚本线程数为6个，总线程数不超过7个（6个脚本线程+1个任务池线程）
        script_count = len(self.__script_ids) if self.__script_ids else 0
        max_script_threads = 6
        actual_script_threads = min(script_count, max_script_threads)
        self.__max_workers = actual_script_threads + 1  # 脚本线程数 + 任务池管理线程
        self.__executor = ThreadPoolExecutor(max_workers=self.__max_workers, thread_name_prefix="ScriptWorker")
        logger.info(f"创建线程池，最大工作线程数: {self.__max_workers} (脚本线程: {actual_script_threads}/{script_count}, 管理线程: 1)")

        if script_count > max_script_threads:
            logger.warning(f"后台返回脚本数量({script_count})超过最大限制({max_script_threads})，仅使用前{max_script_threads}个脚本")

        # 任务拉取控制
        self.__task_fetch_lock = threading.Lock()

    def __load_script_infos(self):
        """加载站点脚本信息"""
        logger.info("开始加载站点脚本信息...")
        try:
            # 使用_script_context.__get_site_script_module获取脚本信息
            logger.debug("调用 _script_context.__get_site_script_module() 获取脚本信息")
            script_modules = list(self._script_context._ScriptContext__get_site_script_module())
            logger.info(f"获取到脚本模块数量: {len(script_modules) if script_modules else 0}")

            if script_modules:
                # 限制脚本数量最多6个，忽略多余的脚本
                max_scripts = 6
                if len(script_modules) > max_scripts:
                    logger.warning(f"后台返回{len(script_modules)}个脚本，超过最大限制{max_scripts}个，仅使用前{max_scripts}个脚本")
                    script_modules = script_modules[:max_scripts]
                    logger.info(f"已限制脚本数量为: {len(script_modules)}")

                self.__script_infos = script_modules
                self.__script_ids = [self.__extract_script_id(script_id_str) for name, md5, module, script_id_str in
                                     script_modules]
                # logger.debug(f"成功加载到 {len(self.__script_infos)} 个脚本")
                logger.info(f"脚本ID列表: {self.__script_ids}")
                for i, (name, md5, module, script_id_str) in enumerate(script_modules):
                    logger.debug(f"脚本 {i + 1}: name={name}, script_id={script_id_str}")
            else:
                logger.warning("未找到可用的站点脚本")
                logger.warning("可能的原因：1. 站点未配置脚本 2. 网络连接问题 3. API调用失败")
                self.__script_infos = []
                self.__script_ids = []
        except Exception as e:
            logger.exception(f"加载脚本信息失败: {e}")
            logger.error("请检查：1. 站点ID是否正确 2. API连接是否正常 3. 站点是否配置了脚本")
            self.__script_infos = []
            self.__script_ids = []

    def __extract_script_id(self, script_id_str):
        """提取脚本ID"""
        return script_id_str

    def run(self) -> None:
        logger.info(f"TaskProcessor.run() 开始执行，脚本数量: {len(self.__script_ids)}")

        if not self.__script_ids:
            logger.error("没有可用的脚本，无法启动脚本工作线程")
            logger.error("请检查站点脚本配置和网络连接")
            logger.warning("将仅启动任务池管理器进行测试...")

            # 即使没有脚本，也启动任务池管理器用于测试
            try:
                logger.info("启动任务池管理器（测试模式）...")
                task_pool_future = self.__executor.submit(self.__task_pool_manager)
                logger.info("任务池管理器已启动（测试模式）")

                # 等待线程完成
                while not self.__stop:
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("接收到中断信号")
            finally:
                self.__stop = True
                self.__executor.shutdown(wait=True)
                logger.info("任务调度线程已停止, 站点: {}".format(self._site_id))
            return

        logger.info(f"启动任务处理，脚本数量: {len(self.__script_ids)} (最大限制: 6)")
        logger.info(f"脚本信息: {[(name, script_id) for name, md5, module, script_id in self.__script_infos]}")

        # 为每个脚本ID启动一个工作线程（最多6个脚本线程）
        futures = []
        try:
            for i, script_id in enumerate(self.__script_ids):
                name, md5, module, script_id_str = self.__script_infos[i]
                logger.info(f"准备启动脚本工作线程 {i+1}/{len(self.__script_ids)}: {name} (ID: {script_id})")
                future = self.__executor.submit(self.__script_worker, script_id, name, module)
                futures.append(future)
                logger.info(f"成功启动脚本工作线程 {i+1}/{len(self.__script_ids)}: {name} (ID: {script_id})")

            # 启动任务池管理线程
            logger.info("准备启动任务池管理线程...")
            task_pool_future = self.__executor.submit(self.__task_pool_manager)
            futures.append(task_pool_future)
            logger.info("任务池管理线程已提交到线程池")

        except Exception as e:
            logger.exception(f"启动工作线程时发生异常: {e}")
            return

        # 等待所有线程完成
        try:
            while not self.__stop:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("接收到中断信号")
        finally:
            self.__stop = True
            self.__executor.shutdown(wait=True)
            logger.info("任务调度线程已停止, 站点: {}".format(self._site_id))

    def __task_pool_manager(self):
        """任务池管理器，负责维护任务池不超过5个任务"""
        logger.info("=== 任务池管理器启动 ===")
        logger.info(f"任务池最大大小: {self.__max_task_pool_size}")

        # loop_count = 0
        while not self.__stop:
            try:
                # loop_count += 1
                # logger.info(f"任务池管理器循环 {loop_count}")

                with self.__task_infos_lock:
                    current_size = len(self.__task_infos)

                logger.debug(f"当前任务池大小: {current_size}/{self.__max_task_pool_size}")

                # 如果任务池未满，尝试获取新任务
                if current_size < self.__max_task_pool_size:
                    # logger.debug("任务池未满，尝试获取新任务...")
                    task_info = self.__safe_next_task_info()
                    # sleep(random.randint(1, 5))

                    task_status = task_info.get("status", "")
                    # logger.debug(f"获取任务结果: status={task_status}")

                    if task_status == "success":
                        # logger.info("获取到新任务，开始验证...")
                        # 验证任务信息
                        is_valid, error_msg = self.validate_task_info(task_info)
                        if is_valid:
                            doc_id = task_info.get('docId', 'Unknown')
                            # logger.info(f"任务验证通过，docId: {doc_id}")
                            with self.__task_infos_lock:
                                if doc_id not in self.__task_infos:
                                    task_obj = TaskInfo(task_info)
                                    task_obj.result_dir_path = self.create_result_dir(task_info)
                                    task_info["resultDirPath"] = task_obj.result_dir_path
                                    task_obj.task_data = task_info  # 更新task_data
                                    self.__task_infos[doc_id] = task_obj
                                    # logger.info(
                                    #     f"✓ 添加新任务到任务池: {doc_id}, 当前任务池大小: {len(self.__task_infos)}")
                                else:
                                    logger.warning(f"任务 {doc_id} 已存在于任务池中")
                        else:
                            logger.warning(f"获取到无效任务: {error_msg}")
                    elif task_status == "none":
                        logger.info("服务端没有新任务")
                    else:
                        logger.warning(f"获取任务失败: {task_info}")
                # else:
                    # logger.debug("任务池已满，跳过获取新任务")

                # 休眠一段时间再检查
                # logger.info("任务池管理器休眠5秒...")
                time.sleep(5)

            except Exception as e:
                logger.exception(f"任务池管理器异常: {e}")
                time.sleep(10)

        logger.info("任务池管理器停止")

    def __script_worker(self, script_id, script_name, script_module):
        """脚本工作线程，处理分配给该脚本的任务"""
        logger.info(f"脚本工作线程启动: {script_name} (ID: {script_id})")

        while not self.__stop:
            try:
                # 从任务池中获取一个未被当前脚本处理过的任务（原子化操作）
                task_obj = self.__get_available_task(script_id)

                if task_obj is None:
                    # 没有可用任务，休眠一段时间
                    time.sleep(3)
                    continue

                logger.info(
                    f"脚本 {script_name} 开始处理任务: {task_obj.doc_id} (ID: {script_id})")

                # 执行下载任务
                success = self.__execute_download_task(task_obj, script_name, script_module)

                if success:
                    # 下载成功，标记任务成功并从任务池中移除
                    task_obj.mark_success()
                    # 记录成功的脚本ID
                    task_obj.task_data["successScriptId"] = script_id
                    with self.__task_infos_lock:
                        if task_obj.doc_id in self.__task_infos:
                            del self.__task_infos[task_obj.doc_id]

                    # 通知服务器下载成功结果
                    self._db.insert_item(task_info=task_obj.task_data)
                    logger.info(f"脚本 {script_name} 成功完成任务: {task_obj.doc_id}，运行结果已存入sqlite，successScriptId: {script_id}")
                else:
                    # 下载失败，标记处理失败状态
                    task_obj.mark_processing_failed()

                    # 检查是否所有脚本都已尝试
                    if task_obj.is_all_scripts_used(self.__script_ids):
                        # 所有脚本都失败了，从任务池中移除
                        with self.__task_infos_lock:
                            if task_obj.doc_id in self.__task_infos:
                                del self.__task_infos[task_obj.doc_id]

                        logger.warning(f"所有脚本都无法处理任务: {task_obj.doc_id}，从任务池移除")

                        # 清理结果目录
                        if task_obj.result_dir_path:
                            util.remove_result_dir(task_obj.result_dir_path)

                        # 通知服务器下载失败结果
                        self._db.insert_item(task_info=task_obj.task_data)
                        logger.info(f"所有脚本都无法处理任务: {task_obj.doc_id}，运行结果已存入sqlite")
                    else:
                        logger.info(f"脚本 {script_name} 处理任务失败: {task_obj.doc_id}，等待其他脚本重试")

                # 任务处理完成后休眠
                sleep_time = self.calculate_sleep_time(task_obj.task_data, is_multi_thread=True)
                time.sleep(sleep_time)

            except Exception as e:
                logger.exception(f"脚本工作线程 {script_name} 异常: {e}")

                # 如果有正在处理的任务，需要清理状态并通知服务器异常结果
                if 'task_obj' in locals() and task_obj is not None:
                    try:
                        # 标记处理失败状态
                        task_obj.mark_processing_failed()

                        # 检查是否所有脚本都已尝试过该任务
                        if task_obj.is_all_scripts_used(self.__script_ids):
                            # 所有脚本都失败了，从任务池中移除并通知服务器
                            with self.__task_infos_lock:
                                if task_obj.doc_id in self.__task_infos:
                                    del self.__task_infos[task_obj.doc_id]

                            logger.warning(f"任务 {task_obj.doc_id} 因异常失败，所有脚本都已尝试，从任务池移除")

                            # 清理结果目录
                            if task_obj.result_dir_path:
                                util.remove_result_dir(task_obj.result_dir_path)

                            # 通知服务器异常失败结果
                            self._db.insert_item(task_info=task_obj.task_data)
                            logger.info(f"任务 {task_obj.doc_id} 异常失败，运行结果已存入sqlite")
                        else:
                            logger.info(f"任务 {task_obj.doc_id} 因异常失败，等待其他脚本重试")
                    except Exception as notify_error:
                        logger.exception(f"通知服务器异常结果时发生错误: {notify_error}")

                time.sleep(10)

        logger.info(f"脚本工作线程停止: {script_name} (ID: {script_id})")

    def __get_available_task(self, script_id):
        """从任务池中获取一个未被当前脚本处理过的任务，并原子化地标记为正在处理"""
        with self.__task_infos_lock:
            for doc_id, task_obj in self.__task_infos.items():
                # 检查任务是否可用：未被当前脚本使用过、未成功、未被其他线程处理
                if (not task_obj.is_script_used(script_id) and
                        not task_obj.is_success and
                        not task_obj.is_processing):
                    # 原子化地标记任务为正在处理
                    if task_obj.mark_processing(script_id):
                        return task_obj
        return None

    def __execute_download_task(self, task_obj, script_name, script_module):
        """执行下载任务"""
        try:
            # 执行脚本
            self.run_script(name=script_name, module=script_module, task=task_obj.task_data)

            # 验证执行结果
            has_result = False
            if util.dict_has_value(task_obj.task_data, "resultDirPath"):
                has_result = util.check_result_file(result_dir_path=task_obj.task_data["resultDirPath"])

            return has_result

        except Exception as e:
            logger.exception(f"执行下载任务异常: {e}")
            return False

    def __safe_next_task_info(self):
        """安全地获取下一个任务信息"""
        try:
            # 根据要求，active_script_ids参数传递空
            return self._api_invoker.next_task_info(active_script_ids=None)
        except Exception as e:
            logger.exception(f"获取任务信息异常: {e}")
            return {"status": "error", "message": str(e)}

    def calculate_sleep_time(self, task_info, is_multi_thread=False):
        """计算任务处理后的休眠时间"""
        # 如果任务信息中有指定的休眠时间，优先使用
        sleep_time = 8
        if util.dict_has_value(task_info, "obtainTaskInterval"):
            try:
                sleep_time = int(task_info["obtainTaskInterval"])
                if sleep_time > 0:
                    sleep_time = random.randint(sleep_time, sleep_time * 3)
            except (ValueError, TypeError):
                pass

        # 设置再大也不超过 8 个小时
        if sleep_time > 60 * 60 * 8:
            sleep_time = 60 * 60 * 8

        sleep_time = max(sleep_time, 3)
        logger.info("【结束】任务执行，休眠 {}s".format(sleep_time))
        return random.randint(sleep_time, sleep_time * 3)

    def stop(self):
        """停止任务处理器"""
        logger.info("收到停止信号，准备停止任务处理器")
        self.__stop = True

    def get_status(self):
        """获取任务处理器状态"""
        with self.__task_infos_lock:
            task_count = len(self.__task_infos)

        return {
            "script_count": len(self.__script_ids),
            "script_ids": self.__script_ids,
            "task_pool_size": task_count,
            "max_task_pool_size": self.__max_task_pool_size,
            "is_stopped": self.__stop
        }
