# coding=utf8
__author__ = 'sw'

import logging
import random
import threading
import time
from collections import deque
from concurrent.futures import ThreadPoolExecutor

import api
import db
import script
from task_executor import TaskExecutor

logger = logging.getLogger()


class MultiThreadTaskProcessor(threading.Thread, TaskExecutor):
    """
    源刊类型线
    多线程任务处理器 - 重写版本
    实现全局任务池管理和脚本执行线程池
    - 全局任务池最大容量5个任务
    - 专用任务获取线程监控任务池
    - 脚本执行线程池最大5个工作线程
    - 确保每个scriptIdStr同时只能被一个线程处理
    """

    def __init__(self, site_id: int, db_file: db.DbFile, api_invoker: api.ApiInvoker):
        threading.Thread.__init__(self, daemon=True)
        TaskExecutor.__init__(self, site_id, db_file, api_invoker)
        self.__stop = False

        # 全局任务池管理
        self.__task_pool_max_size = 5  # 任务池最大容量
        self.__task_infos = deque()  # 全局任务池
        self.__task_pool_lock = threading.Lock()  # 任务池访问锁
        self.__pulled_script_ids = set()  # 当前活跃的脚本ID集合（任务池+运行中）
        self.__pulled_script_ids_lock = threading.Lock()  # 活跃脚本ID锁

        # 脚本执行线程池
        self.__max_workers = 5  # 最大工作线程数
        # self.__executor = ThreadPoolExecutor(max_workers=self.__max_workers, thread_name_prefix="ScriptWorker")
        self.__running_script_ids = set()  # 当前正在运行的脚本ID集合
        self.__running_script_ids_lock = threading.Lock()  # 运行脚本ID锁

        # 任务获取线程
        self.__task_fetcher_thread = None
        self.__task_fetcher_stop = False

        # 统计信息
        self.__stats = {
            'total_tasks_fetched': 0,
            'total_tasks_executed': 0,
            'success_tasks': 0,
            'failed_tasks': 0,
            'current_pool_size': 0,
            'current_running_scripts': 0
        }
        self.__stats_lock = threading.Lock()

        # 脚本上下文
        self._script_context = script.ScriptContext(site_id=self._site_id, api_invoker=self._api_invoker)

    def run(self) -> None:
        """主线程：启动任务获取线程和脚本执行线程池"""
        logger.info(f"多线程任务处理器启动，站点: {self._site_id}")

        try:
            # 启动任务获取线程
            self.__start_task_fetcher_thread()

            # 启动脚本执行工作线程
            self.__start_script_execution_workers()

            # 主线程监控循环
            while not self.__stop:
                self.safe_sleep(15)  # 每15秒检查一次状态
                self.__log_status()

        except Exception as e:
            logger.exception(f"多线程任务处理器运行异常: {e}")
        finally:
            self.__shutdown()

    def __start_task_fetcher_thread(self):
        """启动任务获取线程"""
        self.__task_fetcher_thread = threading.Thread(
            target=self.__task_fetcher_worker,
            name="TaskFetcher",
            daemon=True
        )
        self.__task_fetcher_thread.start()
        logger.info("任务获取线程已启动")

    def __task_fetcher_worker(self):
        """任务获取线程工作函数"""
        logger.info("任务获取线程开始工作")

        while not self.__task_fetcher_stop and not self.__stop:
            try:
                # 检查任务池大小
                with self.__task_pool_lock:
                    current_pool_size = len(self.__task_infos)

                # 如果任务池未满，尝试获取新任务
                if current_pool_size < self.__task_pool_max_size:
                    self.__fetch_new_task()
                else:
                    # 任务池已满，等待一段时间
                    self.safe_sleep(5)

            except Exception as e:
                logger.exception(f"任务获取线程异常: {e}")
                self.safe_sleep(5)  # 异常后等待5秒再重试

        logger.info("任务获取线程已停止")

    def __fetch_new_task(self):
        """获取新任务并添加到任务池"""
        try:
            # 获取当前任务池中和运行中的所有脚本ID列表（线程安全）
            pulled_script_ids_list = self.__get_current_script_ids()

            # 调用API获取任务
            task_info = self._api_invoker.next_task_info(pulled_script_ids_list)

            # 解析任务状态
            task_status = task_info.get("status", "")

            if task_status == "none":
                logger.debug("服务端暂无新任务")
                self.safe_sleep(10)  # 无任务时等待10秒
                return

            if task_status.strip().lower() != "success":
                logger.warning(f"获取任务失败，状态: {task_status}")
                self.safe_sleep(5)
                return

            # 验证任务信息
            is_valid, error_msg = self.validate_task_info(task_info)
            if not is_valid:
                logger.warning(f"任务信息验证失败: {error_msg}")
                return

            # 添加到任务池
            script_id_str = task_info.get("scriptIdStr", "")
            doc_id = task_info.get("docId", "")

            with self.__task_pool_lock:
                self.__task_infos.append(task_info)

            # 更新活跃的脚本ID集合
            if script_id_str:
                with self.__pulled_script_ids_lock:
                    self.__pulled_script_ids.add(script_id_str)

            # 更新统计信息
            with self.__stats_lock:
                self.__stats['total_tasks_fetched'] += 1
                self.__stats['current_pool_size'] = len(self.__task_infos)

            logger.info(
                f"成功获取新任务: doc_id={doc_id}, script_id={script_id_str}, 任务池大小: {len(self.__task_infos)}")
            self.safe_sleep(random.randint(1, 3))

        except Exception as e:
            logger.exception(f"获取新任务异常: {e}")
            self.safe_sleep(5)

    def __get_current_script_ids(self):
        """线程安全地获取当前任务池中和运行中的所有脚本ID列表"""
        script_ids = set()

        # 获取任务池中的脚本ID
        with self.__task_pool_lock:
            for task_info in self.__task_infos:
                script_id_str = task_info.get("scriptIdStr", "")
                if script_id_str:
                    script_ids.add(script_id_str)

        # 获取运行中的脚本ID
        with self.__running_script_ids_lock:
            script_ids.update(self.__running_script_ids)

        return list(script_ids)

    def __start_script_execution_workers(self):
        """启动脚本执行工作线程"""
        for i in range(self.__max_workers):
            worker_thread = threading.Thread(
                target=self.__script_execution_worker,
                name=f"ScriptWorker-{i + 1}",
                daemon=True
            )
            worker_thread.start()
        logger.info(f"已启动 {self.__max_workers} 个脚本执行工作线程")

    def __script_execution_worker(self):
        """脚本执行工作线程函数"""
        thread_name = threading.current_thread().name
        logger.info(f"脚本执行工作线程 {thread_name} 开始工作")

        while not self.__stop:
            try:
                # 从任务池获取可执行的任务
                task_info = self.__get_available_task()

                if task_info is None:
                    # 没有可执行任务，等待一段时间
                    self.safe_sleep(1)
                    continue

                # 执行任务
                self.__execute_script_task(task_info)

            except Exception as e:
                logger.exception(f"脚本执行工作线程 {thread_name} 异常: {e}")
                self.safe_sleep(2)

        logger.info(f"脚本执行工作线程 {thread_name} 已停止")

    def __get_available_task(self):
        """从任务池获取一个可执行的任务"""
        with self.__task_pool_lock:
            if not self.__task_infos:
                return None

            # 查找一个scriptIdStr不在运行中的任务
            for i, task_info in enumerate(self.__task_infos):
                script_id_str = task_info.get("scriptIdStr", "")

                with self.__running_script_ids_lock:
                    if script_id_str not in self.__running_script_ids:
                        # 找到可执行任务，从池中移除并标记为运行中
                        # 修复：正确移除找到的任务
                        del self.__task_infos[i]  # 直接删除找到的任务
                        self.__running_script_ids.add(script_id_str)

                        # 更新统计信息
                        with self.__stats_lock:
                            self.__stats['current_pool_size'] = len(self.__task_infos)
                            self.__stats['current_running_scripts'] = len(self.__running_script_ids)

                        return task_info

            return None  # 没有找到可执行任务

    def __execute_script_task(self, task_info):
        """执行脚本任务"""
        script_id_str = task_info.get("scriptIdStr", "")
        task_id = task_info.get("taskId", "")
        doc_id = task_info.get("docId", "")
        thread_name = threading.current_thread().name

        try:
            logger.info(f"[{thread_name}] 开始执行任务: task_id={task_id}, doc_id={doc_id}, script_id={script_id_str}")

            # 获取脚本模块信息
            script_module_info = self.__get_task_script_module(task_info)
            if script_module_info is None:
                logger.error(f"[{thread_name}] 无法获取脚本模块: script_id={script_id_str}")
                return False

            # name, md5, module, script_id_str_from_module = script_module_info

            # 记录任务开始时间，用于超时检测
            start_time = time.time()

            # 执行任务核心逻辑
            result = self.execute_task_core(task_info)

            # 如果执行成功，记录成功的脚本ID
            if result:
                task_info["successScriptId"] = script_id_str

            # 记录任务执行时间
            execution_time = time.time() - start_time
            logger.info(f"[{thread_name}] 任务执行耗时: {execution_time:.2f} 秒")

            # 更新统计信息
            with self.__stats_lock:
                self.__stats['total_tasks_executed'] += 1
                if result:
                    self.__stats['success_tasks'] += 1
                else:
                    self.__stats['failed_tasks'] += 1

            if result:
                logger.info(f"[{thread_name}] 任务执行完成: task_id={task_id}, 结果={result}, successScriptId={script_id_str}")
            else:
                logger.info(f"[{thread_name}] 任务执行完成: task_id={task_id}, 结果={result}")

            # 任务完成后休眠
            sleep_time = self.calculate_sleep_time(task_info, is_multi_thread=True)
            logger.info(f"[{thread_name}] 脚本 {script_id_str} 执行完成，休眠 {sleep_time} 秒")
            self.safe_sleep(sleep_time)

            return result

        except Exception as e:
            logger.exception(f"[{thread_name}] 执行任务异常: task_id={task_id}, 异常={e}")
            self.handle_task_error(task_info, e, f"[{thread_name}] 脚本执行")

            # 异常情况下也要休眠
            sleep_time = self.calculate_sleep_time(task_info, is_multi_thread=True)
            self.safe_sleep(sleep_time)
            return False

        finally:
            # 清理运行状态
            with self.__running_script_ids_lock:
                self.__running_script_ids.discard(script_id_str)

            # 从已拉取脚本ID集合中移除（任务完成后不再需要排除）
            with self.__pulled_script_ids_lock:
                self.__pulled_script_ids.discard(script_id_str)

            with self.__stats_lock:
                self.__stats['current_running_scripts'] = len(self.__running_script_ids)

    def __get_task_script_module(self, task_info):
        """获取任务对应的脚本模块信息"""
        try:
            # 使用脚本上下文获取任务脚本模块
            script_modules = list(self._script_context.iter_script_modules(task_info))
            if script_modules:
                # 返回第一个可用的脚本模块，格式：(name, md5, module, script_id_str)
                return script_modules[0]
            else:
                logger.warning(f"未找到任务对应的脚本模块: {task_info.get('scriptIdStr', '')}")
                return None
        except Exception as e:
            logger.exception(f"获取脚本模块异常: {e}")
            return None

    def __log_status(self):
        """记录当前状态信息"""
        with self.__stats_lock:
            stats = self.__stats.copy()

        with self.__task_pool_lock:
            pool_size = len(self.__task_infos)

        with self.__running_script_ids_lock:
            running_count = len(self.__running_script_ids)
            running_scripts = list(self.__running_script_ids)

        with self.__pulled_script_ids_lock:
            active_count = len(self.__pulled_script_ids)

        logger.info(f"状态报告 - 任务池: {pool_size}/{self.__task_pool_max_size}, "
                    f"运行中: {running_count}/{self.__max_workers}, "
                    f"活跃脚本: {active_count}, "
                    f"总获取: {stats['total_tasks_fetched']}, "
                    f"总执行: {stats['total_tasks_executed']}, "
                    f"成功: {stats['success_tasks']}, "
                    f"失败: {stats['failed_tasks']}")

        if running_scripts:
            logger.debug(f"当前运行的脚本ID: {running_scripts}")

    def __shutdown(self):
        """关闭任务处理器"""
        logger.info("开始关闭多线程任务处理器...")

        # 停止任务获取线程
        self.__task_fetcher_stop = True
        if self.__task_fetcher_thread and self.__task_fetcher_thread.is_alive():
            self.__task_fetcher_thread.join(timeout=5)

        # 关闭线程池
        # self.__executor.shutdown(wait=True)

        # 清理任务池
        with self.__task_pool_lock:
            remaining_tasks = len(self.__task_infos)
            if remaining_tasks > 0:
                logger.info(f"清理任务池中剩余的 {remaining_tasks} 个任务")
                self.__task_infos.clear()

        # 清理运行状态
        with self.__running_script_ids_lock:
            if self.__running_script_ids:
                logger.info(f"清理运行中的脚本ID: {list(self.__running_script_ids)}")
                self.__running_script_ids.clear()

        logger.info("多线程任务处理器已关闭")

    def get_stats(self):
        """获取统计信息"""
        with self.__stats_lock:
            stats = self.__stats.copy()

        # 添加任务池状态信息
        with self.__task_pool_lock:
            stats['task_pool_size'] = len(self.__task_infos)
            stats['task_pool_max_size'] = self.__task_pool_max_size
            stats['task_pool_usage_rate'] = round(len(self.__task_infos) / self.__task_pool_max_size * 100, 2)

        # 添加运行状态信息
        with self.__running_script_ids_lock:
            stats['running_script_ids'] = list(self.__running_script_ids)
            stats['running_scripts_count'] = len(self.__running_script_ids)

        # 添加活跃脚本信息
        with self.__pulled_script_ids_lock:
            stats['active_script_ids'] = list(self.__pulled_script_ids)
            stats['active_scripts_count'] = len(self.__pulled_script_ids)

        return stats

    def get_task_pool_status(self):
        """获取任务池详细状态"""
        with self.__task_pool_lock:
            current_size = len(self.__task_infos)

        return {
            'current_size': current_size,
            'max_size': self.__task_pool_max_size,
            'usage_rate': round(current_size / self.__task_pool_max_size * 100, 2),
            'is_full': current_size >= self.__task_pool_max_size,
            'is_empty': current_size == 0
        }

    def stop(self):
        """停止任务处理器"""
        logger.info("收到停止信号，开始停止多线程任务处理器...")
        self.__stop = True

        # 等待主线程完成清理
        if self.is_alive():
            self.join(timeout=10)

        logger.info("多线程任务处理器已停止")

    def reset_pulled_script_ids(self):
        """重置活跃脚本ID集合（用于测试或重启）"""
        with self.__pulled_script_ids_lock:
            old_count = len(self.__pulled_script_ids)
            self.__pulled_script_ids.clear()
            logger.info(f"已重置活跃脚本ID集合，清理了 {old_count} 个脚本ID")

    def get_pulled_script_ids(self):
        """获取活跃脚本ID列表（任务池+运行中）"""
        with self.__pulled_script_ids_lock:
            return list(self.__pulled_script_ids)

    def get_running_script_ids(self):
        """获取当前运行中的脚本ID列表"""
        with self.__running_script_ids_lock:
            return list(self.__running_script_ids)

    def get_task_pool_info(self):
        """获取任务池中的任务信息"""
        with self.__task_pool_lock:
            tasks_info = []
            for task in self.__task_infos:
                tasks_info.append({
                    'taskId': task.get('taskId', ''),
                    'docId': task.get('docId', ''),
                    'scriptIdStr': task.get('scriptIdStr', ''),
                    'journalId': task.get('journalId', '')
                })
            return tasks_info
