podman run --name ads-node -d --restart on-failure:5 -m 300m --cpu-shares 512 registry.cn-hangzhou.aliyuncs.com/aliyun-public/adsnode-v3:1 python3 start.py --siteId 23

```bash
yum update -y && yum install -y podman
podman pull registry.cn-hangzhou.aliyuncs.com/aliyun-public/adsnode-v3:1


chmod +x ./ads-node*
```

crontab -e
```shell
PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/bin:$PATH

0 */6 * * * /root/ads-node.sh >> /root/ads.log 2>&1
```

pip install -r requirements.txt