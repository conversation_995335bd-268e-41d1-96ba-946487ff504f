# coding=utf8
__author__ = 'yhju'

import json
import logging
import threading
import time

import api
import util

log = logging.getLogger()


class Handshake(threading.Thread):
    """
    握手线程,守护线程，主进程退出自动退出
    """

    def __init__(self, site_id: int, api_invoker: api.ApiInvoker, interval=60):
        super(Handshake, self).__init__(daemon=True)
        if site_id is None or site_id < 1:
            raise ValueError("握手线程需要指定站点ID")
        if api_invoker is None:
            raise ValueError("请先初始化接口调用类")
        self.__site_id = site_id
        self.__api_invoker = api_invoker
        self.__interval = interval
        # 项目启动时就 初始化时发送一次握手信息，可以判断是否可以链接到服务器
        sys_info = util.system_info()
        print("\n本机信息：\n{}\n".format(json.dumps(sys_info, ensure_ascii=False, sort_keys=True, indent="\t")))
        self.__api_invoker.send_handshake(sys_info)
        self.__stop = False

    def run(self) -> None:
        while True:
            if self.__stop:
                logging.warning("握手线程已停止")
                return
            try:
                time.sleep(self.__interval)
                response = self.__api_invoker.send_handshake(util.system_info())

                # 检查是否需要上传日志
                if response and response.get("data") == 1:
                    try:
                        self.__upload_log_if_needed()
                    except BaseException as e:
                        logging.warning("上传日志失败: {}".format(e))

            except BaseException as e:
                logging.info("发送心跳失败 {}".format(e))

    def __upload_log_if_needed(self):
        """当服务器要求时上传日志文件的最后2000行"""
        import os

        # 使用专门的上传日志文件路径（小文件，2MB限制）
        log_file_path = "./log/ads_upload.log"

        if not os.path.exists(log_file_path):
            logging.warning("日志文件不存在: {}".format(log_file_path))
            return

        try:
            # 读取最后2000行（使用小文件，读取更高效）
            log_content = self.__read_last_lines(log_file_path, 2000)
            if log_content:
                # 发送日志内容到服务器
                self.__api_invoker.send_log_upload(self.__site_id, log_content)
                logging.info("日志上传成功，上传了 {} 字节的日志内容".format(len(log_content)))
            else:
                logging.warning("日志文件为空或无法读取")
        except BaseException as e:
            logging.error("读取或上传日志失败: {}".format(e))
            raise

    def __read_last_lines(self, file_path, num_lines):
        """高效读取小日志文件的最后N行（专为2MB以下的上传日志文件优化）"""
        import os

        try:
            if not os.path.exists(file_path):
                logging.debug("日志文件不存在: {}".format(file_path))
                return ""

            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return ""

            # 对于小文件（<5MB），直接读取全部内容更简单高效
            if file_size <= 5 * 1024 * 1024:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    # 获取最后num_lines行
                    last_lines = lines[-num_lines:] if len(lines) > num_lines else lines
                    return ''.join(last_lines)

            # 对于较大文件，使用反向读取（虽然在2MB限制下不太可能到达这里）
            with open(file_path, 'rb') as f:
                lines_found = 0
                buffer_size = 8192  # 8KB缓冲区
                blocks = []
                pos = file_size

                while pos > 0 and lines_found < num_lines:
                    read_size = min(buffer_size, pos)
                    pos -= read_size

                    f.seek(pos)
                    block = f.read(read_size)
                    blocks.append(block)
                    lines_found += block.count(b'\n')

                # 处理读取的数据
                all_data = b''.join(reversed(blocks))

                # 尝试解码
                text = None
                for encoding in ['utf-8', 'gbk', 'gb2312', 'latin-1']:
                    try:
                        text = all_data.decode(encoding, errors='ignore')
                        break
                    except UnicodeDecodeError:
                        continue

                if text is None:
                    logging.error("无法解码文件内容")
                    return ""

                lines = text.split('\n')

                # 如果不是从文件开头开始读取，第一行可能不完整
                if pos > 0 and lines:
                    lines = lines[1:]

                # 移除最后的空行
                if lines and lines[-1] == '':
                    lines = lines[:-1]

                # 获取最后num_lines行
                last_lines = lines[-num_lines:] if len(lines) > num_lines else lines
                result = '\n'.join(last_lines)

                if result and not result.endswith('\n'):
                    result += '\n'

                return result

        except BaseException as e:
            logging.error("读取日志文件失败: {}".format(e))
            return None

    def stop(self):
        self.__stop = True
