# coding=utf8
__author__ = 'yhju'

import json
import logging
import os
import sqlite3
import threading

import util

log = logging.getLogger()


class DbFile(object):
    def __init__(self, timeout=30):
        """
        控制本地数据库，Sqlite3
        :param timeout:  timeout 是获取数据库锁最大等待时间
        :return:
        """
        self.__timeout = timeout
        self.__lock = threading.Lock()
        # 初始化连接
        self.__conn = self.__init_conn()
        self.__init_table()

    def __init_conn(self):

        db_dir_path = os.path.join(util.program_root_dir(), "db")
        if not os.path.exists(db_dir_path):
            os.makedirs(db_dir_path)

        db_file_path = os.path.join(db_dir_path, "ads.db")
        log.debug("库初始化至 {}".format(db_file_path))

        return sqlite3.connect(
            database=db_file_path,
            timeout=self.__timeout,
            check_same_thread=False
        )

    def __init_table(self):
        """
        如果表不存在，就创建
        :return:
        """
        sql = """
                create table if not exists result_file (
                  id integer primary key autoincrement,
                  task_info text not null,
                  create_time timestamp default current_timestamp not null,
                  update_time timestamp default current_timestamp not null,
                  upload_num integer default 0
                );
                
                -- 创建一个触发器自动更新 update_time
                CREATE TRIGGER if not exists UpdateLastTime AFTER UPDATE OF task_info, upload_num ON result_file
                BEGIN
                  UPDATE result_file SET update_time=CURRENT_TIMESTAMP WHERE new.id=old.id;
                END;
                
                -- 索引
                create index if not exists idx_create_time on result_file(create_time);
                create index if not exists idx_update_time on result_file(update_time);
            """
        self.__lock.acquire(blocking=True, timeout=-1)

        cursor = None
        try:
            cursor = self.__conn.cursor()
            cursor.execute('PRAGMA journal_mode=WAL')
            cursor.executescript(sql)
            self.__conn.commit()
        except BaseException as e:
            self.__conn.rollback()
            raise ValueError("创建库失败 {}".format(str(e)))
        finally:
            self.__cursor_close(cursor)
            self.__lock.release()

    def get_next_item(self, start_id=0) -> dict:

        sql = "select * from result_file where id > ? order by id asc limit 1 offset 0"

        self.__lock.acquire(blocking=True, timeout=-1)

        cursor = None
        try:
            cursor = self.__conn.cursor()
            cursor.execute(sql, (start_id,))
            row = cursor.fetchone()
            return dict(zip([x[0] for x in cursor.description], row)) if row else None
        except Exception as e:
            raise ValueError("查询数据失败 {}".format(str(e)))
        finally:
            self.__cursor_close(cursor)
            self.__lock.release()

    def insert_item(self, task_info: dict):
        """
        保存或更新任务结果信息
        :return:
        """
        if task_info is None:
            return

        sql = "insert into result_file(task_info, upload_num) values(?, ?)"

        self.__lock.acquire(blocking=True, timeout=-1)

        cursor = None
        try:
            cursor = self.__conn.cursor()
            cursor.execute(sql, (json.dumps(task_info, ensure_ascii=False), 0))
            self.__conn.commit()
        except Exception as e:
            self.__conn.rollback()
            raise ValueError("插入数据失败 {}".format(str(e)))
        finally:
            self.__cursor_close(cursor)
            self.__lock.release()

    def delete_item(self, item_id: int):
        if item_id is None or item_id < 1:
            return

        sql = "delete from result_file where id = ?"

        self.__lock.acquire(blocking=True, timeout=-1)

        cursor = None
        try:
            cursor = self.__conn.cursor()
            cursor.execute(sql, (item_id,))
            self.__conn.commit()
        except Exception as e:
            self.__conn.rollback()
            raise ValueError("删除数据失败 {}".format(str(e)))
        finally:
            self.__cursor_close(cursor)
            self.__lock.release()

    def inc_upload_num(self, item_id: int):
        if item_id is None or item_id < 1:
            return

        sql = "update result_file set upload_num=upload_num+1 where id = ?"

        self.__lock.acquire(blocking=True, timeout=-1)

        cursor = None
        try:
            cursor = self.__conn.cursor()
            cursor.execute(sql, (item_id,))
            self.__conn.commit()
        except Exception as e:
            self.__conn.rollback()
            raise ValueError("自增 upload_num 失败 {}".format(str(e)))
        finally:
            self.__cursor_close(cursor)
            self.__lock.release()

    @staticmethod
    def __cursor_close(cursor):
        if not cursor:
            return
        try:
            cursor.close()
        except BaseException as e:
            logging.exception(e)

    def close(self):
        self.__lock.acquire(blocking=True, timeout=-1)
        if self.__conn is not None:
            try:
                self.__conn.commit()
            except Exception as e:
                log.error("关闭库连接失败, {}".format(str(e)))
            try:
                self.__conn.close()
            except Exception as e:
                log.error("关闭库连接失败, {}".format(str(e)))
        self.__lock.release()
        if log:
            log.debug("数据库已关闭")

    def __del__(self):
        self.close()
