#!/bin/bash

# 强制清理所有镜像，慎用
# docker builder prune -f
# docker system prune -a
# docker system prune -a --volumes
# Docker构建脚本 - 支持日志记录和清理选项
# 使用方法：
# ./docker_build.sh                    # 普通构建
# ./docker_build.sh --log              # 构建并记录日志到文件
# ./docker_build.sh --log-background   # 后台构建并记录日志
# ./docker_build.sh --log-tee          # 构建时同时显示和记录日志
# ./docker_build.sh --clean            # 清理后构建
# ./docker_build.sh --clean --log-tee  # 清理后构建并记录日志
# ./docker_build.sh --prune            # 构建后清理无用镜像

IMAGE_NAME="adsnode-v3:2"
LOG_FILE="docker_build_$(date +%Y%m%d_%H%M%S).log"

# 清理函数
clean_before_build() {
    echo "=== 开始清理操作 ==="

    # 1. 删除指定镜像（如果存在）
    if docker images | grep -q "^${IMAGE_NAME%:*}.*${IMAGE_NAME#*:}"; then
        echo "删除现有镜像: $IMAGE_NAME"
        docker rmi $IMAGE_NAME 2>/dev/null || echo "镜像删除失败或不存在"
    fi

    # 2. 清理悬空镜像（<none>标签的镜像）
    DANGLING_IMAGES=$(docker images -f "dangling=true" -q)
    if [ -n "$DANGLING_IMAGES" ]; then
        echo "清理悬空镜像..."
        docker rmi $DANGLING_IMAGES 2>/dev/null || echo "部分悬空镜像清理失败"
    fi

    # 3. 清理构建缓存（可选，会显著增加构建时间）
    # docker builder prune -f

    echo "=== 清理操作完成 ==="
}

# 构建后清理函数
prune_after_build() {
    echo "=== 开始构建后清理 ==="

    # 清理悬空镜像
    DANGLING_IMAGES=$(docker images -f "dangling=true" -q)
    if [ -n "$DANGLING_IMAGES" ]; then
        echo "清理构建产生的悬空镜像..."
        docker rmi $DANGLING_IMAGES 2>/dev/null || echo "部分悬空镜像清理失败"
    fi

    # 清理未使用的构建缓存（保留最近24小时的）
    docker builder prune -f --filter until=24h

    echo "=== 构建后清理完成 ==="
}

# 解析参数
CLEAN_BEFORE=false
PRUNE_AFTER=false
BUILD_MODE=""

for arg in "$@"; do
    case $arg in
        --clean)
            CLEAN_BEFORE=true
            ;;
        --prune)
            PRUNE_AFTER=true
            ;;
        --log|--log-background|--log-tee|--log-tee-background)
            BUILD_MODE=$arg
            ;;
    esac
done

# 如果没有指定构建模式，使用默认模式
if [ -z "$BUILD_MODE" ]; then
    BUILD_MODE="--log-tee"
fi

# 执行清理（如果需要）
if [ "$CLEAN_BEFORE" = true ]; then
    clean_before_build
fi

case "$BUILD_MODE" in
    --log)
        echo "开始Docker构建，日志将保存到: $LOG_FILE"
        docker build -t $IMAGE_NAME . > $LOG_FILE 2>&1
        echo "构建完成，日志已保存到: $LOG_FILE"
        ;;
    --log-background)
        echo "开始后台Docker构建，日志将保存到: $LOG_FILE"
        nohup docker build -t $IMAGE_NAME . > $LOG_FILE 2>&1 &
        BUILD_PID=$!
        echo "构建进程ID: $BUILD_PID"
        echo "可以使用以下命令查看日志: tail -f $LOG_FILE"
        echo "可以使用以下命令检查进程状态: ps -p $BUILD_PID"
        ;;
    --log-tee)
        echo "开始Docker构建，日志将同时显示和保存到: $LOG_FILE"
        docker build -t $IMAGE_NAME . 2>&1 | tee $LOG_FILE
        ;;
    --log-tee-background)
        echo "开始后台Docker构建，日志将同时显示和保存到: $LOG_FILE"
        nohup bash -c "docker build -t $IMAGE_NAME . 2>&1 | tee $LOG_FILE" &
        BUILD_PID=$!
        echo "构建进程ID: $BUILD_PID"
        echo "可以使用以下命令查看日志: tail -f $LOG_FILE"
        ;;
    default|*)
        echo "普通Docker构建（无日志记录）"
        docker build -t $IMAGE_NAME .
        ;;
esac

# 执行构建后清理（如果需要）
if [ "$PRUNE_AFTER" = true ]; then
    prune_after_build
fi

echo "构建完成！"
echo "镜像信息："
docker images | grep "${IMAGE_NAME%:*}" | head -5