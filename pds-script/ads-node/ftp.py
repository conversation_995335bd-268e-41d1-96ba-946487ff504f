# coding=utf8
__author__ = 'yhju'

import logging
import os
import socket
from ftplib import FTP

from retry import retry

import util
from implicit_ftp_tls import ImplicitFtpTLS

logger = logging.getLogger()

socket.setdefaulttimeout(60)


def ftp_protocol_host(host: str):
    """
    判断是否 是 ftps 的地址，当前只支持 implicit 的 TLS 方式
    :param host:
    :return:
    """
    if host is None:
        return "ftp", None
    host = host.strip().lower()
    if host.startswith("ftps://"):
        return "ftps", host.replace("ftps://", "")
    if host.startswith("ftp://"):
        return "ftp", host.replace("ftp://", "")
    split = host.split("://")
    if len(split) > 1 and split[0] and split[0].strip() != "":
        return split[0], split[1]
    return None, host


class FtpClient(object):
    """
    Ftp 客户端工具
    """

    def __init__(self, host: str, username='', pwd='', port=21, acct='', timeout=-999, encode="UTF-8", pasv=True):
        """
        初始化 Ftp 客户端工具，Ftp连接如果获取失败，有重试机制
        :param host:
        :param username:
        :param pwd:
        :param port:
        :param acct:
        :param timeout:
        :param encode:
        :return:
        """
        if host is None:
            raise ValueError("请指定 Ftp 服务器地址")
        if username is None:
            raise ValueError("请指定 Ftp 登陆名")
        if pwd is None:
            raise ValueError("请指定 Ftp 登陆密码")

        # 如果没有 指定 protocol，则默认为 ftp
        protocol, host = ftp_protocol_host(host)
        if protocol and protocol not in ("ftp", "ftps"):
            raise ValueError("Host 格式错误，仅支持 ftp 和 ftps，您设置的是 Protocol: {}".format(protocol))

        self.BUFFER_SIZE = 8 * 1024
        self.is_ssl = True if protocol and protocol.strip() == "ftps" else False
        self.host = host
        self.port = port
        self.username = username
        self.pwd = pwd
        self.acct = acct
        self.timeout = timeout
        self.encode = encode
        self.pasv = pasv

        # Ftp 连接，在第一次访问时才进行创建
        self.__ftp = None

    @retry(tries=2, delay=10)
    def connector(self):
        if not self.__connected():
            self.__ftp = self.__create_connector()

        return self.__ftp

    def __connected(self):
        """
        判断Ftp当前是否连接着
        :return:
        """
        if self.__ftp is None:
            return False
        try:
            current_path = self.__ftp.pwd()
            if current_path:
                return True
        except BaseException as e:
            logger.error("无法 执行 Ftp 的 pwd 命令，服务器可能连接不上 {}".format(e))
        return False

    def __create_connector(self):
        error_msg = "无法连接到Ftp服务器, 请检查您的网络 {}:{} {}/****".format(self.host, self.port, self.username)
        try:
            logger.debug("开始获取 Ftp 连接...")
            ftp_client = ImplicitFtpTLS() if self.is_ssl else FTP()
            ftp_client.encoding = self.encode
            ftp_client.set_pasv(self.pasv)
            ftp_client.connect(host=self.host, port=self.port, timeout=self.timeout)
            # 如果是 TLS 的 Host，在连接服务器后指定加密
            if self.is_ssl:
                ftp_client.prot_p()
            if self.username is not None:
                ftp_client.login(user=self.username, passwd=self.pwd, acct=self.acct)
        except (ConnectionRefusedError, TimeoutError, WindowsError) as e:
            logger.exception("Ftp 链接失败 {}".format(str(e)))
            raise ConnectionRefusedError(error_msg)
        except BaseException as e:
            logger.exception("Ftp 链接失败 {}".format(str(e)))
            raise ConnectionRefusedError(error_msg)
        else:
            logger.debug(ftp_client.welcome)
        return ftp_client

    def mk_directory(self, path):
        """
        在服务器上创建多级目录, 并将当前目录设置为该目录
        注意事项：创建多级目录后，工作目录将被切换到最内层目录中
        文件夹名称，不能含有特殊字符，如 \ 、/ 、: 、* 、?、 "、 <、>...
        :param path:
        :return: True | False
        """

        if path is None or path.strip() == "":
            return

        path = util.norm_file_path(path)
        if path == "/":
            self.change_work_directory("/")
            return

        if path.startswith("/"):
            self.change_work_directory("/")
            path = path[1:]

        path = path[-1] if path.endswith("/") else path
        pathname_array = path.split("/")

        for each in pathname_array:
            if each is None or each.strip() == "":
                continue
            try:
                self.connector().mkd(each)
            except BaseException as e:
                logger.debug("Ftp 目录没有创建，{}".format(e))
            self.change_work_directory(each)

    def exists(self, pathname):
        """
        判断文件是否存在
        :param pathname:
        :return:
        """
        try:
            self.modify_time(pathname)
        except BaseException as e:
            logger.debug("ftp文件 {} 不存在 {}".format(pathname, str(e)))
            return False
        else:
            return True

    def change_work_directory(self, directory):
        """
        进入到服务器的某个目录下
        :param directory:
        :return:
        """
        if directory is None or directory.strip() == "":
            directory = "/"

        directory = util.norm_file_path(directory)
        self.connector().cwd(directory)

    def current_work_directory(self):
        """
        获取Ftp当前目录
        :return:
        """
        return self.connector().pwd()

    def upload(self, local_file_path, to_ftp_path, callback=None, rest=None):
        """
        上传文件
        :param rest:
        :param callback:
        :param local_file_path: 要上传的本地文件路径
        :param to_ftp_path: 上传到Ftp服务器上的那个文件名字
        :return: True | False
        """
        logger.info("上传 {} 到 Ftp {}".format(local_file_path, to_ftp_path))
        if local_file_path is None or local_file_path.strip() == "":
            raise ValueError("请指定要上传的文件路径")
        if not os.path.exists(local_file_path):
            raise FileNotFoundError("上传文件未找到，{}".format(local_file_path))
        if not os.path.isfile(local_file_path):
            raise TypeError("请指定上传的是文件类型 {}".format(local_file_path))
        if to_ftp_path is None or to_ftp_path.strip() == "":
            to_ftp_path = os.path.basename(local_file_path)

        to_ftp_path = util.norm_file_path(to_ftp_path)

        ftp_file_name = to_ftp_path.split("/")[-1]
        self.mk_directory(to_ftp_path.rstrip(ftp_file_name))

        with open(local_file_path, "rb") as file_handel:
            self.connector().storbinary(
                cmd="STOR {}".format(ftp_file_name),
                fp=file_handel,
                blocksize=self.BUFFER_SIZE,
                callback=callback,
                rest=rest
            )
        return to_ftp_path

    def download(self, ftp_file, to_local_file):
        """
        下载文件到 to_local_file
        :return: True | False
        """
        if ftp_file is None or ftp_file.strip() == "":
            raise ValueError("请指定要下载的文件路径")
        if to_local_file is None or to_local_file.strip() == "":
            raise ValueError("请指定下载到本地的文件地址")

        ftp_file = util.norm_file_path(ftp_file)

        with open(to_local_file, "wb") as file_handel:
            self.connector().retrbinary("RETR {}".format(ftp_file), file_handel, self.BUFFER_SIZE)

    def delete_file(self, ftp_file):
        """
        删除Ftp文件
        :param ftp_file:
        :return:
        """
        if ftp_file is None or ftp_file.strip() == "" or ftp_file.strip() == "/":
            raise ValueError("请指定要删除的文件")

        ftp_file = util.norm_file_path(ftp_file)
        if self.exists(ftp_file):
            self.connector().delete(ftp_file)

    def delete_directory(self, ftp_dir):
        """
        删除ftp上的目录及其子文件
        :param ftp_dir:
        :return:
        """
        if ftp_dir is None or ftp_dir.strip() == "":
            raise ValueError("请指定要删除的文件")

        ftp_dir = util.norm_file_path(ftp_dir)

        files = self.connector.mlsd(path=ftp_dir, facts=["type"])
        if files is not None:
            for ftp_file in files:
                if ftp_dir.endswith("/"):
                    file_path = ftp_dir + ftp_file[0]
                else:
                    file_path = ftp_dir + "/" + ftp_file[0]
                if ftp_file[1]["type"] == "dir":
                    self.delete_directory(ftp_dir=file_path)
                else:
                    self.delete_file(ftp_file=file_path)

        if ftp_dir.strip() != "/":
            self.__ftp.rmd(ftp_dir)

    def mlsd(self, pathname, facts=None):
        pathname = util.norm_file_path(pathname)
        yield from self.connector().mlsd(path=pathname, facts=facts)

    def size(self, pathname):
        """
        获取 文件 的 字节数量
        :return:
        """
        if pathname is None or pathname.strip() == "":
            raise ValueError("请指定要获取大小的文件")

        pathname = util.norm_file_path(pathname)

        return self.connector().size(filename=pathname)

    def get_md5(self, pathname):
        """
        获取Ftp上文件的md5值
        :return:
        """
        if pathname is None:
            return None
        pathname = util.norm_file_path(pathname)
        res = self.connector().sendcmd("MMD5 {}".format(pathname))
        return res.strip().split(" ")[-1]

    def modify_time(self, pathname):
        """
        获取Ftp上文件的最后修改时间
        :param pathname:
        :return:
        """
        if pathname is None:
            return None
        pathname = util.norm_file_path(pathname)
        res = self.connector().sendcmd("MDTM {}".format(pathname))
        return res.strip().split(" ")[-1]

    def close(self):
        """
        关闭ftp连接
        :return:
        """
        try:
            if self.__ftp is not None:
                self.__ftp.quit()
        except Exception as e:
            logger.exception("关闭连接发生错误 {}".format(str(e)))
        finally:
            self.__ftp = None
            logger.debug("Ftp Closed")

    def __del__(self):
        self.close()


if __name__ == "__main__":
    ftp = FtpClient(host="ftp://*************", port=210, username="pds_filepool", pwd="pds2015")
    ftp.upload("./conf/logger.conf", "/20200303/Test/logger.1.conf")
    print(ftp.exists("/20200303/Test/logger.1.conf"))
    ftp.close()
