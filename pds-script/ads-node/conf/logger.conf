[loggers]
keys=root

[handlers]
keys=console,file,upload_file

[formatters]
keys=basic

[logger_root]
level=NOTSET
handlers=console,file,upload_file

[formatter_basic]
format=%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(threadName)s : %(lineno)d - %(message)s
datefmt=%Y-%m-%d %H:%M:%S


[handler_console]
class=StreamHandler
formatter=basic
level=DEBUG
args=(sys.stdout,)

[handler_file]
class=handlers.RotatingFileHandler
formatter=basic
level=WARNING
args=("./log/ads.log", "a", 10*1024*1024, 10, "utf8")

[handler_upload_file]
class=handlers.RotatingFileHandler
formatter=basic
level=INFO
args=("./log/ads_upload.log", "a", 2*1024*1024, 3, "utf8")