# coding=utf8
__author__ = 'sw'

import logging
import os
import random
import time
import traceback
from datetime import datetime

import api
import db
import script
import util

logger = logging.getLogger()


class TaskExecutor:
    """
    任务执行器基类，包含任务执行的公共逻辑
    """

    def __init__(self, site_id: int, db_file: db.DbFile, api_invoker: api.ApiInvoker):
        self._site_id = site_id
        self._db = db_file
        self._api_invoker = api_invoker
        self._script_context = script.ScriptContext(site_id=self._site_id, api_invoker=self._api_invoker)

    def execute_task_core(self, task) -> bool:
        """
        任务执行的核心逻辑
        :param task: 任务信息字典
        :return: 执行结果，True表示成功，False表示失败
        """
        task_id = task.get('taskId', 'Unknown')
        doc_id = task.get('docId', 'Unknown')
        pmid = task.get('pmid', '')

        if pmid:
            msg = f"站点 {self._site_id} 获取到 TaskID: {task_id} doc_id: {doc_id} PMID: {pmid} 开始执行任务"
        else:
            msg = f"站点 {self._site_id} 获取到 TaskID: {task_id} doc_id: {doc_id} 开始执行任务"
        logger.info(msg)
        self._api_invoker.send_task_message(task_id, msg)

        # 创建结果文件夹路径
        result_dir_path = self.create_result_dir(task)
        task["resultDirPath"] = result_dir_path

        # 获取脚本模块,返回该站点可以执行本任务的所有脚本，按顺序执行，直到下载到结果或遍历完成
        try:
            script_modules = self._script_context.iter_script_modules(task)
            for name, md5, module, script_id_str in script_modules:
                # logger.warning(f"开始执行脚本 {name}，resultDirPath：{result_dir_path}")
                # 1. 执行脚本
                self.run_script(name=name, module=module, task=task)

                # 2. 验证执行结果,没有结果继续执行下一个脚本
                if not util.dict_has_value(task, "resultDirPath"):
                    if pmid:
                        logger.error(
                            f"脚本执行失败 {name}，没有获取到结果 TaskId: {task_id} doc_id: {doc_id} PMID: {pmid}")
                    else:
                        logger.error(f"脚本执行失败 {name}，没有获取到结果 TaskId: {task_id} doc_id: {doc_id}")
                    continue

                # 3. 验证本地文件是否存在
                has_result = util.check_result_file(result_dir_path=task["resultDirPath"])
                if has_result:
                    # 下载成功，记录成功的脚本ID
                    task["successScriptId"] = script_id_str
                    if pmid:
                        logger.info(f"任务下载成功，等待上传 {name} TaskId: {task_id} doc_id: {doc_id} PMID: {pmid} successScriptId: {script_id_str}")
                    else:
                        logger.info(f"任务下载成功，等待上传 {name} TaskId: {task_id} doc_id: {doc_id} successScriptId: {script_id_str}")
                    return True  # 成功
                else:
                    if pmid:
                        logger.error(
                            f"脚本执行失败 {name}，没有获取到结果 TaskId: {task_id} doc_id: {doc_id} PMID: {pmid}")
                    else:
                        logger.error(f"脚本执行失败 {name}，没有获取到结果 TaskId: {task_id} doc_id: {doc_id}")
                    util.remove_result_dir(task["resultDirPath"])

            return False  # 所有脚本都失败

        finally:
            # 无论成功或失败，都要通知服务器下载结果
            self._db.insert_item(task_info=task)

    def run_script(self, name, module, task: dict):
        """
        执行脚本文件
        :param name: 脚本名称
        :param module: 脚本模块
        :param task: 任务信息
        """
        if module is None:
            return

        _script = None
        try:
            # 结果目录不存在则创建
            if not os.path.exists(task["resultDirPath"]):
                os.makedirs(task["resultDirPath"])

            task_id = task.get('taskId', 'Unknown')
            doc_id = task.get('docId', 'Unknown')
            # pmid = task.get('pmid', '')
            script_id = task.get('scriptIdStr', '')

            msg = f"站点 {self._site_id} 启动脚本下载 {name}， TaskId: {task_id}， doc_id: {doc_id}， script_id: {script_id}"
            logger.info(msg)
            self._api_invoker.send_task_message(task_id, msg)

            _script = module.Script(site_id=self._site_id, task=task, api_invoker=self._api_invoker)

            # 判断脚本是否可以下载本任务
            if hasattr(_script, "can_download"):
                _can_download = _script.can_download()
                _can_download = _can_download if _can_download is not None else True
                if not _can_download:
                    raise ValueError("脚本不支持本任务的下载")

            # 启动脚本执行
            _script.run()

        except BaseException as e:
            logger.exception(e)
            # 执行失败继续下一个脚本，这里暂时只向服务器报告简易信息
            task_id = task.get('taskId', 'Unknown')
            doc_id = task.get('docId', 'Unknown')
            pmid = task.get('pmid', '')

            if pmid:
                msg = f"站点 {self._site_id} 执行脚本失败 {name}，TaskId: {task_id} doc_id: {doc_id} PMID: {pmid} \n\tException: {e}"
            else:
                msg = f"站点 {self._site_id} 执行脚本失败 {name}，TaskId: {task_id} doc_id: {doc_id} \n\tException: {e}"
            self._api_invoker.send_task_message(task_id, msg)
        finally:
            # 清理脚本资源
            if _script is not None:
                # 如果脚本指定了 close 方法，则执行close回收资源
                if hasattr(_script, "close"):
                    _script.close()
                del _script

    @staticmethod
    def create_result_dir(task):
        """
        创建下载结果存储目录
        :param task: 任务信息
        :return: 结果目录路径
        """
        if task is None:
            return None

        task_id = task.get('taskId', 'Unknown')
        doc_id = task.get('docId', 'Unknown')

        result_path = "data{}{}{}".format(
            os.sep + datetime.now().strftime("%Y%m%d"),
            os.sep + task_id,
            os.sep + str(doc_id))
        result_dir = os.path.join(util.program_root_dir(), result_path)

        # 执行前先清理掉任务结果目录中的文件
        util.remove_result_dir(result_dir)

        if not os.path.exists(result_dir):
            os.makedirs(result_dir)
        return result_dir

    def calculate_sleep_time(self, task, is_multi_thread=False):
        """
        计算任务结束后的休眠时间
        :param task: 任务信息
        :param is_multi_thread: 是否为多线程环境
        :return: 休眠时间（秒）
        """
        # return random.randint(5, 10)
        # 默认休眠时间
        sleep_time = 20

        if task is None or not isinstance(task, dict):
            logger.error("【结束】任务类型错误，使用默认休眠时间")
            return sleep_time

        # 从任务中获取休眠时间配置
        if util.dict_has_value(task, "obtainTaskInterval") and isinstance(task["obtainTaskInterval"], int):
            sleep_time = task["obtainTaskInterval"]

        # 设置上限，不超过 8 个小时
        if sleep_time > 60 * 60 * 8:
            sleep_time = 60 * 60 * 8

        sleep_time = max(sleep_time, 3)
        sleep_time = random.randint(sleep_time, sleep_time * 3)
        return sleep_time

    def handle_task_error(self, task_info, exception, context=""):
        """
        处理任务执行错误
        :param task_info: 任务信息
        :param exception: 异常对象
        :param context: 错误上下文
        """
        task_id = task_info.get("taskId", "Unknown") if task_info else "Unknown"
        doc_id = task_info.get("docId", "Unknown") if task_info else "Unknown"
        pmid = task_info.get("pmid", "") if task_info else ""

        if pmid:
            error_msg = f"站点 {self._site_id} {context}执行任务出错, TaskID：{task_id} doc_id: {doc_id} PMID: {pmid}\nException: {traceback.format_exc()}"
        else:
            error_msg = f"站点 {self._site_id} {context}执行任务出错, TaskID：{task_id} doc_id: {doc_id}\nException: {traceback.format_exc()}"

        logger.exception(exception)

        try:
            self._api_invoker.send_task_message(task_id, error_msg)
        except Exception as e:
            logger.warning(f"发送错误消息失败: {e}")

    @staticmethod
    def safe_sleep(t=1):
        """
        安全的休眠方法
        :param t: 休眠时间（秒）
        """
        try:
            time.sleep(t)
        except BaseException as e:
            logger.info(f"休眠被中断: {e}")

    def validate_task_info(self, task_info):
        """
        验证任务信息的完整性
        :param task_info: 任务信息
        :return: 验证结果和错误信息
        """
        if not task_info:
            return False, "任务信息为空"

        if not util.dict_has_value(task_info, "taskId"):
            return False, "任务taskId为空"

        if not util.dict_has_value(task_info, "docId"):
            return False, "任务docId为空"

        return True, ""
