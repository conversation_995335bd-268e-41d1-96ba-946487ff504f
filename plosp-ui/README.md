# PLOSP - 科学文献私人订制图书馆

PLOSP是一个科学文献私人订制图书馆系统，为科研工作者提供便捷的学术资源检索和个性化服务。

## 技术栈

- Vue 3 (使用Composition API和`<script setup>`语法)
- Vite作为构建工具
- Element Plus作为UI组件库
- Vue Router处理路由
- Pinia状态管理
- Axios处理API请求
- SCSS用于样式编写

## 项目结构

```
plosp-app/
├── public/              # 静态资源
├── src/
│   ├── assets/          # 项目资源文件
│   │   ├── icons/       # 图标
│   │   └── images/      # 图片
│   ├── components/      # 组件
│   │   └── layout/      # 布局组件
│   ├── router/          # 路由配置
│   ├── store/           # Pinia状态管理
│   ├── styles/          # 全局样式
│   ├── views/           # 页面视图
│   ├── App.vue          # 根组件
│   └── main.js          # 入口文件
├── index.html           # HTML模板
├── package.json         # 项目依赖
├── vite.config.js       # Vite配置
└── README.md            # 项目说明
```

## 功能特点

- 文献检索：支持关键词、标题、作者、DOI、PMID等多种检索方式
- 高级检索：提供精确的检索条件组合
- 语义检索：基于自然语言处理的智能检索
- 文献统计：分析研究热点和发展趋势
- 数据获取：下载文献原文、元数据和参考文献信息
- 特色文献集：提供各领域精选文献集合

## 开发指南

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 响应式设计

项目采用响应式设计，适配桌面端和移动端设备，断点设置如下：

- 小屏幕：576px
- 中屏幕：768px
- 大屏幕：992px
- 超大屏幕：1200px
- 特大屏幕：1400px 