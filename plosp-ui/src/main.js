import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'
import { useAuthStore } from '@/stores/auth'
import { permissionDirective } from '@/utils/permission'
import { useDictStore } from '@/stores/dict'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

// 导入全局样式
import '@/assets/styles/variables.scss'
import '@/assets/styles/global.scss'
import '@/assets/styles/index.scss'

import { download } from '@/utils/request'

const app = createApp(App)
const pinia = createPinia()

app.config.globalProperties.download = download

app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn
})

// 注册权限指令
app.directive('permission', permissionDirective)

// 初始化认证状态和字典数据
const authStore = useAuthStore()
const dictStore = useDictStore()

// 异步初始化，不阻塞应用启动
Promise.all([
  authStore.initAuth().catch(error => {
    // 静默处理认证初始化错误，不影响应用启动
    console.warn('认证初始化失败:', error)
  }),
  dictStore.initCommonDicts().catch(error => {
    console.warn('字典初始化失败:', error)
  })
]).then(() => {
  console.log('应用初始化完成')
})

app.mount('#app')
