<template>
  <div class="app">
    <!-- 全局加载动画 -->
    <div v-if="globalLoading" class="global-loading-overlay">
      <div class="loading-content">
        <div class="elegant-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
      </div>
    </div>

    <AppHeader />
    <main>
      <router-view />
    </main>
    <AppFooter />
  </div>
</template>

<script setup>
  import AppHeader from '@/components/layout/AppHeader.vue'
  import AppFooter from '@/components/layout/AppFooter.vue'
  import { computed } from 'vue'
  import { useLoadingStore } from '@/stores/loading'
  const loadingStore = useLoadingStore()
  const globalLoading = computed(() => loadingStore.isLoading)
</script>

<style lang="scss">
.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;

  main {
    flex: 1;
  }
}
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-content {
    .elegant-spinner {
      position: relative;
      width: 60px;
      height: 60px;

      .spinner-ring {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 2px solid transparent;
        border-radius: 50%;
        animation: elegant-spin 2s linear infinite;

        &:nth-child(1) {
          border-top-color: #409eff;
          animation-delay: 0s;
        }

        &:nth-child(2) {
          border-right-color: #67c23a;
          animation-delay: 0.3s;
          width: 80%;
          height: 80%;
          top: 10%;
          left: 10%;
        }

        &:nth-child(3) {
          border-bottom-color: rgba(64, 158, 255, 0.3);
          animation-delay: 0.6s;
          width: 60%;
          height: 60%;
          top: 20%;
          left: 20%;
        }
      }
    }
  }
}

@keyframes elegant-spin {
  0% {
    transform: rotate(0deg);
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    transform: rotate(360deg);
    opacity: 1;
  }
}
</style>
