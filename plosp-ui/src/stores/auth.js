import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { login as loginApi, logout as logoutApi, getUserProfile } from '@/api/auth'
import { STORAGE_KEYS } from '@/utils/constants'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(localStorage.getItem(STORAGE_KEYS.TOKEN) || '')
  const userInfo = ref(JSON.parse(localStorage.getItem(STORAGE_KEYS.USER_INFO) || 'null'))
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userName = computed(() => userInfo.value?.userName || '')
  const userEmail = computed(() => userInfo.value?.email || '')

  // 设置 token
  const setToken = newToken => {
    token.value = newToken
    if (newToken) {
      localStorage.setItem(STORAGE_KEYS.TOKEN, newToken)
    } else {
      localStorage.removeItem(STORAGE_KEYS.TOKEN)
    }
  }

  // 设置用户信息
  const setUserInfo = info => {
    userInfo.value = info
    if (info) {
      localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(info))
    } else {
      localStorage.removeItem(STORAGE_KEYS.USER_INFO)
    }
  }

  // 登录
  const login = async loginForm => {
    try {
      isLoading.value = true
      const response = await loginApi(loginForm)
      
      if (response.code === 200) {
        // 保存 token
        setToken(response.token)
        
        // 获取用户信息
        await fetchUserInfo()
        
        ElMessage.success('登录成功')
        return { success: true }
      } else {
        ElMessage.error(response.msg || '登录失败')
        return { success: false, message: response.msg }
      }
    } catch (error) {
      // 只在网络错误时显示网络连接提示
      if (error.message && error.message.includes('Network Error')) {
        ElMessage.error('登录失败，请检查网络连接')
        return { success: false, message: '登录失败，请检查网络连接' }
      } else {
      // 其他错误显示具体错误信息或通用提示
        const errorMessage = error.response?.data?.msg || error.message || '登录失败'
        return { success: false, message: errorMessage }
      }
    } finally {
      isLoading.value = false
    }
  }

  // 获取用户信息
  const fetchUserInfo = async() => {
    try {
      const response = await getUserProfile()
      if (response.code === 200) {
        setUserInfo(response.data)
        return response.data
      } else {
        console.error('获取用户信息失败:', response.msg)
        return null
      }
    } catch (error) {
      console.error('获取用户信息错误:', error)
      return null
    }
  }

  // 退出登录
  const logout = async() => {
    try {
      // 调用后端退出接口
      await logoutApi()
    } catch (error) {
      console.error('退出登录错误:', error)
    } finally {
      // 无论后端调用是否成功，都清除本地数据
      setToken('')
      setUserInfo(null)
      ElMessage.success('已退出登录')
    }
  }

  // 检查登录状态
  const checkAuth = async() => {
    if (!token.value) {
      return false
    }

    try {
      // 尝试获取用户信息来验证 token 是否有效
      const info = await fetchUserInfo()
      return !!info
    } catch (error) {
      console.warn('检查认证状态失败:', error)
      // 只有在明确的401错误时才清除token，其他错误保持登录状态
      if (error.response?.status === 401) {
        setToken('')
        setUserInfo(null)
        return false
      }
      // 网络错误或其他错误时，保持当前登录状态
      return true
    }
  }

  // 初始化认证状态
  const initAuth = async() => {
    if (token.value) {
      // 如果有token但没有用户信息，尝试获取
      if (!userInfo.value) {
        try {
          await fetchUserInfo()
        } catch (error) {
          console.warn('初始化时获取用户信息失败:', error)
          // 如果是401错误，说明token无效，清除它
          if (error.response?.status === 401) {
            console.log('Token已过期，清除本地存储')
            logout(false) // false表示不显示退出消息
          }
        }
      }
    }
  }

  return {
    // 状态
    token,
    userInfo,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    userName,
    userEmail,
    
    // 方法
    setToken,
    setUserInfo,
    login,
    logout,
    fetchUserInfo,
    checkAuth,
    initAuth
  }
}, {
  persist: {
    key: STORAGE_KEYS.AUTH_STORE,
    storage: localStorage,
    paths: ['token', 'userInfo']
  }
})
