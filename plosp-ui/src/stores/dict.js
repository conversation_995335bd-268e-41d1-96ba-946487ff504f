import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getDictData, getBatchDictData, getCountries, getOrganizations } from '@/api/dict'

export const useDictStore = defineStore('dict', () => {
  // 字典数据缓存
  const dictCache = ref(new Map())

  // 常用字典数据
  const countries = ref([])
  const organizations = ref([])
  const userTypes = ref([])
  const positions = ref([])

  // 加载状态
  const loading = ref(false)

  /**
   * 获取字典数据（带缓存）
   * @param {string} dictType - 字典类型
   */
  const getDictOptions = async dictType => {
    // 检查缓存
    if (dictCache.value.has(dictType)) {
      return dictCache.value.get(dictType)
    }

    try {
      const response = await getDictData(dictType)
      if (response.code === 200) {
        const options = response.data.map(item => ({
          label: item.dictLabel,
          value: item.dictValue,
          raw: item
        }))

        // 缓存数据
        dictCache.value.set(dictType, options)
        return options
      }
    } catch (error) {
      console.error(`获取字典 ${dictType} 失败:`, error)
    }

    return []
  }

  /**
   * 批量获取字典数据
   * @param {Array} dictTypes - 字典类型数组
   */
  const getBatchDictOptions = async dictTypes => {
    const uncachedTypes = dictTypes.filter(type => !dictCache.value.has(type))

    if (uncachedTypes.length === 0) {
      // 全部都有缓存，直接返回
      const result = {}
      dictTypes.forEach(type => {
        result[type] = dictCache.value.get(type)
      })
      return result
    }

    try {
      const response = await getBatchDictData(uncachedTypes)
      if (response.code === 200) {
        const result = {}

        // 处理新获取的数据
        Object.keys(response.data).forEach(dictType => {
          const options = response.data[dictType].map(item => ({
            label: item.dictLabel,
            value: item.dictValue,
            raw: item
          }))

          // 缓存数据
          dictCache.value.set(dictType, options)
          result[dictType] = options
        })

        // 添加已缓存的数据
        dictTypes.forEach(type => {
          if (dictCache.value.has(type)) {
            result[type] = dictCache.value.get(type)
          }
        })

        return result
      }
    } catch (error) {
      console.error('批量获取字典失败:', error)
    }

    return {}
  }

  /**
   * 加载国家数据
   */
  const loadCountries = async() => {
    if (countries.value.length > 0) return countries.value

    try {
      const response = await getCountries()
      if (response.code === 200) {
        countries.value = response.data.map(item => {
          // dictLabel 是中文，dictValue 是英文
          const displayLabel = `${item.dictLabel}(${item.dictValue})`

          return {
            label: item.dictLabel, // 中文名
            value: item.dictValue, // 英文名作为值
            displayLabel, // 显示格式：中文(英文)
            raw: item
          }
        })

        // 同时缓存到通用缓存中
        dictCache.value.set('node_country', countries.value)
      }
    } catch (error) {
      console.error('获取国家字典失败:', error)
    }

    return countries.value
  }

  /**
   * 加载机构数据
   */
  const loadOrganizations = async() => {
    if (organizations.value.length > 0) return organizations.value

    try {
      const response = await getOrganizations()
      if (response.code === 200) {
        organizations.value = response.data.map(item => ({
          label: item.dictLabel.replace(/\s*\[.*?\]\s*/g, '').trim(), // 去除中括号及其内容
          value: item.dictValue.replace(/\s*\[.*?\]\s*/g, '').trim(),
          raw: item
        }))

        // 同时缓存到通用缓存中
        dictCache.value.set('node_organization', organizations.value)
      }
    } catch (error) {
      console.error('获取机构字典失败:', error)
    }

    return organizations.value
  }

  /**
   * 初始化常用字典数据
   */
  const initCommonDicts = async() => {
    if (loading.value) return

    loading.value = true

    try {
      // 并行加载常用字典
      await Promise.all([
        loadCountries(),
        loadOrganizations(),
        getDictOptions('sys_user_type').then(data => { userTypes.value = data }),
        getDictOptions('sys_position').then(data => { positions.value = data })
      ])
    } catch (error) {
      console.error('初始化字典数据失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 清除字典缓存
   * @param {string} dictType - 字典类型，不传则清除所有
   */
  const clearCache = dictType => {
    if (dictType) {
      dictCache.value.delete(dictType)

      // 清除对应的常用字典
      switch (dictType) {
        case 'node_country':
          countries.value = []
          break
        case 'node_organization':
          organizations.value = []
          break
        case 'sys_user_type':
          userTypes.value = []
          break
        case 'sys_position':
          positions.value = []
          break
      }
    } else {
      dictCache.value.clear()
      countries.value = []
      organizations.value = []
      userTypes.value = []
      positions.value = []
    }
  }

  return {
    // 状态
    countries,
    organizations,
    userTypes,
    positions,
    loading,

    // 方法
    getDictOptions,
    getBatchDictOptions,
    loadCountries,
    loadOrganizations,
    initCommonDicts,
    clearCache
  }
}, {
  persist: {
    key: 'dict-store',
    storage: localStorage,
    paths: ['countries', 'organizations', 'userTypes', 'positions']
  }
})


