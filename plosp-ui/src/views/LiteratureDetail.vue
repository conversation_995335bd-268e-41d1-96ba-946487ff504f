<template>
  <div class="literature-detail-view">
    <div class="detail-layout">
      <main class="detail-main">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="10" animated />
        </div>
        <div v-else class="container main-content">
          <div id="basic-info">
            <!-- 来源徽章和期刊信息 -->
            <div class="header-info">
              <div class="source-badges">
                <template v-for="(source, index) in articleDetail.source" :key="index">
                  <span class="source-badge" :style="getSourceStyle(source.trim())">{{ source.trim() }}</span>
                </template>
              </div>
              <div class="journal-info">
                <span class="journal-name">{{ articleDetail.journalName }}.</span>
                <span class="publish-date">{{ formatPublishDate() }}</span>
              </div>
            </div>

            <!-- 文章标题 -->
            <h1 class="detail-title" v-html="articleDetail.title"></h1>

            <!-- 元数据信息 -->
            <div class="metadata-row">
              <div v-if="articleDetail.pmid" class="metadata-item">
                <span class="metadata-label">PMID:</span>
                <a
                  :href="`https://pubmed.ncbi.nlm.nih.gov/${articleDetail.pmid}/`"
                  target="_blank"
                  class="metadata-link"
                >
                  {{ articleDetail.pmid }}
                </a>
              </div>
              <div v-if="articleDetail.pmcId" class="metadata-item">
                <span class="metadata-label">PMCID:</span>
                <a
                  :href="`https://www.ncbi.nlm.nih.gov/pmc/articles/PMC${articleDetail.pmcId}/`"
                  target="_blank"
                  class="metadata-link"
                >
                  PMC{{ articleDetail.pmcId }}
                </a>
              </div>
              <div v-if="articleDetail.doi" class="metadata-item">
                <span class="metadata-label">DOI:</span>
                <a :href="`https://doi.org/${articleDetail.doi}`" target="_blank" class="metadata-link">
                  {{ articleDetail.doi }}
                </a>
              </div>
              <div class="metadata-item">
                <img src="@/assets/images/download-stat.svg" alt="" class="stat-icon" />
                <span class="metadata-label">下载量:</span>
                <span class="metadata-value">{{ articleDetail.download || 0 }}</span>
              </div>
              <div class="metadata-item">
                <img src="@/assets/images/browse-stat.svg" alt="" class="stat-icon" />
                <span class="metadata-label">浏览量:</span>
                <span class="metadata-value">{{ articleDetail.hitNum || 0 }}</span>
              </div>
            </div>

            <!-- 作者列表 -->
            <div v-if="articleDetail.authors && articleDetail.authors.length > 0" class="detail-authors-row">
              <div>
                <img src="@/assets/images/author.svg" alt="" class="author-icon" />
                <div ref="authorsRef" class="authors" :class="{ expanded: authorExpanded }">
                  <template v-for="(author, index) in articleDetail.authors" :key="index">
                    <span class="author">
                      {{ author.name }}
                      <sup
                        v-if="author.index"
                        class="author-index clickable"
                        @click="scrollToAffiliation(author.index)"
                      >
                        {{ author.index }}
                      </sup>
                    </span>
                    <el-divider v-if="index < articleDetail.authors.length - 1" direction="vertical" />
                  </template>
                </div>
              </div>
              <el-button
                v-if="showAuthorToggle"
                type="text"
                class="toggle-btn"
                @click="authorExpanded = !authorExpanded"
              >
                <span>{{ authorExpanded ? '收起' : '展开' }}</span>
                <el-icon>
                  <ArrowDown v-if="!authorExpanded" />
                  <ArrowUp v-else />
                </el-icon>
              </el-button>
            </div>
            <!-- 作者机构 -->
            <div
              v-if="articleDetail.affiliation && articleDetail.affiliation.length > 0"
              class="detail-affiliations-row"
            >
              <div>
                <img src="@/assets/images/affiliation.svg" alt="" class="option-icon" />
                <div ref="affiliationsRef" class="affiliations" :class="{ expanded: affiliationExpanded }">
                  <span
                    v-for="(affiliation, index) in articleDetail.affiliation"
                    :id="`affiliation-${index + 1}`"
                    :key="index"
                    class="affiliation"
                  >
                    {{ index + 1 }}. {{ affiliation }}
                  </span>
                </div>
              </div>
              <el-button
                v-if="showAffiliationToggle"
                type="text"
                class="toggle-btn"
                @click="affiliationExpanded = !affiliationExpanded"
              >
                <span>{{ affiliationExpanded ? '收起' : '展开' }}</span>
                <el-icon>
                  <ArrowDown v-if="!affiliationExpanded" />
                  <ArrowUp v-else />
                </el-icon>
              </el-button>
            </div>
            <div class="row-btn">
              <el-popover placement="bottom-start" :width="160" trigger="click">
                <template #reference>
                  <el-button :icon="MessageBox">文献传递</el-button>
                </template>
                <div class="delivery-options">
                  <div class="delivery-option">
                    <span @click="handleDocumentTransmit">获取全文</span>
                  </div>
                  <div class="delivery-option">
                    <span>获取元数据文件</span>
                  </div>
                  <div class="delivery-option">
                    <span>获取图文</span>
                  </div>
                </div>
              </el-popover>
              <el-button :icon="isCollected ? StarFilled : Star" @click="handleBatchFavorite">
                {{ isCollected ? '已收藏' : '收藏' }}
              </el-button>
              <el-button :icon="Document" @click="handleCitation">引用</el-button>
              <el-button :icon="EditPen" @click="handleCorrection">纠错</el-button>
              <el-button :icon="Upload" @click="handleUpload">上传文献</el-button>
            </div>
            <!-- 摘要区块 -->
            <div v-if="articleDetail.articleAbstract" id="abstract" class="detail-section">
              <div class="section-title">摘要</div>
              <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleTabChange">
                <el-tab-pane label="原文" name="first">
                  <div class="section-content abstract" v-html="articleDetail.articleAbstract"></div>
                </el-tab-pane>
                <el-tab-pane label="解读" name="second">
                  <div class="section-content abstract">
                    <!-- 加载状态 -->
                    <div v-if="interpretationLoading" class="interpretation-loading">
                      <el-skeleton :rows="3" animated />
                      <div class="loading-text">正在生成解读内容...</div>
                    </div>
                    <!-- 错误状态 -->
                    <div v-else-if="interpretationError" class="interpretation-error">
                      <el-icon class="error-icon">
                        <Warning />
                      </el-icon>
                      <div class="error-message">{{ interpretationError }}</div>
                      <div class="error-actions">
                        <el-button
                          v-if="!authStore.isLoggedIn"
                          type="primary"
                          size="small"
                          @click="goToLogin"
                        >
                          去登录
                        </el-button>
                        <el-button
                          v-else
                          type="primary"
                          size="small"
                          @click="retryInterpretation"
                        >重新获取</el-button>
                      </div>
                    </div>
                    <!-- 解读内容 -->
                    <div
                      v-else-if="interpretationContent"
                      class="interpretation-content"
                      v-html="interpretationContent"
                    ></div>
                    <!-- 空状态 -->
                    <div v-else class="interpretation-empty">
                      <el-icon class="empty-icon">
                        <Document />
                      </el-icon>
                      <div class="empty-message">暂无解读内容</div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
            <!-- 关键词区块 -->
            <div v-if="articleDetail.keywords != null" id="keywords" class="detail-section">
              <div class="section-title">关键词</div>
              <div class="section-content keywords">
                <el-popover
                  v-for="keyword in articleDetail.keywords"
                  :key="keyword"
                  placement="bottom-start"
                  :width="160"
                  trigger="click"
                >
                  <template #reference>
                    <span class="keyword">{{ keyword }}</span>
                  </template>
                  <div class="keyword-options">
                    <div class="keyword-option">
                      <span>Search in PLOSP</span>
                    </div>
                  </div>
                </el-popover>
              </div>
            </div>
            <!-- 文献分类区块 -->
            <div id="category" class="detail-section">
              <div class="section-title">文献分类</div>
              <div class="section-content keywords">
                <el-popover
                  v-for="pubType in articleDetail.pubTypes"
                  :key="pubType"
                  placement="bottom-start"
                  :width="160"
                  trigger="click"
                >
                  <template #reference>
                    <span class="keyword">{{ pubType }}</span>
                  </template>
                  <div class="keyword-options">
                    <div class="keyword-option">
                      <span>Search in PLOSP</span>
                    </div>
                    <div class="keyword-option" @click="searchInMesh(pubType)">
                      <span>Search in MeSH</span>
                    </div>
                  </div>
                </el-popover>
              </div>
            </div>
            <!-- 图表区块 -->
            <!-- <div id="charts" class="detail-section">
              <div class="section-title">图表</div>
              <swiper
                :modules="modules"
                :slides-per-view="isMobile ? 1 : 2"
                :space-between="isMobile ? 15 : 25"
                :pagination="{ clickable: true }"
                :scrollbar="{ draggable: true }"
                :loop="true"
                :autoplay="{ delay: 4000 }"
                class="swiper"
                @swiper="onSwiper"
                @slide-change="onSlideChange"
              >
                <swiper-slide key="1">
                  <img src="@/assets/images/chart1.png" alt="" class="option-icon" />
                </swiper-slide>
                <swiper-slide key="2">
                  <img src="@/assets/images/chart2.png" alt="" class="option-icon" />
                </swiper-slide>
                <swiper-slide key="3">
                  <img src="@/assets/images/chart2.png" alt="" class="option-icon" />
                </swiper-slide>
              </swiper>
            </div> -->
            <!-- 实体区块 -->
            <!-- <div id="entity" class="entity-section detail-section">
              <div class="entity-title">实体</div>
              <div class="entity-list">
                <div v-for="(entity, idx) in entityTagList" :key="entity.key" class="entity-item">
                  <div class="entity-header">
                    <div class="entity-content-wrapper">
                      <div class="entity-name-wrapper">
                        <span class="entity-name">{{ entity.title }}</span>
                      </div>
                      <div class="entity-tags-wrapper">
                        <div class="section-content keywords" :class="{ expanded: entity.expanded }">
                          <span v-for="(tag, tIdx) in entity.tags" :key="tIdx" class="keyword">{{ tag }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="entity-toggle-wrapper">
                      <el-button type="text" class="toggle-btn" @click="toggleTagExpand(idx)">
                        <span>{{ entity.expanded ? '收起' : '展开' }}</span>
                        <el-icon>
                          <ArrowDown v-if="!entity.expanded" />
                          <ArrowUp v-else />
                        </el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div> -->
            <!-- 基金资助区块 -->
            <div v-if="articleDetail.grants && articleDetail.grants.length > 0" id="funding" class="detail-section">
              <div class="section-title">基金资助</div>
              <div class="section-content">
                <ul class="funding-support">
                  <li v-for="grant in articleDetail.grants" :key="grant.grantId">
                    <span class="grant-link" @click="searchGrant(grant)">
                      {{ formatGrantInfo(grant) }}
                    </span>
                  </li>
                </ul>
              </div>
            </div>
            <!-- 相关数据区块 -->
            <div
              v-if="articleDetail.databanks && articleDetail.databanks.length > 0"
              id="related-data"
              class="detail-section"
            >
              <div class="section-title">相关数据</div>
              <div class="section-content">
                <ul class="funding-support">
                  <li v-for="databank in articleDetail.databanks" :key="databank">
                    <span
                      :class="hasValidUrl(databank.name) ? 'data-link' : 'data-text'"
                      @click="hasValidUrl(databank.name) ? openDatabank(databank) : null"
                    >
                      {{ databank.name }}/{{ databank.value }}
                    </span>
                  </li>
                </ul>
              </div>
            </div>
            <!-- MeSH主题词-->
            <div v-if="articleDetail.meshTerms && articleDetail.meshTerms.length > 0" id="mesh" class="detail-section">
              <div class="section-title">MeSH主题词</div>
              <div class="section-content">
                <ul class="funding-support">
                  <li v-for="meshTerm in articleDetail.meshTerms" :key="meshTerm.descriptorName">
                    <el-popover placement="bottom-start" :width="160" trigger="click">
                      <template #reference>
                        <span class="mesh-term-link">
                          <template v-if="meshTerm.qualifierName != null">
                            {{ meshTerm.descriptorName }} / {{ meshTerm.qualifierName }}
                          </template>
                          <template v-else>
                            {{ meshTerm.descriptorName }}
                          </template>
                        </span>
                      </template>
                      <div class="keyword-options">
                        <div class="keyword-option">
                          <span>Search in PLOSP</span>
                        </div>
                        <div class="keyword-option" @click="searchInMeshTerm(meshTerm)">
                          <span>Search in MeSH</span>
                        </div>
                      </div>
                    </el-popover>
                  </li>
                </ul>
              </div>
            </div>
            <!-- 参考文献 -->
            <div v-if="references && references.length > 0" id="references" class="detail-section">
              <div class="section-title">参考文献</div>
              <!-- 加载状态 -->
              <div v-if="referencesLoading" class="references-loading">
                <el-skeleton :rows="3" animated />
                <div class="loading-text">正在加载参考文献...</div>
              </div>
              <!-- 加载完成状态 -->
              <div v-else-if="references.length > 0" class="section-content references-pubmed">
                <ol class="references-list">
                  <li v-for="(reference, index) in references" :key="reference.id || index" class="reference-item">
                    <div class="reference-content">
                      <span class="reference-text">
                        {{ formatCitation(reference) }}
                      </span>
                      <span class="reference-links">
                        <template
                          v-if="reference.id || reference.hasPubmedLink || reference.hasPmcLink || reference.hasDoiLink"
                        >
                          <span class="separator">-</span>

                          <!-- 构建链接数组，然后用分隔符连接 -->
                          <template v-for="(link, linkIndex) in getAvailableLinks(reference)" :key="link.type">
                            <span v-if="linkIndex > 0" class="separator">-</span>
                            <el-button type="text" :class="`ref-link ${link.type}-link`" @click="link.action">
                              {{ link.label }}
                            </el-button>
                          </template>
                        </template>
                      </span>
                    </div>
                  </li>
                </ol>
                <div v-if="referencesHasMore && references.length < referencesTotalCount" class="show-more-references">
                  <el-button type="text" :loading="referencesLoading" @click="loadAllReferences">
                    Show all {{ referencesTotalCount }} references
                  </el-button>
                </div>
              </div>
            </div>
            <div id="similar-articles" class="detail-section">
              <div class="section-title">相似文章</div>
              <div class="section-content references">
                <div class="reference">
                  <a href="">1. Marine ecosystem responses to Cenozoic global change</a>
                  <div class="date">Nature, 2023</div>
                </div>
                <div class="reference">
                  <a href="">2. Global patterns in marine biodiversity</a>
                  <div class="date">Science, 2023</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 右侧统计/导航/指标卡片 -->
          <aside class="detail-aside">
            <div class="aside-card">
              <div class="metric-card">
                <!-- 影响因子 -->
                <div v-if="articleDetail.impactFactor" class="metric-row">
                  <div class="metric-label">
                    <div class="metric-icon">IF</div>
                    影响因子
                  </div>
                  <div class="metric-circle">{{ articleDetail.impactFactor }}</div>
                </div>
                <!--                <div class="metric-row">-->
                <!--                  <div class="metric-label">-->
                <!--                    <div class="metric-icon">CIT</div>-->
                <!--                    引用量</div>-->
                <!--                  <div class="metric-circle">128</div>-->
                <!--                </div>-->
                <!-- JCR分区 -->
                <div v-if="articleDetail.jcrQuartile" class="metric-row">
                  <div class="metric-label">
                    <div class="metric-icon">JCR</div>
                    JCR分区
                  </div>
                  <div class="metric-circle">{{ articleDetail.jcrQuartile }}</div>
                </div>

                <!-- 中科院分区 -->
                <div v-if="articleDetail.zkySections" class="metric-row">
                  <div class="metric-label">
                    <div class="metric-icon">CAS</div>
                    中科院分区
                  </div>
                  <div class="metric-circle">{{ articleDetail.zkySections }}区</div>
                </div>
              </div>

              <div class="nav-card">
                <div class="nav-title">导航目录</div>
                <ul class="nav-list">
                  <li
                    v-for="item in navList"
                    :key="item.id"
                    :class="{ active: activeNavItem === item.id }"
                    @click="scrollToSection(item.id)"
                  >
                    <img :src="iconMap[item.icon]" alt="" />
                    {{ item.label }}
                  </li>
                </ul>
              </div>
            </div>
          </aside>
        </div>
      </main>
    </div>
    <!-- 收藏弹框 -->
    <collection-modal v-model="showCollectionModal" :article="selectedArticle" @confirm="handleCollectionConfirm" />

    <!-- 纠错弹框 -->
    <el-dialog
      v-model="showCorrectionModal"
      title="文献纠错"
      width="500px"
      center
      :before-close="handleCorrectionClose"
    >
      <el-form :model="correctionForm" label-width="70px">
        <el-form-item label="数据类型">
          <el-select v-model="correctionForm.dataType" placeholder="请选择数据类型" style="width: 100%">
            <el-option
              v-for="item in correctionDataTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈内容">
          <el-input
            v-model="correctionForm.description"
            type="textarea"
            :rows="6"
            placeholder="请详细描述发现的问题..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCorrectionClose">取消</el-button>
          <el-button type="primary" @click="handleCorrectionConfirm">确认提交</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 引用弹框 -->
    <el-dialog
      v-model="showCitationModal"
      title="CITE"
      width="550px"
      center
      :before-close="handleCitationClose"
      class="citation-dialog"
    >
      <div class="citation-content">
        <div class="citation-text-area">
          <el-scrollbar height="180px">
            <div ref="citationTextRef" class="citation-text">
              {{ currentCitationText }}
            </div>
          </el-scrollbar>
        </div>

        <div class="citation-actions">
          <el-button :icon="Document" link @click="copyCitation">Copy</el-button>
          <el-button :icon="Download" link @click="downloadCitation">Download .nbib</el-button>
          <div class="citation-format">
            <span class="format-label">Format:</span>
            <el-select v-model="selectedFormat" class="format-select" @change="handleFormatChange">
              <el-option label="NLM" value="NLM" />
              <el-option label="AMA" value="AMA" />
              <el-option label="APA" value="APA" />
              <el-option label="MLA" value="MLA" />
            </el-select>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 上传文献弹框 -->
    <el-dialog
      v-model="showUploadModal"
      title="上传全文"
      width="500px"
      center
      :before-close="handleUploadClose"
      class="upload-dialog"
    >
      <div class="upload-content">
        <div class="upload-section">
          <el-upload
            ref="uploadRef"
            class="upload-dragger"
            drag
            :auto-upload="false"
            :on-change="handleFileChange"
            :show-file-list="false"
            accept=".pdf,.doc,.docx"
          >
            <el-icon color="#F2F6F8" class="el-icon--upload">
              <upload-filled />
            </el-icon>
          </el-upload>
        </div>

        <div class="upload-actions">
          <el-button
            type="primary"
            class="submit-btn"
            :loading="uploadLoading"
            @click="handleUploadSubmit"
          >
            提交
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 文献传递弹框 -->
    <el-dialog
      v-model="showTransmitModal"
      title="文献传递"
      width="800px"
      center
      :before-close="handleTransmitClose"
      class="transmit-dialog"
    >
      <div>
        <el-form
          ref="transmitFormRef"
          :model="transmitForm"
          :rules="transmitRules"
          label-width="80px"
        >
          <el-form-item label="任务名称" prop="name">
            <el-input
              v-model="transmitForm.name"
              placeholder="请输入任务名称"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="任务描述" prop="description">
            <el-input
              v-model="transmitForm.description"
              type="textarea"
              :rows="4"
              placeholder="请输入任务描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="验证码" prop="captchaCode">
            <div class="captcha-container">
              <el-input
                v-model="transmitForm.captchaCode"
                placeholder="请输入验证码"
                style="flex: 1; margin-right: 10px;"
                @keyup.enter="handleTransmitConfirm"
              />
              <div class="captcha-image" @click="refreshCaptcha">
                <img v-if="captchaImage" :src="captchaImage" alt="验证码" />
                <div v-else class="captcha-loading">加载中...</div>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <div class="story text-center text-warning" style="padding-left: 15px">
          <p>是否确定传送该文献？每次传递传递成功后将会扣除积分</p>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleTransmitClose">取消</el-button>
          <el-button type="primary" :loading="transmitLoading" @click="handleTransmitConfirm">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, computed, nextTick, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import 'swiper/css'
  import 'swiper/css/pagination' // 轮播图底面的小圆点
  import 'swiper/css'
  import 'swiper/css/navigation'
  import 'swiper/css/pagination'
  import 'swiper/css/scrollbar'
  import {
    ArrowDown,
    ArrowUp,
    Star,
    StarFilled,
    EditPen,
    Document,
    MessageBox,
    Download,
    Upload,
    UploadFilled,
    Warning
  } from '@element-plus/icons-vue'
  import { databankUrlConfig, generateDatabankUrl } from '@/utils/databankConfig'
  import { Navigation, Pagination, Scrollbar, Autoplay, Mousewheel } from 'swiper/modules'

  const modules = [Mousewheel, Pagination, Navigation, Scrollbar, Autoplay]
  import { Swiper, SwiperSlide } from 'swiper/vue'
  import CollectionModal from '@/components/CollectionModal.vue'
  import {
    getAllReferences,
    getArticleDetail as fetchArticleDetail,
    interpretArticle,
    getReferences
  } from '@/api/article'
  import { getCodeImg } from '@/api/auth'
  import { isCollect } from '@/api/folder'
  import { useLoadingStore } from '@/stores/loading'
  import { useAuthStore } from '@/stores/auth'
  import { parseTime } from '@/utils/util'
  import { addUserDocHistory } from '@/api/user'

  const route = useRoute()
  const router = useRouter()

  // 文章详情数据
  const articleDetail = ref({
    id: null,
    customId: null,
    title: '',
    pmid: null,
    pmcId: null,
    doi: '',
    download: 0,
    hitNum: 0,
    source: [],
    journalName: '',
    journalContent: '',
    zkySections: null,
    impactFactor: null,
    jcrQuartile: '',
    authors: [],
    affiliation: [],
    articleAbstract: '',
    keywords: null,
    publishedYear: null,
    publishedMonth: '',
    volume: '',
    issue: '',
    page: '',
    pubTypes: [],
    grants: [],
    databanks: null,
    meshTerms: []
  })

  const loadingStore = useLoadingStore()
  const authStore = useAuthStore()
  const loading = ref(false)
  const authorExpanded = ref(false)
  const affiliationExpanded = ref(false)
  const activeName = ref('first')
  const authorsRef = ref(null)
  const affiliationsRef = ref(null)
  const showAuthorToggle = ref(false)
  const showAffiliationToggle = ref(false)

  // 解析相关状态
  const interpretationContent = ref('')
  const interpretationLoading = ref(false)
  const interpretationError = ref('')

  // 参考文献相关状态
  const references = ref([])
  const referencesLoading = ref(false)
  const referencesTotalCount = ref(0)
  const referencesHasMore = ref(false)
  const referencesLimit = ref(5)
  // 移动端检测
  const windowWidth = ref(window.innerWidth)
  const isMobile = computed(() => windowWidth.value <= 768)

  // 格式化发表日期
  const formatPublishDate = () => {
    const { publishedYear, publishedMonth, volume, issue, page } = articleDetail.value
    let dateStr = ''

    if (publishedYear) {
      dateStr += publishedYear
    }
    if (publishedMonth) {
      dateStr += ` ${publishedMonth}`
    }
    if (volume || issue || page) {
      dateStr += ';'
      if (volume) dateStr += `${volume}`
      if (issue) dateStr += `(${issue})`
      if (page) dateStr += `:${page}`
    }

    return dateStr
  }

  // 收藏相关
  const showCollectionModal = ref(false)
  const selectedArticle = ref(null)
  const isCollected = ref(false)

  // 纠错相关
  const showCorrectionModal = ref(false)
  const correctionForm = ref({
    dataType: '',
    description: ''
  })

  // 引用相关
  const showCitationModal = ref(false)
  const selectedFormat = ref('NLM')
  const citationTextRef = ref(null)

  // 不同格式的引用文本
  const citationFormats = {
    NLM: 'Tufariello JM, Kerantzas CA, Vilchèze C, Calder RB, Nordberg EK, Fischer JA, Hartman TE, Yang E, Driscoll T, Cole LE, Sebra R, Maqbool SB, Wattam AR, Jacobs WR Jr. The Complete Genome Sequence of the Emerging Pathogen Mycobacterium haemophilum Explains Its Unique Culture Requirements. mBio. 2015 Nov 17;6(6):e01313-15. doi:',
    AMA: 'Tufariello JM, Kerantzas CA, Vilchèze C, et al. The Complete Genome Sequence of the Emerging Pathogen Mycobacterium haemophilum Explains Its Unique Culture Requirements. mBio. 2015;6(6):e01313-e1315. Published 2015 Nov 17. doi:10.1128/mBio.01313-15',
    APA: 'Tufariello, J. M., Kerantzas, C. A., Vilchèze, C., Calder, R. B., Nordberg, E. K., Fischer, J. A., Hartman, T. E., Yang, E., Driscoll, T., Cole, L. E., Sebra, R., Maqbool, S. B., Wattam, A. R., & Jacobs, W. R., Jr (2015). The Complete Genome Sequence of the Emerging Pathogen Mycobacterium haemophilum Explains Its Unique Culture Requirements. mBio, 6(6), e01313–e1315. https://doi.org/10.1128/mBio.01313-15',
    MLA: 'Tufariello JM, Kerantzas CA, Vilchèze C, Calder RB, Nordberg EK, Fischer JA, Hartman TE, Yang E, Driscoll T, Cole LE, Sebra R, Maqbool SB, Wattam AR, Jacobs WR Jr. The Complete Genome Sequence of the Emerging Pathogen Mycobacterium haemophilum Explains Its Unique Culture Requirements. mBio. 2015 Nov 17;6(6):e01313-15. doi: 10.1128/mBio.01313-15. PMID: 26578674; PMCID: PMC4659460.'
  }

  const currentCitationText = computed(() => {
    return citationFormats[selectedFormat.value]
  })

  // 上传相关
  const showUploadModal = ref(false)
  const uploadLoading = ref(false)
  const uploadFileName = ref('')
  const uploadRef = ref(null)

  // 文献传递相关
  const showTransmitModal = ref(false)
  const transmitLoading = ref(false)
  const transmitFormRef = ref(null)
  const captchaImage = ref('')
  const transmitForm = ref({
    name: '',
    description: '',
    captchaCode: '',
    uuid: ''
  })

  // 表单验证规则
  const transmitRules = {
    name: [
      { required: true, message: '请输入任务名称', trigger: 'blur' },
      { min: 1, max: 100, message: '任务名称长度在 1 到 100 个字符', trigger: 'blur' }
    ],
    description: [
      { required: true, message: '请输入任务描述', trigger: 'blur' },
      { min: 1, max: 500, message: '任务描述长度在 1 到 500 个字符', trigger: 'blur' }
    ],
    captchaCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
  }
  // 检查内容是否超出一行
  const checkOverflow = () => {
    nextTick(() => {
      // 检查作者是否超出一行
      if (authorsRef.value) {
        const element = authorsRef.value
        // 临时添加expanded类来测量真实高度
        const wasExpanded = element.classList.contains('expanded')
        if (!wasExpanded) {
          element.classList.add('expanded')
        }
        const naturalHeight = element.scrollHeight
        if (!wasExpanded) {
          element.classList.remove('expanded')
        }

        // 判断自然高度是否超过一行（约30px，考虑padding和gap）
        showAuthorToggle.value = naturalHeight > 30
      }

      // 检查机构是否超出一行
      if (affiliationsRef.value) {
        const element = affiliationsRef.value
        // 临时添加expanded类来测量真实高度
        const wasExpanded = element.classList.contains('expanded')
        if (!wasExpanded) {
          element.classList.add('expanded')
        }
        const naturalHeight = element.scrollHeight
        if (!wasExpanded) {
          element.classList.remove('expanded')
        }

        // 判断自然高度是否超过一行（约30px，考虑padding和gap）
        showAffiliationToggle.value = naturalHeight > 30
      }
    })
  }

  const handleResize = () => {
    windowWidth.value = window.innerWidth
    checkOverflow()
  }

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('scroll', throttledHandleScroll)
  })

  // 监听数据变化，重新检查溢出
  watch(() => articleDetail.value.authors, checkOverflow)
  watch(() => articleDetail.value.affiliation, checkOverflow)

  // 用import.meta.glob批量导入svg图片
  const images = import.meta.glob('@/assets/images/*.svg', { eager: true, import: 'default' })
  // const chartSwiper = ref(null)
  const iconMap = {}
  Object.keys(images).forEach(key => {
    const fileName = key.split('/').pop()
    iconMap[fileName] = images[key]
  })

  // const onSwiper = swiper => {
  //   chartSwiper.value = swiper
  // }

  // 实体区块数据
  // const entityTagList = ref([
  //   {
  //     key: 'anatomy',
  //     title: '解剖学术语',
  //     expanded: false,
  //     tags: [
  //       'neuroanatomy',
  //       'gross morphology',
  //       'white matter',
  //       'fronto-parietal',
  //       'neuroanatomy',
  //       'gross morphology',
  //       'white matter',
  //       'fronto-parietal'
  //     ]
  //   },
  //   {
  //     key: 'function',
  //     title: '功能指标',
  //     expanded: false,
  //     tags: ['cognitive ability', 'cognitive tests']
  //   },
  //   {
  //     key: 'method',
  //     title: '研究方法',
  //     expanded: false,
  //     tags: [
  //       'hierarchical independent component analysis (hICA)',
  //       'hierarchical independent component analysis (hICA)',
  //       'hierarchical independent component analysis (hICA)'
  //     ]
  //   },
  //   {
  //     key: 'bio',
  //     title: '生物学特征',
  //     expanded: false,
  //     tags: ['phenotypic variation', 'gross morphology', 'white matter']
  //   }
  // ])
  // const toggleTagExpand = idx => {
  //   entityTagList.value[idx].expanded = !entityTagList.value[idx].expanded
  // }

  // 纠错数据类型选项（基于navList + 其它）
  const correctionDataTypes = computed(() => {
    const types = navList.value.map(item => ({
      label: item.label,
      value: item.label
    }))
    types.push({
      label: '其它',
      value: '其它'
    })
    return types
  })
  // 动态导航列表，根据数据存在情况显示
  const navList = computed(() => {
    const items = [
      {
        id: 'basic-info',
        icon: 'info.svg',
        label: '基本信息'
      }
    ]

    // 摘要
    if (articleDetail.value.articleAbstract) {
      items.push({
        id: 'abstract',
        icon: 'references.svg',
        label: '摘要'
      })
    }

    // 关键词
    if (articleDetail.value.keywords > 0) {
      items.push({
        id: 'keywords',
        icon: 'keywords.svg',
        label: '关键词'
      })
    }

    // 文献分类
    items.push({
      id: 'category',
      icon: 'category.svg',
      label: '文献分类'
    })

    // 图表（保留原有的静态区块）
    items.push({
      id: 'charts',
      icon: 'chart.svg',
      label: '图表'
    })

    // 实体（保留原有的静态区块）
    items.push({
      id: 'entity',
      icon: 'entity.svg',
      label: '实体'
    })

    // 基金支持
    if (articleDetail.value.grants && articleDetail.value.grants.length > 0) {
      items.push({
        id: 'funding',
        icon: 'fund.svg',
        label: '基金支持'
      })
    }

    // 相关数据
    if (articleDetail.value.databanks && articleDetail.value.databanks.length > 0) {
      items.push({
        id: 'related-data',
        icon: 'link-data.svg',
        label: '相关数据'
      })
    }

    // MeSH主题词
    if (articleDetail.value.meshTerms && articleDetail.value.meshTerms.length > 0) {
      items.push({
        id: 'mesh',
        icon: 'mesh.svg',
        label: 'MeSH主题词'
      })
    }

    // 参考文献
    if (references.value && references.value.length > 0) {
      items.push({
        id: 'references',
        icon: 'frame.svg',
        label: '参考文献'
      })
    }
    // 相似文章（保留原有的静态区块）
    items.push({
      id: 'similar-articles',
      icon: 'article.svg',
      label: '相似文章'
    })

    return items
  })

  // 当前激活的导航项
  const activeNavItem = ref('basic-info')
  // 收藏相关方法
  const handleBatchFavorite = async() => {
    // 优先使用统一鉴权方法；没有则使用状态位
    const isLoggedIn = typeof authStore.checkAuth === 'function' ? await authStore.checkAuth() : authStore.isLoggedIn

    if (!isLoggedIn) {
      // 记录当前路由，登录成功后重定向回来
      const redirect = encodeURIComponent(router.currentRoute.value.fullPath)
      router.push(`/login?redirect=${redirect}`)
      return
    }
    showCollectionModal.value = true
  }

  const handleCollectionConfirm = async data => {
    try {
      // 导入收藏API和用户认证
      const { addToFavorites } = await import('@/api/folder')
      const { useAuthStore } = await import('@/stores/auth')
      const authStore = useAuthStore()

      if (!authStore.userInfo?.userId) {
        ElMessage.error('请先登录')
        return
      }

      const favoriteDocDto = {
        folderId: data.folderId,
        docId: articleDetail.value.id
      }

      const response = await addToFavorites(favoriteDocDto)
      // 兼容不同的响应格式
      const isSuccess = response.code === 200 || response.status === 200 || response.success === true

      if (isSuccess) {
        ElMessage.success(`已收藏到"${data.folderName}"`)
        isCollected.value = true
      } else {
        const errorMsg = response.msg || response.message || response.error || '收藏失败'
        ElMessage.error(errorMsg)
      }
    } catch (error) {
      console.log(error)
    }
  }

  // 纠错相关方法
  const handleCorrection = () => {
    showCorrectionModal.value = true
  }

  const handleCorrectionClose = () => {
    showCorrectionModal.value = false
    // 重置表单
    correctionForm.value = {
      dataType: '',
      description: ''
    }
  }

  const handleCorrectionConfirm = () => {
    handleCorrectionClose()
  }

  // 引用相关方法
  const handleCitation = () => {
    showCitationModal.value = true
  }

  const handleCitationClose = () => {
    showCitationModal.value = false
  }

  const handleFormatChange = format => {
    selectedFormat.value = format
  }

  const copyCitation = async() => {
    try {
      await navigator.clipboard.writeText(currentCitationText.value)
      ElMessage.success('引用内容已复制到剪贴板')
    } catch (err) {
      ElMessage.error('复制失败，请手动选择文本复制')
    }
  }

  const downloadCitation = () => {
    const content = currentCitationText.value
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'citation.nbib'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // 文献传递相关方法
  const handleDocumentTransmit = async() => {
    // 检查登录状态
    const isLoggedIn = await authStore.checkAuth()
    if (!isLoggedIn) {
      // 跳转到登录页面
      router.push('/login')
      return
    }
    transmitForm.value.name = [parseTime(new Date(), '{y}/{m}/{d}'), authStore.userEmail, articleDetail.value.id].join(
      '-'
    )
    // console.log(articleDetail.value.journalName)
    transmitForm.value.description = [
      authStore.userEmail,
      articleDetail.value.journalName,
      articleDetail.value.id
    ].join('-')

    // 已登录，打开文献传递弹窗
    showTransmitModal.value = true
    // 获取验证码
    await getCaptcha()
  }

  const handleTransmitClose = () => {
    showTransmitModal.value = false
    // 重置表单
    transmitForm.value = {
      name: '',
      description: '',
      captchaCode: '',
      uuid: ''
    }
    captchaImage.value = ''
    // 重置表单验证
    if (transmitFormRef.value) {
      transmitFormRef.value.resetFields()
    }
  }

  const getCaptcha = async() => {
    try {
      const response = await getCodeImg()
      if (response && response.img) {
        captchaImage.value = `data:image/jpeg;base64,${response.img}`
        transmitForm.value.uuid = response.uuid
      }
    } catch (error) {
      console.error('获取验证码失败:', error)
      ElMessage.error('获取验证码失败，请重试')
    }
  }

  const refreshCaptcha = () => {
    getCaptcha()
  }

  const handleTransmitConfirm = async() => {
    if (!transmitFormRef.value) return

    try {
      // 表单验证
      // await transmitFormRef.value.validate()

      transmitLoading.value = true

      // 准备提交数据
      const submitData = {
        articleId: articleDetail.value.id,
        name: transmitForm.value.name,
        description: transmitForm.value.description,
        captchaCode: transmitForm.value.captchaCode,
        uuid: transmitForm.value.uuid
      }

      // 调用文献传递API（需要在article.js中添加）
      const { submitArticleTransmit } = await import('@/api/articleTransmit')
      const response = await submitArticleTransmit(submitData)

      if (response.code === 200) {
        ElMessage.success('文献传递申请提交成功')
        handleTransmitClose()
      } else {
        ElMessage.error(response.msg || '提交失败')
        // 如果是验证码错误，刷新验证码
        if (response.msg && response.msg.includes('验证码')) {
          refreshCaptcha()
        }
      }
    } catch (error) {
      console.error('提交文献传递申请失败:', error)
      ElMessage.error('提交失败，请重试')
      // 刷新验证码
      refreshCaptcha()
    } finally {
      transmitLoading.value = false
    }
  }
  // 上传相关方法
  const handleUpload = () => {
    showUploadModal.value = true
  }

  const handleUploadClose = () => {
    showUploadModal.value = false
    uploadFileName.value = ''
    uploadLoading.value = false
  }

  const handleFileChange = file => {
    uploadFileName.value = file.name
  }

  const handleUploadSubmit = async() => {
    if (!uploadFileName.value) {
      ElMessage.warning('请选择要上传的文件')
      return
    }

    uploadLoading.value = true
    try {
      // 这里应该调用上传文件的接口
      await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟上传过程

      ElMessage.success('文献上传成功')
      handleUploadClose()
    } catch (error) {
      ElMessage.error('上传失败，请重试')
    } finally {
      uploadLoading.value = false
    }
  }

  // 滚动到指定区域
  const scrollToSection = sectionId => {
    const element = document.getElementById(sectionId)

    if (element) {
      // 计算元素位置，考虑固定导航的高度
      const headerOffset = 120 // 根据固定导航的位置调整
      const elementPosition = element.offsetTop
      const offsetPosition = elementPosition - headerOffset

      // 平滑滚动到目标位置
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      })

      // 更新激活状态
      activeNavItem.value = sectionId
    }
  }

  // 监听页面滚动事件，更新激活状态
  const handleScroll = () => {
    const scrollPosition = window.scrollY + 200 // 偏移量，提前激活

    // 找到当前可见的区域
    for (let i = navList.value.length - 1; i >= 0; i--) {
      const element = document.getElementById(navList.value[i].id)
      if (element && element.offsetTop <= scrollPosition) {
        activeNavItem.value = navList.value[i].id
        break
      }
    }
  }

  // 节流函数
  const throttle = (func, limit) => {
    let inThrottle
    return function() {
      const args = arguments
      const context = this
      if (!inThrottle) {
        func.apply(context, args)
        inThrottle = true
        setTimeout(() => (inThrottle = false), limit)
      }
    }
  }

  const throttledHandleScroll = throttle(handleScroll, 100)

  // 获取来源样式
  const getSourceStyle = sourceValue => {
    const source = sourceValue?.toLowerCase() || ''

    if (source === 'pubmed') {
      return {} // 保持原来的样式，不覆盖
    } else if (source === 'pmc') {
      return { backgroundColor: '#e3f2fd', color: '#1565c0' }
    } else if (source === 'biorxiv') {
      return { backgroundColor: '#fff3e0', color: '#ef6c00' }
    } else if (source === 'medrxiv') {
      return { backgroundColor: '#fce4ec', color: '#c2185b' }
    } else if (source === 'custom') {
      return { backgroundColor: '#f3e5f5', color: '#7b1fa2' }
    } else {
      return { backgroundColor: '#f5f5f5', color: '#616161' }
    }
  }

  // 滚动到对应机构
  const scrollToAffiliation = affiliationIndex => {
    const element = document.getElementById(`affiliation-${affiliationIndex}`)
    if (element) {
      // 先展开机构列表（如果是折叠状态）
      if (!affiliationExpanded.value && articleDetail.value.affiliation.length > 2) {
        affiliationExpanded.value = true
        // 等待DOM更新后再滚动
        nextTick(() => {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
          // 添加高亮效果
          element.classList.add('highlight')
          setTimeout(() => {
            element.classList.remove('highlight')
          }, 2000)
        })
      } else {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
        // 添加高亮效果
        element.classList.add('highlight')
        setTimeout(() => {
          element.classList.remove('highlight')
        }, 2000)
      }
    }
  }

  // 在MeSH中搜索文献分类
  const searchInMesh = pubType => {
    const meshUrl = `https://www.ncbi.nlm.nih.gov/mesh?term=${encodeURIComponent(pubType)}`
    window.open(meshUrl, '_blank')
  }

  // 在MeSH中搜索主题词
  const searchInMeshTerm = meshTerm => {
    const searchTerm = meshTerm.descriptorName
    const meshUrl = `https://www.ncbi.nlm.nih.gov/mesh?sort=date&sort_order=asc&term=${encodeURIComponent(searchTerm)}`
    window.open(meshUrl, '_blank')
  }

  // 检查数据库是否有有效的URL配置
  const hasValidUrl = name => {
    const urlTemplate = databankUrlConfig[name]
    return (
      urlTemplate &&
      urlTemplate !== 'https://www.pactr.org/' &&
      urlTemplate !== 'http://www.slctr.lk/' &&
      urlTemplate !== 'http://www.clinicaltrials.in.th/' &&
      urlTemplate !== 'https://www.uniprot.org/'
    )
  }

  // 打开相关数据库链接
  const openDatabank = databank => {
    if (!hasValidUrl(databank.name)) return
    const { name, value } = databank
    const url = generateDatabankUrl(name, value)
    window.open(url, '_blank')
  }

  // 格式化基金信息，避免显示多余的分隔符
  const formatGrantInfo = grant => {
    const parts = []

    if (grant.grantId) parts.push(grant.grantId)
    if (grant.acronym) parts.push(grant.acronym)
    if (grant.agency) parts.push(grant.agency)
    if (grant.country) parts.push(grant.country)

    return parts.join('/')
  }
  // 搜索基金资助
  const searchGrant = grant => {
    const searchTerm = formatGrantInfo(grant)
      .replace(/\([^)]*\)$/, '')
      .trim()

    const pubmedUrl = `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(searchTerm)}[Grants and Funding]&sort=date&sort_order=desc`
    window.open(pubmedUrl, '_blank')
  }

  // 获取文章详情
  const getArticleDetail = async id => {
    if (!id) return

    loadingStore.showLoading()
    loading.value = true

    try {
      const response = await fetchArticleDetail(id)
      if (response && response.data) {
        articleDetail.value = response.data
        // 文章详情加载完成后，异步加载参考文献（不阻塞主流程）
        nextTick(() => {
          fetchReferencesAsync(articleDetail.value.id, referencesLimit.value)
        })
        // 只有在用户已登录时才检查收藏状态
        if (authStore.isLoggedIn) {
          checkIfCollected(articleDetail.value.id)
          addUserDocHistory(response.data.id)
        }
      }
    } catch (error) {
      ElMessage.error(`获取文章详情失败: ${error.message || '未知错误'}`)
    } finally {
      loading.value = false
      // 延迟隐藏初始加载画面，确保内容渲染完成
      setTimeout(() => {
        loadingStore.hideLoading()
      }, 300)
    }
  }

  // 获取文献解析
  const getInterpretation = async docId => {
    if (!docId || interpretationLoading.value) return

    // 检查登录状态
    if (!authStore.isLoggedIn) {
      interpretationError.value = '请先登录后再获取文献解析'
      ElMessage.warning('请先登录后再获取文献解析')
      return
    }

    interpretationLoading.value = true
    interpretationError.value = ''

    try {
      const response = await interpretArticle(docId)

      if (response && response.data) {
        // 如果响应有data字段，使用data
        interpretationContent.value = response.data
      } else if (typeof response === 'string') {
        // 如果响应直接是字符串
        interpretationContent.value = response
      } else {
        interpretationContent.value = '暂无解析内容'
      }
    } catch (error) {
      const errorMessage = '获取解析失败，请稍后重试'
      interpretationError.value = errorMessage
      ElMessage.error(`获取文献解析失败: ${errorMessage}`)
    } finally {
      interpretationLoading.value = false
    }
  }

  // 处理标签页切换
  const handleTabChange = tabName => {
    if (
      tabName === 'second' &&
      !interpretationContent.value &&
      !interpretationLoading.value &&
      !interpretationError.value
    ) {
      // 当切换到解析标签页且还没有解析内容时，获取解析
      const docId = articleDetail.value.id
      if (docId) {
        getInterpretation(docId)
      }
    }
  }

  // 重试获取解析
  const retryInterpretation = () => {
    // 检查登录状态
    if (!authStore.isLoggedIn) {
      ElMessage.warning('请先登录后再获取文献解析')
      return
    }

    const docId = articleDetail.value.id
    if (docId) {
      getInterpretation(docId)
    }
  }

  // 跳转到登录页面
  const goToLogin = () => {
    router.push(`/login?redirect=${encodeURIComponent(router.currentRoute.value.fullPath)}`)
  }

  // 异步获取参考文献（不阻塞主流程）
  const fetchReferencesAsync = async(docId, limit = 5) => {
    if (!docId) return
    referencesLoading.value = true

    try {
      // 添加小延迟，确保主内容先渲染
      await new Promise(resolve => setTimeout(resolve, 100))

      const response = await getReferences(docId, limit)

      if (response && response.data) {
        references.value = response.data.references || []
        referencesTotalCount.value = response.data.count || 0
        // 当获取的数据数量小于总数时才显示"Show all"按钮
        referencesHasMore.value = references.value.length < (response.data.count || 0)
      }
    } catch (error) {
      // 显示错误状态，但不影响其他功能
      references.value = []
      referencesTotalCount.value = 0
      referencesHasMore.value = false
    } finally {
      referencesLoading.value = false
    }
  }
  // 加载所有参考文献
  const loadAllReferences = async() => {
    const docId = articleDetail.value.id
    if (docId && !referencesLoading.value) {
      referencesLoading.value = true
      try {
        const response = await getAllReferences(docId)

        if (response && response.data) {
          const allReferences = response.data.references || []

          references.value = allReferences
          referencesTotalCount.value = response.data.count || allReferences.length
          referencesHasMore.value = false // 已经加载了所有参考文献，不再需要"Show all"按钮

          // 强制触发Vue的响应式更新
          nextTick(() => {
            console.log('nextTick后 - references.value.length:', references.value.length)
          })
        } else {
          console.error('API响应格式不正确:', response)
        }
      } catch (error) {
        console.error('加载所有参考文献失败:', error)
      } finally {
        referencesLoading.value = false
      }
    }
  }

  // 格式化citation - 后端已处理HTML标签，只需处理分隔符
  const formatCitation = reference => {
    if (!reference.citation) return ''

    let citation = reference.citation

    // 去除多余的空格和换行符
    citation = citation.replace(/\s+/g, ' ').trim()

    // 检查citation是否已经包含链接分隔符（如 "- DOI", "- PubMed" 等）
    // 如果包含，我们需要特殊处理以避免重复分隔符
    const hasTrailingSeparator = /\s*-\s*(DOI|PubMed|PMC|PLOSP)\s*$/i.test(citation)

    if (hasTrailingSeparator) {
      // 如果citation末尾已经有分隔符和链接标识，移除它们
      citation = citation.replace(/\s*-\s*(DOI|PubMed|PMC|PLOSP)\s*$/i, '').trim()
    }

    return citation
  }

  // 获取可用的链接数组
  const getAvailableLinks = reference => {
    const links = []

    // PLOSP链接
    if (reference.pmid) {
      links.push({
        type: 'plosp',
        label: 'PLOSP',
        action: () => openPLOSP(reference.pmid)
      })
    }

    // PubMed链接
    if (reference.hasPubmedLink && reference.pmid) {
      links.push({
        type: 'pubmed',
        label: 'PubMed',
        action: () => openPubMed(reference.pmid)
      })
    }

    // PMC链接
    if (reference.hasPmcLink && reference.pmcid) {
      links.push({
        type: 'pmc',
        label: 'PMC',
        action: () => openPMC(reference.pmcid)
      })
    }

    // DOI链接
    if (reference.hasDoiLink && reference.doi) {
      links.push({
        type: 'doi',
        label: 'DOI',
        action: () => openDOI(reference.doi)
      })
    }

    return links
  }

  // 在PLOSP中打开参考文献详情
  const openPLOSP = pmid => {
    if (pmid) {
      const url = `http://localhost:3000/detail/${pmid}`
      window.open(url, '_blank')
    }
  }

  // 打开PubMed链接
  const openPubMed = pmid => {
    window.open(`https://pubmed.ncbi.nlm.nih.gov/${pmid}/`, '_blank')
  }

  // 打开PMC链接
  const openPMC = pmcId => {
    const cleanPmcId = pmcId.toString().replace(/^PMC/, '')
    window.open(`https://www.ncbi.nlm.nih.gov/pmc/articles/PMC${cleanPmcId}/`, '_blank')
  }

  // 打开DOI链接
  const openDOI = doi => {
    window.open(`https://doi.org/${doi}`, '_blank')
  }

  // 检查文献是否已收藏
  const checkIfCollected = async docId => {
    if (!authStore.isLoggedIn) return

    try {
      const response = await isCollect({
        docId,
        userId: authStore.userInfo.userId
      })
      if (response.data === '该文献已收藏') {
        isCollected.value = true
      } else {
        isCollected.value = false
      }
    } catch (error) {
      console.error('检查收藏状态失败:', error)
    }
  }

  // 页面加载时获取数据
  onMounted(() => {
    const articleId = route.params.id
    if (articleId) {
      getArticleDetail(articleId)
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('scroll', throttledHandleScroll)
    checkOverflow()
  })
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/variables';

  .literature-detail-view {
    background: $background-color;
    min-height: 100vh;
    padding: 0;
  }

  .loading-container {
    padding: $spacing-xxl;

    @media (max-width: $breakpoint-md) {
      padding: $spacing-md;
    }
  }

  .detail-layout {
    padding: $spacing-xxl 0;
    font-size: $font-size-small;

    @media (max-width: $breakpoint-md) {
      padding: $spacing-md 0;
    }
  }

  .detail-main {
    flex: 1 1 0;
    background: $white;
    border-radius: $border-radius-xl;
    box-shadow: $box-shadow;
    padding-top: $spacing-xxl;

    @media (max-width: $breakpoint-md) {
      padding: $spacing-md $spacing-xs;
    }
  }

  .main-content {
    display: flex;
    gap: 140px;

    @media (max-width: $breakpoint-lg) {
      flex-direction: column;
      gap: $spacing-lg;
      padding: $spacing-lg 0;
    }
  }

  .row-btn {
    display: flex;
    gap: 20px;
    margin-top: 20px;

    @media (max-width: $breakpoint-md) {
      flex-wrap: wrap;
      gap: $spacing-sm;
    }

    .el-button {
      border: 1px solid #374151;
      width: 107px;
      font-weight: 400;

      @media (max-width: $breakpoint-md) {
        flex: 1;
        min-width: calc(50% - #{$spacing-sm / 2});
        width: auto;
      }

      &:hover {
        background-color: #043873;
        color: #ffffff;
      }
    }
  }

  // 文献传递下拉选项样式
  .delivery-options {
    padding: 8px 0;

    .delivery-option {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.2s;
      font-size: 14px;
      color: #374151;

      &:hover {
        background-color: #f3f4f6;
      }

      .el-icon {
        font-size: 16px;
        color: #6b7280;
      }

      span {
        flex: 1;
      }
    }
  }

  // 关键词下拉选项样式
  .keyword-options {
    .keyword-option {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.2s;
      font-size: 14px;
      color: #374151;

      &:hover {
        background-color: #f3f4f6;
      }

      span {
        flex: 1;
      }
    }
  }

  .detail-aside {
    width: 310px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    gap: $spacing-xl;

    @media (max-width: $breakpoint-lg) {
      width: 100%;
      flex-direction: row;
      gap: $spacing-md;
    }

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      gap: $spacing-md;
    }
  }

  .aside-card {
    position: sticky;
    top: 150px;
    background: $white;
    border-radius: $border-radius-lg;
    padding: $spacing-lg;
    margin-bottom: $spacing-md;
    border: 1px solid #b9b9b9;
  }

  .metric-card {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
  }

  .metric-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: $spacing-md;
  }

  .metric-circle {
    padding: 0px 14px;
    background: #f3f6f9;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: $font-size-small;
    color: $gray;
  }

  .metric-label {
    display: flex;
    gap: 8px;
    font-size: 16px;
    color: $gray;
  }

  .metric-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    line-height: 20px;
    background: #4a5662;
    color: white;
    font-size: 10px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .nav-title {
    font-size: $font-size-medium;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 36px 0 16px 0;
  }

  .nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 2px;

    li {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: $font-size-small;
      color: $gray;
      transition: all 0.3s ease;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;

      &:hover {
        color: $primary-color;
        background-color: rgba(0, 69, 109, 0.05);
      }

      &.active {
        color: $primary-color;
        background-color: rgba(0, 69, 109, 0.1);
        font-weight: $font-weight-medium;
      }

      img {
        width: 16px;
        height: 16px;
      }
    }
  }

  /* 头部信息容器 */
  .header-info {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: $spacing-md;
    margin-bottom: $spacing-md;
    flex-wrap: nowrap;

    @media (max-width: $breakpoint-md) {
      gap: $spacing-sm;
    }
  }

  /* 来源徽章容器 */
  .source-badges {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: $font-size-small;
    color: $gray;
    flex-shrink: 1;
    min-width: 0;
    flex-direction: column;
  }

  .source-badge {
    font-size: $font-size-small;
    padding: 4px 14px;
    border-radius: 9999px;
    flex-shrink: 0;
    font-weight: $font-weight-medium;
    background: #dcfce7; // PubMed 默认样式
    color: #166534;
  }

  .source-separator {
    color: $gray;
    margin: 0 4px;
  }

  /* 期刊信息 */
  .journal-info {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    color: $gray;
    font-size: $font-size-medium;
    flex-shrink: 1;
    min-width: 0;
    margin-left: 27px;

    @media (max-width: $breakpoint-md) {
      gap: $spacing-xs;
      font-size: $font-size-small;
    }

    .journal-name {
      color: $gray;
    }

    .publish-date {
      color: $gray;
      white-space: nowrap;
    }
  }

  .detail-title {
    font-size: $font-size-large;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0 0 $spacing-md 0;
    line-height: 1.4;
    word-break: break-word;

    @media (max-width: $breakpoint-md) {
      font-size: $font-size-medium;
      margin-bottom: $spacing-sm;
    }
  }

  /* 元数据行 */
  .metadata-row {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-lg;
    align-items: center;
    margin-bottom: 8px;

    @media (max-width: $breakpoint-md) {
      gap: $spacing-md;
      padding: $spacing-sm;
    }
  }

  .metadata-item {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: $font-size-small;
    color: $gray;

    @media (max-width: $breakpoint-md) {
      flex: 1;
      min-width: calc(50% - #{$spacing-xs});
    }

    .metadata-label {
      color: $gray;
    }

    .metadata-link {
      color: $primary-color;
      text-decoration: none;
      font-weight: $font-weight-medium;

      &:hover {
        text-decoration: underline;
      }
    }

    .metadata-value {
      font-weight: $font-weight-medium;
      color: $gray;
    }

    .stat-icon {
      width: 16px;
      height: 16px;
      flex-shrink: 0;
    }
  }

  .detail-authors-row {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-md;
    margin-bottom: $spacing-sm;
    justify-content: space-between;

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      gap: $spacing-sm;
    }

    & > div:first-child {
      flex: 1;
      display: flex;
      gap: $spacing-md;
      align-items: start;

      @media (max-width: $breakpoint-md) {
        gap: $spacing-sm;
      }

      .author-icon {
        margin-top: 4px;
      }

      .authors {
        display: flex;
        gap: $spacing-md;
        align-items: center;
        max-height: 24px;
        transition: all 0.2s;
        flex-wrap: wrap;
        overflow: hidden;

        @media (max-width: $breakpoint-md) {
          gap: $spacing-sm;
        }

        &.expanded {
          max-height: 300px;
        }
      }
    }

    .author {
      font-size: $font-size-small;
      color: $gray;
      font-weight: $font-weight-medium;

      .author-index {
        display: inline-block;
        width: 15px;
        height: 15px;
        line-height: 15px;
        background-color: #f1f1f1;
        text-align: center;
        border-radius: 50%;
        font-weight: 400;

        &.clickable {
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background-color: $primary-color;
            color: white;
            transform: scale(1.1);
          }
        }
      }
    }
  }

  .detail-affiliations-row {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-md;
    margin-bottom: $spacing-sm;
    justify-content: space-between;

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      gap: $spacing-sm;
    }

    & > div:first-child {
      flex: 1;
      display: flex;
      gap: $spacing-md;
      align-items: start;

      @media (max-width: $breakpoint-md) {
        gap: $spacing-sm;
      }

      .option-icon {
        margin-top: 4px;
      }

      .affiliations {
        display: flex;
        flex-direction: column;
        gap: $spacing-xs;
        max-height: 24px; // 约2行的高度 (每行约24px)
        transition: all 0.2s;
        overflow: hidden;

        @media (max-width: $breakpoint-md) {
          max-height: 24px; // 移动端稍小一些
        }

        &.expanded {
          max-height: 300px;
        }
      }
    }

    .affiliation {
      font-size: $font-size-small;
      color: $gray;
      transition: all 0.3s ease;
      padding: 2px 4px;
      border-radius: 4px;

      @media (max-width: $breakpoint-md) {
        font-size: 14px;
        line-height: 1.4;
      }

      &.highlight {
        background-color: rgba(0, 65, 111, 0.1);
        color: $primary-color;
        font-weight: $font-weight-medium;
      }
    }
  }

  .detail-section {
    margin: $spacing-xl 0;
    position: relative;

    .section-title {
      font-size: $font-size-medium;
      font-weight: $font-weight-bold;
      color: $primary-color;
      margin: $spacing-xs 0;
    }

    .section-content {
      font-size: $font-size-small;
      color: #374151;
      line-height: 1.7;

      &.keywords {
        display: flex;
        flex-wrap: wrap;
        gap: $spacing-xs;

        .keyword {
          background: #eff6ff;
          color: #4f9cf9;
          font-size: $font-size-small;
          padding: 1px 12px 2px 12px;
          border-radius: 9999px;
          line-height: 31px;

          &:hover {
            cursor: pointer;
            background: #4f9cf9;
            color: #ffffff;
          }
        }
      }

      &.categories {
        display: flex;
        flex-wrap: wrap;
        gap: $spacing-xs;

        .category {
          background: $yellow;
          color: $primary-color;
          font-size: $font-size-small;
          padding: 4px 14px;
          border-radius: 9999px;
          margin-bottom: 2px;
        }
      }

      &.references {
        display: flex;
        flex-direction: column;
        gap: $spacing-xs;

        .reference {
          font-size: $font-size-small;
          color: $secondary-color;
          line-height: 1.5;

          .date {
            color: $gray;
            font-size: 14px;
            margin-top: 2px;
          }
        }
      }

      .data-link {
        color: $secondary-color;
        margin-right: $spacing-md;
        font-size: $font-size-small;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.2s ease;

        &:hover {
          color: $primary-color;
          text-decoration: underline;
        }
      }

      .funding-support {
        color: $secondary-color;
        padding-left: 18px;
      }

      .mesh-term-link {
        color: $secondary-color;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.2s ease;

        &:hover {
          color: $primary-color;
          text-decoration: underline;
        }
      }

      .grant-link {
        color: $secondary-color;
        cursor: pointer;
        text-decoration: none;
        transition: all 0.2s ease;

        &:hover {
          color: $primary-color;
          text-decoration: underline;
        }
      }

      &.chart-content {
        img {
          width: 50%;

          @media (max-width: $breakpoint-md) {
            width: 100%;
          }
        }
      }
    }

    .demo-tabs {
      :deep(.el-tabs__nav-scroll) {
        padding: 4px;
      }

      :deep(.el-tabs__active-bar) {
        display: none;
      }

      :deep(.el-tabs__header) {
        width: 145px;
        height: 36px;
        position: absolute;
        top: -8px;
        right: 0;
      }

      :deep(.el-tabs__nav-wrap) {
        background-color: #f6f7f9;
        width: 145px;
        height: 36px;
        border-radius: 4px;

        &::after {
          display: none;
        }
      }

      :deep(.el-tabs__item) {
        padding: 4px 20px;
        height: 28px;

        &.is-active {
          background-color: #ffffff;
        }
      }
    }

    .abstract {
      text-align: justify;
    }
  }

  .entity-section {
    border-radius: $border-radius-lg;
    margin-bottom: $spacing-xl;

    .keywords {
      max-height: 36px;
      overflow: hidden;
      transition: all 0.2s;

      @media (max-width: $breakpoint-md) {
        max-height: 32px;
      }

      &.expanded {
        max-height: 500px;
      }
    }

    .entity-title {
      font-size: $font-size-large;
      font-weight: $font-weight-bold;
      color: $primary-color;
      margin-bottom: $spacing-md;

      @media (max-width: $breakpoint-md) {
        font-size: $font-size-medium;
        margin-bottom: $spacing-sm;
      }
    }

    .entity-list {
      display: flex;
      flex-direction: column;
      gap: $spacing-md;

      @media (max-width: $breakpoint-md) {
        gap: $spacing-sm;
      }
    }

    .entity-item {
      background: $white;
      border-radius: $border-radius-md;
      position: relative;

      @media (max-width: $breakpoint-md) {
        padding: $spacing-sm;
        border: 1px solid #f0f0f0;
      }
    }

    .entity-header {
      display: flex;
      justify-content: space-between;
      cursor: pointer;
      font-size: $font-size-medium;
      color: $primary-color;
      padding: 0;

      @media (max-width: $breakpoint-md) {
        flex-direction: column;
        gap: $spacing-sm;
      }
    }

    .entity-content-wrapper {
      display: flex;
      align-items: start;
      flex: 1;

      @media (max-width: $breakpoint-md) {
        flex-direction: column;
        width: 100%;
        gap: $spacing-sm;
      }
    }

    .entity-name-wrapper {
      flex: 0 0 100px;

      @media (max-width: $breakpoint-md) {
        flex: 0 0 auto;
        width: 100%;
      }
    }

    .entity-name {
      font-size: $font-size-small;
      color: #000000;
      font-weight: 400;
      display: flex;
      align-items: center;
      height: 33px;

      @media (max-width: $breakpoint-md) {
        height: auto;
        padding: $spacing-xs 0;
        font-weight: 500;
      }
    }

    .entity-tags-wrapper {
      flex: 1;

      @media (max-width: $breakpoint-md) {
        width: 100%;
      }
    }

    .entity-toggle-wrapper {
      flex-shrink: 0;

      @media (max-width: $breakpoint-md) {
        align-self: flex-end;
        margin-top: $spacing-xs;
      }
    }

    .entity-content {
      margin-top: $spacing-sm;
      font-size: $font-size-small;
      color: $gray;

      ul {
        padding-left: 1.2em;
        margin: 0;

        li {
          margin-bottom: 4px;
        }
      }
    }

    .entity-divider {
      height: 1px;
      background: $light-gray;
      margin: $spacing-md 0 0 0;
      border-radius: 1px;
    }
  }

  .toggle-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    background: none;
    border: none;
    color: $primary-color;
    font-size: 14px;
    cursor: pointer;
    transition: color 0.2s;
    font-weight: 400;
    width: 45px;

    @media (max-width: $breakpoint-md) {
      font-size: 12px;
      width: auto;
      padding: $spacing-xs;
      background-color: rgba($primary-color, 0.1);
      border-radius: $border-radius-sm;
      min-height: 32px;
    }

    &:hover {
      color: $primary-color;
    }

    svg {
      transition: transform 0.2s;
    }

    &.open svg {
      transform: rotate(90deg);
    }
  }

  .swiper {
    width: 1055px !important;

    @media (max-width: $breakpoint-md) {
      width: 100% !important;
    }
  }

  .text-primary {
    color: #4f9cf9;
  }

  .text-warning {
    color: #f3aa44;
  }

  .text-danger {
    color: #e363b6;
  }

  .text-success {
    color: #31bd2c;
  }

  a {
    color: $primary-color;
  }

  @media (max-width: $breakpoint-md) {
    .detail-main {
      padding: $spacing-md $spacing-xs;
      border-radius: $border-radius-md;
    }

    .detail-title {
      font-size: $font-size-large;
      line-height: 1.4;
      margin-bottom: $spacing-md;
    }

    .detail-section {
      margin: $spacing-lg 0;

      .section-title {
        font-size: $font-size-medium;
        margin-bottom: $spacing-sm;
      }

      .section-content {
        font-size: $font-size-small;

        &.keywords .keyword {
          font-size: 14px;
          padding: 2px 8px;
        }

        &.references .reference {
          font-size: 14px;
          margin-bottom: $spacing-sm;
        }
      }

      .demo-tabs {
        :deep(.el-tabs__header) {
          width: 140px;
          height: 32px;
        }

        :deep(.el-tabs__nav-wrap) {
          width: 140px;
          height: 32px;
        }

        :deep(.el-tabs__item) {
          padding: 2px 12px;
          font-size: 14px;
        }
      }
    }

    .entity-section {
      padding: 0;

      .entity-title {
        font-size: $font-size-medium;
      }

      .entity-item {
        margin-bottom: $spacing-sm;

        .entity-header {
          align-items: flex-start;
        }

        .entity-content-wrapper {
          gap: $spacing-xs;
        }

        .entity-name {
          font-size: $font-size-small;
          font-weight: 500;
          color: $primary-color;
          border-bottom: 1px solid #f0f0f0;
          padding-bottom: $spacing-xs;
          margin-bottom: $spacing-xs;
        }

        .keywords {
          .keyword {
            font-size: 12px;
            padding: 2px 6px;
            margin: 2px 4px 2px 0;
          }
        }
      }
    }

    .entity-item {
      padding: 0;
    }

    .toggle-btn {
      font-size: 12px;
      width: auto;
      padding: 4px 8px;
    }

    .source-badge {
      font-size: 12px;
      padding: 2px 8px;
    }

    .metric-card {
      .metric-row {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-xs;
      }

      .metric-label {
        font-size: $font-size-small;
      }

      .metric-circle {
        font-size: $font-size-small;
        padding: 4px 8px;
      }
    }

    .partition-section {
      .partition-title {
        font-size: $font-size-small;
      }

      .partition-item {
        padding: 6px 8px;
        font-size: 12px;
      }
    }

    .nav-list {
      gap: $spacing-sm;

      li {
        font-size: $font-size-small;
        gap: 6px;
      }
    }
  }

  // 引用弹框样式
  :deep(.citation-dialog) {
    .el-dialog__header {
      padding: 0 20px 0 20px;

      .el-dialog__title {
        font-size: $font-size-large;
        color: #303133;
        font-weight: $font-weight-bold;
      }
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-dialog__close {
      font-size: 18px;
      color: $gray;

      &:hover {
        color: $primary-color;
      }
    }
  }

  .citation-content {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }

  .citation-text-area {
    border: 1px solid #e4e7ed;
    border-radius: $border-radius-md;
    background-color: #f9f9f9;

    .citation-text {
      padding: $spacing-md;
      font-size: $font-size-small;
      line-height: 1.6;
      text-align: justify;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }

  .citation-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      align-items: stretch;
      gap: $spacing-sm;
    }
  }

  .el-button.is-link {
    font-size: 16px;
    color: #374151;
  }

  .citation-format {
    display: flex;
    align-items: center;
    gap: $spacing-sm;

    @media (max-width: $breakpoint-md) {
      justify-content: center;
    }

    .format-label {
      font-size: 16px;
      color: #374151;
    }

    .format-select {
      width: 85px;

      :deep(.el-select__wrapper) {
        box-shadow: none;

        &:hover {
          border-color: $primary-color;
        }

        &.is-focus {
          border-color: $primary-color;
          box-shadow: 0 0 0 1px rgba($primary-color, 0.2);
        }
      }
    }
  }

  // 上传弹框样式
  :deep(.upload-dialog) {
    .el-dialog__header {
      padding-right: 0;
    }

    .el-dialog__body {
      padding: 0 20px 20px 20px;
    }

    .el-dialog__close {
      font-size: 18px;
      color: $gray;

      &:hover {
        color: $primary-color;
      }
    }
  }

  .upload-content {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }

  .upload-section {
    .upload-title {
      font-size: $font-size-medium;
      font-weight: $font-weight-medium;
      color: $primary-color;
      margin-bottom: $spacing-md;
    }

    .upload-dragger {
      :deep(.el-upload-dragger) {
        width: 100%;
        height: 120px;
        border: 2px dashed #d9d9d9;
        border-radius: $border-radius-md;
        background-color: #fafafa;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          border-color: $primary-color;
          background-color: rgba($primary-color, 0.05);
        }
      }

      :deep(.el-icon--upload) {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 65px;
      }
    }

    .upload-area {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;

      .upload-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: $spacing-xs;

        .upload-text {
          padding: $spacing-xs $spacing-md;
          background-color: $primary-color;
          color: white;
          border-radius: $border-radius-md;
          font-size: $font-size-small;
          font-weight: $font-weight-medium;
          min-width: 80px;
          text-align: center;
        }

        .upload-status {
          font-size: $font-size-small;
          color: #999;
        }
      }
    }
  }

  .upload-actions {
    display: flex;
    justify-content: center;

    .submit-btn {
      padding: $spacing-sm $spacing-xl;
      border-radius: $border-radius-md;
      font-weight: $font-weight-medium;
      min-width: 120px;
    }
  }

  @media (max-width: $breakpoint-md) {
    :deep(.upload-dialog) {
      .el-dialog {
        width: 90% !important;
        margin: 5vh auto;
      }

      .el-dialog__body {
        padding: $spacing-md;
      }
    }

    .upload-section {
      .upload-dragger {
        :deep(.el-upload-dragger) {
          height: 100px;
        }

        :deep(.el-icon--upload) {
          font-size: 36px;
        }
      }
    }
  }

  .initial-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;

    .loading-content {
      .elegant-spinner {
        position: relative;
        width: 60px;
        height: 60px;

        .spinner-ring {
          position: absolute;
          width: 100%;
          height: 100%;
          border: 2px solid transparent;
          border-radius: 50%;
          animation: elegant-spin 2s linear infinite;

          &:nth-child(1) {
            border-top-color: $primary-color;
            animation-delay: 0s;
          }

          &:nth-child(2) {
            border-right-color: $secondary-color;
            animation-delay: 0.3s;
            width: 80%;
            height: 80%;
            top: 10%;
            left: 10%;
          }

          &:nth-child(3) {
            border-bottom-color: rgba($primary-color, 0.3);
            animation-delay: 0.6s;
            width: 60%;
            height: 60%;
            top: 20%;
            left: 20%;
          }
        }
      }
    }
  }

  @keyframes elegant-spin {
    0% {
      transform: rotate(0deg);
      opacity: 1;
    }

    50% {
      opacity: 0.6;
    }

    100% {
      transform: rotate(360deg);
      opacity: 1;
    }
  }

  .data-link {
    color: #409eff;
    cursor: pointer;
    text-decoration: underline;

    &:hover {
      color: #66b1ff;
    }
  }

  .data-text {
    color: #606266;
    cursor: default;
  }

  // 解读相关样式
  .interpretation-loading {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;
    padding: $spacing-lg;

    .loading-text {
      text-align: center;
      color: $gray;
      font-size: $font-size-small;
    }
  }

  .interpretation-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacing-md;
    padding: $spacing-lg;
    text-align: center;

    .error-icon {
      font-size: 32px;
      color: #f56565;
    }

    .error-message {
      color: $gray;
      font-size: $font-size-small;
      line-height: 1.6;
    }

    .error-actions {
      display: flex;
      gap: $spacing-sm;
      justify-content: center;
    }
  }

  .interpretation-content {
    line-height: 1.8;
    text-align: justify;

    // 确保解读内容的样式与原文保持一致
    p {
      margin-bottom: $spacing-sm;
    }

    ul,
    ol {
      margin: $spacing-sm 0;
      padding-left: $spacing-lg;
    }

    li {
      margin-bottom: $spacing-xs;
    }
  }

  .interpretation-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: $spacing-md;
    padding: $spacing-lg;
    text-align: center;

    .empty-icon {
      font-size: 32px;
      color: #cbd5e0;
    }

    .empty-message {
      color: $gray;
      font-size: $font-size-small;
    }
  }

  // PubMed风格的参考文献样式
  .references-loading {
    padding: $spacing-lg;

    .loading-text {
      text-align: center;
      color: #666;
      font-size: 14px;
      margin-top: 10px;
    }
  }

  .no-references {
    padding: $spacing-lg;
    text-align: center;
  }

  .references-pubmed {
    .references-list {
      list-style: decimal;
      padding-left: 20px;
      margin: 0;
      counter-reset: reference-counter;

      .reference-item {
        margin-bottom: 8px;
        padding-left: 4px;
        line-height: 1.5;
        font-size: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .reference-content {
          .reference-text {
            color: #333;
            display: inline;
            font-family: Arial, sans-serif;
          }

          .reference-links {
            display: inline;

            .separator {
              color: #333;
              font-size: 16px;
              font-family: Arial, sans-serif;
            }

            .ref-link {
              display: inline;
              padding: 0;
              margin: 0;
              font-size: 16px;
              text-decoration: none;
              border: none;
              background: none;
              color: #0066cc;
              cursor: pointer;
              font-family: Arial, sans-serif;

              &:hover {
                text-decoration: underline;
              }

              &.plosp-link {
                color: #0066cc;
              }

              &.pubmed-link {
                color: #0066cc;
              }

              &.pmc-link {
                color: #0066cc;
              }

              &.doi-link {
                color: #0066cc;
              }
            }
          }
        }
      }
    }

    .show-more-references {
      margin-top: 12px;

      .el-button {
        background-color: #f5f5f5;
        border: 1px solid #ccc;
        color: #333;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 14px;

        &:hover {
          background-color: #e5e5e5;
          border-color: #999;
        }
      }
    }
  }

  // 文献传递弹窗样式
  .transmit-dialog {
    .captcha-container {
      display: flex;
      align-items: center;

      .captcha-image {
        width: 100px;
        height: 40px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f7fa;

        img {
          max-width: 100%;
          max-height: 100%;
        }

        .captcha-loading {
          font-size: 12px;
          color: #909399;
        }

        &:hover {
          border-color: #c0c4cc;
        }
      }
    }
  }
</style>
