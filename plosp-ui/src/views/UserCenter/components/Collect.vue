<template>
  <div class="collect-page">
    <div class="collect-content">
      <!-- 左侧收藏夹列表 -->
      <div class="left-sidebar">
        <div class="folders-card">
          <!-- 收藏夹列表 -->
          <div class="folders-list">
            <!-- 加载状态 -->
            <div v-if="foldersLoading" class="loading-container">
              <el-skeleton :rows="3" animated />
            </div>
            <!-- 收藏夹列表 -->
            <div v-else>
              <div
                v-for="folder in folders"
                :key="folder.id"
                :class="['folder-item', { active: selectedFolderId === folder.id }]"
              >
                <!-- 编辑状态 -->
                <div v-if="editingFolderId === folder.id" class="folder-edit-mode">
                  <div class="folder-edit-input">
                    <el-input
                      v-model="editingFolderName"
                      size="small"
                      maxlength="20"
                      @keyup.enter="confirmEdit(folder.id)"
                      @blur="handleBlur"
                    />
                  </div>
                  <div class="folder-edit-actions">
                    <el-button size="small" type="primary" @click="confirmEdit(folder.id)">确定</el-button>
                    <el-button size="small" @click="cancelEdit">取消</el-button>
                  </div>
                </div>
                <!-- 正常显示状态 -->
                <div v-else class="folder-normal-mode" @click="selectFolder(folder.id)">
                  <div class="folder-info">
                    <el-icon class="folder-icon">
                      <Folder />
                    </el-icon>
                    <span class="folder-name">{{ folder.folderName }}</span>
                  </div>
                  <div class="folder-actions">
                    <span class="folder-count">{{ folder.count || 0 }}</span>
                    <el-dropdown v-if="folder.status === 0" trigger="click" @command="handleFolderCommand">
                      <el-button
                        text
                        size="small"
                        class="folder-menu-btn"
                        @click.stop
                      >
                        <el-icon>
                          <MoreFilled />
                        </el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item :command="`edit_${folder.id}`">
                            <el-icon>
                              <Edit />
                            </el-icon>
                            编辑信息
                          </el-dropdown-item>
                          <el-dropdown-item :command="`delete_${folder.id}`" divided>
                            <el-icon>
                              <Delete />
                            </el-icon>
                            删除
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 新建收藏夹按钮 -->
          <div class="add-folder-footer">
            <el-button class="add-folder-btn" @click="showAddFolderDialog = true">
              <el-icon class="mr-1">
                <Plus />
              </el-icon>
              新建收藏夹
            </el-button>
          </div>
        </div>
      </div>

      <!-- 右侧文献列表 -->
      <div class="right-content">
        <div class="literature-card">
          <!-- 工具栏 -->
          <div class="toolbar">
            <div class="toolbar-left">
              <el-radio-group v-model="sort" size="large">
                <el-radio-button label="最近收藏" value="最近收藏" />
                <el-radio-button label="最多阅读" value="最多阅读" />
                <el-radio-button label="最多下载" value="最多下载" />
              </el-radio-group>
            </div>

            <div class="toolbar-right">
              <div class="select-all-section">
                <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
                <el-button
                  type="text"
                  size="small"
                  class="filter-btn"
                  @click="showSearchPanel = !showSearchPanel"
                >
                  <el-icon>
                    <Filter />
                  </el-icon>
                </el-button>
              </div>

              <div class="batch-actions">
                <el-button type="primary" @click="batchExport">批量导出</el-button>
                <el-button
                  type="danger"
                  class="btn-link"
                  :loading="isDeleting"
                  :disabled="isDeleting"
                  @click="batchRemoveFromFavorites"
                >
                  {{ isDeleting ? '处理中...' : '取消收藏' }}
                </el-button>
              </div>
            </div>
          </div>

          <el-collapse-transition>
            <!-- 搜索面板 -->
            <div v-show="showSearchPanel" class="search-panel">
              <div class="search-row">
                <div class="search-field">
                  <label class="search-label">文献ID</label>
                  <el-input v-model="searchFilters.id" placeholder="请输入文献ID" class="search-input" />
                </div>
                <div class="search-field">
                  <label class="search-label">文献标题</label>
                  <el-input v-model="searchFilters.title" placeholder="请输入文献标题" class="search-input" />
                </div>
                <div class="search-field">
                  <label class="search-label">作者</label>
                  <el-input v-model="searchFilters.author" placeholder="请输入作者" class="search-input" />
                </div>
              </div>
              <div class="search-row">
                <div class="search-field">
                  <label class="search-label">关键字</label>
                  <el-input v-model="searchFilters.keyword" placeholder="请输入关键字" class="search-input" />
                </div>
                <div class="search-field">
                  <label class="search-label">发表时间</label>
                  <el-select v-model="searchFilters.publishTime" placeholder="请选择发表时间" class="search-input">
                    <el-option label="不限" value="" />
                    <el-option label="近1年" value="1year" />
                    <el-option label="近3年" value="3years" />
                    <el-option label="近5年" value="5years" />
                  </el-select>
                </div>
                <div class="search-field search-buttons">
                  <el-button type="primary" @click="handleSearch">
                    <el-icon class="mr-1">
                      <Search />
                    </el-icon>
                    搜索
                  </el-button>
                  <el-button @click="handleResetSearch">重置</el-button>
                </div>
              </div>
            </div>
          </el-collapse-transition>

          <!-- 文献列表 -->
          <div class="literature-list">
            <!-- 加载状态 -->
            <div v-if="articlesLoading || articlesLoading" class="loading-state">
              <el-skeleton :rows="5" animated />
            </div>

            <!-- 空状态 -->
            <div v-else-if="filteredArticles.length === 0" class="empty-state">
              <el-empty description="暂无收藏的文献" :image-size="120">
                <template #description>
                  <span class="empty-description">暂无收藏文献</span>
                </template>
              </el-empty>
            </div>

            <!-- 文献项 -->
            <transition-group v-else name="literature-list" tag="div">
              <div v-for="article in filteredArticles" :key="article.id" class="literature-item">
                <div class="literature-checkbox">
                  <el-checkbox
                    :model-value="selectedArticles.includes(article.id)"
                    @update:model-value="checked => toggleArticleSelection(article.id, checked)"
                  />
                </div>

                <div class="literature-content">
                  <!-- 标题行 -->
                  <div class="title-row">
                    <h4 class="literature-title" @click="navigateToDetail(article)" v-html="article.title"></h4>
                  </div>

                  <!-- 作者 -->
                  <div class="literature-authors">
                    {{ formatAuthors(article.authors) }}
                  </div>

                  <!-- 期刊信息和标识符 -->
                  <div class="literature-meta-line">
                    <div class="literature-meta-line mb-0">
                      <div class="journal-info">
                        <span v-if="article.journal" class="journal-name">{{ article.journal }}.</span>
                        <span class="publication-year">{{ article.year }}</span>
                        <span class="volume-info">
                          {{ article.volume }}{{ article.issue ? `(${article.issue})` : ''
                          }}{{ article.page ? `:${article.page}` : '' }}
                        </span>
                      </div>
                      <div class="literature-ids">
                        <span v-if="article.pmid" class="id-tag pmid-tag">PMID: {{ article.pmid }}</span>
                        <span v-if="article.doi" class="id-tag doi-tag">DOI: {{ article.doi }}</span>
                      </div>
                    </div>
                    <span class="collect-time">{{ formatTime(article.collectTime) }}</span>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="literature-actions">
                  <el-button
                    type="danger"
                    size="small"
                    text
                    @click="removeFromCollection(article.id)"
                  >
                    <el-icon>
                      <Delete />
                    </el-icon>
                    取消收藏
                  </el-button>
                </div>
              </div>
            </transition-group>
          </div>
          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              class="pagination"
              @current-change="handlePageChange"
              @size-change="handlePageSizeChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 新建收藏夹对话框 -->
    <el-dialog v-model="showAddFolderDialog" title="新建收藏夹" width="400px">
      <el-form :model="newFolder" label-width="80px" @submit.prevent>
        <el-form-item label="收藏夹名">
          <el-input v-model="newFolder.name" placeholder="请输入收藏夹名称" @keyup.enter.stop="createFolder" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddFolderDialog = false">取消</el-button>
          <el-button type="primary" @click="createFolder">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, computed, reactive, onMounted, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { Folder, Plus, Filter, Search, MoreFilled, Edit, Delete } from '@element-plus/icons-vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    getFavoriteFolders,
    createFavoriteFolder,
    updateFavoriteFolder,
    deleteFavoriteFolder,
    getFavoriteDocuments,
    removeFromFavorites
  } from '@/api/folder'
  import { useAuthStore } from '@/stores/auth'
  import { formatAuthors } from '@/utils/formatUtils'
  // 获取用户信息和路由
  const authStore = useAuthStore()
  const router = useRouter()
  const currentUserId = computed(() => authStore.userInfo?.userId)

  // 响应式数据
  const selectedFolderId = ref(null)
  const selectAll = ref(false)
  const selectedArticles = ref([])
  const showSearchPanel = ref(false)
  const showAddFolderDialog = ref(false)
  const sort = ref('最近收藏')
  const total = ref(0)
  const foldersLoading = ref(false)
  const isSearching = ref(false) // 标记是否在搜索状态
  const isDeleting = ref(false) // 删除状态

  // 编辑相关状态
  const editingFolderId = ref(null)
  const editingFolderName = ref('')
  const isConfirming = ref(false) // 防止blur和click冲突

  // 分页数据
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10
  })

  // 搜索过滤器
  const searchFilters = ref({
    id: '',
    title: '',
    author: '',
    keyword: '',
    publishTime: ''
  })

  // 新建收藏夹表单
  const newFolder = ref({
    name: ''
  })

  // 收藏夹数据
  const folders = ref([])

  // 文献数据 - 改为动态加载
  const articles = ref([])
  const articlesLoading = ref(false)

  // 计算属性
  const filteredArticles = computed(() => {
    return articles.value
  })

  // 加载收藏夹列表
  const loadFolders = async() => {
    if (!currentUserId.value) {
      return
    }

    foldersLoading.value = true

    try {
      const response = await getFavoriteFolders()

      if (response.code === 200) {
        folders.value = response.data || []
        if (folders.value.length > 0 && !selectedFolderId.value) {
          selectedFolderId.value = folders.value[0].id
          // 选中收藏夹后加载对应的文献
          await loadArticlesForFolder(folders.value[0].id, false)
        }
      } else {
        ElMessage.error(response.msg || '获取收藏夹列表失败')
      }
    } catch (error) {
      ElMessage.error('获取收藏夹列表失败')
    } finally {
      foldersLoading.value = false
    }
  }

  // 将前端排序值转换为后端排序字段
  const getSortByValue = sortValue => {
    const sortMap = {
      最近收藏: 'createTime',
      最多阅读: 'hitNum',
      最多下载: 'download'
    }
    return sortMap[sortValue] || 'createTime'
  }

  // 处理搜索
  const handleSearch = () => {
    if (!selectedFolderId.value) {
      ElMessage.warning('请先选择收藏夹')
      return
    }
    // 标记为搜索状态
    isSearching.value = true
    // 重置分页到第一页
    pagination.currentPage = 1
    // 使用搜索过滤器加载文献
    loadArticlesForFolder(selectedFolderId.value, true)
  }

  // 重置搜索
  const handleResetSearch = () => {
    // 清空搜索条件
    searchFilters.value = {
      id: '',
      title: '',
      author: '',
      keyword: '',
      publishTime: ''
    }
    // 取消搜索状态
    isSearching.value = false
    // 重置分页到第一页
    pagination.currentPage = 1
    // 重新加载文献（不使用搜索过滤器）
    if (selectedFolderId.value) {
      loadArticlesForFolder(selectedFolderId.value, false)
    }
  }

  // 处理分页变化
  const handlePageChange = () => {
    if (selectedFolderId.value) {
      loadArticlesForFolder(selectedFolderId.value, isSearching.value)
    }
  }

  // 处理每页大小变化
  const handlePageSizeChange = () => {
    // 重置到第一页
    pagination.currentPage = 1
    if (selectedFolderId.value) {
      loadArticlesForFolder(selectedFolderId.value, isSearching.value)
    }
  }

  // 加载收藏的文献列表
  const loadArticlesForFolder = async(folderId, useSearchFilters = false) => {
    if (!folderId || !currentUserId.value) {
      articles.value = []
      return
    }

    articlesLoading.value = true
    try {
      // 构建请求参数
      const params = {
        folderId,
        pageNum: pagination.currentPage,
        pageSize: pagination.pageSize,
        sortBy: getSortByValue(sort.value)
      }

      // 如果使用搜索过滤器，添加搜索参数
      if (useSearchFilters) {
        if (searchFilters.value.id) {
          params.id = searchFilters.value.id
        }
        if (searchFilters.value.title) {
          params.title = searchFilters.value.title
        }
        if (searchFilters.value.author) {
          params.author = searchFilters.value.author
        }
        if (searchFilters.value.keyword) {
          params.keyword = searchFilters.value.keyword
        }
        if (searchFilters.value.publishTime) {
          // 将发表时间选项转换为年份
          const currentYear = new Date().getFullYear()
          switch (searchFilters.value.publishTime) {
            case '1year':
              params.publisherYear = currentYear - 1
              break
            case '3years':
              params.publisherYear = currentYear - 3
              break
            case '5years':
              params.publisherYear = currentYear - 5
              break
          }
        }
      }

      const response = await getFavoriteDocuments(params)

      if (response.code === 200) {
        articles.value = response.rows || []
        total.value = response.total || 0
      } else {
        ElMessage.error(response.msg || '获取文献列表失败')
      }
    } catch (error) {
      ElMessage.error('获取文献列表失败')
    } finally {
      articlesLoading.value = false
    }
  }

  // 选择收藏夹
  const selectFolder = async folderId => {
    selectedFolderId.value = folderId
    selectedArticles.value = []
    selectAll.value = false
    // 重置搜索状态
    isSearching.value = false
    // 重置分页
    pagination.currentPage = 1
    // 加载选中收藏夹的文献
    await loadArticlesForFolder(folderId, false)
  }

  // 开始编辑收藏夹
  const startEdit = folderId => {
    const folder = folders.value.find(f => f.id === folderId)
    if (folder) {
      editingFolderId.value = folderId
      editingFolderName.value = folder.folderName
    }
  }

  // 确认编辑
  const confirmEdit = async folderId => {
    isConfirming.value = true

    if (!editingFolderName.value.trim()) {
      ElMessage.warning('请输入收藏夹名称')
      isConfirming.value = false
      return
    }

    // 获取原始收藏夹信息
    const originalFolder = folders.value.find(f => f.id === folderId)
    const newName = editingFolderName.value.trim()

    // 如果名称没有变化，直接取消编辑
    if (originalFolder && originalFolder.folderName === newName) {
      editingFolderId.value = null
      editingFolderName.value = ''
      isConfirming.value = false
      return
    }

    try {
      const response = await updateFavoriteFolder({
        id: folderId,
        folderName: newName
      })

      if (response.code === 200) {
        ElMessage.success('修改成功')
        editingFolderId.value = null
        editingFolderName.value = ''
        await loadFolders()
      } else {
        ElMessage.error(response.msg || '修改失败')
      }
    } catch (error) {
      ElMessage.error('修改失败')
    } finally {
      isConfirming.value = false
    }
  }

  // 处理输入框失去焦点
  const handleBlur = () => {
    // 如果正在确认编辑，不执行取消操作
    if (isConfirming.value) {
      return
    }
    // 延迟执行，给确认按钮点击事件时间
    setTimeout(() => {
      if (!isConfirming.value) {
        cancelEdit()
      }
    }, 100)
  }

  // 取消编辑
  const cancelEdit = () => {
    editingFolderId.value = null
    editingFolderName.value = ''
    isConfirming.value = false
  }

  // 删除收藏夹
  const deleteFolder = async folderId => {
    try {
      await ElMessageBox.confirm('确定要删除这个收藏夹吗？删除后收藏夹内的收藏文献也会被清空', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const response = await deleteFavoriteFolder(folderId)

      if (response.code === 200) {
        ElMessage.success('删除成功')
        if (selectedFolderId.value === folderId) {
          selectedFolderId.value = null
        }
        await loadFolders()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 批量取消收藏
  const batchRemoveFromFavorites = async() => {
    if (selectedArticles.value.length === 0) {
      ElMessage.warning('请先选择要取消收藏的文献')
      return
    }

    if (isDeleting.value) {
      ElMessage.warning('正在处理中，请稍候...')
      return
    }

    try {
      await ElMessageBox.confirm(`确定要取消收藏选中的 ${selectedArticles.value.length} 篇文献吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      isDeleting.value = true

      // selectedArticles 中存储的就是文献ID
      const docIds = selectedArticles.value

      const response = await removeFromFavorites(docIds)

      if (response.code === 200) {
        ElMessage.success('批量取消收藏成功')
        // 清空选中状态
        selectedArticles.value = []
        selectAll.value = false
        // 重新加载当前收藏夹的文献
        await loadArticlesForFolder(selectedFolderId.value)
        // 更新收藏夹数量
        const folder = folders.value.find(f => f.id === selectedFolderId.value)
        if (folder) {
          folder.count -= docIds.length
        }
      } else {
        ElMessage.error(response.msg || '批量取消收藏失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('批量取消收藏失败')
      }
    } finally {
      isDeleting.value = false
    }
  }

  // 批量导出
  const batchExport = () => {
    if (selectedArticles.value.length === 0) {
      ElMessage.warning('请先选择要导出的文献')
      return
    }
    ElMessage.info(`已选择 ${selectedArticles.value.length} 篇文献，导出功能待实现`)
  }

  // 处理收藏夹操作命令
  const handleFolderCommand = command => {
    const [action, folderId] = command.split('_')
    if (action === 'edit') {
      startEdit(folderId)
    } else if (action === 'delete') {
      deleteFolder(folderId)
    }
  }

  const handleSelectAll = checked => {
    if (checked) {
      selectedArticles.value = filteredArticles.value.map(article => article.id)
    } else {
      selectedArticles.value = []
    }
  }

  const toggleArticleSelection = (articleId, checked) => {
    if (checked) {
      // 添加到选中列表
      if (!selectedArticles.value.includes(articleId)) {
        selectedArticles.value.push(articleId)
      }
    } else {
      // 从选中列表移除
      const index = selectedArticles.value.indexOf(articleId)
      if (index > -1) {
        selectedArticles.value.splice(index, 1)
      }
    }

    // 更新全选状态
    selectAll.value = selectedArticles.value.length === filteredArticles.value.length
  }

  // 移除收藏文献
  const removeFromCollection = async id => {
    if (!selectedFolderId.value) {
      ElMessage.warning('请先选择收藏夹')
      return
    }

    if (!currentUserId.value) {
      ElMessage.error('用户信息获取失败')
      return
    }

    try {
      await ElMessageBox.confirm('确定要移除这篇文献吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      const response = await removeFromFavorites([id])

      if (response.code === 200) {
        ElMessage.success('移除成功')
        await loadArticlesForFolder(selectedFolderId.value)
        const folder = folders.value.find(f => f.id === selectedFolderId.value)
        if (folder) {
          folder.count -= 1
        }
      } else {
        ElMessage.error(response.msg || '移除失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('移除失败')
      }
    }
  }

  const createFolder = async() => {
    if (!newFolder.value.name.trim()) {
      ElMessage.warning('请输入收藏夹名称')
      return
    }

    if (!currentUserId.value) {
      // 尝试重新获取用户信息
      if (authStore.token) {
        try {
          await authStore.fetchUserInfo()
          if (!currentUserId.value) {
            ElMessage.error('用户信息获取失败，请重新登录')
            return
          }
        } catch (error) {
          ElMessage.error('用户信息获取失败，请重新登录')
          return
        }
      } else {
        ElMessage.error('请先登录')
        return
      }
    }

    try {
      const response = await createFavoriteFolder({
        folderName: newFolder.value.name.trim()
      })

      if (response.code === 200) {
        ElMessage.success('创建成功')
        newFolder.value.name = ''
        showAddFolderDialog.value = false
        // 重新加载收藏夹列表
        await loadFolders()
      } else {
        ElMessage.error(response.msg || '创建失败')
      }
    } catch (error) {
      console.log(`创建失败: ${error.message || '未知错误'}`)
    }
  }

  // 监听排序变化
  watch(sort, () => {
    if (selectedFolderId.value) {
      // 重置分页到第一页
      pagination.currentPage = 1
      // 重新加载当前收藏夹的文献（保持当前搜索状态）
      loadArticlesForFolder(selectedFolderId.value, isSearching.value)
    }
  })

  // 只在用户ID变化时清空数据，不自动加载
  watch(currentUserId, newUserId => {
    if (!newUserId) {
      folders.value = []
      selectedFolderId.value = null
    }
  })

  // 格式化时间
  const formatTime = time => {
    if (!time) return ''
    const date = new Date(time)
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}`
  }

  // 导航到文献详情页
  const navigateToDetail = article => {
    // 根据文献的ID或PMID跳转到详情页
    const id = article.pmid || article.id
    if (id) {
      router.push(`/detail/${id}`)
    } else {
      ElMessage.warning('无法获取文献ID')
    }
  }

  // 组件挂载时加载数据
  onMounted(async() => {
    // 如果有token但没有用户信息，尝试获取用户信息
    if (authStore.token && !authStore.userInfo) {
      try {
        await authStore.fetchUserInfo()
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    }

    // 确保有用户信息后加载收藏夹
    if (currentUserId.value) {
      await loadFolders()
    }
  })
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/variables';

  .collect-page {
    padding: 0;
  }

  .collect-content {
    display: flex;
    gap: $spacing-xl;
    align-items: stretch;

    @media (max-width: $breakpoint-lg) {
      flex-direction: column;
      gap: $spacing-lg;
    }
  }

  .left-sidebar {
    flex: 0 0 280px;

    @media (max-width: $breakpoint-lg) {
      flex: none;
      width: 100%;
    }
  }

  .right-content {
    flex: 1;
    min-width: 0;
  }

  // 收藏夹卡片样式
  .folders-card {
    background: white;
    border-radius: $border-radius-lg;
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
    min-height: 600px;
  }

  .btn-link {
    background: #ffffff;
    border: 1px solid $primary-color;
    color: $primary-color;

    &:hover {
      background: #e9edf1;
      color: $primary-color;
    }
  }

  // 文献卡片样式
  .literature-card {
    background: white;
    border-radius: $border-radius-lg;
    padding: $spacing-md $spacing-lg;
    border: 1px solid rgba(226, 232, 240, 0.5);
  }

  // 新建收藏夹底部
  .add-folder-footer {
    border-top: 1px solid rgba(226, 232, 240, 0.6);
    position: relative;
    bottom: 0;
    width: 100%;
  }

  //分页
  .pagination-wrapper {
    margin-top: 20px;

    .el-pagination {
      justify-content: center;
    }
  }

  // 收藏夹列表样式
  .folders-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-xs;
    min-height: fit-content;
    overflow-y: auto;
    flex: 1;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .folder-item {
    padding: $spacing-sm $spacing-md;
    border-radius: $border-radius-md;
    background: white;
    transition: all 0.3s ease;
    font-size: 16px;
    margin-bottom: $spacing-xs;

    &:hover {
      background: #e5ebf1;
      border-color: rgba(148, 163, 184, 0.4);

      .folder-count {
        opacity: 0;
        visibility: hidden;
      }

      .folder-menu-btn {
        opacity: 1;
        visibility: visible;
      }
    }

    &.active {
      background: #003d71;
      color: white;
      border-color: #003d71;

      .folder-name {
        color: white;
        font-size: 16px;
      }

      .folder-icon {
        color: white !important;
      }

      .folder-count {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        opacity: 1;
        visibility: visible;
      }

      .folder-menu-btn {
        color: white;
        opacity: 0;
        visibility: hidden;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      }

      &:hover {
        .folder-count {
          opacity: 0;
          visibility: hidden;
        }

        .folder-menu-btn {
          opacity: 1;
          visibility: visible;
        }
      }
    }

    // 正常显示模式
    .folder-normal-mode {
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      width: 100%;
    }

    // 编辑模式
    .folder-edit-mode {
      width: 100%;

      .folder-edit-input {
        margin-bottom: $spacing-xs;
      }

      .folder-edit-actions {
        display: flex;
        gap: $spacing-xs;
        justify-content: flex-end;
      }
    }

    .folder-info {
      display: flex;
      align-items: center;
      gap: $spacing-xs;

      .folder-icon {
        font-size: 18px;
        color: #003d71;
        transition: color 0.3s ease;
      }
    }

    .folder-actions {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      position: relative;
      min-width: 32px;
      justify-content: flex-end;
    }

    .folder-count {
      background: rgba(148, 163, 184, 0.2);
      color: $gray;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: $font-size-small;
      font-weight: $font-weight-bold;
      min-width: 24px;
      text-align: center;
      transition: all 0.3s ease;
      opacity: 1;
      visibility: visible;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    .folder-menu-btn {
      padding: 4px;
      color: $gray;
      transition: all 0.3s ease;
      opacity: 0;
      visibility: hidden;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);

      &:hover {
        background: rgba(148, 163, 184, 0.1);
        color: $primary-color;
      }
    }
  }

  // 加载状态样式
  .loading-state {
    padding: $spacing-xxl;

    :deep(.el-skeleton__item) {
      margin-bottom: $spacing-md;
    }
  }

  // 空状态样式
  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: $spacing-xxl;

    .empty-description {
      color: #6b7280;
      font-size: $font-size-medium;
    }

    :deep(.el-empty__image) {
      margin-bottom: $spacing-lg;
    }

    :deep(.el-empty__description) {
      margin-top: $spacing-md;
    }
  }

  // 文献列表动画
  .literature-list-enter-active,
  .literature-list-leave-active {
    transition: all 0.3s ease;
  }

  .literature-list-enter-from {
    opacity: 0;
    transform: translateX(-30px);
  }

  .literature-list-leave-to {
    opacity: 0;
    transform: translateX(30px);
  }

  .literature-list-move {
    transition: transform 0.3s ease;
  }

  .add-folder-btn {
    width: 100%;
    background: #ffffff;
    border: none;
    color: #00416f;
    font-weight: $font-weight-bold;
    padding: $spacing-md $spacing-lg;
    border-radius: $border-radius-md;
    transition: all 0.3s ease;
    height: 44px;

    &:hover {
      background: #e3ebf1;
      color: #003d71;
      transform: translateY(-1px);
      //box-shadow: 0 4px 12px rgba(79, 195, 247, 0.3);
    }
  }

  // 工具栏样式
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;
    padding-bottom: $spacing-sm;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    flex-wrap: wrap;
    gap: $spacing-sm;

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      align-items: stretch;
    }
  }

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: $spacing-md;
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    flex-wrap: wrap;

    @media (max-width: $breakpoint-md) {
      justify-content: space-between;
    }
  }

  .toolbar-left {
    .el-radio-button.is-active {
      :deep(.el-radio-button__inner) {
        background: #2e4e73;
        color: #ffffff;
      }
    }
  }

  .select-all-section {
    display: flex;
    align-items: center;
    gap: $spacing-xs;

    .filter-btn {
      padding: 6px;
      color: $primary-color;

      &:hover {
        background: rgba(4, 56, 115, 0.1);
      }
    }
  }

  .batch-actions {
    display: flex;
    align-items: center;
    gap: $spacing-xs;

    .selected-count {
      font-size: $font-size-small;
      color: $primary-color;
      font-weight: $font-weight-medium;
    }
  }

  // 搜索面板样式
  .search-panel {
    background: rgba(241, 245, 249, 0.5);
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: $border-radius-md;
    padding: $spacing-lg;
    margin-bottom: $spacing-md;

    .search-row {
      display: flex;
      gap: $spacing-lg;
      margin-bottom: $spacing-md;
      align-items: flex-end;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .search-field {
      flex: 1;
      min-width: 150px;

      .search-label {
        display: block;
        font-size: $font-size-small;
        color: $gray;
        font-weight: $font-weight-medium;
        margin-bottom: $spacing-xs;
      }

      .search-input {
        width: 100%;
      }

      &.search-buttons {
        display: flex;
        align-items: flex-end;
        gap: $spacing-sm;

        .el-button {
          margin-bottom: 0;
        }
      }
    }
  }

  // 文献列表样式
  .literature-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
  }

  .literature-item {
    display: flex;
    gap: $spacing-sm;
    padding: $spacing-sm $spacing-md;
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: $border-radius-md;
    background: white;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(148, 163, 184, 0.4);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);

      .literature-actions {
        opacity: 1;
      }
    }

    .literature-checkbox {
      flex-shrink: 0;
      display: flex;
      align-items: flex-start;
      padding-top: 2px;
    }

    .literature-content {
      flex: 1;
      min-width: 0;
    }

    .literature-actions {
      flex-shrink: 0;
      display: flex;
      opacity: 0;
      transition: opacity 0.2s ease;
      margin-left: auto;
      height: 20px;
    }
  }

  .title-row {
    display: flex;
    align-items: flex-start;
    gap: $spacing-xs;
    margin-bottom: $spacing-xs;
  }

  .source-tag {
    flex-shrink: 0;
    padding: 2px 14px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: $font-weight-bold;
    margin-top: 2px;

    &.pubmed {
      background: #e3f2fd;
      color: #1976d2;
    }

    &.pmc {
      background: #f3e5f5;
      color: #7b1fa2;
    }

    &.biorxiv {
      background: #e8f5e8;
      color: #388e3c;
    }
  }

  // 表格样式
  :deep(.el-table__header) {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));

    th {
      background: #fafbfd;
      color: $gray;
      font-weight: $font-weight-bold;
      font-size: $font-size-small;
      border-bottom: 2px solid rgba(226, 232, 240, 0.8);
    }
  }

  .literature-title {
    font-size: $font-size-medium;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;
    line-height: 1.5;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;

    &:hover {
      color: #2563eb;
    }
  }

  .literature-description {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .literature-authors {
    font-size: $font-size-small;
    color: #575757;
    margin-bottom: $spacing-xs;
    line-height: 1.4;
    font-weight: $font-weight-medium;
  }

  .literature-meta-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: $spacing-md;
    font-size: 14px;
    color: #374151;
    margin-bottom: $spacing-xxs;

    .journal-info {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      font-size: $font-size-small;
      flex: 1;
      min-width: 0;

      .journal-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        min-width: 0;
        max-width: 300px;
      }

      .publication-year {
        color: #6b7280;
        flex-shrink: 0;
      }

      .volume-info {
        color: #6b7280;
        flex-shrink: 0;
      }
    }

    .literature-ids {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      flex-shrink: 0;

      .id-tag {
        display: inline-flex;
        align-items: center;
        padding: 3px 8px;
        border-radius: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        white-space: nowrap;

        &.pmid-tag,
        &.doi-tag {
          background: #f2f7fb;
          color: #374151;

          &:hover {
            background: #dae8fa;
          }
        }
      }
    }

    .collect-time {
      font-size: $font-size-small;
      color: #6b7280;
      flex-shrink: 0;
    }
  }

  // 响应式调整
  @media (max-width: $breakpoint-md) {
    .collect-content {
      gap: $spacing-md;
    }

    .left-sidebar {
      flex: none;
      width: 100%;
    }

    .section-card {
      padding: $spacing-sm $spacing-md;
    }

    .literature-item {
      padding: $spacing-xs $spacing-sm;
      flex-direction: column;
      gap: $spacing-xs;

      .literature-checkbox {
        align-self: flex-start;
      }
    }

    .literature-content {
      .title-row {
        flex-direction: column;
        gap: $spacing-xxs;
      }

      .literature-title {
        font-size: $font-size-small;
      }

      .literature-authors {
        font-size: 14px;
        margin-bottom: $spacing-xxs;
      }

      .literature-meta-line {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-xs;

        .journal-info {
          gap: 2px;

          .journal-name {
            max-width: 200px;
          }
        }

        .literature-ids {
          gap: $spacing-xxs;
          flex-wrap: wrap;
          width: 100%;

          .id-tag {
            padding: 1px 6px;
          }

          .collect-time {
            font-size: 10px;
          }
        }
      }
    }

    .search-panel {
      .search-row {
        flex-direction: column;
        gap: $spacing-sm;

        .search-field {
          min-width: 100%;
        }
      }
    }

    .toolbar {
      .toolbar-left,
      .toolbar-right {
        width: 100%;
        justify-content: space-between;
      }

      .sort-buttons {
        flex-wrap: wrap;
      }

      .batch-actions {
        flex-wrap: wrap;
        justify-content: flex-start;
      }
    }
  }

  // 对话框样式调整
  .el-dialog {
    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: $spacing-sm;
    }
  }
</style>
