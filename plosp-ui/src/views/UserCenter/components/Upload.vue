<template>
  <div class="error-correction-page">
    <div class="page-content">
      <!-- 表格区域 -->
      <div class="table-section">
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <h3 class="section-title">
                <el-icon><Promotion /></el-icon>
                我的上传
              </h3>
              <div class="table-info">
                共 {{ total }} 条记录
              </div>
            </div>
          </template>
          <div class="search-section">
            <el-form :model="searchForm" class="search-form" :inline="true">
              <el-form-item label="搜索词" class="form-item">
                <el-input
                  v-model="searchForm.articleId"
                  placeholder="PMID/PMCID/DOI/标题"
                  clearable
                  class="search-input"
                />
              </el-form-item>
              <el-form-item label="状态" class="form-item">
                <el-select
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  class="search-input"
                >
                  <el-option label="全部" value="" />
                  <el-option label="待审核" value="pending" />
                  <el-option label="已接受" value="accepted" />
                  <el-option label="已驳回" value="rejected" />
                </el-select>
              </el-form-item>

              <el-form-item label="上传时间" class="form-item date-range">
                <el-date-picker
                  v-model="searchForm.uploadTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="起始时间"
                  end-placeholder="截止时间"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  class="search-input date-picker"
                />
              </el-form-item>

              <el-form-item class="form-item search-actions">
                <el-button type="primary" class="search-btn" @click="handleSearch">
                  <el-icon class="mr-1"><Search /></el-icon>
                  搜索
                </el-button>
                <el-button class="reset-btn" @click="handleReset">
                  <el-icon class="mr-1"><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 上传记录列表 -->
          <div class="upload-list">
            <div
              v-for="upload in tableData"
              :key="upload.id"
              class="upload-item"
            >
              <div class="upload-content">
                <!-- 标题行 -->
                <div class="title-row">
                  <h4 class="literature-title">{{ upload.title }}</h4>
                  <div class="upload-header">
                    <el-tag
                      :type="getStatusTagType(upload.status)"
                      class="status-tag"
                    >
                      {{ getStatusText(upload.status) }}
                    </el-tag>
                  </div>
                </div>

                <!-- 作者行 -->
                <div class="authors-row">
                  <div class="literature-authors">
                    {{ upload.authors }}
                  </div>
                </div>

                <!-- 期刊信息和时间 -->
                <div class="upload-meta-line">
                  <div class="journal-info">
                    <div class="d-flex gap-8">
                      <span class="journal-name">{{ upload.journal }}</span>
                      <span class="publication-year">{{ upload.year }}</span>
                      <span class="volume-info">{{ upload.volume }}({{ upload.issue }}):{{ upload.pages }}</span>
                    </div>

                    <div class="literature-ids">
                      <span v-if="upload.pmid" class="id-tag pmid-tag">
                        PMID: {{ upload.pmid }}
                      </span>
                      <span v-if="upload.doi" class="id-tag doi-tag">
                        DOI: {{ upload.doi }}
                      </span>
                    </div>
                  </div>
                  <div class="upload-time">{{ upload.uploadTime }}</div>
                </div>
                <div v-if="upload.pdfFile" class="pdf-download">
                  <span class="pdf-filename">{{ upload.pdfFile }}</span>
                </div>
                <!-- 驳回原因行 -->
                <div v-if="upload.status === 'rejected'" class="reject-info-row">
                  <div class="reject-quote">
                    <span class="reject-content">驳回原因：{{ upload.rejectReason }}</span>
                  </div>
                </div>

              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              class="pagination"
            />
          </div>
        </el-card>
      </div>
    </div>


  </div>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue'
  import {
    Search,
    Refresh,
    Promotion
  } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'

  // 响应式数据
  const total = ref(0)

  // 搜索表单
  const searchForm = reactive({
    articleId: '',
    status: '',
    uploadTimeRange: null
  })

  // 分页数据
  const pagination = reactive({
    currentPage: 1,
    pageSize: 20
  })

  // 表格数据
  const tableData = ref([
    {
      id: 1,
      articleId: '30715263',
      title: '新型冠状病毒感染中医药防治的临床研究进展及其机制分析',
      uploadTime: '2024-01-15',
      status: 'pending',
      authors: 'Zhang L, Wang Y, Li J, Wang K, Chen M, Sun E',
      rejectReason: '文献格式不符合要求，请按照期刊标准格式重新整理后提交。',
      journal: 'Nature',
      year: '2024',
      volume: '615',
      issue: '7952',
      pages: '456-468',
      doi: '10.1038/s41586-024-07123-4',
      pmid: '38234567',
      pdfFile: 'COVID19_TCM_Research_Analysis.pdf'
    },
    {
      id: 2,
      articleId: '30715264',
      title: 'PD-1/PD-L1免疫检查点抑制剂在非小细胞肺癌中的应用及其耐药机制',
      uploadTime: '2024-01-14',
      status: 'accepted',
      authors: 'Chen H, Liu Y, Yang R, Zhao W, Wang J, Dong Z',
      journal: 'Nature',
      year: '2024',
      volume: '615',
      issue: '7952',
      pages: '123-135',
      doi: '10.1038/s41586-024-07456-7',
      pmid: '38345678',
      pdfFile: 'PD1_PDL1_NSCLC_Resistance.pdf'
    },
    {
      id: 3,
      articleId: '30715265',
      title: '基于深度学习的医学影像诊断技术在早期癌症检测中的应用研究',
      uploadTime: '2024-01-13',
      status: 'rejected',
      rejectTime: '2024-01-14',
      rejectReason: '文献质量不符合发表标准，数据分析方法存在缺陷，建议重新设计实验方案。',
      journal: 'Science',
      year: '2024',
      volume: '383',
      issue: '6630',
      authors: 'Li Q, Wu J, Xu L, Huang P, Zhang J',
      pages: '789-801',
      doi: '10.1126/science.abcd5678',
      pmid: '38456789',
      pdfFile: 'DeepLearning_Medical_Imaging_Cancer.pdf'
    },
    {
      id: 4,
      articleId: '30715266',
      title: '机器学习在药物发现中的应用：当前挑战与未来展望',
      correctionType: 'category',
      uploadTime: '2024-01-12',
      status: 'accepted',
      rejectReason: '提交的关键词修改建议与文献主题不符，现有关键词准确反映了文献内容。',
      description: '本研究通过系统性回顾和荟萃分析，探讨了中医药在新型冠状病毒感染防治中的临床应用效果及其潜在机制，为中西医结合治疗提供了重要的理论依据和实践指导。',
      authors: 'Zhang L, Wang Y, Li J, Wang K, Chen M, Sun E',
      journal: 'Cell',
      year: '2024',
      volume: '187',
      issue: '3',
      pages: '567-580',
      doi: '10.1016/j.cell.2024.01.012',
      pmid: '38567890',
      pdfFile: 'COVID19_TCM_Research_Analysis.pdf'
    },
    {
      id: 5,
      articleId: '30715267',
      title: '气候变化对全球农业生产力的影响：基于卫星数据的综合评估',
      correctionType: 'figures',
      uploadTime: '2024-01-11',
      status: 'rejected',
      rejectTime: '2024-01-12',
      authors: 'Zhang L, Wang Y, Li J, Wang K, Chen M, Sun E',
      rejectReason: '图表标注信息经核实无误，您提交的修改建议与原始数据不符。',
      description: '本研究通过系统性回顾和荟萃分析，探讨了中医药在新型冠状病毒感染防治中的临床应用效果及其潜在机制，为中西医结合治疗提供了重要的理论依据和实践指导。',

      journal: 'Nature Climate Change',
      year: '2024',
      volume: '14',
      issue: '2',
      pages: '123-138',
      doi: '10.1038/s41558-024-01234-5',
      pmid: '38678901',
      pdfFile: 'COVID19_TCM_Research_Analysis.pdf'
    },
    {
      id: 6,
      articleId: '30715268',
      title: '量子计算在密码学中的应用：当前挑战与未来机遇',
      correctionType: 'author',
      uploadTime: '2024-01-10',
      authors: 'Zhang L, Wang Y, Li J, Wang K, Chen M, Sun E',
      status: 'pending',
      description: '本研究通过系统性回顾和荟萃分析，探讨了中医药在新型冠状病毒感染防治中的临床应用效果及其潜在机制，为中西医结合治疗提供了重要的理论依据和实践指导。',
      rejectReason: '提交的关键词修改建议与文献主题不符，现有关键词准确反映了文献内容。',
      journal: 'Nature Physics',
      year: '2024',
      volume: '20',
      issue: '1',
      pages: '45-58',
      doi: '10.1038/s41567-024-02345-6',
      pmid: '38789012',
      pdfFile: 'COVID19_TCM_Research_Analysis.pdf'
    },
    {
      id: 7,
      articleId: '30715269',
      title: '纳米材料在生物医学中的应用及其安全性评估',
      correctionType: 'references',
      uploadTime: '2024-01-09',
      authors: 'Zhang L, Wang Y, Li J, Wang K, Chen M, Sun E',
      status: 'accepted',
      description: '本研究通过系统性回顾和荟萃分析，探讨了中医药在新型冠状病毒感染防治中的临床应用效果及其潜在机制，为中西医结合治疗提供了重要的理论依据和实践指导。',
      journal: 'Biomaterials',
      rejectReason: '提交的关键词修改建议与文献主题不符，现有关键词准确反映了文献内容。',
      year: '2024',
      volume: '298',
      issue: '',
      pages: '122045',
      doi: '10.1016/j.biomaterials.2024.122045',
      pmid: '38890123',
      pdfFile: 'COVID19_TCM_Research_Analysis.pdf'
    }
  ])

  // 方法
  const handleSearch = () => {
    ElMessage.success('搜索功能待实现')
  }

  const handleReset = () => {
    Object.keys(searchForm).forEach(key => {
      if (key === 'uploadTimeRange') {
        searchForm[key] = null
      } else {
        searchForm[key] = ''
      }
    })
    ElMessage.success('重置成功')
  }

  // PDF下载处理
  const handlePdfDownload = upload => {
    // 这里应该调用实际的下载API
    ElMessage.success(`正在下载：${upload.pdfFile}`)
    // 实际实现中应该是：
    // window.open(`/api/download/pdf/${upload.id}`)
  }
  // 获取状态文本
  const getStatusText = status => {
    const statusMap = {
      pending: '待审核',
      accepted: '已接受',
      rejected: '已驳回'
    }
    return statusMap[status] || status
  }

  // 获取状态标签类型
  const getStatusTagType = status => {
    const statusMap = {
      pending: 'warning',
      accepted: 'success',
      rejected: 'danger'
    }
    return statusMap[status] || ''
  }





  // 初始化
  onMounted(() => {
    total.value = tableData.value.length
  })
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.error-correction-page {
  padding: 0;
}

.page-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

// 通用卡片样式
.search-card,
.table-card {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04), 0 1px 4px rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
  }

  :deep(.el-card__header) {
    padding: $spacing-md $spacing-lg;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
  }

  :deep(.el-card__body) {
    padding: $spacing-lg;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .section-title {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: 18px;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;

    .el-icon {
      font-size: 28px;
      background: linear-gradient(135deg, $primary-color, #2563eb);
      color: white;
      padding: 6px;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(4, 56, 115, 0.2);
    }
  }

  .table-info {
    font-size: $font-size-small;
    color: #6b7280;
    font-weight: $font-weight-medium;
  }
}

// 搜索表单样式
.search-form {
  .form-item {
    margin-bottom: $spacing-md;
    margin-right: $spacing-lg;

    :deep(.el-form-item__label) {
      font-weight: $font-weight-medium;
      color: $gray;
      font-size: $font-size-small;
      width: 80px;
      text-align: right;
      padding-right: $spacing-sm;
    }

    :deep(.el-form-item__content) {
      flex: 1;
      min-width: 200px;
    }

    &.date-range {
      :deep(.el-form-item__content) {
        min-width: 300px;
      }
    }

    &.search-actions {
      :deep(.el-form-item__label) {
        width: 0;
      }

      :deep(.el-form-item__content) {
        margin-left: 0;
      }
    }
  }

  .search-input {
    width: 100%;
  }

  .date-picker {
    width: 100%;
  }

  .search-btn {
    background:  $primary-color;
    border: none;
    color: white;
    font-weight: $font-weight-bold;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }

  .reset-btn {
    background: white;
    border: 1px solid rgba(226, 232, 240, 0.8);
    color: $gray;
    font-weight: $font-weight-medium;
    transition: all 0.3s ease;

    &:hover {
      border-color: $primary-color;
      color: $primary-color;
      transform: translateY(-1px);
    }
  }
}

// 上传记录列表样式
.upload-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.upload-item {
  padding: $spacing-md;
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: $border-radius-md;
  background: white;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(148, 163, 184, 0.4);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  }

  .upload-content {
    width: 100%;
  }
}

.title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: $spacing-xs;
  margin-bottom: $spacing-xs;

  .upload-header {
    display: flex;
    align-items: center;
    flex-shrink: 0;

    .status-tag {
      font-size: 14px;
      cursor: pointer;
    }
  }
}

.literature-title {
  font-size: $font-size-medium;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin: 0;
  line-height: 1.5;
  flex: 1;
}

// 作者行样式
.authors-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-xs;

  .literature-authors {
    flex: 1;
    font-size: $font-size-small;
    color: #374151;
  }
}
.pdf-download {
  margin-bottom:8px;

  .pdf-filename {
    font-size: $font-size-small;
    color: #4477dd;
    cursor: pointer;

    &:hover {
      color: #2165ed;
      text-decoration: underline;
    }
  }
}
// 驳回原因行
.reject-info-row {
  margin-bottom: $spacing-xs;

  .reject-quote {
    position: relative;
    padding: $spacing-sm $spacing-md;
    background: #F8F9FA;
    border-radius: 0 4px 4px 0;
    font-size: $font-size-small;
    color: #495057;
    line-height: 1.5;
    border-left: 3px solid gray;

    .reject-content {
      display: block;
      font-style: italic;
    }
  }
}

.upload-meta-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: $spacing-md;
  font-size: 14px;
  color: #374151;
  margin: $spacing-xs 0;

  .journal-info {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: $font-size-small;

    .publication-year {
      color: #6b7280;
    }

    .volume-info {
      color: #6b7280;
    }
  }

  .literature-ids {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    flex-shrink: 0;

    .id-tag {
      display: inline-flex;
      align-items: center;
      padding: 3px 8px;
      border-radius: 12px;
      transition: all 0.3s ease;
      cursor: pointer;
      white-space: nowrap;

      &.pmid-tag,
      &.doi-tag {
        background: #F2F7FB;
        color: #374151;

        &:hover {
          background: #dae8fa;
        }
      }
    }
  }

  .upload-time {
    font-size: $font-size-small;
    color: #6b7280;
    font-weight: $font-weight-medium;
    flex-shrink: 0;
  }
}


//分页
.pagination-wrapper{
  margin-top: 20px;
  .el-pagination{
    justify-content: center;

  }
}

// 响应式设计
@media (max-width: $breakpoint-lg) {
  .search-form {
    .form-item {
      margin-right: $spacing-md;

      :deep(.el-form-item__label) {
        width: 70px;
      }

      :deep(.el-form-item__content) {
        min-width: 180px;
      }

      &.date-range {
        :deep(.el-form-item__content) {
          min-width: 250px;
        }
      }
    }
  }

  .upload-item {
    .upload-meta-line {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-xs;

      .literature-ids {
        gap: $spacing-xxs;
        flex-wrap: wrap;
        width: 100%;

        .id-tag {
          padding: 1px 6px;
        }
      }
    }
  }
}

@media (max-width: $breakpoint-md) {
  .page-content {
    gap: $spacing-md;
  }

  .search-card,
  .table-card {
    :deep(.el-card__body) {
      padding: $spacing-md;
    }
  }

  .search-form {
    :deep(.el-form--inline) {
      .el-form-item {
        display: block;
        margin-right: 0;
        margin-bottom: $spacing-sm;

        .el-form-item__label {
          width: 100%;
          text-align: left;
          padding-right: 0;
          margin-bottom: $spacing-xs;
        }

        .el-form-item__content {
          width: 100%;
          margin-left: 0;
        }
      }
    }

    .search-btn,
    .reset-btn {
      width: 100%;
      margin-bottom: $spacing-xs;
    }
  }

  .upload-item {
    padding: $spacing-xs $spacing-sm;

    .upload-content {
      .title-row {
        flex-direction: column;
        gap: $spacing-xxs;
      }

      .literature-title {
        font-size: $font-size-small;
      }

      .authors-row {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-xs;

        .pdf-download {
          align-self: flex-end;

          .download-btn {
            width: 24px;
            height: 24px;

            .el-icon {
              font-size: 12px;
            }
          }
        }
      }

      .reject-info-row {
        .reject-quote {
          padding: $spacing-xs $spacing-sm;
          font-size: 12px;
        }
      }

      .upload-meta-line {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-xs;

        .journal-info {
          gap: 2px;
        }

        .literature-ids {
          gap: $spacing-xxs;
          flex-wrap: wrap;
          width: 100%;

          .id-tag {
            padding: 1px 6px;
          }
        }
      }
    }
  }
}

// 加载状态样式
:deep(.el-loading-mask) {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

:deep(.el-loading-spinner) {
  .el-loading-text {
    color: $primary-color;
    font-weight: $font-weight-medium;
  }

  .circular {
    stroke: $primary-color;
  }
}
</style>
