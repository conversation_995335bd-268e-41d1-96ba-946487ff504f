<template>
  <div class="subject-page">
    <div class="subject-content">
      <!-- 左侧专题合集列表 -->
      <div class="left-sidebar">
        <div class="subjects-card">
          <!-- 专题合集列表 -->
          <div class="subjects-list">
            <div
              v-for="subject in subjects"
              :key="subject.id"
              :class="['subject-item', { active: selectedSubjectId === subject.id }]"
              @click="selectSubject(subject.id)"
            >
              <div class="subject-info">
                <el-icon class="subject-icon">
                  <Collection />
                </el-icon>
                <span class="subject-name">{{ subject.name }}</span>
              </div>
              <span class="subject-count">{{ subject.count }}</span>
            </div>
          </div>

          <!-- 新建专题按钮 -->
          <div class="add-subject-footer">
            <el-button
              class="add-subject-btn"
              @click="showAddSubjectDialog = true"
            >
              <el-icon class="mr-1"><Plus /></el-icon>
              新建专题
            </el-button>
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <div class="content-card">
          <div class="d-flex justify-space-between align-center">
            <!-- 标签页 -->
            <el-tabs v-model="activeTab" class="subject-tabs">
              <el-tab-pane label="待确认" name="pending">
                <template #label>
                  <span class="tab-label">
                    待确认
                  </span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="已确认" name="confirmed">
                <template #label>
                  <span class="tab-label">
                    已确认
                  </span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="已忽略" name="ignored">
                <template #label>
                  <span class="tab-label">
                    已忽略
                  </span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="设置" name="settings">
                <template #label>
                  <span class="tab-label">设置</span>
                </template>
              </el-tab-pane>
            </el-tabs>
            <!-- 工具栏 -->
            <div class="toolbar">

              <div class="toolbar-right">
                <div v-if="activeTab !== 'settings'" class="select-all-section">
                  <el-checkbox v-model="selectAll" @change="handleSelectAll">
                    全选
                  </el-checkbox>
                  <el-button
                    type="text"
                    size="small"
                    class="filter-btn"
                    @click="showSearchPanel = !showSearchPanel"
                  >
                    <el-icon><Filter /></el-icon>
                  </el-button>
                </div>

                <div class="batch-actions">
                  <!-- 待确认状态的批量操作 -->
                  <template v-if="activeTab === 'pending'">
                    <el-button
                      type="primary"
                      :disabled="selectedArticles.length === 0"
                      @click="batchExport"
                    >
                      批量导出
                    </el-button>
                    <el-button
                      type="primary"
                      :disabled="selectedArticles.length === 0"
                      @click="batchConfirm"
                    >
                      批量确认
                    </el-button>
                    <el-button
                      type="info"
                      :disabled="selectedArticles.length === 0"
                      class="btn-link"
                      @click="batchIgnore"
                    >
                      批量忽略
                    </el-button>
                  </template>

                  <!-- 已确认状态的批量操作 -->
                  <template v-else-if="activeTab === 'confirmed'">
                    <el-button
                      type="primary"
                      :disabled="selectedArticles.length === 0"
                      @click="batchExport"
                    >
                      批量导出
                    </el-button>
                    <el-button
                      type="info"
                      class="btn-link"
                      :disabled="selectedArticles.length === 0"
                      @click="batchCancel"
                    >
                      批量取消
                    </el-button>
                  </template>

                  <!-- 已忽略状态的批量操作 -->
                  <template v-else-if="activeTab === 'ignored'">
                    <el-button
                      type="primary"
                      :disabled="selectedArticles.length === 0"
                      @click="batchExport"
                    >
                      批量导出
                    </el-button>
                    <el-button
                      type="primary"
                      class="btn-link"
                      :disabled="selectedArticles.length === 0"
                      @click="batchRestore"
                    >
                      批量恢复
                    </el-button>
                  </template>

                  <!-- 设置页面不显示批量操作 -->
                </div>
              </div>
            </div>
          </div>
          <!-- 内容区域 -->
          <div class="tab-content">
            <!-- 待确认/已确认/已忽略 - 文献列表 -->
            <div v-if="activeTab !== 'settings'" class="literature-section">

              <el-collapse-transition>
                <!-- 搜索面板 -->
                <div v-show="showSearchPanel" class="search-panel">
                  <div class="search-row">
                    <div class="search-field">
                      <label class="search-label">文献ID</label>
                      <el-input
                        v-model="searchFilters.id"
                        placeholder="请输入文献ID"
                        class="search-input"
                      />
                    </div>
                    <div class="search-field">
                      <label class="search-label">文献标题</label>
                      <el-input
                        v-model="searchFilters.title"
                        placeholder="请输入文献标题"
                        class="search-input"
                      />
                    </div>
                    <div class="search-field">
                      <label class="search-label">作者</label>
                      <el-input
                        v-model="searchFilters.author"
                        placeholder="请输入作者"
                        class="search-input"
                      />
                    </div>
                  </div>
                  <div class="search-row">
                    <div class="search-field">
                      <label class="search-label">关键字</label>
                      <el-input
                        v-model="searchFilters.keywords"
                        placeholder="请输入关键字"
                        class="search-input"
                      />
                    </div>
                    <div class="search-field">
                      <label class="search-label">发表时间</label>
                      <el-select
                        v-model="searchFilters.publishTime"
                        placeholder="请选择发表时间"
                        class="search-input"
                      >
                        <el-option label="不限" value="" />
                        <el-option label="近1年" value="1year" />
                        <el-option label="近3年" value="3years" />
                        <el-option label="近5年" value="5years" />
                      </el-select>
                    </div>
                    <div class="search-field">
                      <el-button type="primary">
                        <el-icon class="mr-1"><Search /></el-icon>
                        搜索
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-collapse-transition>

              <!-- 文献列表 -->
              <div class="literature-list">
                <div
                  v-for="article in filteredArticles"
                  :key="article.id"
                  class="literature-item"
                >
                  <div class="literature-checkbox">
                    <el-checkbox
                      :model-value="selectedArticles.includes(article.id)"
                      @change="toggleArticleSelection(article.id)"
                    />
                  </div>

                  <div class="literature-content">
                    <!-- 标题行 -->
                    <div class="title-row">
                      <h4 class="literature-title">{{ article.title }}</h4>
                      <div class="action-buttons">
                        <!-- 待确认状态的操作按钮 -->
                        <template v-if="activeTab === 'pending'">
                          <el-button
                            type="primary"
                            size="small"
                            @click="confirmArticle(article.id)"
                          >
                            确认
                          </el-button>
                          <el-button
                            type="info"
                            size="small"
                            class="btn-link"
                            @click="ignoreArticle(article.id)"
                          >
                            忽略
                          </el-button>
                        </template>

                        <!-- 已确认状态的操作按钮 -->
                        <template v-else-if="activeTab === 'confirmed'">
                          <el-button
                            type="info"
                            size="small"
                            class="btn-link"
                            @click="cancelArticle(article.id)"
                          >
                            取消
                          </el-button>
                        </template>

                        <!-- 已忽略状态的操作按钮 -->
                        <template v-else-if="activeTab === 'ignored'">
                          <el-button
                            type="primary"
                            size="small"
                            class="btn-link"
                            @click="restoreArticle(article.id)"
                          >
                            恢复
                          </el-button>
                        </template>
                      </div>
                    </div>

                    <!-- 作者 -->
                    <div class="literature-authors">
                      {{ article.authors }}
                    </div>

                    <!-- 期刊信息和标识符 -->
                    <div class="literature-meta-line">
                      <div class="literature-meta-line mb-0">
                        <div class="journal-info">
                          <span class="journal-name">{{ article.journal }}</span>
                          <span class="publication-year">{{ article.year }}</span>
                          <span class="volume-info">{{ article.volume }}({{ article.issue }}):{{ article.pages }}</span>
                        </div>
                        <div class="literature-ids">
                          <span v-if="article.pmid" class="id-tag pmid-tag">
                            PMID: {{ article.pmid }}
                          </span>
                          <span v-if="article.doi" class="id-tag doi-tag">
                            DOI: {{ article.doi }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <div class="pagination-wrapper">
                <el-pagination
                  v-model:current-page="pagination.currentPage"
                  v-model:page-size="pagination.pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="total"
                  layout="total, sizes, prev, pager, next, jumper"
                  class="pagination"
                />
              </div>
            </div>

            <!-- 设置页面 -->
            <div v-else class="settings-section">
              <div class="settings-content">
                <div class="settings-form">
                  <!-- 专题名称 -->
                  <div class="form-group">
                    <label class="form-label">专题名称</label>
                    <el-input
                      v-model="settingsForm.subjectName"
                      placeholder="请输入专题名称"
                      class="form-input"
                    />
                  </div>

                  <!-- 专题描述 -->
                  <div class="form-group">
                    <label class="form-label">专题描述</label>
                    <el-input
                      v-model="settingsForm.subjectDescription"
                      type="textarea"
                      :rows="5"
                      placeholder="请输入专题描述"
                      class="form-input"
                    />
                  </div>

                  <!-- 监控规则 -->
                  <div class="form-group">
                    <label class="form-label">监控规则</label>

                    <!-- 规则条件列表 -->
                    <div class="monitoring-rules">
                      <div
                        v-for="(rule, index) in settingsForm.monitoringRules"
                        :key="rule.id"
                        class="rule-item"
                      >
                        <div class="rule-row">
                          <!-- 逻辑操作符 -->
                          <el-select
                            v-model="rule.logic"
                            class="logic-select"
                          >
                            <el-option label="AND" value="AND" />
                            <el-option label="OR" value="OR" />
                          </el-select>

                          <!-- 字段选择 -->
                          <el-select
                            v-model="rule.field"
                            placeholder="选择字段"
                            class="field-select"
                          >
                            <el-option
                              v-for="option in fieldOptions"
                              :key="option.value"
                              :label="option.label"
                              :value="option.value"
                            />
                          </el-select>

                          <!-- 值输入 -->
                          <el-input
                            v-model="rule.value"
                            placeholder="输入关键词..."
                            class="value-input"
                          />

                          <!-- 删除按钮 -->
                          <el-button
                            type="text"
                            size="small"
                            class="remove-btn"
                            :disabled="settingsForm.monitoringRules.length <= 1"
                            @click="removeRule(rule.id)"
                          >
                            ×
                          </el-button>
                        </div>
                      </div>

                      <!-- 添加条件按钮 -->
                      <div class="add-rule-section">
                        <el-button
                          type="primary"
                          text
                          class="add-rule-btn"
                          @click="addRule"
                        >
                          + 添加检索条件
                        </el-button>
                      </div>
                    </div>
                  </div>

                  <!-- 查询语句 -->
                  <div class="form-group">
                    <div class="query-header">
                      <label class="form-label">查询语句</label>
                      <el-button
                        type="primary"
                        text
                        size="small"
                        class="copy-btn"
                        @click="copyQueryStatement"
                      >
                        复制
                      </el-button>
                    </div>
                    <el-input
                      v-model="settingsForm.queryStatement"
                      type="textarea"
                      :rows="4"
                      readonly
                      class="form-input query-textarea"
                    />
                  </div>

                  <!-- 保存按钮 -->
                  <div class="form-actions">
                    <el-button type="primary" @click="saveSettings">保存</el-button>
                    <el-button class="btn-link" @click="resetSettings">重置</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 新建专题对话框 -->
    <el-dialog
      v-model="showAddSubjectDialog"
      title="新建专题"
      width="400px"
    >
      <el-form :model="newSubject" label-width="80px">
        <el-form-item label="专题名称">
          <el-input v-model="newSubject.name" placeholder="请输入专题名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddSubjectDialog = false">取消</el-button>
          <el-button type="primary" @click="createSubject">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, computed, reactive, watch } from 'vue'
  import {
    Collection,
    Plus,
    Filter,
    Search, Refresh
  } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'

  // 响应式数据
  const selectedSubjectId = ref('jure-leskovec')
  const activeTab = ref('pending')
  const selectAll = ref(false)
  const selectedArticles = ref([])
  const showSearchPanel = ref(false)
  const showAddSubjectDialog = ref(false)
  const total = ref(50)

  // 分页数据
  const pagination = reactive({
    currentPage: 1,
    pageSize: 20
  })

  // 搜索过滤器
  const searchFilters = ref({
    id: '',
    title: '',
    author: '',
    keywords: '',
    publishTime: ''
  })

  // 新建专题表单
  const newSubject = ref({
    name: ''
  })

  // 设置表单
  const settingsForm = ref({
    subjectName: '辐射专题',
    subjectDescription: '追踪辐射领域的最新进展',
    autoConfirm: false,
    emailNotify: true,
    updateFrequency: 'weekly',
    monitoringRules: [
      {
        id: 1,
        logic: 'AND',
        field: '所有字段',
        value: ''
      },
      {
        id: 2,
        logic: 'AND',
        field: '所有字段',
        value: ''
      }
    ],
    queryStatement: '(title:"辐射安全" OR title:"radiation safety") AND (abstract:"CAR-T" OR keywords:"CAR-T") AND year:[2020 TO 2023] AND language:zh'
  })

  // 监控规则字段选项
  const fieldOptions = [
    { label: '所有字段', value: '所有字段' },
    { label: '标题', value: '标题' },
    { label: '作者', value: '作者' },
    { label: '关键词', value: '关键词' },
    { label: '摘要', value: '摘要' },
    { label: 'DOI', value: 'DOI' },
    { label: 'PMID', value: 'PMID' },
    { label: '期刊', value: '期刊' }
  ]

  // 检索历史数据
  const searchHistory = ref([
    {
      id: 1,
      query: 'title:"辐射安全" AND keywords:"CAR-T"',
      results: 51946
    },
    {
      id: 2,
      query: 'author:"Zhang L" AND year:2023',
      results: 1234
    }
  ])

  // 专题合集数据
  const subjects = ref([
    {
      id: 'jure-leskovec',
      name: 'Jure Leskovec 相关的论文',
      count: 8
    },
    {
      id: 'machine-learning',
      name: '机器学习专题',
      count: 15
    },
    {
      id: 'deep-learning',
      name: '深度学习专题',
      count: 23
    }
  ])

  // 文献数据
  const articles = ref([
    {
      id: 1,
      title: '新型冠状病毒感染中医药防治的临床研究进展及其机制分析',
      authors: 'Zhang L, Wang Y, Li J, Wang K, Chen M, Sun E',
      journal: 'Nature',
      year: '2024',
      volume: '615',
      issue: '7952',
      pages: '456-468',
      doi: '10.1038/s41586-024-07123-4',
      pmid: '38234567',
      status: 'pending',
      subjectId: 'jure-leskovec'
    },
    {
      id: 2,
      title: 'PD-1/PD-L1免疫检查点抑制剂在非小细胞肺癌中的应用及其耐药机制',
      authors: 'Chen H, Liu Y, Yang R, Zhao W, Wang J, Dong Z',
      journal: 'Science',
      year: '2024',
      volume: '383',
      issue: '6630',
      pages: '123-135',
      doi: '10.1126/science.abcd5678',
      pmid: '38345678',
      status: 'confirmed',
      subjectId: 'jure-leskovec'
    },
    {
      id: 12,
      title: 'PD-1/PD-L1免疫检查点抑制剂在非小细胞肺癌中的应用及其耐药机制2',
      authors: 'Chen H, Liu Y, Yang R, Zhao W, Wang J, Dong Z',
      journal: 'Science',
      year: '2024',
      volume: '383',
      issue: '6630',
      pages: '123-135',
      doi: '10.1126/science.abcd5678',
      pmid: '38345678',
      status: 'confirmed',
      subjectId: 'jure-leskovec'
    },
    {
      id: 3,
      title: '基于深度学习的医学影像诊断技术在早期癌症检测中的应用研究',
      authors: 'Li Q, Wu J, Xu L, Huang P, Zhang J',
      journal: 'Cell',
      year: '2024',
      volume: '187',
      issue: '12',
      pages: '789-801',
      doi: '10.1016/j.cell.2024.03.456',
      pmid: '38456789',
      status: 'ignored',
      subjectId: 'jure-leskovec'
    },
    {
      id: 13,
      title: '基于深度学习的医学影像诊断技术在早期癌症检测中的应用研究',
      authors: 'Li Q, Wu J, Xu L, Huang P, Zhang J',
      journal: 'Cell',
      year: '2024',
      volume: '187',
      issue: '12',
      pages: '789-801',
      doi: '10.1016/j.cell.2024.03.456',
      pmid: '38456789',
      status: 'ignored',
      subjectId: 'jure-leskovec'
    },
    {
      id: 4,
      title: '新型冠状病毒感染中医药防治的临床研究进展及其机制分析2',
      authors: 'Zhang L, Wang Y, Li J, Wang K, Chen M, Sun E',
      journal: 'Nature',
      year: '2024',
      volume: '615',
      issue: '7952',
      pages: '456-468',
      doi: '10.1038/s41586-024-07123-4',
      pmid: '38234567',
      status: 'pending',
      subjectId: 'jure-leskovec'
    },
    {
      id: 5,
      title: '量子计算在密码学中的应用：当前挑战与未来机遇',
      authors: 'Zhang L, Wang Y, Li J, Wang K, Chen M, Sun E',
      journal: 'Nature',
      year: '2024',
      volume: '615',
      issue: '7952',
      pages: '456-468',
      doi: '10.1038/s41586-024-07123-4',
      pmid: '38234567',
      status: 'pending',
      subjectId: 'jure-leskovec'
    },
    {
      id: 6,
      title: '量子计算在密码学中的应用：当前挑战与未来机遇',
      authors: 'Zhang L, Wang Y, Li J, Wang K, Chen M, Sun E',
      journal: 'Nature',
      year: '2024',
      volume: '615',
      issue: '7952',
      pages: '456-468',
      doi: '10.1038/s41586-024-07123-4',
      pmid: '38234567',
      status: 'pending',
      subjectId: 'machine-learning'
    },
    {
      id: 6,
      title: '量子计算在密码学中的应用：当前挑战与未来机遇',
      authors: 'Zhang L, Wang Y, Li J, Wang K, Chen M, Sun E',
      journal: 'Nature',
      year: '2024',
      volume: '615',
      issue: '7952',
      pages: '456-468',
      doi: '10.1038/s41586-024-07123-4',
      pmid: '38234567',
      status: 'pending',
      subjectId: 'deep-learning'
    }
  ])

  // 计算属性
  const filteredArticles = computed(() => {
    return articles.value.filter(article =>
      article.subjectId === selectedSubjectId.value &&
      article.status === activeTab.value
    )
  })

  const pendingCount = computed(() => {
    return articles.value.filter(article =>
      article.subjectId === selectedSubjectId.value &&
      article.status === 'pending'
    ).length
  })

  const confirmedCount = computed(() => {
    return articles.value.filter(article =>
      article.subjectId === selectedSubjectId.value &&
      article.status === 'confirmed'
    ).length
  })

  const ignoredCount = computed(() => {
    return articles.value.filter(article =>
      article.subjectId === selectedSubjectId.value &&
      article.status === 'ignored'
    ).length
  })

  // 方法
  const selectSubject = subjectId => {
    selectedSubjectId.value = subjectId
    selectedArticles.value = []
    selectAll.value = false
  }

  const handleSelectAll = checked => {
    if (checked) {
      selectedArticles.value = filteredArticles.value.map(article => article.id)
    } else {
      selectedArticles.value = []
    }
  }

  const toggleArticleSelection = articleId => {
    const index = selectedArticles.value.indexOf(articleId)
    if (index > -1) {
      selectedArticles.value.splice(index, 1)
    } else {
      selectedArticles.value.push(articleId)
    }

    // 更新全选状态
    selectAll.value = selectedArticles.value.length === filteredArticles.value.length
  }

  const confirmArticle = articleId => {
    const article = articles.value.find(a => a.id === articleId)
    if (article) {
      article.status = 'confirmed'
      ElMessage.success('文献已确认')
    }
  }

  const ignoreArticle = articleId => {
    const article = articles.value.find(a => a.id === articleId)
    if (article) {
      article.status = 'ignored'
      ElMessage.success('文献已忽略')
    }
  }

  const cancelArticle = articleId => {
    const article = articles.value.find(a => a.id === articleId)
    if (article) {
      article.status = 'pending'
      ElMessage.success('文献已取消确认，状态变为待确认')
    }
  }

  const restoreArticle = articleId => {
    const article = articles.value.find(a => a.id === articleId)
    if (article) {
      article.status = 'pending'
      ElMessage.success('文献已恢复，状态变为待确认')
    }
  }

  // 批量操作方法
  const batchExport = () => {
    if (selectedArticles.value.length === 0) {
      ElMessage.warning('请选择要导出的文献')
      return
    }
    ElMessage.success(`批量导出 ${selectedArticles.value.length} 篇文献`)
    // 这里可以添加实际的导出逻辑
  }

  const batchConfirm = () => {
    if (selectedArticles.value.length === 0) {
      ElMessage.warning('请选择要确认的文献')
      return
    }

    selectedArticles.value.forEach(articleId => {
      const article = articles.value.find(a => a.id === articleId)
      if (article) {
        article.status = 'confirmed'
      }
    })

    ElMessage.success(`批量确认 ${selectedArticles.value.length} 篇文献`)
    selectedArticles.value = []
    selectAll.value = false
  }

  const batchIgnore = () => {
    if (selectedArticles.value.length === 0) {
      ElMessage.warning('请选择要忽略的文献')
      return
    }

    selectedArticles.value.forEach(articleId => {
      const article = articles.value.find(a => a.id === articleId)
      if (article) {
        article.status = 'ignored'
      }
    })

    ElMessage.success(`批量忽略 ${selectedArticles.value.length} 篇文献`)
    selectedArticles.value = []
    selectAll.value = false
  }

  const batchCancel = () => {
    if (selectedArticles.value.length === 0) {
      ElMessage.warning('请选择要取消的文献')
      return
    }

    selectedArticles.value.forEach(articleId => {
      const article = articles.value.find(a => a.id === articleId)
      if (article) {
        article.status = 'pending'
      }
    })

    ElMessage.success(`批量取消 ${selectedArticles.value.length} 篇文献，状态变为待确认`)
    selectedArticles.value = []
    selectAll.value = false
  }

  const batchRestore = () => {
    if (selectedArticles.value.length === 0) {
      ElMessage.warning('请选择要恢复的文献')
      return
    }

    selectedArticles.value.forEach(articleId => {
      const article = articles.value.find(a => a.id === articleId)
      if (article) {
        article.status = 'pending'
      }
    })

    ElMessage.success(`批量恢复 ${selectedArticles.value.length} 篇文献，状态变为待确认`)
    selectedArticles.value = []
    selectAll.value = false
  }

  // 监控规则相关方法
  const addRule = () => {
    const newRule = {
      id: Date.now(),
      logic: 'AND',
      field: '所有字段',
      value: ''
    }
    settingsForm.value.monitoringRules.push(newRule)
    updateQueryStatement()
  }

  const removeRule = ruleId => {
    const index = settingsForm.value.monitoringRules.findIndex(rule => rule.id === ruleId)
    if (index > -1 && settingsForm.value.monitoringRules.length > 1) {
      settingsForm.value.monitoringRules.splice(index, 1)
      updateQueryStatement()
    }
  }

  const updateQueryStatement = () => {
    // 这里可以根据监控规则动态生成查询语句
    // 简化示例，实际应用中需要更复杂的逻辑
    const rules = settingsForm.value.monitoringRules
    let query = ''

    rules.forEach((rule, index) => {
      if (rule.value.trim()) {
        if (index > 0) {
          query += ` ${rule.logic} `
        }

        const fieldMap = {
          所有字段: '',
          标题: 'title:',
          作者: 'author:',
          关键词: 'keywords:',
          摘要: 'abstract:',
          DOI: 'doi:',
          PMID: 'pmid:',
          期刊: 'journal:'
        }

        const prefix = fieldMap[rule.field] || ''
        query += `${prefix}"${rule.value}"`
      }
    })

    settingsForm.value.queryStatement = query || '(title:"辐射安全" OR title:"radiation safety") AND (abstract:"CAR-T" OR keywords:"CAR-T") AND year:[2020 TO 2023] AND language:zh'
  }

  const saveSettings = () => {
    // 保存设置逻辑
    ElMessage.success('设置已保存')
  }

  const resetSettings = () => {
    // 重置设置
    settingsForm.value.subjectName = '辐射专题'
    settingsForm.value.subjectDescription = '追踪辐射领域的最新进展'
    settingsForm.value.monitoringRules = [
      { id: Date.now(), logic: 'AND', field: '所有字段', value: '' },
      { id: Date.now() + 1, logic: 'AND', field: '所有字段', value: '' }
    ]
    settingsForm.value.autoConfirm = false
    settingsForm.value.emailNotify = true
    settingsForm.value.updateFrequency = 'weekly'
    updateQueryStatement()
    ElMessage.success('设置已重置')
  }

  const testQuery = () => {
    // 测试查询
    if (!settingsForm.value.queryStatement.trim()) {
      ElMessage.warning('请先配置监控规则')
      return
    }

    // 模拟检索结果
    const results = Math.floor(Math.random() * 10000) + 100
    ElMessage.success(`检索完成，找到 ${results} 条结果`)

    // 添加到检索历史
    const newHistory = {
      id: Date.now(),
      query: settingsForm.value.queryStatement,
      results
    }
    searchHistory.value.unshift(newHistory)

    // 限制历史记录数量
    if (searchHistory.value.length > 10) {
      searchHistory.value = searchHistory.value.slice(0, 10)
    }
  }

  const editHistory = history => {
    // 编辑历史记录 - 将历史查询加载到当前设置
    settingsForm.value.queryStatement = history.query
    ElMessage.success('历史查询已加载到当前设置')
  }

  const deleteHistory = historyId => {
    const index = searchHistory.value.findIndex(h => h.id === historyId)
    if (index > -1) {
      searchHistory.value.splice(index, 1)
      ElMessage.success('历史记录已删除')
    }
  }

  const copyQueryStatement = async() => {
    try {
      await navigator.clipboard.writeText(settingsForm.value.queryStatement)
      ElMessage.success('查询语句已复制到剪贴板')
    } catch (err) {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = settingsForm.value.queryStatement
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      ElMessage.success('查询语句已复制到剪贴板')
    }
  }

  const createSubject = () => {
    if (!newSubject.value.name.trim()) {
      ElMessage.warning('请输入专题名称')
      return
    }

    const newId = Date.now().toString()
    subjects.value.push({
      id: newId,
      name: newSubject.value.name,
      count: 0
    })

    newSubject.value.name = ''
    showAddSubjectDialog.value = false
    ElMessage.success('创建成功')
  }

  // 监听监控规则变化，自动更新查询语句
  watch(
    () => settingsForm.value.monitoringRules,
    () => {
      updateQueryStatement()
    },
    { deep: true }
  )

</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.subject-page {
  padding: 0;
}

.subject-content {
  display: flex;
  gap: $spacing-xl;
  align-items: stretch;

  @media (max-width: $breakpoint-lg) {
    flex-direction: column;
    gap: $spacing-lg;
  }
}

.left-sidebar {
  flex: 0 0 280px;

  @media (max-width: $breakpoint-lg) {
    flex: none;
    width: 100%;
  }
}

.right-content {
  flex: 1;
  min-width: 0;
}
.btn-link{
  background: #ffffff;
  border: 1px solid $primary-color;
  color: $primary-color;
  &:hover{
    background: #E9EDF1;
    color: $primary-color;
  }

}
// 专题合集卡片样式
.subjects-card {
  background: white;
  border-radius: $border-radius-lg;
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 内容卡片样式
.content-card {
  background: white;
  border-radius: $border-radius-lg;
  padding: $spacing-md $spacing-lg;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

// 新建专题底部
.add-subject-footer {
  border-top: 1px solid rgba(226, 232, 240, 0.6);
}

// 专题合集列表样式
.subjects-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
  flex: 1;
}

.subject-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-sm $spacing-md;
  border-radius: $border-radius-md;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;

  &:hover {
    background: #E5EBF1;
    border-color: rgba(148, 163, 184, 0.4);
  }

  &.active {
    background: #003D71;
    color: white;
    border-color: #003D71;

    .subject-name {
      color: white;
      font-size: 16px;
    }

    .subject-icon {
      color: white!important;
    }

    .subject-count {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }
  }

  .subject-info {
    display: flex;
    align-items: center;
    gap: $spacing-xs;

    .subject-icon {
      font-size: 18px;
      color:#003D71;
      transition: color 0.3s ease;
    }
  }

  .subject-count {
    background: rgba(148, 163, 184, 0.2);
    color: $gray;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: $font-size-small;
    font-weight: $font-weight-bold;
    min-width: 24px;
    text-align: center;
    transition: all 0.3s ease;
  }
}

.add-subject-btn {
  width: 100%;
  background:#ffffff;
  border: none;
  color: #00416F;
  font-weight: $font-weight-bold;
  padding: $spacing-md $spacing-lg;
  border-radius: $border-radius-md;
  transition: all 0.3s ease;
  height: 44px;

  &:hover {
    background: #e3ebf1;
    color: #003d71;
    transform: translateY(-1px);
  }
}

// 标签页样式
.subject-tabs {

  :deep(.el-tabs__header) {
    margin: 0 0 $spacing-lg 0;
    border-bottom: none;
  }

  :deep(.el-tabs__nav-wrap::after) {
    display: none;
  }

  :deep(.el-tabs__item) {
    font-size: $font-size-medium;
    font-weight: $font-weight-medium;
    color: $gray;
    padding: 0 $spacing-lg;
    height: 44px;
    line-height: 44px;

    &.is-active {
      color: $primary-color;
      font-weight: $font-weight-bold;
    }

    &:hover {
      color: $primary-color;
    }
  }

  :deep(.el-tabs__active-bar) {
    background-color: $primary-color;
    height: 3px;
  }

  .tab-label {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
  }

  .tab-badge {
    :deep(.el-badge__content) {
      background-color: $primary-color;
      border: none;
      font-size: 11px;
      height: 16px;
      line-height: 16px;
      padding: 0 5px;
      min-width: 16px;
    }
  }
}

// 工具栏样式
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;
  padding-bottom: $spacing-sm;
  flex-wrap: wrap;
  gap: $spacing-sm;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: stretch;
  }
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: $spacing-md;

  .add-period-btn {
    background: #E4EBF1;
    color: #00416f;
    border: 1px solid #00416f38;

    &:hover {
      background: #00416f;
      color: #ffffff;
      border: 1px solid #00416f;
    }
  }
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex-wrap: wrap;

  @media (max-width: $breakpoint-md) {
    justify-content: space-between;
  }
}

.select-all-section {
  display: flex;
  align-items: center;
  gap: $spacing-xs;

  .filter-btn {
    padding: 6px;
    color: $primary-color;

    &:hover {
      background: rgba(4, 56, 115, 0.1);
    }
  }
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

// 设置页面样式
.settings-section {
  .settings-content {
    background: white;
  }

  .settings-form {
    max-width: 100%;
  }

  .form-group {
    margin-bottom: $spacing-xl;

    .form-label {
      font-size: 16px;
      color: rgb(55, 65, 81);
      display: block;
      font-weight: 500;
      margin-bottom: $spacing-sm;
      line-height: 1.5;
    }

    .form-input {
      width: 100%;

      .el-input__wrapper {
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 8px 12px;
        background: white;
        box-shadow: none;

        &:hover {
          border-color: #9ca3af;
        }

        &.is-focus {
          border-color: $primary-color;
          box-shadow: 0 0 0 3px rgba(4, 56, 115, 0.1);
        }
      }

      .el-textarea__inner {
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 8px 12px;
        background: white;
        font-family: inherit;
        line-height: 1.5;
        resize: vertical;

        &:hover {
          border-color: #9ca3af;
        }

        &:focus {
          border-color: $primary-color;
          box-shadow: 0 0 0 3px rgba(4, 56, 115, 0.1);
          outline: none;
        }

        &[readonly] {
          background: #f9fafb;
          color: #6b7280;
          font-family: 'Courier New', monospace;
          font-size: 13px;
        }
      }
    }
  }

  .monitoring-rules {
    .rule-item {
      margin-bottom: $spacing-sm;
      border-radius: 6px;

      .rule-row {
        display: flex;
        align-items: center;
        gap: $spacing-sm;

        .logic-select {
          width: 80px;

          .el-input__wrapper {
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
          }
        }

        .field-select {
          width: 140px;

          .el-input__wrapper {
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
          }
        }

        .value-input {
          flex: 1;

          .el-input__wrapper {
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
          }
        }

        .remove-btn {
          width: 32px;
          height: 32px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          color: #ef4444;
          border: none;
          background: transparent;

          &:hover {
            background: #fef2f2;
            color: #dc2626;
          }

          &:disabled {
            color: #d1d5db;
            background: transparent;
          }
        }
      }
    }

    .add-rule-section {
      margin-top: $spacing-md;

      .add-rule-btn {
        color: $primary-color;
        font-weight: 500;
        border: none;
        background: transparent;

        &:hover {
          background: rgba(4, 56, 115, 0.1);
        }
      }
    }
  }

  .query-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-sm;

    .copy-btn {
      color: $primary-color;
      font-weight: 500;
      padding: 4px 8px;
      border: none;
      background: transparent;
      font-size: 14px;

      &:hover {
        background: rgba(4, 56, 115, 0.1);
      }
    }
  }

  .history-list {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: white;

    .history-item {
      padding: $spacing-md;
      border-bottom: 1px solid #f1f5f9;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:last-child {
        border-bottom: none;
      }

      .history-content {
        flex: 1;

        .history-number {
          font-weight: 600;
          color: #374151;
          margin-right: $spacing-sm;
        }

        .history-query {
          color: #6b7280;
          font-family: 'Courier New', monospace;
          font-size: 13px;
        }
      }

      .history-stats {
        margin-right: $spacing-md;
        color: #6b7280;
        font-size: 13px;

        .stat-number {
          font-weight: 600;
          color: #374151;
        }
      }

      .history-actions {
        display: flex;
        gap: $spacing-xs;

        .el-button {
          padding: 4px 8px;
          font-size: 12px;
          height: auto;
          border: none;
        }
      }
    }
  }

  .form-actions {
    margin-top: $spacing-xl;
    padding-top: $spacing-lg;
    display: flex;
    gap: $spacing-sm;
    justify-content: center;

    .el-button {
      padding: 10px 24px;
      font-weight: 500;
      border-radius: 6px;

      &[type="primary"] {
        background: $primary-color;
        border-color: $primary-color;

        &:hover {
          background: darken($primary-color, 10%);
          border-color: darken($primary-color, 10%);
        }
      }
    }
  }
}

// 搜索面板样式
.search-panel {
  background: rgba(241, 245, 249, 0.5);
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: $border-radius-md;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;

  .search-row {
    display: flex;
    gap: $spacing-lg;
    margin-bottom: $spacing-md;
    align-items: flex-end;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .search-field {
    flex: 1;
    min-width: 150px;

    .search-label {
      display: block;
      font-size: $font-size-small;
      color: $gray;
      font-weight: $font-weight-medium;
      margin-bottom: $spacing-xs;
    }

    .search-input {
      width: 100%;
    }

  }
}

// 文献列表样式
.literature-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.literature-item {
  display: flex;
  gap: $spacing-sm;
  padding: $spacing-sm $spacing-md;
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: $border-radius-md;
  background: white;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(148, 163, 184, 0.4);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  }

  .literature-checkbox {
    flex-shrink: 0;
    display: flex;
    align-items: flex-start;
    padding-top: 2px;
  }

  .literature-content {
    flex: 1;
    min-width: 0;
  }
}

.title-row {
  display: flex;
  align-items: flex-start;
  gap: $spacing-xs;
  margin-bottom: $spacing-xs;
}

.literature-title {
  font-size: $font-size-medium;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin: 0;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;

  &:hover {
    color: #2563eb;
  }
}

.literature-authors {
  font-size: $font-size-small;
  color: #575757;
  margin-bottom: $spacing-xs;
  line-height: 1.4;
  font-weight: $font-weight-medium;
}

.literature-meta-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: $spacing-md;
  font-size: 14px;
  color: #374151;
  margin-bottom: $spacing-xxs;

  .journal-info {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: $font-size-small;

    .publication-year {
      color: #6b7280;
    }

    .volume-info {
      color: #6b7280;
    }
  }

  .literature-ids {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    flex-shrink: 0;

    .id-tag {
      display: inline-flex;
      align-items: center;
      padding: 3px 8px;
      border-radius: 12px;
      transition: all 0.3s ease;
      cursor: pointer;
      white-space: nowrap;

      &.pmid-tag,
      &.doi-tag {
        background: #F2F7FB;
        color: #374151;

        &:hover {
          background: #dae8fa;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: $spacing-xs;
    flex-shrink: 0;
  }
}

// 设置页面样式
.settings-content {

  h3 {
    color: $primary-color;
    font-size: $font-size-large;
    margin-bottom: $spacing-xs;
  }

  .settings-description {
    color: $gray;
    margin-bottom: $spacing-xl;
    font-size: $font-size-medium;
  }
}

.settings-form {
  .form-help {
    margin-left: $spacing-sm;
    font-size: $font-size-small;
    color: $gray;
  }
  :deep(.el-input__wrapper),
  :deep(.el-select__wrapper),
  :deep(.el-textarea__inner){
    border-radius: 8px;
  }
}

// 分页样式
.pagination-wrapper{
  margin-top: 20px;
  .el-pagination{
    justify-content: center;
  }
}

// 响应式调整
@media (max-width: $breakpoint-md) {
  .subject-content {
    gap: $spacing-md;
  }

  .left-sidebar {
    flex: none;
    width: 100%;
  }

  .content-card {
    padding: $spacing-sm $spacing-md;
  }

  .literature-item {
    padding: $spacing-xs $spacing-sm;
    flex-direction: column;
    gap: $spacing-xs;

    .literature-checkbox {
      align-self: flex-start;
    }
  }

  .literature-content {
    .title-row {
      flex-direction: column;
      gap: $spacing-xxs;
    }

    .literature-title {
      font-size: $font-size-small;
    }

    .literature-authors {
      font-size: 14px;
      margin-bottom: $spacing-xxs;
    }

    .literature-meta-line {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-xs;

      .journal-info {
        gap: 2px;
      }

      .literature-ids {
        gap: $spacing-xxs;
        flex-wrap: wrap;
        width: 100%;

        .id-tag {
          padding: 1px 6px;
        }
      }

      .action-buttons {
        width: 100%;
        justify-content: flex-start;
      }
    }
  }

  .search-panel {
    .search-row {
      flex-direction: column;
      gap: $spacing-sm;

      .search-field {
        min-width: 100%;
      }
    }
  }

  .toolbar {
    .toolbar-left,
    .toolbar-right {
      width: 100%;
      justify-content: space-between;
    }

    .batch-actions {
      flex-wrap: wrap;
      justify-content: flex-start;
    }
  }
}

// 对话框样式调整
.el-dialog {
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-sm;
  }
}
</style>