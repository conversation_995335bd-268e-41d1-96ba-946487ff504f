<template>
  <div class="api-page">
    <div class="page-content">
      <!-- API Keys 管理区域 -->
      <div class="api-section">
        <el-card class="api-card">
          <template #header>
            <div class="card-header">
              <h3 class="section-title">
                <el-icon><Key /></el-icon>
                API keys
              </h3>
              <el-button
                type="primary"
                class="create-key-btn"
                :loading="loading"
                @click="openCreateDialog"
              >
                <el-icon class="mr-1"><Plus /></el-icon>
                创建 API key
              </el-button>
            </div>
          </template>

          <!-- 说明文字 -->
          <div class="description-section">
            <span class="description-title">列表内是你的全部 API key，API key 仅在创建时可见可复制，请妥善保存。不要与他人共享你的 API key，或将其暴露在浏览器或其他客户端代码中。</span>
          </div>

          <!-- API Keys 表格 -->
          <div class="api-table-section">
            <el-table
              v-loading="tableLoading"
              :data="apiKeys"
              class="api-table"
              border
              empty-text="暂无 API Key"
            >
              <el-table-column
                prop="keyName"
                label="名称"
                width="280"
                show-overflow-tooltip
              />

              <el-table-column
                prop="apiKey"
                label="密钥"
                width="400"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  <div class="key-container">
                    <span class="api-key">{{ row.apiKey }}</span>
                    <el-tooltip content="复制完整密钥">
                      <el-icon
                        class="ml-1 cursor-pointer"
                        color="#004581"
                        @click="copyApiKey(row)"
                      >
                        <DocumentCopy />
                      </el-icon>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                prop="createTime"
                label="创建日期"
                sortable
                width="300"
              >
                <template #default="{ row }">
                  <span class="create-date">{{ row.createTime }}</span>
                </template>
              </el-table-column>

              <el-table-column
                prop="updateTime"
                label="最新使用日期"
                sortable
                width="300"
              >
                <template #default="{ row }">
                  <span class="last-used">{{ row.updateTime || '未使用' }}</span>
                </template>
              </el-table-column>

              <el-table-column
                label="操作"
                align="center"
              >
                <template #default="{ row }">
                  <div class="action-buttons">
                    <el-button
                      type="primary"
                      size="small"
                      circle
                      class="edit-api"
                      :icon="Edit"
                      @click="editKey(row)"
                    />
                    <el-button
                      type="danger"
                      size="small"
                      circle
                      :icon="Delete"
                      :loading="loading"
                      @click="deleteKey(row)"
                    />
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </div>
    </div>

    <!-- API Key 对话框（创建） -->
    <el-dialog
      v-model="showDialog"
      :title="dialogTitle"
      width="500px"
      class="create-api-dialog"
      :show-close="true"
      center
    >
      <div class="dialog-content">
        <div class="form-section">
          <label class="form-label">名称</label>
          <el-input
            v-model="apiKeyName"
            placeholder="输入 API key 的名称"
            class="name-input"
            clearable
            maxlength="50"
            show-word-limit
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="handleCancel">
            取消
          </el-button>
          <el-button
            type="primary"
            class="create-btn"
            :loading="loading"
            @click="handleConfirm"
          >
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
  import { ref, computed, onMounted } from 'vue'
  import {
    Key,
    Plus,
    DocumentCopy,
    Edit,
    Delete
  } from '@element-plus/icons-vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { useAuthStore } from '@/stores/auth'
  import { getUserApiKeys, createApiKey, deleteApiKey, updateApiKeyName, getFullApiKey } from '@/api/user'

  const authStore = useAuthStore()

  // 响应式数据
  const showDialog = ref(false)
  const apiKeyName = ref('')
  const isEditMode = ref(false)
  const currentEditRow = ref(null)
  const loading = ref(false)
  const tableLoading = ref(false)

  // 计算属性
  const dialogTitle = computed(() => isEditMode.value ? '修改名称' : '创建 API key')

  // API Keys 数据
  const apiKeys = ref([])
  // 获取用户ID
  const userId = computed(() => {
    const id = authStore.userInfo?.userId
    return id
  })
  // 获取API密钥列表
  const fetchApiKeys = async() => {
    tableLoading.value = true
    try {
      const response = await getUserApiKeys()
      if (response.code === 200) {
        apiKeys.value = response.data || []
      } else {
        ElMessage.error(response.msg || '获取API密钥列表失败')
      }
    } catch (error) {
      ElMessage.error('获取API密钥列表失败')
    } finally {
      tableLoading.value = false
    }
  }
  // 编辑密钥名称
  const editKey = row => {
    isEditMode.value = true
    currentEditRow.value = row
    apiKeyName.value = row.keyName
    showDialog.value = true
  }

  // 删除API密钥
  const deleteKey = async row => {
    try {
      await ElMessageBox.confirm(
        `确定要删除API密钥"${row.keyName}"吗？删除后将无法恢复。`,
        '确认删除',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      )
      loading.value = true
      const response = await deleteApiKey(row.id)
      if (response.code === 200) {
        ElMessage.success('API密钥删除成功')
        await fetchApiKeys() // 重新获取列表
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败，请重试')
      }
    } finally {
      loading.value = false
    }
  }

  // 复制API密钥 - 获取完整密钥
  const copyApiKey = async row => {
    try {
      const response = await getFullApiKey(row.id)
      if (response.code === 200) {
        // 后端返回的数据在 response.data 中
        const fullApiKey = response.data
        if (fullApiKey) {
          await navigator.clipboard.writeText(fullApiKey)
          ElMessage.success('API密钥已复制到剪贴板')
        } else {
          ElMessage.error('获取到的密钥为空')
        }
      } else {
        ElMessage.error(response.msg || '获取完整密钥失败')
      }
    } catch (error) {
      // 如果是网络错误或其他错误，尝试降级方案
      if (row.apiKey) {
        try {
          const textArea = document.createElement('textarea')
          textArea.value = row.apiKey
          document.body.appendChild(textArea)
          textArea.select()
          document.body.removeChild(textArea)
          ElMessage.success('已复制表格中显示的密钥')
        } catch (fallbackError) {
          ElMessage.error('复制失败，请重试')
        }
      } else {
        ElMessage.error('复制失败，请重试')
      }
    }
  }

  // 打开创建对话框
  const openCreateDialog = async() => {
    // 如果用户信息不存在，尝试重新获取
    if (!authStore.userInfo || !userId.value) {
      try {
        await authStore.fetchUserInfo()
      } catch (error) {
        ElMessage.error('获取用户信息失败，请重新登录')
        return
      }
    }
    if (!userId.value) {
      ElMessage.warning('用户信息获取失败，请刷新页面重试')
      return
    }
    isEditMode.value = false
    apiKeyName.value = ''
    showDialog.value = true
  }

  // 统一的对话框确认处理
  const handleConfirm = async() => {

    if (!apiKeyName.value.trim()) {
      ElMessage.warning('请输入API密钥名称')
      return
    }
    loading.value = true
    try {
      if (isEditMode.value) {
        // 修改密钥名称
        const response = await updateApiKeyName(currentEditRow.value.id, apiKeyName.value.trim())

        if (response.code === 200) {
          ElMessage.success('API密钥名称修改成功')
          showDialog.value = false
          apiKeyName.value = ''
          isEditMode.value = false
          currentEditRow.value = null
          await fetchApiKeys() // 重新获取列表
        } else {
          ElMessage.error(response.msg || '修改失败')
        }
      } else {
        // 创建新密钥
        const response = await createApiKey(apiKeyName.value.trim())

        if (response.code === 200) {
          ElMessage.success('API密钥创建成功')
          showDialog.value = false
          apiKeyName.value = ''
          await fetchApiKeys() // 重新获取列表
        } else {
          ElMessage.error(response.msg || '创建失败')
          return
        }
      }
    } finally {
      loading.value = false
    }
  }

  // 统一的对话框取消处理
  const handleCancel = () => {
    showDialog.value = false
    apiKeyName.value = ''
    isEditMode.value = false
    currentEditRow.value = null
  }

  // 组件挂载时获取数据
  onMounted(async() => {
    // 确保用户信息已加载
    if (!authStore.userInfo) {
      try {
        await authStore.fetchUserInfo()
      } catch (error) {
        ElMessage.error('获取用户信息失败，请重新登录')
        return
      }
    }

    if (userId.value) {
      fetchApiKeys()
    } else {
      ElMessage.warning('用户信息加载失败，请刷新页面重试')
    }
  })
</script>
<style lang="scss" scoped>
@import "@/assets/styles/variables";

.page-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

// API 卡片样式
.api-card {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04), 0 1px 4px rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
  }

  :deep(.el-card__header) {
    padding: $spacing-md $spacing-lg;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
  }

  :deep(.el-card__body) {
    padding: $spacing-lg;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .section-title {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: $font-size-large;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;

    .el-icon {
      font-size: 28px;
      background: linear-gradient(135deg, $primary-color, #2563eb);
      color: white;
      padding: 6px;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(4, 56, 115, 0.2);
    }
  }

  .create-key-btn {
    background:  $primary-color;
    border: none;
    color: white;
    font-weight: $font-weight-bold;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(4, 56, 115, 0.3);
    }
  }
}
.edit-api{
  background: #004C92;
  color: #ffffff;
  &:hover {
    background: #004c92ba;
    color: #ffffff;
  }
}

// 描述区域样式
.description-section {
  margin-bottom: $spacing-lg;
  padding: $spacing-md;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
  border-radius: $border-radius-lg;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
  }

  .info-icon {
      font-size: 20px;
      color: #4FC3F7;
      background: rgba(79, 195, 247, 0.1);
      padding: 6px;
      border-radius: 50%;
  }

  .description-title {
      font-size: $font-size-small;
      color: $primary-color;
  }

}
.form-section{
  display: flex;
  padding: 20px 0;
  align-items: center;
  .form-label{
    color: #333333;
    width: 60px;
  }
}
// 表格样式
.api-table {
  :deep(.el-table__header) {
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));

    th {
      background: #FAFBFD;
      color: $gray;
      font-weight: $font-weight-bold;
      font-size: $font-size-small;
      border-bottom: 2px solid rgba(226, 232, 240, 0.8);
    }
  }

}
// 响应式设计
@media (max-width: $breakpoint-lg) {
  .card-header {
    flex-direction: column;
    gap: $spacing-md;
    align-items: stretch;

    .section-title {
      justify-content: center;
    }

    .create-key-btn {
      width: 100%;
    }
  }

  .api-table {
    font-size: $font-size-small;

    :deep(.el-table__body) {
      tr td {
        padding: $spacing-xs $spacing-sm;
      }
    }
  }
}

@media (max-width: $breakpoint-md) {
  .page-content {
    gap: $spacing-md;
  }

  .api-card {
    :deep(.el-card__body) {
      padding: $spacing-md;
    }
  }

  .description-section {
    padding: $spacing-md;

    .description-header {
      flex-direction: column;
      text-align: center;
      gap: $spacing-xs;

      .description-title {
        font-size: $font-size-small;
      }
    }

    .description-content {
      grid-template-columns: 1fr;
      gap: $spacing-sm;
    }

    .description-item {
      padding: $spacing-xs;

      .item-text {
        font-size: $font-size-small;
      }
    }
  }

  .api-table {
    :deep(.el-table) {
      font-size: 12px;
    }

    .key-container {
      flex-direction: column;
      align-items: stretch;
      gap: $spacing-xxs;

      .copy-btn {
        align-self: flex-end;
      }
    }

    .action-buttons {
      flex-direction: column;
      gap: $spacing-xxs;
    }
  }
}

// 响应式对话框
@media (max-width: $breakpoint-md) {
  :deep(.create-api-dialog) {
    .el-dialog {
      width: 90% !important;
      margin: 5vh auto;
    }

    .el-dialog__body {
      padding: $spacing-md;
    }
  }

  .dialog-footer {
    flex-direction: column;

    .cancel-btn,
    .create-btn {
      width: 100%;
      margin: 0;
    }
  }
}
</style>
