<template>
  <div class="article-history-page">
    <div class="page-content">
      <!-- 文献历史列表区域 -->
      <div class="history-section">
        <el-card class="history-card">
          <template #header>
            <div class="card-header">
              <h3 class="section-title">
                <el-icon>
                  <Document />
                </el-icon>
                文献浏览历史
              </h3>
              <div class="table-info">共 {{ total }} 条记录</div>
            </div>
          </template>
          <!-- 工具栏 -->
          <div class="d-flex justify-space-between">
            <div class="search-form-wrapper">
              <el-form :model="searchForm" class="search-form" :inline="true">
                <el-form-item label="搜索词" class="form-item">
                  <el-input
                    v-model="searchForm.keyword"
                    placeholder="PMID/PMCID/DOI/标题"
                    clearable
                    class="search-input"
                  />
                </el-form-item>

                <el-form-item label="浏览时间" class="form-item date-range">
                  <el-date-picker
                    v-model="searchForm.browseTimeRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="起始时间"
                    end-placeholder="截止时间"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    class="search-input date-picker"
                  />
                </el-form-item>

                <el-form-item class="form-item search-actions">
                  <el-button type="primary" class="search-btn" @click="handleSearch">
                    <el-icon class="mr-1">
                      <Search />
                    </el-icon>
                    搜索
                  </el-button>
                  <el-button class="reset-btn" @click="handleReset">
                    <el-icon class="mr-1">
                      <Refresh />
                    </el-icon>
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <div class="toolbar-right">
              <div class="select-all-section">
                <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
              </div>
              <div class="batch-actions">
                <el-button type="danger" class="btn-link" @click="handleClearHistory">清空历史</el-button>
                <el-button type="danger" class="btn-link" @click="handleDeleteSelected">删除</el-button>
              </div>
            </div>
          </div>

          <!-- 文献列表 -->
          <div class="literature-list">
            <!-- 加载状态骨架屏 -->
            <div v-if="loading" class="loading-state">
              <div v-for="n in 5" :key="n" class="literature-item-skeleton">
                <div class="skeleton-checkbox">
                  <el-skeleton-item variant="circle" style="width: 16px; height: 16px" />
                </div>
                <div class="skeleton-content">
                  <el-skeleton animated>
                    <template #template>
                      <el-skeleton-item variant="h3" style="width: 85%; margin-bottom: 8px" />
                      <el-skeleton-item variant="text" style="width: 65%; margin-bottom: 6px" />
                      <div style="display: flex; justify-content: space-between; align-items: center">
                        <div style="display: flex; gap: 8px">
                          <el-skeleton-item variant="text" style="width: 120px" />
                          <el-skeleton-item variant="text" style="width: 80px" />
                        </div>
                        <el-skeleton-item variant="text" style="width: 100px" />
                      </div>
                    </template>
                  </el-skeleton>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else-if="articles.length === 0" class="empty-state">
              <el-empty description="暂无浏览历史" :image-size="120">
                <template #description>
                  <span class="empty-<>description">暂无浏览历史</span>
                </template>
              </el-empty>
            </div>

            <div
              v-for="article in articles"
              v-else
              :key="article.id"
              class="literature-item"
            >
              <div class="literature-checkbox">
                <el-checkbox
                  :model-value="selectedArticles.includes(article.id)"
                  @change="toggleArticleSelection(article.id)"
                />
              </div>
              <div class="literature-content">
                <!-- 标题行 -->
                <div class="title-row">
                  <h4 class="literature-title" @click="navigateToDetail(article)" v-html="article.title"></h4>
                </div>

                <!-- 作者 -->
                <div class="literature-authors">
                  {{ formatAuthors(article.author) }}
                </div>

                <!-- 期刊信息和标识符 -->
                <div class="literature-meta-line">
                  <div class="literature-meta-line mb-0">
                    <div class="journal-info">
                      <span v-if="article.journalTitle" class="journal-name">{{ article.journalTitle }}.</span>
                      <span class="publication-year">{{ article.year }}</span>
                      <span class="volume-info">
                        {{ article.volume }}{{ article.issue ? `(${article.issue})` : ''
                        }}{{ article.page ? `:${article.page}` : '' }}
                      </span>
                    </div>
                    <div class="literature-ids">
                      <span v-if="article.pmid" class="id-tag pmid-tag">PMID: {{ article.pmid }}</span>
                      <span v-if="article.doi" class="id-tag doi-tag">DOI: {{ article.doi }}</span>
                    </div>
                  </div>
                  <div class="browse-info">
                    <span class="browse-time">{{ formatTime(article.createTime) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              class="pagination"
              @current-change="handlePageChange"
              @size-change="handleSizeChange"
            />
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, computed, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { Search, Refresh, Document } from '@element-plus/icons-vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { getUserDocHistoryList, deleteUserDocHistory } from '@/api/user'
  import { formatTime, formatAuthors } from '@/utils/formatUtils'
  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const total = ref(0)
  const selectAll = ref(false)
  const selectedArticles = ref([])
  const articles = ref([])
  // 搜索表单
  const searchForm = reactive({
    keyword: '',
    browseTimeRange: null
  })

  // 分页数据
  const pagination = reactive({
    currentPage: 1,
    pageSize: 20
  })

  // 计算属性 - 过滤后的文章列表
  const filteredArticles = computed(() => {
    return articles.value
  })
  // 导航到文献详情页
  const navigateToDetail = article => {
    // 根据文献的ID或PMID跳转到详情页
    const id = article.pmid || article.id
    if (id) {
      router.push(`/detail/${id}`)
    } else {
      ElMessage.warning('无法获取文献ID')
    }
  }
  // 加载浏览历史数据
  const loadHistoryData = () => {
    loading.value = true

    const params = {
      pageNum: pagination.currentPage,
      pageSize: pagination.pageSize
    }

    // 添加搜索条件
    if (searchForm.keyword) {
      params.search = searchForm.keyword
    }

    if (searchForm.browseTimeRange && searchForm.browseTimeRange.length === 2) {
      params.startTime = searchForm.browseTimeRange[0]
      params.endTime = searchForm.browseTimeRange[1]
    }

    return getUserDocHistoryList(params)
      .then(response => {
        if (response.code === 200) {
          articles.value = response.rows || []
          total.value = response.total || 0

          // 重置选择状态
          selectedArticles.value = []
          selectAll.value = false
        } else {
          ElMessage.error(response.message || '获取浏览历史失败')
        }
      })
      .catch(error => {
        console.error('加载浏览历史失败:', error)
        ElMessage.error('加载浏览历史失败')
      })
      .finally(() => {
        loading.value = false
      })
  }

  // 方法
  const handleSearch = () => {
    pagination.currentPage = 1
    loadHistoryData()
  }

  const handleReset = () => {
    Object.keys(searchForm).forEach(key => {
      if (key === 'browseTimeRange') {
        searchForm[key] = null
      } else {
        searchForm[key] = ''
      }
    })
    pagination.currentPage = 1
    loadHistoryData()
  }

  const handleSelectAll = checked => {
    if (checked) {
      selectedArticles.value = filteredArticles.value.map(article => article.id)
    } else {
      selectedArticles.value = []
    }
  }

  const toggleArticleSelection = articleId => {
    const index = selectedArticles.value.indexOf(articleId)
    if (index > -1) {
      selectedArticles.value.splice(index, 1)
    } else {
      selectedArticles.value.push(articleId)
    }

    // 更新全选状态
    selectAll.value = selectedArticles.value.length === filteredArticles.value.length
  }

  const handleClearHistory = () => {
    ElMessageBox.confirm('确定要清空所有浏览历史吗？此操作不可恢复。', '确认清空', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async() => {
        try {
          // 获取所有历史记录的ID
          const allIds = articles.value.map(article => article.id).join(',')
          if (allIds) {
            await deleteUserDocHistory(allIds)
            ElMessage.success('清空历史成功')
            loadHistoryData()
          }
        } catch (error) {
          console.error('清空历史失败:', error)
          ElMessage.error('清空历史失败')
        }
      })
      .catch(() => {
        ElMessage.info('已取消清空')
      })
  }

  const handleDeleteSelected = () => {
    if (selectedArticles.value.length === 0) {
      ElMessage.warning('请先选择要删除的记录')
      return
    }

    ElMessageBox.confirm(`确定要删除选中的 ${selectedArticles.value.length} 条浏览记录吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async() => {
        try {
          // 获取所有历史记录的ID
          const selectedIds = selectedArticles.value.join(',')
          await deleteUserDocHistory(selectedIds)
          ElMessage.success('删除选中记录成功')
          loadHistoryData()
        } catch (error) {
          console.error('清空历史失败:', error)
          ElMessage.error('清空历史失败')
        }
      })
      .catch(() => {
        ElMessage.info('已取消删除')
      })
  }

  // 分页事件处理
  const handlePageChange = page => {
    pagination.currentPage = page
    loadHistoryData()
  }

  const handleSizeChange = size => {
    pagination.pageSize = size
    pagination.currentPage = 1
    loadHistoryData()
  }

  // 初始化
  onMounted(async() => {
    await loadHistoryData()
  })
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/variables';

  .article-history-page {
    padding: 0;
  }

  .page-content {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;
  }

  .btn-link {
    background: #ffffff;
    border: 1px solid $primary-color;
    color: $primary-color;

    &:hover {
      background: #e9edf1;
      color: $primary-color;
    }
  }

  // 通用卡片样式
  .search-card,
  .history-card {
    background: white;
    border-radius: $border-radius-lg;
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.04),
      0 1px 4px rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(226, 232, 240, 0.5);
    transition: all 0.3s ease;

    &:hover {
      box-shadow:
        0 6px 25px rgba(0, 0, 0, 0.06),
        0 2px 8px rgba(0, 0, 0, 0.04);
      transform: translateY(-1px);
    }

    :deep(.el-card__header) {
      padding: $spacing-md $spacing-lg;
      border-bottom: 1px solid rgba(226, 232, 240, 0.6);
      background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
    }

    :deep(.el-card__body) {
      padding: $spacing-lg;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .section-title {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      font-size: 18px;
      font-weight: $font-weight-bold;
      color: $primary-color;
      margin: 0;

      .el-icon {
        font-size: 28px;
        background: linear-gradient(135deg, $primary-color, #2563eb);
        color: white;
        padding: 6px;
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(4, 56, 115, 0.2);
      }
    }

    .table-info {
      font-size: $font-size-small;
      color: #6b7280;
      font-weight: $font-weight-medium;
    }
  }

  // 搜索表单样式
  .search-form-wrapper {
    .search-form {
      .form-item {
        margin-bottom: $spacing-md;
        margin-right: $spacing-lg;

        :deep(.el-form-item__label) {
          font-weight: $font-weight-medium;
          color: $gray;
          font-size: $font-size-small;
          width: 80px;
          text-align: right;
          padding-right: $spacing-sm;
        }

        :deep(.el-form-item__content) {
          flex: 1;
          min-width: 200px;
        }

        &.date-range {
          :deep(.el-form-item__content) {
            min-width: 300px;
          }
        }

        &.search-actions {
          :deep(.el-form-item__label) {
            width: 0;
          }

          :deep(.el-form-item__content) {
            margin-left: 0;
          }
        }
      }

      .search-input {
        width: 100%;
      }

      .date-picker {
        width: 100%;
      }

      .search-btn {
        background: $primary-color;
        border: none;
        color: white;
        font-weight: $font-weight-bold;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
        }
      }

      .reset-btn {
        background: white;
        border: 1px solid rgba(226, 232, 240, 0.8);
        color: $gray;
        font-weight: $font-weight-medium;
        transition: all 0.3s ease;

        &:hover {
          border-color: $primary-color;
          color: $primary-color;
          transform: translateY(-1px);
        }
      }
    }
  }

  // 工具栏样式
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-md;
    padding-bottom: $spacing-sm;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    flex-wrap: wrap;
    gap: $spacing-sm;

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      align-items: stretch;
    }
  }

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: $spacing-md;

    .el-radio-button.is-active {
      :deep(.el-radio-button__inner) {
        background: #2e4e73;
        color: #ffffff;
      }
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: $spacing-md;
    flex-wrap: wrap;
    margin-bottom: 20px;
    justify-content: flex-end;

    @media (max-width: $breakpoint-md) {
      justify-content: space-between;
    }
  }

  .select-all-section {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
  }

  // 文献列表样式
  .literature-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
  }

  .literature-item {
    display: flex;
    gap: $spacing-sm;
    padding: $spacing-sm $spacing-md;
    border: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: $border-radius-md;
    background: white;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(148, 163, 184, 0.4);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
    }

    .literature-checkbox {
      flex-shrink: 0;
      display: flex;
      align-items: flex-start;
      padding-top: 2px;
    }

    .literature-content {
      flex: 1;
      min-width: 0;
    }
  }

  .title-row {
    display: flex;
    align-items: flex-start;
    gap: $spacing-xs;
    margin-bottom: $spacing-xs;
  }

  .source-tag {
    flex-shrink: 0;
    padding: 2px 14px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: $font-weight-bold;
    margin-top: 2px;

    &.pubmed {
      background: #e3f2fd;
      color: #1976d2;
    }

    &.pmc {
      background: #f3e5f5;
      color: #7b1fa2;
    }

    &.biorxiv {
      background: #e8f5e8;
      color: #388e3c;
    }
  }

  .literature-title {
    font-size: $font-size-medium;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;
    line-height: 1.5;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;

    &:hover {
      color: #2563eb;
    }
  }

  .literature-authors {
    font-size: $font-size-small;
    color: #575757;
    margin-bottom: $spacing-xs;
    line-height: 1.4;
    font-weight: $font-weight-medium;
  }

  .literature-meta-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: $spacing-md;
    font-size: 14px;
    color: #374151;
    margin-bottom: $spacing-xxs;

    .journal-info {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      font-size: $font-size-small;

      .journal-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        min-width: 0;
        max-width: 300px;
      }

      .publication-year {
        color: #6b7280;
      }

      .volume-info {
        color: #6b7280;
      }
    }

    .literature-ids {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      flex-shrink: 0;

      .id-tag {
        display: inline-flex;
        align-items: center;
        padding: 3px 8px;
        border-radius: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        white-space: nowrap;

        &.pmid-tag,
        &.doi-tag {
          background: #f2f7fb;
          color: #374151;

          &:hover {
            background: #dae8fa;
          }
        }
      }
    }

    .browse-info {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 2px;
      font-size: $font-size-small;
      color: #6b7280;
      flex-shrink: 0;

      .browse-time {
        font-weight: $font-weight-medium;
      }

      .browse-count {
        color: $primary-color;
        font-weight: $font-weight-bold;
      }
    }
  }

  .literature-description {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  // 分页样式
  .pagination-wrapper {
    margin-top: 20px;

    .el-pagination {
      justify-content: center;
    }
  }

  // 响应式设计
  @media (max-width: $breakpoint-lg) {
    .search-form {
      .form-item {
        margin-right: $spacing-md;

        :deep(.el-form-item__label) {
          width: 70px;
        }

        :deep(.el-form-item__content) {
          min-width: 180px;
        }

        &.date-range {
          :deep(.el-form-item__content) {
            min-width: 250px;
          }
        }
      }
    }
  }

  @media (max-width: $breakpoint-md) {
    .page-content {
      gap: $spacing-md;
    }

    .search-card,
    .history-card {
      :deep(.el-card__body) {
        padding: $spacing-md;
      }
    }

    .search-form {
      :deep(.el-form--inline) {
        .el-form-item {
          display: block;
          margin-right: 0;
          margin-bottom: $spacing-sm;

          .el-form-item__label {
            width: 100%;
            text-align: left;
            padding-right: 0;
            margin-bottom: $spacing-xs;
          }

          .el-form-item__content {
            width: 100%;
            margin-left: 0;
          }
        }
      }

      .search-btn,
      .reset-btn {
        width: 100%;
        margin-bottom: $spacing-xs;
      }
    }

    .literature-item {
      padding: $spacing-xs $spacing-sm;
      flex-direction: column;
      gap: $spacing-xs;

      .literature-checkbox {
        align-self: flex-start;
      }
    }

    .literature-content {
      .title-row {
        flex-direction: column;
        gap: $spacing-xxs;
      }

      .literature-title {
        font-size: $font-size-small;
      }

      .literature-authors {
        font-size: 14px;
        margin-bottom: $spacing-xxs;
      }

      .literature-meta-line {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-xs;

        .journal-info {
          gap: 2px;
        }

        .literature-ids {
          gap: $spacing-xxs;
          flex-wrap: wrap;
          width: 100%;

          .id-tag {
            padding: 1px 6px;
          }
        }

        .browse-info {
          align-items: flex-start;
          font-size: 12px;
        }
      }
    }

    .toolbar {
      .toolbar-left,
      .toolbar-right {
        width: 100%;
        justify-content: space-between;
      }

      .batch-actions {
        flex-wrap: wrap;
        justify-content: flex-start;
      }
    }
  }

  // 加载状态样式
  :deep(.el-loading-mask) {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
  }

  :deep(.el-loading-spinner) {
    .el-loading-text {
      color: $primary-color;
      font-weight: $font-weight-medium;
    }

    .circular {
      stroke: $primary-color;
    }
  }
</style>
