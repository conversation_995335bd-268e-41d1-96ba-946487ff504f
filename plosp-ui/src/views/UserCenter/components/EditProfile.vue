<template>
  <div class="edit-profile">
    <div class="edit-profile-container container">

      <div class="profile-tabs-container">
        <el-tabs v-model="activeTab" class="profile-tabs">
          <!-- 个人资料 Tab -->
          <el-tab-pane label="个人资料" name="profile">
            <div class="tab-content">
              <el-form
                ref="profileFormRef"
                :model="profileForm"
                :rules="profileRules"
                label-width="90px"
                label-position="left"
                class="register-form"
              >
                <!-- 头像上传部分 -->
                <el-form-item label="头像" prop="avatar" class="form-group avatar-group">
                  <div class="avatar-wrapper">
                    <!-- 显示当前头像 -->
                    <div v-if="currentAvatar" class="avatar-display">
                      <img :src="currentAvatar" alt="用户头像" class="avatar-image" />
                      <div class="avatar-overlay">
                        <el-button type="danger" size="small" @click="removeAvatar">
                          删除头像
                        </el-button>
                      </div>
                    </div>

                    <!-- 上传头像按钮 -->
                    <div v-else class="avatar-upload-btn" @click="triggerUpload">
                      <el-icon><Plus /></el-icon>
                      <span class="upload-text">上传头像</span>
                    </div>

                    <!-- 隐藏的文件输入框 -->
                    <input
                      ref="fileInputRef"
                      type="file"
                      accept="image/*"
                      style="display: none"
                      @change="handleFileSelect"
                    />
                  </div>
                </el-form-item>

                <div class="form-row">
                  <el-form-item
                    label="姓"
                    prop="lastName"
                    required
                    class="form-group"
                  >
                    <el-input
                      v-model="profileForm.lastName"
                      placeholder="请输入姓"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item
                    label="名"
                    prop="firstName"
                    required
                    class="form-group"
                  >
                    <el-input
                      v-model="profileForm.firstName"
                      placeholder="请输入名"
                      clearable
                    />
                  </el-form-item>
                </div>
                <div class="form-row">
                  <el-form-item
                    label="邮箱"
                    prop="email"
                    required
                    class="form-group"
                  >
                    <el-input
                      v-model="profileForm.email"
                      placeholder="请输入邮箱地址"
                      readonly
                      disabled
                      class="full-width"
                    />
                  </el-form-item>
                  <el-form-item
                    label="机构"
                    prop="organization"
                    required
                    class="form-group"
                  >
                    <el-autocomplete
                      v-model="profileForm.organization"
                      :fetch-suggestions="queryOrganizations"
                      placeholder="请输入机构名称"
                      class="form-input"
                      clearable
                      :trigger-on-focus="false"
                      @select="handleOrganizationSelect"
                    >
                      <template #default="{ item }">
                        <div class="organization-item">
                          <span>{{ item.label }}</span>
                          <span v-if="item.isNew" class="new-tag">(新增)</span>
                        </div>
                      </template>
                    </el-autocomplete>
                  </el-form-item>
                </div>
                <div class="form-row">
                  <el-form-item label="部门" prop="department" class="form-group">
                    <el-input
                      v-model="profileForm.department"
                      placeholder="请输入部门名称"
                      clearable
                      class="full-width"
                    />
                  </el-form-item>
                  <el-form-item label="PI姓名" prop="piName" class="form-group">
                    <el-input
                      v-model="profileForm.piName"
                      placeholder="请输入PI姓名"
                      clearable
                      class="full-width"
                    />
                  </el-form-item>
                </div>

                <div class="form-row">

                  <el-form-item label="职位" prop="title" class="form-group">
                    <el-input v-model="profileForm.title" />
                  </el-form-item>
                  <el-form-item label="电话" prop="phone" class="form-group">
                    <el-input
                      v-model="profileForm.phone"
                      placeholder="请输入电话号码"
                      clearable
                    />
                  </el-form-item>
                </div>
                <div class="form-row">
                  <el-form-item
                    label="国家/地区"
                    prop="country"
                    required
                    class="form-group"
                  >
                    <el-select
                      v-model="profileForm.country"
                      placeholder="--请选择--"
                      class="full-width"
                      filterable
                    >
                      <el-option
                        v-for="country in countryOptions"
                        :key="country.value"
                        :label="country.displayLabel"
                        :value="country.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="省/州" prop="province" class="form-group">
                    <el-input
                      v-model="profileForm.province"
                      placeholder="请输入省份或州"
                      clearable
                    />
                  </el-form-item>
                </div>
                <el-form-item label="城市" prop="city" class="form-group w-50">
                  <el-input
                    v-model="profileForm.city"
                    placeholder="请输入城市"
                    clearable
                  />
                </el-form-item>

                <div class="form-buttons">
                  <el-button
                    type="primary"
                    :loading="loading"
                    @click="saveProfile"
                  >
                    保存资料
                  </el-button>
                  <el-button @click="resetForm('profile')">重置</el-button>
                </div>
              </el-form>
            </div>
          </el-tab-pane>

          <!-- 修改密码 Tab -->
          <el-tab-pane label="修改密码" name="password">
            <div class="tab-content">
              <el-form
                ref="passwordFormRef"
                :model="passwordForm"
                :rules="passwordRules"
                label-width="100px"
                class="register-form"
                label-position="left"
              >
                <el-form-item
                  label="原密码"
                  prop="oldPassword"
                  required
                  class="form-group"
                >
                  <el-input
                    v-model="passwordForm.oldPassword"
                    type="password"
                    placeholder="请输入原密码"
                    show-password
                    clearable
                  />
                </el-form-item>

                <el-form-item
                  label="新密码"
                  prop="newPassword"
                  required
                  class="form-group"
                >
                  <el-input
                    v-model="passwordForm.newPassword"
                    type="password"
                    placeholder="请输入新密码"
                    show-password
                    clearable
                  />
                </el-form-item>

                <el-form-item
                  label="确认密码"
                  prop="confirmPassword"
                  required
                  class="form-group"
                >
                  <el-input
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    placeholder="请再次输入新密码"
                    show-password
                    clearable
                  />
                </el-form-item>

                <div class="form-buttons">
                  <el-button
                    type="primary"
                    :loading="loading"
                    @click="changeUserPassword"
                  >
                    修改密码
                  </el-button>
                  <el-button @click="resetForm('password')">重置</el-button>
                </div>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, computed } from 'vue'
  import { Plus } from '@element-plus/icons-vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { useAuthStore } from '@/stores/auth'
  import { useDictStore } from '@/stores/dict'
  import { updateUserProfile, changePassword } from '@/api/auth'

  const authStore = useAuthStore()
  const dictStore = useDictStore()
  const activeTab = ref('profile')

  // 计算属性 - 从字典存储获取数据
  const countryOptions = computed(() => dictStore.countries)
  const organizationOptions = computed(() => dictStore.organizations)

  // 机构搜索方法
  const queryOrganizations = (queryString, callback) => {
    const organizations = organizationOptions.value || []

    if (!queryString) {
      // 如果没有输入，返回前10个选项
      callback(organizations.slice(0, 10))
      return
    }

    // 根据输入过滤机构
    const results = organizations.filter(org =>
      org.label.toLowerCase().includes(queryString.toLowerCase()) ||
      org.value.toLowerCase().includes(queryString.toLowerCase())
    )

    // 如果没有匹配结果，允许用户输入新的机构名称
    if (results.length === 0) {
      callback([{
        label: queryString,
        value: queryString,
        isNew: true
      }])
    } else {
      callback(results)
    }
  }

  // 选择机构时的处理
  const handleOrganizationSelect = item => {
    profileForm.organization = item.label
  }

  // 表单引用
  const profileFormRef = ref()
  const passwordFormRef = ref()
  const fileInputRef = ref()

  // 个人资料表单数据
  const profileForm = reactive({
    lastName: '',
    firstName: '',
    email: '',
    organization: '',
    department: '',
    piName: '',
    title: '',
    phone: '',
    country: '',
    province: '',
    city: '',
    avatar: ''
  })

  // 修改密码表单数据
  const passwordForm = reactive({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  // 加载状态
  const loading = ref(false)

  // 头像相关状态
  const selectedFile = ref(null)
  const previewUrl = ref('')
  const avatarChanged = ref(false) // 添加头像变更标记

  // 当前头像计算属性
  const currentAvatar = computed(() => {
    // 如果明确删除了头像
    if (profileForm.avatar === '' && !selectedFile.value && !previewUrl.value) {
      return null
    }

    // 优先显示预览图片
    if (previewUrl.value) {
      return previewUrl.value
    }

    // 显示用户现有头像
    if (authStore.userInfo?.avatar) {
      let avatarUrl = authStore.userInfo.avatar
      if (!avatarUrl.startsWith('http') && !avatarUrl.startsWith('data:')) {
        avatarUrl = `data:image/jpeg;base64,${avatarUrl}`
      } else if (!avatarUrl.startsWith('http')) {
        avatarUrl = `${import.meta.env.VITE_APP_BASE_API}${avatarUrl}`
      }
      return avatarUrl
    }

    return null
  })

  // 触发文件选择
  const triggerUpload = () => {
    fileInputRef.value?.click()
  }

  // 处理文件选择
  const handleFileSelect = event => {
    const file = event.target.files[0]
    if (!file) return

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      ElMessage.error('请选择图片文件')
      return
    }

    // 验证文件大小 (限制为2MB)
    if (file.size > 2 * 1024 * 1024) {
      ElMessage.error('图片大小不能超过2MB')
      return
    }

    selectedFile.value = file
    avatarChanged.value = true // 标记头像已变更

    // 创建预览URL
    const reader = new FileReader()
    reader.onload = e => {
      previewUrl.value = e.target.result
    }
    reader.readAsDataURL(file)

    ElMessage.success('头像已选择，请点击保存资料完成更新')
  }

  // 删除头像
  const removeAvatar = async() => {
    try {
      await ElMessageBox.confirm(
        '确定要删除当前头像吗？',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 标记为删除状态
      selectedFile.value = null
      previewUrl.value = ''
      profileForm.avatar = ''
      avatarChanged.value = true // 标记头像已变更

      ElMessage.success('头像已标记删除，请点击保存资料完成更新')
    } catch {
      // 用户取消删除
    }
  }

  // 将文件转换为Base64
  const fileToBase64 = file => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        // 移除data:image/xxx;base64,前缀
        const base64 = reader.result.split(',')[1]
        resolve(base64)
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  // 初始化表单数据
  const initFormData = () => {
    if (authStore.userInfo) {
      const userInfo = authStore.userInfo
      profileForm.lastName = userInfo.lastName || ''
      profileForm.firstName = userInfo.firstName || ''
      profileForm.email = userInfo.email || ''
      profileForm.organization = userInfo.organization || ''
      profileForm.department = userInfo.department || ''
      profileForm.piName = userInfo.piName || ''
      profileForm.title = userInfo.title || ''
      profileForm.phone = userInfo.phone || ''
      profileForm.country = userInfo.countryRegion || ''
      profileForm.province = userInfo.stateProvince || ''
      profileForm.city = userInfo.city || ''
      profileForm.avatar = userInfo.avatar || ''
    }
  }

  // 保存个人资料 - 每次都上传头像
  const saveProfile = async() => {
    try {
      await profileFormRef.value.validate()
      loading.value = true

      const updateData = {
        lastName: profileForm.lastName,
        firstName: profileForm.firstName,
        email: profileForm.email,
        organization: profileForm.organization,
        department: profileForm.department,
        piName: profileForm.piName,
        title: profileForm.title,
        phone: profileForm.phone,
        countryRegion: profileForm.country,
        stateProvince: profileForm.province,
        city: profileForm.city
      }

      // 头像处理：每次保存都包含头像字段
      if (selectedFile.value) {
        // 有新选择的文件
        const base64Avatar = await fileToBase64(selectedFile.value)
        updateData.avatar = base64Avatar
      } else if (profileForm.avatar === '') {
        // 删除头像的情况
        updateData.avatar = ''
      } else {
        // 保持现有头像
        updateData.avatar = profileForm.avatar
      }

      const response = await updateUserProfile(updateData)

      if (response.code === 200) {
        // 更新本地用户信息
        await authStore.fetchUserInfo()

        // 只有在有新文件时才清空临时状态
        if (selectedFile.value) {
          selectedFile.value = null
          previewUrl.value = ''

          // 清空文件输入框
          if (fileInputRef.value) {
            fileInputRef.value.value = ''
          }
        }

        // 重置头像变更标记
        avatarChanged.value = false

        // 重新初始化表单数据
        initFormData()

        ElMessage.success('个人资料更新成功')
      } else {
        ElMessage.error(response.msg || '更新失败')
      }
    } catch (error) {
      ElMessage.error('更新失败，请重试')
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changeUserPassword = async() => {
    try {
      await passwordFormRef.value.validate()
      loading.value = true

      const response = await changePassword(
        passwordForm.oldPassword,
        passwordForm.newPassword
      )

      if (response.code === 200) {
        ElMessage.success('密码修改成功')
        // 清空表单
        passwordForm.oldPassword = ''
        passwordForm.newPassword = ''
        passwordForm.confirmPassword = ''
      } else {
        ElMessage.error(response.msg || '密码修改失败')
      }
    } catch (error) {
      ElMessage.error('修改密码失败，请重试')
    } finally {
      loading.value = false
    }
  }

  // 重置表单
  const resetForm = formType => {
    if (formType === 'profile') {
      // 清空临时头像状态
      selectedFile.value = null
      previewUrl.value = ''
      avatarChanged.value = false // 重置头像变更标记

      // 重置表单数据
      initFormData()

      // 清空文件输入框
      if (fileInputRef.value) {
        fileInputRef.value.value = ''
      }

      ElMessage.info('已重置为原始数据')
    } else if (formType === 'password') {
      passwordForm.oldPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
      ElMessage.info('已清空密码表单')
    }
  }

  // 组件挂载时初始化数据
  onMounted(async() => {
    // 确保获取最新的用户信息和字典数据
    await Promise.all([
      authStore.fetchUserInfo(),
      dictStore.initCommonDicts()
    ])
    initFormData()
  })

  // 表单验证规则
  const profileRules = {
    lastName: [
      { required: true, message: '请输入姓', trigger: 'blur' }
    ],
    firstName: [
      { required: true, message: '请输入名', trigger: 'blur' }
    ],
    organization: [
      { required: true, message: '请选择机构', trigger: 'change' }
    ],
    country: [
      { required: true, message: '请选择国家/地区', trigger: 'change' }
    ],
    phone: [
      {
        validator: (rule, value, callback) => {
          if (value && !/^1[3-9]\d{9}$/.test(value)) {
            callback(new Error('请输入正确的手机号码'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }

  const passwordRules = {
    oldPassword: [
      { required: true, message: '请输入原密码', trigger: 'blur' }
    ],
    newPassword: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      { min: 8, max: 30, message: '密码长度应为8-30位', trigger: 'blur' },
      { pattern: /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?])[A-Za-z\d!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]+$/, message: '密码应包含字母、数字和特殊字符', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value && value === passwordForm.oldPassword) {
            callback(new Error('新密码不能与原密码相同'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    confirmPassword: [
      { required: true, message: '请确认新密码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value !== passwordForm.newPassword) {
            callback(new Error('两次输入的密码不一致'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }


</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.edit-profile {
  background-color: #F5F5F5;
  padding: $spacing-xl 0;
  min-height: calc(100vh - 520px);
}

.page-header {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  margin-bottom: $spacing-xl;

  .back-btn {
    font-size: $font-size-medium;
    color: #0066CD;

    &:hover {
      color: #0052A3;
    }
  }

  .page-title {
    font-size: $font-size-xlarge;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;
  }
}

.profile-tabs-container {
  background: $white;
  border-radius: $border-radius-lg;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.profile-tabs {
  :deep(.el-tabs__header) {
    margin: 0;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }

  :deep(.el-tabs__nav-wrap) {
    padding: 0 $spacing-lg;
  }

  :deep(.el-tabs__item) {
    font-size: $font-size-medium;
    font-weight: $font-weight-medium;
    color: $gray;
    padding: 0 $spacing-lg;
    height: 50px;
    line-height: 50px;

    &.is-active {
      color: #0066CD;
    }

    &:hover {
      color: #0066CD;
    }
  }

  :deep(.el-tabs__active-bar) {
    background-color: #0066CD;
  }
}

.tab-content {
  padding: $spacing-xl;
}
.register-form {
  :deep(.el-form-item) {
    margin-bottom: $spacing-lg;

    .el-form-item__label {
      font-size: 16px;
      color: #374151;
      font-weight: 500;
      padding: 0;
      justify-content: flex-end;
      margin-right: 10px;
    }

    .el-form-item__content {
      line-height: 1.4;
    }
  }

  .form-group {
      :deep(.el-input__wrapper),
      :deep(.el-select__wrapper) {
        border-radius: $border-radius-md;
        box-shadow: none;
        border: 1px solid #e1e5e9;

        &:hover {
          border-color: $primary-color;
        }

        &.is-focus {
          border-color: $primary-color;
          box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
        }
      }
  }


  .form-row {
    display: flex;
    gap: $spacing-sm;
    .el-form-item{
      flex: 1;
    }

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      gap: 0;

      .form-group {
        width: 100% !important;
      }
    }
  }
  .register-button {
    width: 100%;
    height: 48px;
    background-color: $primary-color;
    border-color: $primary-color;
    border-radius: $border-radius-md;
    font-size: $font-size-medium;
    font-weight: $font-weight-medium;

    &:hover {
      background-color: darken($primary-color, 10%);
      border-color: darken($primary-color, 10%);
    }
  }
}
 .form-buttons {
   text-align: center;
   margin-top: 15px;
 justify-content: center;
}
// 响应式设计
@media (max-width: $breakpoint-md) {
  .edit-profile-container {
    padding: 0 $spacing-sm;
  }

  .tab-content {
    padding: $spacing-lg;
  }

  .profile-form,
  .password-form {
    .form-row {
      flex-direction: column;
      gap: 0;
    }
  }

  .form-buttons {
    flex-direction: column;
    .el-button {
      width: 100%;
      margin: 0;
      &:last-child{
        margin-top: $spacing-md;
      }
    }
  }
}

// 头像上传样式
.avatar-group {
  .avatar-wrapper {
    .avatar-display {
      position: relative;
      display: inline-block;

      .avatar-image {
        width: 100px;
        height: 100px;
        border-radius: 12px;
        object-fit: cover;
        border: 3px solid #e1e5e9;
        display: block;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
      }

      .avatar-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;
        border-radius: 12px;
        cursor: pointer;

        &:hover {
          opacity: 1;
        }

        .el-button {
          padding: 6px 12px;
          font-size: 12px;
          height: auto;
          border-radius: 6px;
          font-weight: 500;
        }
      }
    }

    .avatar-upload-btn {
      width: 100px;
      height: 100px;
      border: 2px dashed #cbd5e1;
      border-radius: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background: linear-gradient(135deg, #f8fafc, #f1f5f9);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      &:hover {
        border-color: #1890ff;
        background: linear-gradient(135deg, rgba(24, 144, 255, 0.05), rgba(64, 169, 255, 0.05));
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(24, 144, 255, 0.15);
      }

      .el-icon {
        color: #94a3b8;
        font-size: 24px;
        margin-bottom: 6px;
        transition: all 0.3s ease;
      }

      .upload-text {
        font-size: 12px;
        color: #64748b;
        font-weight: 500;
      }

      &:hover {
        .el-icon {
          color: #1890ff;
          transform: scale(1.1);
        }

        .upload-text {
          color: #1890ff;
        }
      }
    }
  }
}

// 机构自动完成样式
.organization-item {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .new-tag {
    font-size: 12px;
    color: #1890ff;
    font-weight: 500;
  }
}
</style>
