<template>
  <div class="article-transmit-page">
    <div class="page-content">
      <!-- 搜索区域 -->

      <!-- 表格区域 -->
      <div class="table-section">
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <h3 class="section-title">
                <el-icon><Document /></el-icon>
                我的传递文献
              </h3>
              <div class="table-info">
                共 {{ total }} 条记录
              </div>
            </div>
          </template>
          <div class="search-section">
            <el-form
              :model="searchForm"
              class="search-form"
              :inline="true"
            >
              <el-form-item label="搜索词" class="form-item">
                <el-input
                  v-model="searchForm.articleTitle"
                  placeholder="PMID/PMCID/DOI/标题"
                  clearable
                  class="search-input"
                />
              </el-form-item>

              <el-form-item label="状态" class="form-item">
                <el-select
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                  class="search-input"
                >
                  <el-option label="全部" value="" />
                  <el-option
                    v-for="item in statusData"
                    :key="`tr-sta-${item.value}`"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="请求时间" class="form-item date-range">
                <el-date-picker
                  v-model="searchForm.requestTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="起始时间"
                  end-placeholder="截止时间"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  class="search-input date-picker"
                />
              </el-form-item>

              <el-form-item class="form-item search-actions">
                <el-button type="primary" class="search-btn" @click="handleSearch">
                  <el-icon class="mr-1"><Search /></el-icon>
                  搜索
                </el-button>
                <el-button class="reset-btn" @click="handleReset">
                  <el-icon class="mr-1"><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 文献传递列表 -->
          <div v-loading="loading" class="transmit-list">
            <template v-if="tableData&&tableData.length>0">
              <div
                v-for="article in tableData"
                :key="article.id"
                class="transmit-item"
              >
                <div class="transmit-content">
                  <!-- 标题行 -->
                  <div class="title-row">
                    <h4 class="literature-title" @click="goToDetail(article.articleId)">{{ article.title }}</h4>
                    <!-- 头部：状态 -->

                    <div class="transmit-header">
                      <el-button
                        v-if="article.status === 'success'"
                        type="primary"
                        size="small"
                        class="download-btn"
                        @click="handleDownload(article.articleId)"
                      >
                        下载
                      </el-button>

                      <el-tag
                        v-else
                        :type="article.status === 'success' ? 'success' : 'danger'"
                        class="status-tag cursor-pointer"
                      >
                        {{ findLabelByStatus(article.status) }}
                      </el-tag>
                    </div>
                  </div>

                  <!-- 作者行 -->
                  <div class="authors-row">
                    <div class="literature-authors">
                      {{ formatAuthors(article) }}
                    </div>
                  </div>

                  <!-- 期刊信息和标识符 -->
                  <div class="literature-meta-line">
                    <div class="journal-info">
                      <span class="journal-name">{{ article.journal }}</span>
                      <span class="publication-year">{{ article.year }}</span>
                      <span class="volume-info"> {{ formatJournalInfo(article) }}</span>
                      <div class="literature-ids">
                        <span v-if="article.pmid" class="id-tag pmid-tag">
                          PMID:&nbsp; <a :href="`https://pubmed.ncbi.nlm.nih.gov/${article.pmid}`" target="_blank">{{ article.pmid }}</a>
                        </span>
                        <span v-if="article.doi" class="id-tag doi-tag">
                          DOI:&nbsp; <a :href="`https://doi.org/${article.doi}`" target="_blank">{{ article.doi }}</a>
                        </span>
                      </div>
                    </div>

                    <div class="request-time">{{ article.requestTime }}</div>
                  </div>

                  <!-- 驳回原因行 -->
                  <!--<div v-if="article.status === 'failed'" class="reject-info-row">
                    <div class="reject-quote">
                      <span class="reject-content">失败原因：{{ article.failureReason }}</span>
                    </div>
                  </div>-->
                </div>
              </div>
            </template>
            <div v-else style="text-align: center">
              <el-empty description="暂无文献传递数据" />
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.pageNum"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              class="pagination"
              @size-change="getList"
              @current-change="getList"
            />
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
  import { useRouter } from 'vue-router'
  import {
    Search,
    Refresh,
    Document,
    Download
  } from '@element-plus/icons-vue'
  import { getTransmitList } from '@/api/articleTransmit'
  import { ElMessageBox } from 'element-plus'

  const { proxy } = getCurrentInstance()
  // 路由
  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const total = ref(0)

  // 搜索表单
  const searchForm = reactive({
    articleTitle: '',
    status: '',
    requestTimeRange: null
  })

  // 分页数据
  const pagination = reactive({
    pageNum: 1,
    pageSize: 20
  })

  // 文献传递数据
  const tableData = ref([])

  const statusData = ref([
    { label: '等待中', value: 'waiting' },
    { label: '传递中', value: 'executing' },
    { label: '传递完成', value: 'success' },
    { label: '传递失败', value: 'failed' }
  ])

  function findLabelByStatus(status) {
    const length = statusData.value.length
    let currItem = null
    for (let i = 0; i < length; i++) {
      const item = statusData.value[i]
      if (item.value === status) {
        currItem = item
        break
      }
    }
    return currItem ? currItem.label : ''
  }

  // 格式化作者信息
  const formatAuthors = article => {
    // 优先使用author字段（数组），其次使用authors字段（可能是字符串）
    const authors = article.author || article.authors

    if (!authors) {
      return ''
    }

    // 如果是数组，用逗号和空格连接
    if (Array.isArray(authors)) {
      return authors.filter(author => author && author.trim()).join(', ')
    }

    // 如果是字符串，直接返回
    return authors.toString()
  }

  // 格式化期刊信息：动态格式，根据有无数据决定符号
  const formatJournalInfo = article => {

    // 获取各个字段
    // const journalName = ''
    // const year = ''
    const volume = article.volume
    const issue = article.issue
    const page = article.page

    let journalInfo = ''

    // 期刊名称
    // if (journalName) {
    //   journalInfo += journalName
    // }

    // 年份 - 如果有年份，前面加点号
    // if (year) {
    //   if (journalInfo) journalInfo += '. '
    //   journalInfo += year
    // }

    // 卷号 - 如果有卷号，前面加分号
    if (volume) {
      if (journalInfo) journalInfo += ';'
      journalInfo += volume
    }

    // 期号 - 如果有期号，用括号包围
    if (issue) {
      journalInfo += `(${issue})`
    }

    // 页码 - 如果有页码，前面加冒号
    if (page) {
      if (journalInfo) journalInfo += ':'
      journalInfo += page
    }

    // 最后加点号（如果有任何内容）
    if (journalInfo) {
      journalInfo += '.'
    }

    return journalInfo || '期刊信息不完整'
  }

  /**
   * 获取文献传递列表
   */
  function getList() {
    loading.value = true
    const requestTimeRange = searchForm.requestTimeRange
    let beginTime = null
    let endTime = null
    if (requestTimeRange && requestTimeRange.length === 2) {
      beginTime = requestTimeRange[0]
      endTime = requestTimeRange[1]
    }
    const params = {
      articleTitle: searchForm.articleTitle,
      status: searchForm.status,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      beginTime,
      endTime
    }
    getTransmitList(params).then(response => {
      // console.log(response)
      if (response.code === 200) {
        tableData.value = response.rows
        total.value = response.total
      } else {
        tableData.value = []
        total.value = 0
        ElMessage.error(response.msg || '提交失败')
      }
    }).finally(() => {
      loading.value = false
    })
  }

  /**
   * 搜索
   */
  function handleSearch() {
    pagination.pageNum = 1
    getList()
  }

  /**
   * 重置
   */
  function handleReset() {
    searchForm.articleTitle = ''
    searchForm.status = ''
    searchForm.requestTimeRange = null
    handleSearch()
  }

  /**
   * 跳转到详情页
   * @param {string} articleId - 文章ID
   */
  function goToDetail(articleId) {
    router.push(`/detail/${articleId}`)
  }

  function handleDownload(articleId) {
    ElMessageBox.confirm('第一次下载将扣除积分，是否下载？', '系统提示', {
      confirmButtonText: '下载',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      proxy.download(
        '/transmit/downloadArticlePDF',
        { docId: articleId, sys_time: new Date().getTime() },
        `${articleId}.pdf`
      )
    }).catch(() => {
      // 取消删除操作
    })
  }

  onMounted(() => {
    getList()
  })
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.article-transmit-page {
  padding: 0;
}

.page-content {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

// 通用卡片样式
.search-card,
.table-card {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04), 0 1px 4px rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
  }

  :deep(.el-card__header) {
    padding: $spacing-md $spacing-lg;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
  }

  :deep(.el-card__body) {
    padding: $spacing-lg;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .section-title {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: 18px;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;

    .el-icon {
      font-size: 28px;
      background: linear-gradient(135deg, $primary-color, #2563eb);
      color: white;
      padding: 6px;
      border-radius: 50%;
      box-shadow: 0 2px 8px rgba(4, 56, 115, 0.2);
    }
  }

  .table-info {
    font-size: $font-size-small;
    color: #6b7280;
    font-weight: $font-weight-medium;
  }
}
// 失败原因行
.reject-info-row {
  margin-bottom: $spacing-xs;

  .reject-quote {
    position: relative;
    padding: $spacing-sm $spacing-md;
    background: #F8F9FA;
    border-radius: 0 4px 4px 0;
    font-size: $font-size-small;
    color: #495057;
    line-height: 1.5;
    border-left: 3px solid gray;

    .reject-content {
      display: block;
      font-style: italic;
    }
  }
}

// 搜索表单样式
.search-form {
  .form-item {
    margin-bottom: $spacing-md;
    margin-right: $spacing-lg;

    :deep(.el-form-item__label) {
      font-weight: $font-weight-medium;
      color: $gray;
      font-size: $font-size-small;
      width: 80px;
      text-align: right;
      padding-right: $spacing-sm;
    }

    :deep(.el-form-item__content) {
      flex: 1;
      min-width: 200px;
    }

    &.date-range {
      :deep(.el-form-item__content) {
        min-width: 300px;
      }
    }

    &.search-actions {
      :deep(.el-form-item__label) {
        width: 0;
      }

      :deep(.el-form-item__content) {
        margin-left: 0;
      }
    }
  }

  .search-input {
    width: 100%;
  }

  .date-picker {
    width: 100%;
  }

  .search-btn {
    background:  $primary-color;
    border: none;
    color: white;
    font-weight: $font-weight-bold;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
    }
  }

  .reset-btn {
    background: white;
    border: 1px solid rgba(226, 232, 240, 0.8);
    color: $gray;
    font-weight: $font-weight-medium;
    transition: all 0.3s ease;

    &:hover {
      border-color: $primary-color;
      color: $primary-color;
      transform: translateY(-1px);
    }
  }
}

// 文献传递列表样式
.transmit-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.transmit-item {
  padding: $spacing-md;
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: $border-radius-md;
  background: white;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(148, 163, 184, 0.4);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  }

  .transmit-content {
    width: 100%;
  }
}

// 头部：状态
.transmit-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;

  .status-tag {
    font-size: 14px;
    padding: 6px 14px;
  }
}

.title-row {
  display: flex;
  align-items: flex-start;
  gap: $spacing-xs;
  margin-bottom: $spacing-xs;
}
.download-btn {
  padding: 6px 14px;
  line-height: 24px;
  font-size: 14px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  &:hover {
    background: #00456D;
    color: #ffffff;
  }

  .el-icon {
    font-size: 14px;
  }
}
// 作者行

.source-tag {
  flex-shrink: 0;
  padding: 2px 14px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: $font-weight-bold;
  margin-top: 2px;

  &.pubmed {
    background: #E3F2FD;
    color: #1976D2;
  }

  &.pmc {
    background: #F3E5F5;
    color: #7B1FA2;
  }

  &.biorxiv {
    background: #E8F5E8;
    color: #388E3C;
  }
}

.literature-title {
  font-size: $font-size-medium;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin: 0;
  line-height: 1.5;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;

  &:hover {
    color: #2563eb;
  }
}

.literature-authors {
  font-size: $font-size-small;
  color: #575757;
  line-height: 1.4;
  font-weight: $font-weight-medium;
}

.literature-meta-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: $spacing-md;
  font-size: 14px;
  color: #374151;
  margin: $spacing-xs 0;

  .journal-info {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    font-size: $font-size-small;

    .publication-year {
      color: #6b7280;
    }

    .volume-info {
      color: #6b7280;
    }
  }

  .literature-ids {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    flex-shrink: 0;

    .id-tag {
      display: inline-flex;
      align-items: center;
      padding: 3px 8px;
      border-radius: 12px;
      transition: all 0.3s ease;
      cursor: pointer;
      white-space: nowrap;

      &.pmid-tag,
      &.doi-tag {
        background: #F2F7FB;
        color: #374151;
        font-size: 14px;

        &:hover {
          background: #dae8fa;
        }
      }
    }
  }

  .request-time {
    font-size: $font-size-small;
    color: #6b7280;
    font-weight: $font-weight-medium;
    flex-shrink: 0;
  }
}

.literature-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  margin-bottom: $spacing-sm;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}


//分页
.pagination-wrapper{
  margin-top: 20px;
  .el-pagination{
    justify-content: center;

  }
}

// 响应式设计
@media (max-width: $breakpoint-lg) {
  .search-form {
    .form-item {
      margin-right: $spacing-md;

      :deep(.el-form-item__label) {
        width: 70px;
      }

      :deep(.el-form-item__content) {
        min-width: 180px;
      }

      &.date-range {
        :deep(.el-form-item__content) {
          min-width: 250px;
        }
      }
    }
  }

  .transmit-item {
    .literature-meta-line {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-xs;

      .literature-ids {
        gap: $spacing-xxs;
        flex-wrap: wrap;
        width: 100%;

        .id-tag {
          padding: 1px 6px;
        }
      }
    }

    .authors-row {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-xs;

      .download-btn {
        align-self: flex-end;
      }
    }
  }
}

@media (max-width: $breakpoint-md) {
  .page-content {
    gap: $spacing-md;
  }

  .search-card,
  .table-card {
    :deep(.el-card__body) {
      padding: $spacing-md;
    }
  }

  .search-form {
    :deep(.el-form--inline) {
      .el-form-item {
        display: block;
        margin-right: 0;
        margin-bottom: $spacing-sm;

        .el-form-item__label {
          width: 100%;
          text-align: left;
          padding-right: 0;
          margin-bottom: $spacing-xs;
        }

        .el-form-item__content {
          width: 100%;
          margin-left: 0;
        }
      }
    }

    .search-btn,
    .reset-btn {
      width: 100%;
      margin-bottom: $spacing-xs;
    }
  }

  .transmit-item {
    padding: $spacing-xs $spacing-sm;

    .transmit-content {
      .title-row {
        flex-direction: column;
        gap: $spacing-xxs;
      }

      .literature-title {
        font-size: $font-size-small;
      }

      .literature-authors {
        font-size: 14px;
        margin-bottom: $spacing-xxs;
      }

      .literature-meta-line {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-xs;

        .journal-info {
          gap: 2px;
        }

        .literature-ids {
          gap: $spacing-xxs;
          flex-wrap: wrap;
          width: 100%;

          .id-tag {
            padding: 1px 6px;
          }
        }
      }

      .literature-description {
        font-size: 14px;
        -webkit-line-clamp: 1;
      }
    }



    .authors-row {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-xs;

      .download-btn {
        align-self: flex-end;
        width: 24px;
        height: 24px;

        .el-icon {
          font-size: 12px;
        }
      }
    }
  }
}

// 加载状态样式
:deep(.el-loading-mask) {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

:deep(.el-loading-spinner) {
  .el-loading-text {
    color: $primary-color;
    font-weight: $font-weight-medium;
  }

  .circular {
    stroke: $primary-color;
  }
}
</style>
