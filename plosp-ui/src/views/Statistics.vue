<template>
  <div class="statistics">
    <section class="content-section">
      <div class="container">
        <!-- 统计卡片区域 -->
        <div class="statistics-cards">
          <div class="statistics-card">
            <div class="card-header">
              <h2>文献总数</h2>
              <div class="card-icon">
                <div class="icon-bg">
                  <img src="@/assets/images/bookmark.svg" class="stat-icon" />
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-number" v-text="formatNumber(homeStats.totalArticles)"></div>
              <div class="card-trend up">
                <img src="@/assets/images/rise.svg" class="trend-icon" />
                <span>12.5% (+6,789)</span>
              </div>
            </div>
          </div>

          <div class="statistics-card">
            <div class="card-header">
              <h2>期刊总数</h2>
              <div class="card-icon">
                <div class="icon-bg">
                  <img src="@/assets/images/book.svg" class="stat-icon" />
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-number" v-text="formatNumber(homeStats.totalJournals)"></div>
              <div class="card-trend up">
                <img src="@/assets/images/rise.svg" class="trend-icon" />
                <span>8.0% (+6,789)</span>
              </div>
            </div>
          </div>

          <div class="statistics-card">
            <div class="card-header">
              <h2>文献下载量</h2>
              <div class="card-icon">
                <div class="icon-bg">
                  <img src="@/assets/images/download.svg" class="stat-icon" />
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-number" v-text="formatNumber(homeStats.totalDownloadNum)"></div>
              <div class="card-trend up">
                <img src="@/assets/images/rise.svg" class="trend-icon" />
                <span>12.5% (+6,789)</span>
              </div>
            </div>
          </div>

          <div class="statistics-card">
            <div class="card-header">
              <h2>访问人次</h2>
              <div class="card-icon">
                <div class="icon-bg">
                  <img src="@/assets/images/visits.svg" class="stat-icon" />
                </div>
              </div>
            </div>
            <div class="card-content">
              <div class="card-number">1,234,567</div>
              <div class="card-trend down">
                <img src="@/assets/images/decline.svg" class="trend-icon" />
                <span>1.2% (789)</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 排行榜 -->
        <div class="statistics-column">
          <!-- 热门文献排行 -->
          <div class="statistics-panel">
            <div class="title-more">
              <h2 class="panel-title">热门文献排行</h2>
              <!--<div class="view-more">
                <span>查看更多</span>
                <el-icon><ArrowRight /></el-icon>
              </div>-->
            </div>

            <div class="ranking-list">
              <div v-for="(item, index) in popularArticles" :key="index" class="ranking-item">
                <div class="ranking-number">{{ index + 1 }}</div>
                <div class="ranking-content">
                  <div class="d-flex is-justify-space-between title-visits">
                    <div class="ranking-title clickable" @click="goToArticleDetail(item.id)" v-html="item.title"></div>
                    <div class="ranking-visits">{{ formatNumber(item.hitNum) }}次访问</div>
                  </div>
                  <div class="ranking-info">{{ formatAuthors(item.author, 3) }} | {{ item.journalTitle }}</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 热门期刊排行 -->
          <div class="statistics-panel">
            <div class="title-more">
              <h2 class="panel-title">热门期刊排行</h2>
              <!--<div class="view-more">
                <span>查看更多</span>
                <el-icon><ArrowRight /></el-icon>
              </div>-->
            </div>
            <div class="ranking-list">
              <div v-for="(item, index) in popularJournals" :key="index" class="ranking-item">
                <div class="ranking-number">{{ index + 1 }}</div>
                <div class="ranking-content">
                  <div class="d-flex is-justify-space-between title-visits">
                    <div class="ranking-title clickable" @click="goToLiterature(item.title)" v-html="item.title"></div>
                    <div class="ranking-visits">{{ formatNumber(item.hitNum) }}次访问</div>
                  </div>
                  <div class="ranking-info">IF: {{ item.impactFactor }} | {{ item.category }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 关键词区域 -->
        <div class="statistics-column">
          <div class="statistics-keywords">
            <div class="statistics-card">
              <div class="keywords-panel">
                <h2 class="panel-title">热门搜索关键词</h2>
                <div class="keywords-container">
                  <div v-for="(keyword, index) in searchKeywords" :key="index" class="keyword-tag">
                    {{ keyword.text }} ({{ keyword.count }})
                  </div>
                </div>
              </div>
            </div>
            <div class="statistics-card">
              <div class="keywords-panel">
                <h2 class="panel-title">文献高频关键词</h2>
                <div class="keywords-container purple">
                  <div v-for="(keyword, index) in literatureKeywords" :key="index" class="keyword-tag">
                    {{ keyword.text }} ({{ formatNumber(keyword.count) }})
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 文献增长趋势 -->
        <div class="statistics-column">
          <div class="statistics-panel">
            <h2 class="panel-title">文献增长趋势</h2>
            <div class="chart-container">
              <div ref="literatureTrendChart" class="chart"></div>
            </div>
          </div>

          <!-- 期刊增长趋势 -->
          <div class="statistics-panel">
            <h2 class="panel-title">期刊增长趋势</h2>
            <div class="chart-container">
              <div ref="journalTrendChart" class="chart"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import * as echarts from 'echarts'
  import { getHomeData, getPopluarArticles, getPopularJournals, getTopKeywords } from '@/api/home'
  import { formatAuthors, formatNumber } from '@/utils/formatUtils'

  const router = useRouter()

  // 热门文献排行数据
  const hotLiteratures = [
    {
      title: '肿瘤免疫微环境中巨噬细胞极化调控机制的研究进展',
      author: 'Zhang L, et al.',
      journal: 'Journal of Immunology Research',
      visits: '2,345'
    },
    {
      title: 'PD-1/PD-L1免疫检查点抑制剂联合化疗在晚期非小细胞肺癌中的临床疗效研究',
      author: 'Zhang L, et al.',
      journal: 'Journal of Immunology Research',
      visits: '2,345'
    },
    {
      title: '肿瘤突变负荷(TMB)作为免疫治疗疗效预测标志物的研究进展',
      author: 'Zhang L, et al.',
      journal: 'Journal of Immunology Research',
      visits: '2,345'
    },
    {
      title: 'PD-1/PD-L1免疫检查点抑制剂联合化疗在晚期非小细胞肺癌中的临床疗效研究',
      author: 'Zhang L, et al.',
      journal: 'Journal of Immunology Research',
      visits: '2,345'
    },
    {
      title: '肿瘤突变负荷(TMB)作为免疫治疗疗效预测标志物的研究进展',
      author: 'Zhang L, et al.',
      journal: 'Journal of Immunology Research',
      visits: '2,345'
    }
  ]

  // 热门期刊排行数据
  const hotJournals = [
    {
      title: 'Nature Medicine',
      impactFactor: '87.241',
      category: '医学综合',
      visits: '2,345'
    },
    {
      title: 'Cell',
      impactFactor: '66.850',
      category: '细胞生物学',
      visits: '2,345'
    },
    {
      title: 'Science',
      impactFactor: '63.714',
      category: '综合科学',
      visits: '2,345'
    },
    {
      title: 'Nature Medicine',
      impactFactor: '87.241',
      category: '医学综合',
      visits: '2,345'
    },
    {
      title: 'Cell',
      impactFactor: '66.850',
      category: '细胞生物学',
      visits: '2,345'
    }
  ]

  // 热门搜索关键词
  const searchKeywords = [
    { text: '肿瘤免疫', count: '2,345' },
    { text: '免疫治疗', count: '1,654' },
    { text: 'CAR-T细胞', count: '1,987' },
    { text: 'PD-1/PD-L1', count: '1,876' },
    { text: '肿瘤微环境', count: '1,543' },
    { text: '临床试验', count: '1,432' },
    { text: '生物标志物', count: '1,321' }
  ]

  // 文献高频关键词（从后端获取）
  const literatureKeywords = ref([])

  // 图表引用
  const literatureTrendChart = ref(null)
  const journalTrendChart = ref(null)

  const homeStats = ref({
    totalArticles: 0,
    monthlyArticle: 0,
    totalJournals: 0,
    monthlyJournals: 0,
    totalDownloadNum: 0,
    totalVisitNum: 0
  })
  const popularArticles = ref([])
  const popularJournals = ref([])
  // 初始化图表
  onMounted(() => {
    initLiteratureTrendChart()
    initJournalTrendChart()
    getHomeData().then(response => {
      homeStats.value = response.data
    })
    getPopluarArticles().then(response => {
      popularArticles.value = response.data
    })
    getPopularJournals().then(response => {
      popularJournals.value = response.data
    })
    getTopKeywords().then(response => {
      literatureKeywords.value = response.data
    })

    // 窗口大小变化时重新调整图表大小
    window.addEventListener('resize', () => {
      literatureTrendChart.value && echarts.getInstanceByDom(literatureTrendChart.value).resize()
      journalTrendChart.value && echarts.getInstanceByDom(journalTrendChart.value).resize()
    })
  })

  // 初始化文献增长趋势图表
  function initLiteratureTrendChart() {
    const chartDom = literatureTrendChart.value
    const myChart = echarts.init(chartDom)

    const months = ['一月', '二月', '三月', '四月', '五月', '六月']
    const newLiteratureData = [120, 180, 150, 220, 200, 250]
    const totalLiteratureData = [800, 920, 1070, 1290, 1490, 1740]

    const option = {
      grid: {
        top: '10%',
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      legend: {},
      tooltip: {
        trigger: 'axis', // 在坐标轴上触发
        axisPointer: {
          axis: 'x'
        }
      },
      xAxis: {
        type: 'category',
        data: months,
        axisLine: {
          lineStyle: {
            color: '#E3E3E3'
          }
        },
        axisLabel: {
          color: 'rgba(24, 24, 25, 0.42)',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#E3E3E3'
          }
        },
        axisLabel: {
          color: 'rgba(24, 24, 25, 0.42)',
          fontSize: 12,
          formatter(value) {
            if (value >= 1000) {
              return `${value / 1000},000`
            }
            return value
          }
        }
      },
      series: [
        {
          name: '系统保有文献',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#F4BE37'
          },
          lineStyle: {
            width: 2,
            color: '#F4BE37'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#F8DD9C' },
                { offset: 1, color: 'rgba(255, 255, 255, 0)' }
              ]
            }
          },
          data: totalLiteratureData
        },
        {
          name: '新增文献',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#1F72CF'
          },
          lineStyle: {
            width: 2,
            color: '#5388D8'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#5388D8' },
                { offset: 1, color: 'rgba(255, 255, 255, 0)' }
              ]
            }
          },
          data: newLiteratureData
        }
      ]
    }

    myChart.setOption(option)
  }

  // 初始化期刊增长趋势图表
  function initJournalTrendChart() {
    const chartDom = journalTrendChart.value
    const myChart = echarts.init(chartDom)

    const months = ['一月', '二月', '三月', '四月', '五月', '六月']
    const newJournalData = [20, 30, 25, 40, 35, 45]
    const totalJournalData = [300, 320, 345, 385, 420, 465]

    const option = {
      grid: {
        top: '10%',
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      legend: {},
      tooltip: {
        trigger: 'axis', // 在坐标轴上触发
        axisPointer: {
          axis: 'x'
        }
      },
      xAxis: {
        type: 'category',
        data: months,
        axisLine: {
          lineStyle: {
            color: '#E3E3E3'
          }
        },
        axisLabel: {
          color: 'rgba(24, 24, 25, 0.42)',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#E3E3E3'
          }
        },
        axisLabel: {
          color: 'rgba(24, 24, 25, 0.42)',
          fontSize: 12,
          formatter(value) {
            if (value >= 1000) {
              return `${value / 1000},000`
            }
            return value
          }
        }
      },
      series: [
        {
          name: '系统保有期刊',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#F4BE37'
          },
          lineStyle: {
            width: 2,
            color: '#F4BE37'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#F8DD9C' },
                { offset: 1, color: 'rgba(255, 255, 255, 0)' }
              ]
            }
          },
          data: totalJournalData
        },
        {
          name: '新增期刊',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#1F72CF'
          },
          lineStyle: {
            width: 2,
            color: '#5388D8'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#5388D8' },
                { offset: 1, color: 'rgba(255, 255, 255, 0)' }
              ]
            }
          },
          data: newJournalData
        }
      ]
    }

    myChart.setOption(option)
  }

  // 跳转到文献详情页
  function goToArticleDetail(articleId) {
    router.push(`/detail/${articleId}`)
  }

  // 跳转到文献列表页
  function goToLiterature(journalTitle) {
    if (journalTitle) {
      // 构建查询字符串
      const query = `(${journalTitle}[journalName])`
      router.push({
        path: '/literature',
        query: { query }
      })
    } else {
      router.push('/literature')
    }
  }
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/variables';

  .statistics {
    background-color: #f5f5f5;
    min-height: 100vh;
  }
  .stat-icon {
    width: 28px;
  }
  .content-section {
    padding: $spacing-xxl 0;
  }

  .container {
    max-width: 1536px;
    margin: 0 auto;
    padding: 0 $spacing-md;
    overflow-x: hidden;
  }

  .page-title {
    font-size: $font-size-xlarge;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin-bottom: $spacing-lg;
  }

  // 统计卡片样式
  .statistics-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: $spacing-lg;
    margin-bottom: $spacing-lg;

    @media (max-width: $breakpoint-lg) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: $breakpoint-sm) {
      grid-template-columns: 1fr;
    }
  }

  .statistics-card {
    background-color: $white;
    border-radius: $border-radius-lg;
    padding: $spacing-md;
    box-shadow: $box-shadow;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-xs;

    h2 {
      font-size: $font-size-medium;
      font-weight: $font-weight-regular;
      color: #374151;
    }
  }

  .card-icon {
    .icon-bg {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background-color: #eff6ff;

      .el-icon {
        font-size: 28px;
        color: $primary-color;
      }
    }
  }

  .card-content {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
  }

  .card-number {
    font-family: 'Alimama ShuHeiTi', sans-serif;
    font-size: $font-size-xlarge;
    font-weight: $font-weight-extrabold;
    color: $primary-color;
  }

  .card-trend {
    display: flex;
    align-items: center;
    font-size: $font-size-medium;

    &.up {
      color: #16a34a;
    }

    &.down {
      color: #ff7c52;
    }

    .el-icon {
      margin-right: 4px;
    }
  }

  .statistics-column {
    display: flex;
    gap: $spacing-lg;
    margin-bottom: $spacing-lg;
    width: 100%; // 确保容器宽度
    overflow: hidden; // 防止内容溢出
  }
  .statistics-panel {
    flex: 0 1 50%; // 严格设置为50%，不允许伸缩
    min-width: 0; // 防止flex子元素撑开容器
    max-width: 50%; // 确保最大宽度不超过50%
    background-color: $white;
    border-radius: $border-radius-lg;
    padding: $spacing-lg;
    box-shadow: $box-shadow;
    overflow: hidden; // 防止内容溢出
  }
  .title-more {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }
  .panel-title {
    font-size: $font-size-medium;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;
  }

  // 排行榜样式
  .ranking-list {
    width: 100%; // 确保不超出父容器
    overflow: hidden; // 防止内容溢出

    .ranking-item {
      display: flex;
      padding: $spacing-xs $spacing-md;
      background-color: #f9fafb;
      border-radius: $border-radius-md;
      margin-bottom: $spacing-sm;
      align-items: center;
      width: 100%; // 确保项目宽度
      box-sizing: border-box; // 包含padding在内
      min-width: 0; // 防止flex项目撑开容器
    }

    .ranking-number {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: $primary-color;
      color: $white;
      font-size: $font-size-medium;
      font-weight: $font-weight-bold;
      margin-right: $spacing-sm;
    }

    .ranking-content {
      flex: 1;
      min-width: 0; // 防止内容撑开容器
      overflow: hidden; // 防止溢出
    }
    .title-visits {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 4px;
    }

    .d-flex {
      display: flex;
    }

    .is-justify-space-between {
      justify-content: space-between;
    }

    .ranking-title {
      font-size: $font-size-small;
      font-weight: $font-weight-regular;
      color: #111827;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
      min-width: 0; // 防止文本撑开容器
      margin-right: $spacing-sm;
      max-width: calc(100% - 120px); // 为访问次数预留至少120px空间

      &.clickable {
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover {
          color: $primary-color;
          text-decoration: underline;
        }
      }
    }

    .ranking-info {
      font-size: 14px;
      color: #6b7280;
    }

    .ranking-visits {
      font-size: 14px;
      color: #4b5563;
      flex-shrink: 0; // 防止访问次数被压缩
      white-space: nowrap; // 防止换行
    }
  }

  .view-more {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: $primary-color;
    font-size: $font-size-small;
    cursor: pointer;

    span {
      margin-right: $spacing-xs;
    }
  }

  // 图表样式
  .chart-container {
    height: 300px;
    margin-bottom: $spacing-md;
  }

  .chart {
    width: 100%;
    height: 100%;
  }

  .chart-legend {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-lg;

    .legend-item {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.38);
    }

    .legend-color {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: $spacing-xs;

      &.new {
        background-color: #5388d8;
      }

      &.total {
        background-color: #f4be37;
      }
    }
  }

  // 关键词区域样式
  .statistics-keywords {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: $spacing-xl;
    .statistics-card {
      padding: $spacing-lg;
    }

    @media (max-width: $breakpoint-lg) {
      grid-template-columns: 1fr;
    }
  }
  .keywords-panel {
    .panel-title {
      margin-bottom: 15px;
    }
  }

  .keywords-container {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-sm;

    .keyword-tag {
      background-color: #eff6ff;
      color: $secondary-color;
      border-radius: 9999px;
      padding: 4px 12px;
      font-size: $font-size-small;
    }

    &.purple {
      .keyword-tag {
        background-color: rgba(147, 51, 234, 0.05);
        color: #b069f0;
      }
    }
  }

  // 响应式调整
  @media (max-width: $breakpoint-md) {
    .statistics-cards {
      gap: $spacing-md;
    }

    .statistics-details,
    .statistics-keywords {
      gap: $spacing-lg;
    }

    .card-number {
      font-size: $font-size-xxlarge;
    }

    .card-trend {
      font-size: $font-size-small;
    }

    .chart-container {
      height: 250px;
    }
  }

  @media (max-width: $breakpoint-sm) {
    .statistics-column {
      flex-direction: column;
      width: 100%;
    }

    .container {
      padding: 0 $spacing-sm;
    }

    .content-section {
      padding: $spacing-lg 0;
    }

    .statistics-panel {
      padding: $spacing-md;
      width: 100%;
      box-sizing: border-box;
    }

    .ranking-title {
      flex: 1;
      min-width: 0;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: $spacing-xs;
      max-width: calc(100% - 100px); // 为访问次数预留空间
    }

    .chart-container {
      height: 200px;
      width: 100%;
      overflow-x: hidden;
    }

    .ranking-number {
      width: 20px;
      height: 20px;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      aspect-ratio: 1/1;
      min-width: 20px;
    }

    .card-icon .icon-bg {
      width: 40px;
      height: 40px;

      .el-icon {
        font-size: 22px;
      }
    }

    .card-number {
      font-size: $font-size-large;
    }

    .card-trend {
      font-size: 14px;
    }

    .ranking-item {
      padding: $spacing-xs $spacing-sm;
    }

    .ranking-visits {
      font-size: 12px;
    }

    .title-more {
      margin-bottom: $spacing-sm;
    }

    .keywords-container .keyword-tag {
      font-size: 14px;
      padding: 2px 8px;
    }

    .view-more {
      font-size: 14px;
    }
  }

  @media (max-width: 480px) {
    .statistics-cards {
      grid-template-columns: 1fr;
      gap: $spacing-md;
    }

    .card-header h2 {
      font-size: 16px;
    }

    .card-number {
      font-size: $font-size-large;
    }

    .ranking-title {
      flex: 1;
      min-width: 0;
      font-size: 13px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: $spacing-xs;
      max-width: calc(100% - 90px); // 为访问次数预留空间
    }

    .chart-container {
      height: 180px;
    }

    .keywords-container {
      gap: $spacing-xs;
    }

    .keywords-container .keyword-tag {
      font-size: 12px;
      padding: 2px 6px;
    }

    .statistics-column {
      gap: $spacing-sm;
    }

    .ranking-number {
      min-width: 20px;
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
    }

    .title-visits {
      flex-wrap: nowrap;
    }

    .chart {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
  }
</style>
