<template>
  <div class="home">
    <section class="hero-section">
      <div class="container">
        <h1 class="hero-title">科学文献私人订制图书馆</h1>
        <p class="hero-subtitle">精准检索、深度分析、个性化定制您的专业文献资源库</p>

        <div class="search-container">
          <div class="search-bar">
            <div class="search-field">
              <input
                v-model="searchKeyword"
                type="text"
                class="search-input"
                :placeholder="searchPlaceholder"
                @keyup.enter="handleSearch"
              />
            </div>
            <button class="search-button" @click="handleSearch">
              <el-icon>
                <Search />
              </el-icon>
            </button>
          </div>

          <div class="search-options">
            <div class="search-option" @click="goToAdvancedSearch">
              <img src="@/assets/images/filter-icon.svg" alt="高级检索" class="option-icon" />
              <span>高级检索</span>
            </div>
            <div class="search-option" @click="goToSemanticSearch">
              <img src="@/assets/images/semantic-search-icon.svg" alt="语义检索" class="option-icon" />
              <span>语义检索</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计数据 -->
    <section class="stats-section">
      <div class="container">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-content">
              <h3 class="stat-number">{{ formatNumber(homeStats.totalArticles) }}</h3>
              <p class="stat-label">收录文献总数</p>
            </div>
            <div class="stat-icon">
              <img src="@/assets/images/doc-icon.svg" alt="文献总数" />
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-content">
              <h3 class="stat-number">{{ formatNumber(homeStats.monthlyArticle) }}</h3>
              <p class="stat-label">近期新增文献</p>
            </div>
            <div class="stat-icon">
              <img src="@/assets/images/data-icon.svg" alt="新增文献" />
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-content">
              <h3 class="stat-number">{{ formatNumber(homeStats.totalJournals) }}</h3>
              <p class="stat-label">覆盖期刊数量</p>
            </div>
            <div class="stat-icon">
              <img src="@/assets/images/journal-icon.svg" alt="期刊数量" />
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-content">
              <h3 class="stat-number">{{ formatNumber(homeStats.monthlyJournals) }}</h3>
              <p class="stat-label">近期新增期刊</p>
            </div>
            <div class="stat-icon">
              <img src="@/assets/images/new-journal-icon.svg" alt="新增期刊" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 特色功能 -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title">特色功能</h2>
        <div class="section-divider">
          <div class="divider-line"></div>
          <div class="divider-highlight"></div>
        </div>
        <div class="features-grid">
          <div class="feature-card" @click="navigateToFeature('literature')">
            <img src="@/assets/images/data-browse.svg" />
            <div class="feature-content">
              <h3 class="feature-title">数据浏览</h3>
              <p class="feature-description">浏览丰富的文献资源，按领域、主题进行探索</p>
            </div>
          </div>

          <div class="feature-card" @click="navigateToFeature('api')">
            <img src="@/assets/images/data-acquisition.svg" />
            <div class="feature-content">
              <h3 class="feature-title">获取数据</h3>
              <p class="feature-description">下载文献原文、元数据和参考文献信息</p>
            </div>
          </div>
          <div class="feature-card" @click="navigateToFeature('statistics')">
            <img src="@/assets/images/literature-statistics.svg" />
            <div class="feature-content">
              <h3 class="feature-title">文献统计</h3>
              <p class="feature-description">文献趋势分析，了解研究热点和发展方向</p>
            </div>
          </div>
          <div class="feature-card" @click="navigateToFeature('help')">
            <img src="@/assets/images/help.svg" />

            <div class="feature-content">
              <h3 class="feature-title">帮助手册</h3>
              <p class="feature-description">详细的使用指南，解答常见问题</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 特色文献集 -->
    <section class="collections-section">
      <div class="container">
        <h2 class="section-title">特色文献集</h2>
        <div class="section-divider">
          <div class="divider-line"></div>
          <div class="divider-highlight"></div>
        </div>

        <div class="collections-grid">
          <div class="collection-card" @click="navigateToLiterature">
            <div class="collection-content">
              <h3 class="collection-title">AI医疗研究集</h3>
              <p class="collection-description">人工智能在医疗领域的应用前沿</p>
            </div>
            <img src="@/assets/images/ai-medical.svg" />
          </div>

          <div class="collection-card" @click="navigateToLiterature">
            <div class="collection-content">
              <h3 class="collection-title">COVID-19研究专题</h3>
              <p class="collection-description">收录2,500+新冠病毒相关研究文献</p>
            </div>
            <img src="@/assets/images/covid.svg" />
          </div>

          <div class="collection-card" @click="navigateToLiterature">
            <div class="collection-content">
              <h3 class="collection-title">基因治疗前沿</h3>
              <p class="collection-description">最新基因编辑与治疗技术研究成果</p>
            </div>
            <img src="@/assets/images/geno.svg" />
          </div>

          <div class="collection-card" @click="navigateToLiterature">
            <div class="collection-content">
              <h3 class="collection-title">AI医疗研究集</h3>
              <p class="collection-description">人工智能在医疗领域的应用前沿</p>
            </div>
            <img src="@/assets/images/ai-medical.svg" />
          </div>
        </div>
      </div>
    </section>

    <!-- 热门文献 & 最新文献 -->
    <section class="articles-section">
      <div class="container">
        <div class="articles-grid">
          <!-- 热门文献 -->
          <div class="articles-column">
            <div class="articles-header">
              <h2 class="section-title">热门文献</h2>
              <router-link to="/literature" class="view-more">
                查看更多
                <el-icon>
                  <ArrowRight />
                </el-icon>
              </router-link>
            </div>
            <div class="section-divider">
              <div class="divider-line flex-1"></div>
            </div>

            <div class="articles-list">
              <ArticleList :articles="popularArticles" :loading="isLoadingPopular" />
            </div>
          </div>

          <!-- 最新文献 -->
          <div class="articles-column">
            <div class="articles-header">
              <h2 class="section-title">最新文献</h2>
              <router-link to="/literature" class="view-more">
                查看更多
                <el-icon>
                  <ArrowRight />
                </el-icon>
              </router-link>
            </div>
            <div class="section-divider">
              <div class="divider-line flex-1"></div>
            </div>

            <div class="articles-list">
              <ArticleList :articles="latestArticles" :loading="isLoadingLatest" />
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
  import { ref, onMounted, computed } from 'vue'
  import { Search, ArrowRight } from '@element-plus/icons-vue'
  import ArticleList from '@/components/ArticleList.vue'
  import { getHomeData, getPopluarArticles, getLastArticles } from '@/api/home'
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'

  const router = useRouter()

  // 搜索相关
  const searchType = ref('所有字段')
  const searchKeyword = ref('')
  // const searchOptions = [
  //   { label: '所有字段', value: '所有字段' },
  //   { label: '标题', value: '标题' },
  //   { label: '作者', value: '作者' },
  //   { label: 'DOI', value: 'DOI' },
  //   { label: 'PMID', value: 'PMID' }
  // ]

  const searchPlaceholder = computed(() => {
    switch (searchType.value) {
      case '标题':
        return '输入文献标题关键词'
      case '作者':
        return '输入作者姓名'
      case 'DOI':
        return '输入DOI号'
      case 'PMID':
        return '输入PMID号'
      default:
        return '输入标题、作者、DOI、PMID 等关键词进行检索'
    }
  })

  const handleSearch = () => {
    if (searchKeyword.value.trim()) {
      router.push({
        path: '/literature',
        query: {
          homeKeyword: searchKeyword.value.trim()
        }
      })
    } else {
      router.push('/literature')
    }
  }

  // 跳转到高级检索
  const goToAdvancedSearch = () => {
    router.push({
      path: '/literature',
      query: { mode: 'advanced' }
    })
  }

  // 跳转到语义检索
  const goToSemanticSearch = () => {
    router.push({
      path: '/literature',
      query: { mode: 'semantic' }
    })
  }

  // 特色功能导航
  const navigateToFeature = feature => {
    const routeMap = {
      literature: '/literature',
      api: '/api',
      statistics: '/statistics',
      help: '/help'
    }
    router.push(routeMap[feature])
  }

  // 跳转到文献页面
  const navigateToLiterature = () => {
    router.push('/literature')
  }

  // 数据状态
  const homeStats = ref({
    totalArticles: 0,
    monthlyArticle: 0,
    totalJournals: 0,
    monthlyJournals: 0
  })

  const popularArticles = ref([])
  const latestArticles = ref([])
  const isLoadingPopular = ref(true)
  const isLoadingLatest = ref(true)

  // 格式化数字
  const formatNumber = num => {
    if (num === null || num === undefined) return '0'
    return num.toLocaleString()
  }

  // 加载统计数据
  const loadHomeData = async() => {
    try {
      const response = await getHomeData()
      if (response.code === 200) {
        homeStats.value = response.data
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
      ElMessage.error('加载统计数据失败')
    }
  }

  // 格式作者
  const formatAuthors = authors => {
    if (!authors || authors.length === 0) return ''
    return authors.join(', ')
  }

  // 转换 VO -> 列表展示结构
  const mapToListItem = vo => {
    const y = vo.year || ''
    const v = vo.volume ? String(vo.volume) : ''
    const i = vo.issue ? String(vo.issue) : ''
    const p = vo.page ? String(vo.page) : ''
    // 构建显示字符串，根据字段是否存在添加对应符号
    // Fertility and sterility. 2025 123(5):913

    let volumeInfo = `${y} ${v}`
    if (i) {
      volumeInfo += `(${i})`
    }
    if (p) {
      volumeInfo += `:${p}`
    }
    // 格式化时间

    if (!vo.createTime) return ''
    const date = new Date(vo.createTime)
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const time = `${year}-${month}-${day}`

    return {
      id: vo.id,
      title: vo.title || '',
      authors: formatAuthors(vo.author),
      journal: vo.journalTitle || '',
      info: volumeInfo,
      date: time,
      hitnum: vo.hitNum
    }
  }

  // 加载热门文献
  const loadPopluarArticles = async() => {
    try {
      isLoadingPopular.value = true
      const response = await getPopluarArticles()
      if (response.code === 200) {
        popularArticles.value = (response.data || []).map(mapToListItem)
      }
    } catch (error) {
      console.error('加载热门文献失败:', error)
      ElMessage.error('加载热门文献失败')
    } finally {
      isLoadingPopular.value = false
    }
  }

  // 加载最新文献
  const loadLastArticles = async() => {
    try {
      isLoadingLatest.value = true
      const response = await getLastArticles()
      if (response.code === 200) {
        latestArticles.value = (response.data || []).map(mapToListItem)
      }
    } catch (error) {
      console.error('加载最新文献失败:', error)
      ElMessage.error('加载最新文献失败')
    } finally {
      isLoadingLatest.value = false
    }
  }

  // 初始化数据
  onMounted(async() => {
    await Promise.all([loadHomeData(), loadPopluarArticles(), loadLastArticles()])
  })
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/variables';

  .home {
    // 英雄区域
    .hero-section {
      //background:  rgba(205, 228, 255, 0.15);
      padding: 20px 0 30px;
      text-align: center;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('@/assets/images/hero-bg.png');
        background-size: cover;
        background-position: center;
        z-index: -1;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
      }
    }

    .hero-title {
      font-family: $font-family-title;
      font-size: $font-size-xxlarge;
      font-weight: $font-weight-extrabold;
      color: $primary-color;
      margin-bottom: $spacing-md;

      @media (max-width: $breakpoint-md) {
        font-size: 28px;
      }
    }

    .hero-subtitle {
      font-size: $font-size-medium;
      color: $primary-color;
      margin-bottom: $spacing-xl;

      @media (max-width: $breakpoint-md) {
        font-size: $font-size-large;
      }
    }

    .search-container {
      max-width: 893px;
      margin: 0 auto;
    }

    .search-bar {
      display: flex;
      height: 60px;
      background-color: $background-color;
      border-radius: $border-radius-md;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .search-field {
      display: flex;
      align-items: center;
      flex: 1;
      padding: 0 $spacing-md 0 0;

      :deep(.el-select__wrapper) {
        box-shadow: none !important;
        padding-left: 20px;
        font-weight: 600;

        &:hover {
          box-shadow: none !important;
        }
      }

      :deep(.el-select__selected-item) {
        color: rgba($primary-color, 1);
        font-size: $font-size-small;
      }
    }

    .search-type {
      width: 130px;
      border: none;
      background: transparent;

      :deep(.el-input__wrapper) {
        background-color: transparent;
        box-shadow: none !important;
        padding: 0;
      }

      :deep(.el-input__inner) {
        height: 100%;
        display: none;
      }

      :deep(.el-select__caret) {
        color: $primary-color;
        font-size: $font-size-small;
        margin-left: 4px;
      }

      :deep(.el-input__prefix) {
        color: $primary-color;
        font-weight: $font-weight-medium;
        font-size: $font-size-medium;
      }
    }

    :global(.search-type-dropdown) {
      .el-select-dropdown__item {
        font-size: $font-size-small;
        padding: 8px 12px;

        &.selected {
          color: $primary-color;
          font-weight: $font-weight-medium;
        }
      }
    }

    .search-input {
      flex: 1;
      height: 100%;
      border: none;
      background: transparent;
      font-size: $font-size-medium;
      color: $primary-color;
      padding: 0 $spacing-md;

      &::placeholder {
        color: rgba($primary-color, 1);
        font-size: $font-size-small;
      }

      &:focus {
        outline: none;
      }
    }

    .search-button {
      width: 75px;
      height: 100%;
      background-color: $primary-color;
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      .el-icon {
        font-size: 20px;
        color: $white;
      }
    }

    .search-options {
      display: flex;
      justify-content: center;
      gap: $spacing-xxl;
      margin-top: $spacing-xl;
    }

    .search-option {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      color: $primary-color;
      font-size: $font-size-medium;
      font-weight: $font-weight-medium;
      cursor: pointer;

      .option-icon {
        width: 22px;
        height: 22px;
      }
    }

    // 统计数据
    .stats-section {
      padding: $spacing-xl 0;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: $spacing-xl;

      @media (max-width: $breakpoint-lg) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: $breakpoint-sm) {
        grid-template-columns: 1fr;
      }
    }

    .stat-card {
      background-color: $white;
      border-radius: $border-radius-lg;
      padding: $spacing-sm $spacing-lg;
      box-shadow: $box-shadow;
      display: flex;
      align-items: center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba($primary-color, 0.05), transparent);
        transition: left 0.5s ease-in-out;
      }

      &:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow:
          0 12px 32px rgba(0, 0, 0, 0.15),
          0 4px 16px rgba($primary-color, 0.1);
        background-color: rgba($white, 0.98);

        &::before {
          left: 100%;
        }

        .stat-number {
          color: darken($primary-color, 5%);
          transform: scale(1.05);
        }

        .stat-icon {
          transform: scale(1.1) rotate(5deg);
        }
      }
    }

    .stat-icon {
      width: 74px;
      height: 74px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: $spacing-md;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      img {
        max-width: 100%;
        max-height: 100%;
        transition: inherit;
      }
    }

    .stat-content {
      flex: 1;
    }

    .stat-number {
      font-family: $font-family-title;
      font-size: $font-size-xxxlarge;
      font-weight: $font-weight-extrabold;
      color: $primary-color;
      line-height: 0.8;
      margin-bottom: $spacing-md;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .stat-label {
      font-size: $font-size-medium;
      color: $black;
    }

    // 特色功能
    .section-title {
      font-size: $font-size-xlarge;
      color: $primary-color;
      margin-bottom: $spacing-xs;
      margin-top: 0;
    }

    .section-divider {
      display: flex;
      align-items: center;
      margin-bottom: $spacing-xl;
    }

    .divider-line {
      height: 1px;
      background-color: $primary-color;
      width: 100px;
    }

    .divider-highlight {
      width: 54px;
      height: 4px;
      background-color: $primary-color;
      position: absolute;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: $spacing-xl;

      @media (max-width: $breakpoint-lg) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: $breakpoint-sm) {
        grid-template-columns: 1fr;
      }
    }

    .feature-card {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: $spacing-lg;
      border-radius: $border-radius-lg;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      position: relative;
      background-color: transparent;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba($white, 0.8), rgba($white, 0.4));
        border-radius: $border-radius-lg;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: -1;
      }

      &:hover {
        transform: translateY(-8px) scale(1.03);
        box-shadow:
          0 16px 40px rgba(0, 0, 0, 0.12),
          0 6px 20px rgba($primary-color, 0.08);

        &::before {
          opacity: 1;
        }

        img {
          transform: scale(1.1) rotate(-3deg);
          filter: brightness(1.1);
        }

        .feature-title {
          color: darken($primary-color, 8%);
          transform: translateY(-2px);
        }

        .feature-description {
          color: lighten($black, 10%);
          transform: translateY(-1px);
        }
      }

      img {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        margin-bottom: $spacing-lg;
      }
    }

    .feature-icon {
      width: 120px;
      height: 120px;
      margin-bottom: $spacing-lg;

      .icon-bg {
        width: 100%;
        height: 100%;
        background-color: rgba($primary-color, 0.2);
        border-radius: 50%;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 10%;
          left: 10%;
          width: 80%;
          height: 80%;
          background-color: $primary-color;
          border-radius: 50%;
        }
      }
    }

    .feature-content {
      max-width: 197px;
    }

    .feature-title {
      font-size: $font-size-large;
      font-weight: $font-weight-bold;
      margin: $spacing-sm;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .feature-description {
      font-size: $font-size-medium;
      //text-align: left;
      color: $black;
      margin: 10px 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    // 特色文献集
    .collections-section {
      padding: $spacing-xxl 0;
    }

    .collections-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: $spacing-lg;

      @media (max-width: $breakpoint-lg) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: $breakpoint-sm) {
        grid-template-columns: 1fr;
      }
    }

    .collection-card {
      background-color: $white;
      border-radius: $border-radius-xl;
      padding: $spacing-xs $spacing-md;
      box-shadow: $box-shadow;
      display: flex;
      align-items: center;
      column-gap: 15px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      position: relative;
      border: 1px solid transparent;
      overflow: hidden;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba($primary-color, 0.02), rgba($secondary-color, 0.01));
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 0;
      }

      &:hover {
        transform: translateY(-6px) scale(1.02);
        box-shadow:
          0 14px 36px rgba(0, 0, 0, 0.12),
          0 5px 18px rgba($primary-color, 0.08);
        background-color: rgba($white, 0.98);
        border-color: rgba($primary-color, 0.1);

        &::after {
          opacity: 1;
        }

        img {
          transform: scale(1.08) rotate(-2deg);
          filter: brightness(1.05) saturate(1.1);
        }

        .collection-title {
          color: darken($primary-color, 8%);
          transform: translateX(2px);
        }

        .collection-description {
          color: lighten($black, 15%);
          transform: translateX(1px);
        }
      }

      > * {
        position: relative;
        z-index: 1;
      }

      img {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }

    .collection-content {
      flex: 1;
    }

    .collection-title {
      font-size: $font-size-large;
      font-weight: $font-weight-bold;
      color: $primary-color;
      margin-bottom: $spacing-xs;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .collection-description {
      font-size: $font-size-medium;
      color: $black;
      margin: 12px 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .collection-icon {
      width: 95px;
      height: 95px;
      background-color: rgba($secondary-color, 0.1);
      border-radius: 50%;
    }

    // 文献列表

    .articles-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: $spacing-xxl;

      @media (max-width: $breakpoint-lg) {
        grid-template-columns: 1fr;
      }
    }

    .articles-column {
      padding-bottom: 40px;
    }

    .articles-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-xs;
    }

    .view-more {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      font-size: $font-size-medium;
      color: $primary-color;
    }

    .articles-list {
      display: flex;
      flex-direction: column;
      gap: $spacing-lg;
      //margin-top: $spacing-lg;
    }

    // 临时替代ArticleCard组件的样式
    .article-placeholder {
      padding: $spacing-md 0;
      border-bottom: 1px solid rgba($primary-color, 0.1);

      h3 {
        font-size: $font-size-xlarge;
        font-weight: $font-weight-bold;
        color: $primary-color;
        margin-bottom: $spacing-sm;
      }

      p {
        font-size: $font-size-medium;
        color: $black;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }
</style>
