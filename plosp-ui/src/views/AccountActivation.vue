<template>
  <div class="activation-page">
    <!-- 装饰性几何图形 -->
    <div class="decoration-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>
    
    <div class="activation-container">
      <div class="activation-card">
        <div class="activation-header">
          <h1 class="activation-title">请激活您的注册账户</h1>
        </div>

        <div class="activation-content">
          <!-- 激活提示信息 -->
          <el-alert
            :title="alertTitle"
            type="success"
            show-icon
            :closable="false"
            class="activation-alert"
          >
            <template #default>
              <div class="alert-description" v-html="alertDescription"></div>
            </template>
          </el-alert>

          <!-- 操作按钮区域 -->
          <div class="activation-actions">
            <el-button 
              type="primary" 
              size="large" 
              class="login-button"
              @click="goToLogin"
            >
              登录
            </el-button>
          </div>

          <!-- 底部链接 -->
          <div class="activation-links">
            <div class="link-item">
              <el-icon><Lock /></el-icon>
              <router-link to="/forgot-password" class="link">忘记密码</router-link>
            </div>
            <div class="link-item">
              <el-icon><UserFilled /></el-icon>
              <router-link to="/register" class="link">创建新账户</router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { Message, User, Lock, UserFilled } from '@element-plus/icons-vue'

  const router = useRouter()
  const route = useRoute()

  // 从路由参数或注册页面传递的邮箱地址
  const userEmail = ref(route.query.email || '用户邮箱')

  // 动态生成提示信息
  const alertTitle = ref('激活链接已发送到您的注册邮箱')
  const alertDescription = ref('')

  onMounted(() => {
    // 构建描述信息，为邮箱添加样式
    alertDescription.value = `激活链接已发送到您的注册邮箱 <span class="email-highlight">${userEmail.value}</span>，请点击链接在48小时内激活账户。如需其他帮助，请联系 <span class="email-highlight"><EMAIL></span>`
  })

  // 跳转到登录页面
  const goToLogin = () => {
    // 模拟用户已激活账户，直接设置登录状态
    localStorage.setItem('userToken', 'mock-token-123')
    localStorage.setItem('userInfo', JSON.stringify({
      name: 'Wu',
      email: userEmail.value
    }))

    // 跳转到首页并设置登录状态
    window.location.href = '/?from=login'
  }
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.activation-page {
  min-height: 60vh;
  //background: linear-gradient(135deg, rgba($primary-color, 0.05) 0%, rgba($secondary-color, 0.1) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 20% 80%, rgba($secondary-color, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba($primary-color, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba($light-blue, 0.05) 0%, transparent 50%);
    z-index: 0;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="dots" width="60" height="60" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="1.5" fill="rgba(4,56,115,0.1)"/><circle cx="10" cy="10" r="1" fill="rgba(79,156,249,0.08)"/><circle cx="50" cy="50" r="1" fill="rgba(79,156,249,0.08)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
    z-index: 0;
  }
}

.activation-container {
  width: 100%;
  max-width: 600px;
}

.activation-card {
  background: $white;
  border-radius: $border-radius-xxl;
  box-shadow: $box-shadow;
  padding: 30px;
  position: relative;
  z-index: 1;
  border: 1px solid rgba($primary-color, 0.08);

  
  @media (max-width: $breakpoint-md) {
    padding: $spacing-xl;
  }
}

.activation-header {
  text-align: center;
  margin-bottom: $spacing-xxxl;
  
  .activation-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, $primary-color, lighten($primary-color, 20%));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto $spacing-lg auto;
    box-shadow: 0 10px 30px rgba($primary-color, 0.3);
    
    .el-icon {
      color: white;
    }
  }
  
  .activation-title {
    font-size: $font-size-xxxxxlarge;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;
    background: linear-gradient(135deg, $primary-color, lighten($primary-color, 20%));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.activation-content {
  .activation-alert {
    margin-bottom: $spacing-xl;

    :deep(.el-alert__content) {
      .el-alert__title {
        color: $primary-color;
        font-weight: $font-weight-medium;
        font-size: $font-size-medium;
      }

      .el-alert__description {
        color: $gray;
        line-height: 1.6;
        margin-top: $spacing-xs;
      }
    }

    :deep(.el-alert__icon) {
      color: $primary-color;
    }

    :deep(.alert-description) {
      color: $gray;
      line-height: 1.6;

      .email-highlight {
        color: $primary-color;
        font-weight: $font-weight-medium;
      }
    }
  }
  
  .activation-actions {
    text-align: center;
    margin-bottom: $spacing-xl;
    
    .login-button {
      width: 200px;
      height: 48px;
      background-color: $primary-color;
      border-color: $primary-color;
      border-radius: $border-radius-md;
      font-size: $font-size-medium;
      font-weight: $font-weight-medium;
      
      &:hover {
        background-color: darken($primary-color, 10%);
        border-color: darken($primary-color, 10%);
      }
    }
  }
  
  .activation-links {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      gap: $spacing-md;
    }
    
    .link-item {
      display: flex;
      align-items: center;
      gap: $spacing-xs;
      color: $gray;
      font-size: $font-size-small;
      
      .el-icon {
        color: $primary-color;
      }
      
      .link {
        color: $primary-color;
        text-decoration: none;
        font-weight: $font-weight-medium;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// 装饰性几何图形
.decoration-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
  
  .shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.6;
    animation: float 6s ease-in-out infinite;
    
    &.shape-1 {
      width: 120px;
      height: 120px;
      background: linear-gradient(135deg, rgba($secondary-color, 0.2), rgba($light-blue, 0.1));
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }
    
    &.shape-2 {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, rgba($primary-color, 0.15), rgba($secondary-color, 0.1));
      top: 70%;
      right: 15%;
      animation-delay: 2s;
    }
    
    &.shape-3 {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, rgba($light-blue, 0.2), rgba($secondary-color, 0.1));
      top: 30%;
      right: 25%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}
</style>
