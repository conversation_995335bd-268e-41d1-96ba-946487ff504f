<template>
  <div class="register-page">
    <!-- 装饰性几何图形 -->
    <div class="decoration-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
      <div class="shape shape-4"></div>
    </div>

    <div class="register-container">
      <div class="register-card">
        <div class="register-header">
          <h1 class="register-title">用户注册</h1>
        </div>
        <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerRules"
          class="register-form"
          label-position="left"
          size="large"
          label-width="100"
        >
          <!-- 账户信息 -->
          <div class="form-section">
            <h3 class="section-title">账户信息</h3>

            <el-form-item
              prop="email"
              class="form-group form-group-with-hint"
              required
              label="邮箱"
            >
              <div class="d-flex form-email">

                <div class="d-flex email-input-group">
                  <el-input
                    v-model="registerForm.email"
                    placeholder="请填写邮箱地址"
                    class="form-input"
                  />
                  <el-button
                    type="primary"
                    class="send-code-btn"
                    :loading="sendingCode"
                    :disabled="!isEmailValid || countdown > 0"
                    @click="handleSendCode"
                  >
                    {{ countdown > 0 ? `${countdown}s后重发` : '发送验证码' }}
                  </el-button>
                </div>
              </div>

            </el-form-item>
            <el-form-item
              prop="verificationCode"
              class="form-group form-group-with-hint"
              required
              label="邮箱验证码"
            >
              <el-input
                v-model="registerForm.verificationCode"
                autocomplete="new-password"
                placeholder="请输入邮箱验证码"
                class="form-input"
              />
            </el-form-item>
            <el-form-item
              prop="password"
              class="form-group form-group-with-hint"
              required
              label="密码"
            >
              <el-input
                v-model="registerForm.password"
                autocomplete="new-password"
                type="password"
                placeholder="请输入密码"
                class="form-input"
                show-password
              />
              <div class="form-hint">
                密码应为字母、数字和特殊字符（如@!?%#）的组合，长度为8-30位
              </div>
            </el-form-item>

            <el-form-item
              prop="confirmPassword"
              class="form-group form-group-with-hint"
              required
              label="确认密码"
            >
              <el-input
                v-model="registerForm.confirmPassword"
                autocomplete="off"
                type="password"
                placeholder="请再次输入密码"
                class="form-input"
                show-password
              />
            </el-form-item>
          </div>

          <!-- 个人信息 -->
          <div class="form-section">
            <h3 class="section-title">个人信息</h3>

            <div class="form-row">
              <el-form-item
                prop="lastName"
                class="form-group half"
                required
                label="姓"
              >
                <el-input
                  v-model="registerForm.lastName"
                  placeholder="请输入姓"
                  class="form-input"
                />
              </el-form-item>
              <el-form-item
                prop="firstName"
                class="form-group half"
                required
                label="名"
              >
                <el-input
                  v-model="registerForm.firstName"
                  placeholder="请输入名"
                  class="form-input"
                />
              </el-form-item>
            </div>

            <el-form-item
              prop="organization"
              class="form-group"
              required
              label="机构"
            >
              <el-autocomplete
                v-model="registerForm.organization"
                :fetch-suggestions="queryOrganizations"
                placeholder="请输入机构名称"
                class="form-input"
                clearable
                :trigger-on-focus="false"
                @select="handleOrganizationSelect"
              >
                <template #default="{ item }">
                  <div class="organization-item">
                    <span>{{ item.label }}</span>
                  </div>
                </template>
              </el-autocomplete>
            </el-form-item>

            <el-form-item class="form-group" label="部门">
              <el-input
                v-model="registerForm.department"
                placeholder="请输入部门名称"
                class="form-input"
              />
            </el-form-item>

            <el-form-item class="form-group" label="PI姓名">
              <el-input
                v-model="registerForm.piName"
                placeholder="请输入PI姓名"
                class="form-input"
              />
            </el-form-item>

            <div class="form-row">
              <el-form-item class="form-group half" label="职位">
                <el-input v-model="registerForm.title" class="form-input" placeholder="请输入职位" />
              </el-form-item>

              <el-form-item class="form-group half" label="电话" prop="phone">
                <el-input
                  v-model="registerForm.phone"
                  placeholder="请输入电话号码"
                  class="form-input"
                />
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item
                prop="country"
                class="form-group third"
                required
                label="国家/地区"
              >
                <el-select
                  v-model="registerForm.country"
                  placeholder="--请选择--"
                  class="form-input"
                >
                  <el-option
                    v-for="country in countryOptions"
                    :key="country.value"
                    :label="country.displayLabel"
                    :value="country.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item class="form-group third" label="省/州">
                <el-input
                  v-model="registerForm.province"
                  placeholder="请输入省份或州"
                  class="form-input"
                />
              </el-form-item>

              <el-form-item class="form-group third" label="城市">
                <el-input
                  v-model="registerForm.city"
                  placeholder="请输入城市"
                  class="form-input"
                />
              </el-form-item>
            </div>
          </div>

          <!-- 隐私政策 -->
          <el-form-item
            prop="agreePolicy"
            class="form-group"
          >
            <el-checkbox v-model="registerForm.agreePolicy">
              我已阅读并同意
              <a href="https://www.biosino.org/bmdc/privacyPolicy" target="_blank">隐私政策</a>
            </el-checkbox>
          </el-form-item>

          <!-- 提交按钮 -->
          <el-button
            type="primary"
            size="large"
            class="register-button"
            :loading="loading"
            @click="handleRegister"
          >
            立即注册
          </el-button>

          <div class="login-link">
            已有账户？
            <router-link to="/login" class="link">立即登录</router-link>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import { useAuthStore } from '@/stores/auth'
  import { register, sendVerificationCode } from '@/api/auth'
  import { getRedirectPath } from '@/utils/permission'
  import { useDictStore } from '@/stores/dict'

  const router = useRouter()
  const route = useRoute()
  const authStore = useAuthStore()
  const dictStore = useDictStore()

  // 计算属性
  const countryOptions = computed(() => dictStore.countries)
  const organizationOptions = computed(() => dictStore.organizations)

  // 表单引用
  const registerFormRef = ref()

  // 状态管理
  const sendingCode = ref(false)
  const countdown = ref(0)
  let countdownTimer = null

  // 表单数据
  const registerForm = ref({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    organization: '',
    department: '',
    piName: '',
    title: '',
    phone: '',
    country: '',
    province: '',
    city: '',
    agreePolicy: false,
    verificationCode: ''
  })

  // 加载状态
  const loading = ref(false)
  // 表单验证规则
  const registerRules = {
    email: [
      { required: true, message: '请输入邮箱地址', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    verificationCode: [
      { required: true, message: '请输入验证码', trigger: 'blur' },
      { len: 6, message: '验证码必须为6位数字', trigger: 'blur' },
      { pattern: /^\d{6}$/, message: '验证码必须为6位数字', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 8, max: 30, message: '密码长度应为8-30位', trigger: 'blur' },
      { pattern: /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?])[A-Za-z\d!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]+$/, message: '密码应包含字母、数字和特殊字符', trigger: 'blur' }
    ],
    confirmPassword: [
      { required: true, message: '请再次输入密码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value !== registerForm.value.password) {
            callback(new Error('两次输入的密码不一致'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    firstName: [
      { required: true, message: '请输入姓', trigger: 'blur' }
    ],
    lastName: [
      { required: true, message: '请输入名', trigger: 'blur' }
    ],
    organization: [
      { required: true, message: '请输入机构', trigger: 'blur' }
    ],
    country: [
      { required: true, message: '请选择国家/地区', trigger: 'change' }
    ],
    phone: [
      {
        validator: (rule, value, callback) => {
          if (value && !/^1[3-9]\d{9}$/.test(value)) {
            callback(new Error('请输入正确的手机号码'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }

  // 邮箱格式验证
  const isEmailValid = computed(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(registerForm.value.email)
  })
  // 发送验证码
  const handleSendCode = async() => {
    if (!isEmailValid.value) {
      ElMessage.warning('请输入正确的邮箱地址')
      return
    }

    sendingCode.value = true

    try {
      await sendVerificationCode(registerForm.value.email)
      ElMessage.success('验证码已发送到您的邮箱，请查收')
      startCountdown()
    } catch (error) {
      ElMessage.error('发送验证码失败，请稍后重试')
    } finally {
      sendingCode.value = false
    }
  }

  // 开始倒计时
  const startCountdown = () => {
    countdown.value = 60
    countdownTimer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(countdownTimer)
        countdownTimer = null
      }
    }, 1000)
  }

  // 处理注册
  const handleRegister = async() => {
    if (!registerFormRef.value) return

    const valid = await registerFormRef.value.validate().catch(() => false)
    if (!valid) {
      ElMessage.warning('请检查表单输入')
      return
    }

    loading.value = true

    try {
      // 构建注册数据
      const registerData = {
        email: registerForm.value.email,
        password: registerForm.value.password,
        confirmPassword: registerForm.value.confirmPassword,
        firstName: registerForm.value.firstName,
        lastName: registerForm.value.lastName,
        organization: registerForm.value.organization,
        department: registerForm.value.department,
        piName: registerForm.value.piName,
        title: registerForm.value.title,
        phone: registerForm.value.phone,
        countryRegion: registerForm.value.country,
        stateProvince: registerForm.value.province,
        city: registerForm.value.city,
        userType: 'researcher', // 默认用户类型
        verificationCode: registerForm.value.verificationCode
      }

      const response = await register(registerData)

      if (response.code === 200) {
        ElMessage.success('注册成功！请登录您的账户')
        router.push('/login')
      } else {
        ElMessage.error(response.msg || '注册失败，请联系管理员！')
      }
    } finally {
      loading.value = false
    }
  }

  // 组件挂载时检查是否已登录
  onMounted(async() => {
    if (authStore.isLoggedIn) {
      const redirectPath = getRedirectPath(route)
      router.push(redirectPath)
      return
    }

    // 初始化字典数据
    await dictStore.initCommonDicts()
  })

  // 组件卸载时清理定时器
  onUnmounted(() => {
    if (countdownTimer) {
      clearInterval(countdownTimer)
    }
  })

  // 机构搜索方法
  const queryOrganizations = (queryString, callback) => {
    const organizations = organizationOptions.value || []

    if (!queryString) {
      // 如果没有输入，返回前10个选项
      callback(organizations.slice(0, 10))
      return
    }

    // 根据输入过滤机构
    const results = organizations.filter(org =>
      org.label.toLowerCase().includes(queryString.toLowerCase()) ||
      org.value.toLowerCase().includes(queryString.toLowerCase())
    )

    // 如果没有匹配结果，允许用户输入新的机构名称
    if (results.length === 0) {
      callback([{
        label: queryString,
        value: queryString,
        isNew: true
      }])
    } else {
      callback(results)
    }
  }

  // 选择机构时的处理
  const handleOrganizationSelect = item => {
    registerForm.value.organization = item.label

    if (item.isNew) {
      console.log('用户输入了新的机构:', item.value)
      // 可以在这里添加新机构到本地缓存
    }
  }
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, rgba($primary-color, 0.05) 0%, rgba($secondary-color, 0.1) 100%);
  padding: $spacing-xl 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 80%, rgba($secondary-color, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba($primary-color, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba($light-blue, 0.05) 0%, transparent 50%);
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="dots" width="60" height="60" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="1.5" fill="rgba(4,56,115,0.1)"/><circle cx="10" cy="10" r="1" fill="rgba(79,156,249,0.08)"/><circle cx="50" cy="50" r="1" fill="rgba(79,156,249,0.08)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
    z-index: 0;
  }
}

.register-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 $spacing-lg;
}

.register-card {
  background: $white;
  border-radius: $border-radius-xxl;
  box-shadow: $box-shadow;
  padding:30px;
  position: relative;
  z-index: 1;
  border: 1px solid rgba($primary-color, 0.08);

  @media (max-width: $breakpoint-md) {
    padding: $spacing-xl;
  }
}

.register-header {
  text-align: center;
  margin-bottom: $spacing-xxxl;

  .register-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, $primary-color, lighten($primary-color, 20%));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto $spacing-lg auto;
    box-shadow: 0 10px 30px rgba($primary-color, 0.3);

    .el-icon {
      color: white;
    }
  }

  .register-title {
    font-size: $font-size-xxxxxlarge;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0 0 $spacing-sm 0;
    background: linear-gradient(135deg, $primary-color, lighten($primary-color, 20%));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .register-subtitle {
    font-size: $font-size-small;
    color: $gray;
    margin: 0;
    text-align: right;

    .required {
      color: #ff4757;
      margin-right: 4px;
    }
  }
}

.register-form {
  :deep(.el-form-item) {
    margin-bottom: $spacing-lg;

    .el-form-item__label {
      font-size: 16px;
      color: #374151;
      font-weight: 500;
      padding: 0;
      margin-right: 10px;
      justify-content: flex-end;
    }

    .el-form-item__content {
      line-height: 1.4;
    }

    .el-form-item__error {
      font-size: 12px;
      color: #ff4757;
      padding-top: 4px;
    }
  }

  .form-section {
    margin-bottom: 6px;

    .section-title {
      font-size: $font-size-large;
      font-weight: $font-weight-medium;
      color: $gray;
      margin: 0 0 $spacing-lg 0;
      padding-bottom: $spacing-sm;
      border-bottom: 1px solid #e1e5e9;
    }
  }

  .form-group {
    &.half {
      width: calc(50% - #{$spacing-sm / 2});
    }

    &.third {
      width: calc(33.333% - #{$spacing-sm * 2 / 3});
    }

    .form-label {
      display: block;
      font-size: $font-size-small;
      color: $gray;
      font-weight: $font-weight-medium;

      .required {
        color: #ff4757;
        margin-right: 4px;
      }
    }

    .form-input {
      width: 100%;

      :deep(.el-input__wrapper),
      :deep(.el-select__wrapper) {
        border-radius: $border-radius-md;
        box-shadow: none;
        border: 1px solid #e1e5e9;

        &:hover {
          border-color: $primary-color;
        }

        &.is-focus {
          border-color: $primary-color;
          box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
        }
      }
    }

    .form-hint {
      font-size: 14px;
      color: #4F9CF9;
      margin-top: $spacing-xs;
      line-height: 1.4;
    }
  }

  // 当表单项验证通过时隐藏提示文字
  .form-group-with-hint {
    &.is-success {
      .form-hint {
        display: none;
      }
    }

    // 默认显示提示文字，验证失败时也显示
    .form-hint {
      display: block;
    }
  }

  .form-row {
    display: flex;
    gap: $spacing-sm;

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
      gap: 0;

      .form-group {
        width: 100% !important;
      }
    }
  }

  .privacy-link {
    color: #4F9CF9;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .register-button {
    width: 100%;
    height: 48px;
    background-color: $primary-color;
    border-color: $primary-color;
    border-radius: $border-radius-md;
    font-size: $font-size-medium;
    font-weight: $font-weight-medium;

    &:hover {
      background-color: darken($primary-color, 10%);
      border-color: darken($primary-color, 10%);
    }
  }

  .login-link {
    text-align: center;
    margin-top: $spacing-lg;
    color: $gray;
    font-size: $font-size-small;

    .link {
      color: $primary-color;
      text-decoration: none;
      font-weight: $font-weight-medium;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  :deep(.el-checkbox) {
    .el-checkbox__label {
      color: $gray;
      font-size: $font-size-small;
    }
  }
}
.form-email{
  display: flex;
  flex-direction: column;
  width: 100%;
  :deep(.el-input__wrapper){
    width: 100%;
    margin-right: $spacing-lg;
  }
}
.send-code-btn {
  flex-shrink: 0;
  width: 130px;
  height: 40px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid $primary-color;
  color: $primary-color;
  background: transparent;

  &:hover:not(:disabled) {
    background: $primary-color;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba($primary-color, 0.3);
  }

  &:disabled {
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    color: #999;
    transform: none;
    box-shadow: none;
  }
}

// 装饰性几何图形
.decoration-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;

  .shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.6;
    animation: float 8s ease-in-out infinite;

    &.shape-1 {
      width: 100px;
      height: 100px;
      background: linear-gradient(135deg, rgba($secondary-color, 0.2), rgba($light-blue, 0.1));
      top: 5%;
      left: 8%;
      animation-delay: 0s;
    }

    &.shape-2 {
      width: 70px;
      height: 70px;
      background: linear-gradient(135deg, rgba($primary-color, 0.15), rgba($secondary-color, 0.1));
      top: 80%;
      right: 10%;
      animation-delay: 2s;
    }

    &.shape-3 {
      width: 90px;
      height: 90px;
      background: linear-gradient(135deg, rgba($light-blue, 0.2), rgba($secondary-color, 0.1));
      top: 40%;
      right: 5%;
      animation-delay: 4s;
    }

    &.shape-4 {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, rgba($secondary-color, 0.15), rgba($primary-color, 0.1));
      top: 60%;
      left: 5%;
      animation-delay: 6s;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(180deg);
  }
}
</style>
