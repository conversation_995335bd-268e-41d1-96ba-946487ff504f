<template>
  <div class="data-management-container">
    <div class="page-header">
      <h1 class="page-title">ES数据管理</h1>
      <p class="page-description">管理系统数据的更新、生成和状态监控</p>
    </div>

    <div class="management-content">
      <!-- Token输入区域 -->
      <div class="token-input-section">
        <div class="token-input-card">
          <h3 class="token-title">认证Token</h3>
          <p class="token-description">请输入管理员Token以执行数据管理操作</p>
          <div class="token-input-wrapper">
            <el-input
              v-model="adminToken"
              type="password"
              placeholder="请输入管理员Token"
              show-password
              size="large"
              class="token-input"
            />
          </div>

          <!-- 管理按钮区域 -->
          <div class="button-list">
            <!-- 全量更新 -->
            <div class="button-item">
              <el-button
                type="primary"
                :loading="esUpdateLoading"
                class="long-button"
                @click="handleEsUpdate"
              >
                <el-icon v-if="!esUpdateLoading">
                  <Refresh />
                </el-icon>
                {{ esUpdateLoading ? '更新中...' : '全量更新' }}
              </el-button>
            </div>

            <!-- 向量生成 -->
            <div class="button-item">
              <el-button
                type="success"
                :loading="vectorGenerateLoading"
                class="long-button"
                @click="handleVectorGenerate"
              >
                <el-icon v-if="!vectorGenerateLoading">
                  <Refresh />
                </el-icon>
                {{ vectorGenerateLoading ? '生成中...' : '向量生成' }}
              </el-button>
            </div>

            <!-- MeSH生成 -->
            <div class="button-item">
              <el-button
                type="warning"
                :loading="meshProcessLoading"
                class="long-button"
                @click="handleMeshProcess"
              >
                <el-icon v-if="!meshProcessLoading">
                  <Refresh />
                </el-icon>
                {{ meshProcessLoading ? '生成中...' : 'MeSH生成' }}
              </el-button>
            </div>

            <!-- 任务状态 -->
            <div class="button-item">
              <el-button
                type="info"
                :loading="taskStatusLoading"
                class="long-button"
                @click="handleCheckTaskStatus"
              >
                <el-icon v-if="!taskStatusLoading">
                  <InfoFilled />
                </el-icon>
                {{ taskStatusLoading ? '查询中...' : '任务状态' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div></template>

<script setup>
  import { ref } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Refresh, InfoFilled } from '@element-plus/icons-vue'
  import {
    dataProcess,
    getTaskStatus
  } from '@/api/article'

  // 响应式变量
  const adminToken = ref('')
  const esUpdateLoading = ref(false)
  const vectorGenerateLoading = ref(false)
  const meshProcessLoading = ref(false)
  const taskStatusLoading = ref(false)

  // 验证Token
  const validateToken = () => {
    if (!adminToken.value || adminToken.value.trim() === '') {
      ElMessage.warning('请先输入管理员Token')
      return false
    }
    return true
  }

  // ES数据全量更新方法
  const handleEsUpdate = async() => {
    if (!validateToken()) return

    try {
      esUpdateLoading.value = true

      // 确认对话框
      await ElMessageBox.confirm(
        'ES数据全量更新将重新索引所有数据，此操作可能需要较长时间，确定要继续吗？',
        '确认ES数据全量更新',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 调用统一数据处理接口
      const response = await dataProcess({
        token: adminToken.value.trim(),
        type: 'basic'
      })

      if (response.code === 200 || response.success) {
        ElMessage.success('ES数据全量更新已启动，请稍后刷新页面查看最新数据')
      } else {
        ElMessage.error(response.message || 'ES数据更新失败')
      }
    } catch (error) {
      if (error === 'cancel') {
        // 用户取消操作
        return
      }
      console.error('ES数据更新失败:', error)
      ElMessage.error('ES数据更新失败，请稍后重试')
    } finally {
      esUpdateLoading.value = false
    }
  }

  // 查看任务状态方法
  const handleCheckTaskStatus = async() => {
    if (!validateToken()) return

    try {
      taskStatusLoading.value = true

      // 调用后端taskStatus接口，传递token
      const response = await getTaskStatus({ token: adminToken.value.trim() })

      if (response.code === 200) {
        const taskData = response.data

        // 检查各个任务的运行状态
        const runningTasks = []

        if (taskData.processBasicData) {
          runningTasks.push('全量更新（基础数据处理）')
        }

        if (taskData.generateVectors) {
          runningTasks.push('向量生成')
        }

        if (taskData.processMesh) {
          runningTasks.push('MeSH数据处理')
        }

        if (taskData.incrementalUpdate) {
          runningTasks.push('增量更新')
        }

        if (taskData.updateByIds) {
          runningTasks.push('指定ID更新')
        }

        // 显示任务状态信息
        if (runningTasks.length > 0) {
          const message = `正在运行的任务：${runningTasks.join('、')}`
          ElMessage.info({
            message,
            duration: 6000,
            showClose: true
          })
        } else {
          ElMessage.success('当前无任务在运行')
        }
      } else {
        ElMessage.warning('获取任务状态失败')
      }
    } catch (error) {
      // 如果是401错误，不显示错误提示（会自动跳转登录）
      if (error.response?.status === 401) {
        // 静默处理401错误
      } else {
        ElMessage.error('查询任务状态失败，请稍后重试')
      }
    } finally {
      taskStatusLoading.value = false
    }
  }

  // 向量生成方法
  const handleVectorGenerate = async() => {
    if (!validateToken()) return

    try {
      vectorGenerateLoading.value = true

      // 确认对话框
      await ElMessageBox.confirm(
        '向量生成将为所有文献生成向量数据，此操作可能需要较长时间，确定要继续吗？',
        '确认向量生成',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 调用统一数据处理接口
      const response = await dataProcess({
        token: adminToken.value.trim(),
        type: 'vectors'
      })

      if (response.code === 200 || response.success) {
        ElMessage.success('向量生成已启动，请稍后刷新页面查看最新数据')
      } else {
        ElMessage.error(response.message || '向量生成失败')
      }
    } catch (error) {
      if (error === 'cancel') {
        // 用户取消操作
        return
      }
      console.error('向量生成失败:', error)
      ElMessage.error('向量生成失败，请稍后重试')
    } finally {
      vectorGenerateLoading.value = false
    }
  }

  // mesh生成方法
  const handleMeshProcess = async() => {
    if (!validateToken()) return

    try {
      meshProcessLoading.value = true

      // 确认对话框
      await ElMessageBox.confirm(
        'MeSH生成将为所有文献生成MeSH数据，此操作可能需要较长时间，确定要继续吗？',
        '确认MeSH生成',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 调用统一数据处理接口
      const response = await dataProcess({
        token: adminToken.value.trim(),
        type: 'mesh'
      })

      if (response.code === 200 || response.success) {
        ElMessage.success('MeSH生成已启动，请稍后刷新页面查看最新数据')
      } else {
        ElMessage.error(response.message || 'MeSH生成失败')
      }
    } catch (error) {
      if (error === 'cancel') {
        // 用户取消操作
        return
      }
      console.error('MeSH生成失败:', error)
      ElMessage.error('MeSH生成失败，请稍后重试')
    } finally {
      meshProcessLoading.value = false
    }
  }
</script>

<style lang="scss" scoped>
  .data-management-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;
  }

  .page-header {
    text-align: center;
    margin-bottom: 40px;

    .page-title {
      font-size: 32px;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 10px;
    }

    .page-description {
      font-size: 16px;
      color: #7f8c8d;
      margin: 0;
    }
  }

  .management-content {
    max-width: 1200px;
    margin: 0 auto;
  }

  .token-input-section {
    margin-bottom: 40px;
  }

  .token-input-card {
    background: white;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
  }

  .token-title {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
  }

  .token-description {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 24px;
  }

  .token-input-wrapper {
    max-width: 800px;  /* 增加输入框宽度 */
    margin: 0 auto;
  }

  .token-input {
    font-size: 16px;
  }

  .button-list {
    display: flex;
    flex-direction: row;
    gap: 12px;
    max-width: 800px;
    margin: 200px auto 0 auto;
    margin-top: 24px;
  }

  .button-item {
    flex: 1;  /* 四个按钮平均分配宽度 */
  }

  .long-button {
    width: 100%;
    height: 45px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .el-icon {
      margin-right: 6px;
      font-size: 14px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .data-management-container {
      padding: 16px;
    }

    .page-header {
      margin-bottom: 24px;

      .page-title {
        font-size: 24px;
      }

      .page-description {
        font-size: 14px;
      }
    }

    .token-input-card {
      padding: 24px 20px;
    }

    .token-input-wrapper {
      max-width: 100%;
    }

    .button-list {
      gap: 8px;
      flex-direction: column;
    }

    .long-button {
      height: 42px;
      font-size: 14px;
    }
  }
</style>
