<template>
  <div class="literature">
    <!-- 搜索区域 -->
    <section class="search-section">
      <div class="container">
        <div class="search-container">
          <div class="search-bar">
            <div class="search-field">
              <input
                v-model="searchKeyword"
                type="text"
                class="search-input"
                placeholder="输入标题、作者、DOI、PMID 等关键词进行检索"
                @keyup.enter="handleSearch"
              />
            </div>
            <button class="search-button" @click="handleSearch">
              <el-icon>
                <Search />
              </el-icon>
            </button>
          </div>

          <div class="search-options">
            <div class="search-option" :class="{ active: showAdvancedSearch }" @click="toggleAdvancedSearch">
              <img src="@/assets/images/filter-icon.svg" alt="高级检索" class="option-icon" />
              <span>高级检索</span>
            </div>
            <div class="search-option" :class="{ active: showProblemSearch }" @click="toggleProblemSearch">
              <img src="@/assets/images/semantic-search-icon.svg" alt="语义检索" class="option-icon" />
              <span>语义检索</span>
            </div>
          </div>
        </div>
      </div>
    </section>
    <div class="container ">
      <!-- 高级检索面板 -->
      <el-collapse-transition>
        <div v-if="showAdvancedSearch" class="advanced-search-container mt-0 mb-4">
          <div class="advanced-search-content mt-0">
            <div v-for="(condition, index) in searchConditions" :key="index" class="search-row">
              <el-select v-if="index > 0" v-model="condition.logic" class="logic-select">
                <el-option label="AND" value="AND" />
                <el-option label="OR" value="OR" />
                <el-option label="NOT" value="NOT" />
              </el-select>

              <el-select
                v-model="condition.field"
                class="field-select"
                popper-class="field-select-dropdown"
                placeholder="请选择字段"
              >
                <el-option
                  v-for="field in advancedSearchFields"
                  :key="field.value"
                  :label="field.label"
                  :value="field.value"
                />
              </el-select>

              <!-- 发表时间范围字段使用6个输入框 -->
              <div v-if="condition.field === 'published_date_range'" class="date-range-container">
                <div class="date-inputs-group">
                  <span class="date-label">开始日期</span>
                  <div class="date-inputs">
                    <el-select
                      v-model="condition.startYear"
                      placeholder="年份"
                      class="year-select"
                      filterable
                      clearable
                      @change="onYearChange(condition, 'start')"
                    >
                      <el-option
                        v-for="year in getYearOptions()"
                        :key="year"
                        :label="year"
                        :value="year"
                      />
                    </el-select>
                    <el-select
                      v-model="condition.startMonth"
                      placeholder="月份"
                      class="month-select"
                      :disabled="!condition.startYear"
                      clearable
                      @change="onMonthChange(condition, 'start')"
                    >
                      <el-option
                        v-for="month in getMonthOptions()"
                        :key="month.value"
                        :label="month.label"
                        :value="month.value"
                      />
                    </el-select>
                    <el-select
                      v-model="condition.startDay"
                      placeholder="日期"
                      class="day-select"
                      :disabled="!condition.startMonth"
                      clearable
                      @change="onDayChange(condition, 'start')"
                    >
                      <el-option
                        v-for="day in getDayOptions(condition.startYear, condition.startMonth)"
                        :key="day.value"
                        :label="day.label"
                        :value="day.value"
                      />
                    </el-select>
                  </div>
                </div>
                <span class="date-separator">至</span>
                <div class="date-inputs-group">
                  <span class="date-label">结束日期</span>
                  <div class="date-inputs">
                    <el-select
                      v-model="condition.endYear"
                      placeholder="年份"
                      class="year-select"
                      filterable
                      clearable
                      @change="onYearChange(condition, 'end')"
                    >
                      <el-option
                        v-for="year in getYearOptions()"
                        :key="year"
                        :label="year"
                        :value="year"
                      />
                    </el-select>
                    <el-select
                      v-model="condition.endMonth"
                      placeholder="月份"
                      class="month-select"
                      :disabled="!condition.endYear"
                      clearable
                      @change="onMonthChange(condition, 'end')"
                    >
                      <el-option
                        v-for="month in getMonthOptions()"
                        :key="month.value"
                        :label="month.label"
                        :value="month.value"
                      />
                    </el-select>
                    <el-select
                      v-model="condition.endDay"
                      placeholder="日期"
                      class="day-select"
                      :disabled="!condition.endMonth"
                      clearable
                      @change="onDayChange(condition, 'end')"
                    >
                      <el-option
                        v-for="day in getDayOptions(condition.endYear, condition.endMonth)"
                        :key="day.value"
                        :label="day.label"
                        :value="day.value"
                      />
                    </el-select>
                  </div>
                </div>
              </div>
              <!-- MeSH字段使用自动完成输入框 -->
              <el-autocomplete
                v-else-if="condition.field === 'meshUi'"
                v-model="condition.meshDisplayName"
                class="keyword-input"
                placeholder="输入Mesh名称..."
                :select-when-unmatched="false"
                :fetch-suggestions="queryMeshSuggestions"
                :trigger-on-focus="false"
                clearable
                @select="(item) => handleMeshSelect(item, condition)"
                @clear="handleMeshClear(condition)"
                @blur="(event) => handleMeshBlur(event, condition)"
                @input="(value) => handleMeshInput(value, condition)"
              />
              <!-- 其他字段使用普通输入框 -->
              <el-input
                v-else
                v-model="condition.value"
                class="keyword-input"
                placeholder="输入检索词..."
                clearable
              />

              <el-button
                v-if="searchConditions.length > 1"
                type="text"
                class="remove-condition-btn"
                @click="removeSearchCondition(index)"
              >
                <el-icon>
                  <Close />
                </el-icon>
              </el-button>
            </div>

            <div class="add-condition-row">
              <el-button
                type="primary"
                text
                class="add-condition-btn"
                @click="addSearchCondition"
              >
                <el-icon>
                  <Plus />
                </el-icon>
                添加检索条件
              </el-button>
            </div>

            <div class="search-query-container">
              <div class="search-query-box">
                <div class="search-query-label">查询语句</div>
                <div class="query-actions">
                  <el-button type="text" class="action-btn" @click="copySearchQuery">
                    <el-icon>
                      <CopyDocument />
                    </el-icon>
                    复制
                  </el-button>
                </div>
              </div>
              <el-input
                v-model="editableQuery"
                type="textarea"
                rows="5"
                placeholder="可以直接编辑查询语句，格式：(搜索词[字段]) AND/OR/NOT (搜索词[字段])"
              />
            </div>

            <div class="search-history-container">
              <div class="search-history-header">
                <div class="search-history-label">检索历史</div>
                <el-button plain class="clear-all-btn" @click="clearAllSearchHistory">清空</el-button>
              </div>
              <div class="search-history-list">
                <div v-if="searchHistory.length === 0" class="empty-history">
                  <el-icon class="empty-icon"><DocumentCopy /></el-icon>
                  <span>暂无检索历史</span>
                </div>
                <div v-for="(history, index) in searchHistory" :key="index" class="search-history-item">
                  <div class="history-index">#{{ index + 1 }}</div>
                  <div class="history-query">{{ history.query }}</div>
                  <div class="history-result-count">{{ history.count.toLocaleString() }} 结果</div>
                  <div class="history-actions">
                    <el-button type="text" class="edit-btn" @click="editSearchHistory(index)">编辑</el-button>
                    <el-button type="text" class="delete-btn" @click="deleteSearchHistory(index)">删除</el-button>
                  </div>
                </div>
              </div>
            </div>

            <div class="search-actions">
              <el-button plain class="clear-btn" @click="showAdvancedSearch=false">关闭</el-button>
              <el-button plain class="clear-btn" @click="resetAdvancedSearch">重置</el-button>
              <el-button type="primary" class="search-btn" @click="handleAdvancedSearch">检索</el-button>
            </div>
          </div>
        </div>
      </el-collapse-transition>

      <!-- 语义检索面板 -->
      <el-collapse-transition>
        <div v-if="showProblemSearch" class="advanced-search-container mt-0 mb-4">
          <div class="search-query-container mt-0">
            <div class="search-query-label search-query-box">输入问题</div>
            <el-input 
              v-model="problemSearch" 
              type="textarea" 
              rows="5"
            />
          </div>
          <div class="search-actions">
            <el-button plain class="clear-btn" @click="showProblemSearch=false">关闭</el-button>
            <el-button plain class="clear-btn" @click="resetAdvancedSearch">重置</el-button>
            <el-button type="primary" class="search-btn" @click="handleSemanticSearch">检索</el-button>
          </div>
        </div>
      </el-collapse-transition>
    </div>

    <!-- 主要内容区域 -->
    <section class="content-section">
      <div class="container content-container">
        <!-- 移动端筛选按钮 -->
        <div v-if="isMobile" class="mobile-filter-toggle" @click="showMobileFilters = !showMobileFilters">
          <span>筛选条件</span>
          <el-icon class="toggle-icon" :class="{ 'rotated': showMobileFilters }">
            <ArrowDown />
          </el-icon>
        </div>

        <!-- 左侧筛选条件 -->
        <div class="filter-sidebar" :class="{ 'mobile-hidden': isMobile && !showMobileFilters }">
          <div class="filter-header">
            <img src="@/assets/images/filter-icon.svg" alt="高级检索" class="filter-icon" />
            <h2 class="filter-title">筛选条件</h2>
          </div>

          <!-- 发布时间范围 -->
          <div class="filter-group">
            <h3 class="filter-group-title">发布时间范围</h3>
            <el-radio-group v-model="timeRange" class="filter-options">
              <div v-for="item in timeRangeOptions" :key="item.value" class="filter-option">
                <el-radio :label="item.value" @click.prevent="handleRadioClick(item.value)">{{ item.label }}</el-radio>
              </div>
              <div class="filter-option custom-range cursor-pointer" @click="openCustomRangeDialog">
                <span>自定义范围</span>
              </div>
            </el-radio-group>
          </div>

          <!-- 可用文献 -->
          <div class="filter-group">
            <h3 class="filter-group-title">可用文献</h3>
            <el-checkbox-group v-model="selectedFilters.free" class="filter-options" @change="handleFilterChange">
              <div v-for="item in availableTypesOptions" :key="item.value" class="filter-option">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count.toLocaleString() }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 文献类型 -->
          <div class="filter-group">
            <h3 class="filter-group-title">文献类型</h3>
            <el-checkbox-group v-model="selectedFilters.pubtype" class="filter-options" @change="handleFilterChange">
              <div v-for="item in literatureTypesOptions.slice(0, 4)" :key="item.value" class="filter-option">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count.toLocaleString() }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
            <div v-if="literatureTypesOptions.length > 4" class="more-types" @click="literTypeDialog = true">
              <span>...</span>
              <span>更多类型</span>
            </div>
          </div>

          <!-- 文章属性 -->
          <div class="filter-group">
            <h3 class="filter-group-title">文章属性</h3>
            <el-checkbox-group v-model="selectedFilters.articleAttributes" class="filter-options" @change="handleFilterChange">
              <div v-for="item in articleAttributesOptions" :key="item.value" class="filter-option">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count.toLocaleString() }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 影响因子 -->
          <div class="filter-group">
            <h3 class="filter-group-title">影响因子</h3>
            <el-checkbox-group v-model="selectedFilters.impactFactor" class="filter-options" @change="handleFilterChange">
              <div v-for="item in impactFactorsOptions" :key="item.value" class="filter-option">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count.toLocaleString() }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 语言 -->
          <div class="filter-group">
            <h3 class="filter-group-title">语言</h3>
            <el-checkbox-group v-model="selectedFilters.language" class="filter-options" @change="handleFilterChange">
              <div v-for="item in displayedLanguageOptions" :key="item.value" class="filter-option">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count.toLocaleString() }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
            <div v-if="languagesOptions.length > 3" class="more-types" @click="showMoreLanguages = !showMoreLanguages">
              <el-icon>
                <ArrowUp v-if="showMoreLanguages" />
                <ArrowDown v-else />
              </el-icon>
            </div>
          </div>

          <!-- 基金支持 -->
          <div class="filter-group">
            <h3 class="filter-group-title">基金支持</h3>
            <el-checkbox-group v-model="selectedFilters.funding" class="filter-options" @change="handleFilterChange">
              <div v-for="item in fundingSupportOptions" :key="item.value" class="filter-option">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count.toLocaleString() }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- JCR分区 -->
          <div class="filter-group">
            <h3 class="filter-group-title">JCR分区</h3>
            <el-checkbox-group v-model="jcrQuartiles" class="filter-options" @change="handleFilterChange">
              <div v-for="item in jcrQuartilesOptions" :key="item.value" class="filter-option">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count.toLocaleString() }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 中科院分区 -->
          <div class="filter-group">
            <h3 class="filter-group-title">中科院分区</h3>
            <el-checkbox-group v-model="casQuartiles" class="filter-options" @change="handleFilterChange">
              <div v-for="item in casQuartilesOptions" :key="item.value" class="filter-option">
                <el-checkbox :label="item.value">
                  <div class="filter-option-content">
                    <span>{{ item.label }}</span>
                    <div class="count-badge">{{ item.count.toLocaleString() }}</div>
                  </div>
                </el-checkbox>
              </div>
            </el-checkbox-group>
          </div>

          <!-- 显示更多按钮 -->
          <button class="show-more-btn" @click="showOtherFilters=!showOtherFilters">
            {{ showOtherFilters ? '收起更多条件' : '显示更多条件' }}
          </button>

          <!-- 更多筛选条件 -->
          <div v-show="showOtherFilters">
            <!-- 来源 -->
            <div v-if="sourceOptions.length > 0" class="filter-group">
              <h3 class="filter-group-title">来源</h3>
              <el-checkbox-group v-model="selectedFilters.source" class="filter-options">
                <div v-for="item in sourceOptions" :key="item.value" class="filter-option">
                  <el-checkbox :value="item.value">
                    <div class="filter-option-content">
                      <span
                        class="source-label"
                        :style="getSourceStyle(item.value)"
                      >{{ item.label }}</span>
                      <div class="count-badge">{{ item.count }}</div>
                    </div>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>

            <!-- 期刊 -->
            <div v-if="journalOptions.length > 0" class="filter-group">
              <h3 class="filter-group-title">期刊</h3>
              <el-checkbox-group v-model="selectedFilters.journal_name" class="filter-options">
                <div v-for="item in journalOptions.slice(0, 10)" :key="item.value" class="filter-option">
                  <el-checkbox :label="item.value">
                    <div class="filter-option-content">
                      <span>{{ item.label }}</span>
                      <div class="count-badge">{{ item.count }}</div>
                    </div>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
              <div v-if="journalOptions.length > 10" class="more-types">
                显示更多期刊 ({{ journalOptions.length - 10 }}+)
              </div>
            </div>

            <!-- 出版商 -->
            <div v-if="publisherOptions.length > 0" class="filter-group">
              <h3 class="filter-group-title">出版商</h3>
              <el-checkbox-group v-model="selectedFilters.publisher_name" class="filter-options">
                <div v-for="item in publisherOptions.slice(0, 10)" :key="item.value" class="filter-option">
                  <el-checkbox :label="item.value">
                    <div class="filter-option-content">
                      <span>{{ item.label }}</span>
                      <div class="count-badge">{{ item.count }}</div>
                    </div>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
              <div v-if="publisherOptions.length > 10" class="more-types">
                显示更多出版商 ({{ publisherOptions.length - 10 }}+)
              </div>
            </div>

            <!-- 其他 -->
            <div class="filter-group">
              <h3 class="filter-group-title">其他</h3>
              <el-checkbox-group v-model="selectedFilters.other" class="filter-options" @change="handleFilterChange">
                <div v-for="item in otherOptions" :key="item.value" class="filter-option">
                  <el-checkbox :label="item.value">
                    <div class="filter-option-content" :class="{ 'no-text-limit': item.value === 'PubMed Central收录' }">
                      <span>{{ item.label }}</span>
                    </div>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </div>
        </div>

        <!-- 右侧文献列表 -->
        <div class="literature-content">
          <div class="literature-header">
            <div class="literature-header-left">
              <div class="select-all">
                <input
                  id="select-all"
                  type="checkbox"
                  :checked="isAllSelected"
                  :indeterminate="isIndeterminate"
                  @change="handleSelectAll"
                />
                <label for="select-all">全选</label>
              </div>
              <div>
                找到 <span class="result-count">{{ totalResults.toLocaleString() }}</span> 条相关文献
                <span v-if="selectedArticles.size > 0" class="selected-count">
                  （总共已选择 {{ selectedArticles.size }} 条，本页 {{ currentPageSelectedCount }} 条）
                </span>
              </div>
            </div>
            <div class="literature-header-right">
              <div class="sort-options">
                <span>排序：</span>
                <div class="sort-dropdown">
                  <el-select
                    v-model="sort"
                    class="field-select"
                    popper-class="field-select-dropdown"
                    style="width: 100px"
                    @change="onSortChange"
                  >
                    <el-option label="相关度" value="relevance" />
                    <el-option label="发布日期" value="latest" />
                  </el-select>
                </div>
              </div>
              <div class="page-size">
                <span>每页：</span>
                <div class="page-size-dropdown">
                  <el-select
                    v-model="pageSize"
                    class="field-select"
                    popper-class="field-select-dropdown"
                    style="width: 100px"
                    @change="onPageSizeChange"
                  >
                    <el-option label="10" :value="10" />
                    <el-option label="20" :value="20" />
                    <el-option label="50" :value="50" />
                    <el-option label="100" :value="100" />
                  </el-select>
                </div>
              </div>
              <div class="action-button" @click="handleBatchFavorite">
                <el-icon>
                  <Star />
                </el-icon>
                <span>收藏</span>
              </div>
              <div class="action-button">
                <el-icon>
                  <Download />
                </el-icon>
                <el-dropdown placement="bottom">
                  <span class="export">导出</span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item>导出元数据</el-dropdown-item>
                      <el-dropdown-item>导出文献清单</el-dropdown-item>
                      <el-dropdown-item>导出Cite</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>

          <!-- 文献列表 -->
          <div class="article-list-container">
            <div v-if="loading" class="loading-state">
              <el-skeleton :rows="5" animated />
            </div>

            <div v-else-if="searchResults.length === 0 && hasSearched" class="no-results">
              <p>未找到相关文献，请尝试其他关键词</p>
            </div>
            <article-item
              v-for="article in searchResults"
              v-else
              :key="article.id"
              :article="article"
              :is-selected="selectedArticles.has(article.id)"
              @selection-change="handleArticleSelectionChange"
            />
          </div>

          <!-- 分页 -->
          <div v-if="searchResults.length > 0" class="pagination">
            <el-pagination
              :current-page="currentPage"
              :page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="displayTotalResults"
              layout="sizes, prev, pager, next, jumper"
              background
              @size-change="onPageSizeChange"
              @current-change="onPageChange"
            />
          </div>
        </div>
      </div>
    </section>
    <el-dialog
      v-model="literTypeDialog"
      title="文献类型"
      :width="isMobile ? '95%' : '800px'"
      draggable
      :top="isMobile ? '5vh' : '15vh'"
      class="literature-type-dialog"
    >
      <div class="literature-type-content">
        <el-checkbox-group v-model="selectedFilters.pubtype" class="literature-type-checkboxes">
          <el-checkbox
            v-for="item in literatureTypesOptions"
            :key="item.value"
            :label="item.value"
            class="literature-type-checkbox"
          >
            <div class="filter-option-content">
              <span>{{ item.label }}</span>
              <div class="count-badge">{{ item.count }}</div>
            </div>
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="literTypeDialog = false">取消</el-button>
          <el-button type="primary" class="confirm-btn" @click="confirmLiteratureTypes">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 自定义范围对话框 -->
    <el-dialog
      v-model="customRangeDialog"
      title="自定义发布时间范围"
      :width="isMobile ? '90%' : '400px'"
      draggable
    >
      <div class="custom-range-content">
        <!-- 开始日期 -->
        <div class="date-section">
          <div class="date-label">开始日期</div>
          <div class="date-inputs">
            <el-select
              v-model="startYear"
              placeholder="年份"
              class="year-select"
              filterable
              @change="handleYearChange"
            >
              <el-option
                v-for="year in yearOptions"
                :key="year"
                :label="year"
                :value="year"
              />
            </el-select>
            <el-select
              v-model="startMonth"
              placeholder="月份"
              class="month-select"
              :disabled="!startYear"
              @change="handleMonthChange"
            >
              <el-option
                v-for="month in monthOptions"
                :key="month.value"
                :label="month.label"
                :value="month.value"
              />
            </el-select>
            <el-select
              v-model="startDay"
              placeholder="日期"
              class="day-select"
              :disabled="!startMonth"
            >
              <el-option
                v-for="day in dayOptions"
                :key="day"
                :label="day"
                :value="day"
              />
            </el-select>
          </div>
        </div>

        <!-- 结束日期 -->
        <div class="date-section">
          <div class="date-label">结束日期</div>
          <div class="date-inputs">
            <el-select
              v-model="endYear"
              placeholder="年份"
              class="year-select"
              filterable
              clearable
              @change="handleEndYearChange"
            >
              <el-option
                v-for="year in yearOptions"
                :key="year"
                :label="year"
                :value="year"
              />
            </el-select>
            <el-select
              v-model="endMonth"
              placeholder="月份"
              class="month-select"
              :disabled="!endYear"
              clearable
              @change="handleEndMonthChange"
            >
              <el-option
                v-for="month in monthOptions"
                :key="month.value"
                :label="month.label"
                :value="month.value"
              />
            </el-select>
            <el-select
              v-model="endDay"
              placeholder="日期"
              class="day-select"
              :disabled="!endMonth"
              clearable
            >
              <el-option
                v-for="day in endDayOptions"
                :key="day"
                :label="day"
                :value="day"
              />
            </el-select>
          </div>
          <div class="date-note">
          </div>
        </div>

        <!-- 按钮区域 -->
        <div class="dialog-actions">
          <el-button class="clear-btn" @click="clearCustomRange">清空</el-button>
          <el-button type="primary" class="apply-btn" @click="applyCustomRange">应用</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 收藏弹框 -->
    <collection-modal
      v-model="showCollectionModal"
      :article="selectedArticle"
      @confirm="handleCollectionConfirm"
    />
  </div>
</template>

<script setup>
  import { computed, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
  import { ArrowDown, ArrowUp, Close, CopyDocument, DocumentCopy, Download, Plus, Search, Star } from '@element-plus/icons-vue'
  import ArticleItem from '@/components/ArticleItem.vue'
  import CollectionModal from '@/components/CollectionModal.vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { useRoute } from 'vue-router'
  import {
    getSearchFields,
    searchArticles,
    getMeshAutoComplete
  } from '@/api/article'

  const route = useRoute()

  // 搜索相关
  const searchKeyword = ref('')

  const sort = ref('relevance')
  const pageSize = ref(10)
  const showOtherFilters = ref(false)
  const literTypeDialog = ref(false)

  // 搜索结果相关
  const searchResults = ref([])
  const totalResults = ref(0)
  const currentPage = ref(1)
  const loading = ref(false)
  const hasSearched = ref(false)

  // 分页限制常量
  const MAX_DISPLAY_RESULTS = 10000

  // 计算属性：限制显示的总数
  const displayTotalResults = computed(() => {
    return Math.min(totalResults.value, MAX_DISPLAY_RESULTS)
  })

  // 可用字段列表
  const availableFields = ref([])

  // 高级检索字段（排除AI字段）
  const advancedSearchFields = computed(() => {
    return availableFields.value.filter(field => field.value !== 'ai')
  })

  // 全选相关计算属性
  const isAllSelected = computed(() => {
    if (searchResults.value.length === 0) return false
    return searchResults.value.every(article => selectedArticles.value.has(article.id))
  })

  const isIndeterminate = computed(() => {
    const selectedCount = searchResults.value.filter(article => selectedArticles.value.has(article.id)).length
    return selectedCount > 0 && selectedCount < searchResults.value.length
  })

  // 当前页面选中的文献数量
  const currentPageSelectedCount = computed(() => {
    return searchResults.value.filter(article => selectedArticles.value.has(article.id)).length
  })

  // 收藏相关
  const showCollectionModal = ref(false)
  const selectedArticle = ref(null)

  // 文献选择相关
  const selectedArticles = ref(new Set())

  // 自定义范围相关
  const customRangeDialog = ref(false)
  const startYear = ref('')
  const startMonth = ref('')
  const startDay = ref('')
  const endYear = ref('')
  const endMonth = ref('')
  const endDay = ref('')

  // 年份选项（从1900年到当前年份）
  const yearOptions = computed(() => {
    const currentYear = new Date().getFullYear()
    const years = []
    for (let year = currentYear; year >= 1900; year--) {
      years.push(year.toString())
    }
    return years
  })

  // 月份选项
  const monthOptions = [
    { label: '01月', value: '01' },
    { label: '02月', value: '02' },
    { label: '03月', value: '03' },
    { label: '04月', value: '04' },
    { label: '05月', value: '05' },
    { label: '06月', value: '06' },
    { label: '07月', value: '07' },
    { label: '08月', value: '08' },
    { label: '09月', value: '09' },
    { label: '10月', value: '10' },
    { label: '11月', value: '11' },
    { label: '12月', value: '12' }
  ]

  // 开始日期选项（根据选择的年月动态计算）
  const dayOptions = computed(() => {
    if (!startYear.value || !startMonth.value) {
      return []
    }

    const year = parseInt(startYear.value)
    const month = parseInt(startMonth.value)
    const daysInMonth = new Date(year, month, 0).getDate()

    const days = []
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day.toString().padStart(2, '0'))
    }
    return days
  })

  // 结束日期选项（根据选择的年月动态计算）
  const endDayOptions = computed(() => {
    if (!endYear.value || !endMonth.value) {
      return []
    }

    const year = parseInt(endYear.value)
    const month = parseInt(endMonth.value)
    const daysInMonth = new Date(year, month, 0).getDate()

    const days = []
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day.toString().padStart(2, '0'))
    }
    return days
  })

  // 开始年份变化处理
  const handleYearChange = () => {
    // 清空月份和日期
    startMonth.value = ''
    startDay.value = ''
  }

  // 开始月份变化处理
  const handleMonthChange = () => {
    // 清空日期
    startDay.value = ''
  }

  // 结束年份变化处理
  const handleEndYearChange = () => {
    // 清空月份和日期
    endMonth.value = ''
    endDay.value = ''
  }

  // 结束月份变化处理
  const handleEndMonthChange = () => {
    // 清空日期
    endDay.value = ''
  }

  // 移动端检测
  const windowWidth = ref(window.innerWidth)
  const isMobile = computed(() => windowWidth.value <= 768)
  const showMobileFilters = ref(false)

  // 语言选项展开控制
  const showMoreLanguages = ref(false)

  const handleResize = () => {
    windowWidth.value = window.innerWidth
  }

  onMounted(async() => {
    // 确保排序初始值正确
    sort.value = 'relevance'
    window.addEventListener('resize', handleResize)

    // 加载可用字段和搜索历史
    await loadAvailableFields()
    loadSearchHistory()

    // 根据路由参数控制检索面板显示
    const searchMode = route.query.mode
    if (searchMode === 'advanced') {
      showAdvancedSearch.value = true
      showProblemSearch.value = false
    } else if (searchMode === 'semantic') {
      showProblemSearch.value = true
      showAdvancedSearch.value = false
    }

    // 在页面加载的时候检查是否有homeKeyword参数
    const homeKeyword = route.query.homeKeyword
    if (homeKeyword) {
      searchKeyword.value = homeKeyword
      nextTick
      handleSearch()
    } else {
      // 初始加载所有文献，同时获取筛选选项
      await loadAllLiterature()
    }
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })

  // 高级检索相关
  const showAdvancedSearch = ref(false)
  const showProblemSearch = ref(false)
  const searchConditions = ref([
    {
      logic: 'AND',
      field: '',
      value: '',
      dateValue: null,
      dateRange: null,
      startDate: null,
      endDate: null,
      // 新增的日期字段
      publishYear: '',
      publishMonth: '',
      publishDay: '',
      startYear: '',
      startMonth: '',
      startDay: '',
      endYear: '',
      endMonth: '',
      endDay: '',
      // MeSH显示名称字段
      meshDisplayName: ''
    }
  ])
  const problemSearch = ref('')

  // 可编辑的查询语句
  const editableQuery = ref('')

  // 计算查询预览
  const queryPreview = computed(() => {
    return buildQueryPreview()
  })

  // 标记是否正在从条件同步到查询语句（避免循环更新）
  const isUpdatingFromConditions = ref(false)
  // 标记是否正在从查询语句同步到条件（避免循环更新）
  const isUpdatingFromQuery = ref(false)

  // 重置
  const resetAdvancedSearch = () => {
    location.reload()
  }

  // 字段标识符反向映射
  const getFieldValueFromIdentifier = identifier => {
    const reverseFieldMap = {
      AI: 'ai',
      PMID: 'pmid',
      PMCID: 'pmcid',
      DOI: 'doi',
      Title: 'title',
      Author: 'author',
      Lastname: 'lastname',
      Forename: 'forename',
      Affiliation: 'affiliation',
      Abstract: 'articleAbstract',
      Keywords: 'keywords',
      'Publication Type': 'pubtype',
      // support both camelCase (used by frontend field list) and snake_case (backend)
      publisherName: 'publisherName',
      publisher_name: 'publisherName',
      journalName: 'journalName',
      journal_name: 'journalName',
      Volume: 'volume',
      Issue: 'issue',
      Page: 'page',
      'Publication Date Range': 'published_date_range',
      Language: 'language',
      Grant: 'grant',
      Databank: 'databank',
      Source: 'source',
      'Publication Status': 'pub_status',
      Mesh: 'meshUi',
      'Impact Factor': 'impactFactor',
      JCR: 'jcr',
      'CAS Category': 'largeCategorySection'
    }
    return reverseFieldMap[identifier] || identifier.toLowerCase()
  }

  // 解析查询语句为搜索条件（手动触发，显示消息）
  const parseToConditions = () => {
    try {
      const query = editableQuery.value.trim()
      if (!query) {
        ElMessage.warning('查询语句为空')
        return
      }

      parseQueryToConditions(true) // true 表示显示消息
    } catch (error) {
      console.error('解析查询语句失败:', error)
      ElMessage.error('解析查询语句失败，请检查格式')
    }
  }

  // 实际的解析逻辑
  const parseQueryToConditions = (showMessage = true) => {
    try {
      isUpdatingFromQuery.value = true
      const query = editableQuery.value.trim()

      // 提取所有的 term[field] 模式，允许内容中包含括号
      // 使用非贪婪匹配，匹配到第一个 [field] 就停止
      const termPattern = /\((.+?)\[([^\]]+)\]\)/g
      const newConditions = []
      let match
      let lastLogic = 'AND'

      // 找到所有匹配的条件
      const matches = []
      // 重置正则表达式的 lastIndex
      termPattern.lastIndex = 0
      while ((match = termPattern.exec(query)) !== null) {
        const matchInfo = {
          fullMatch: match[0],
          term: match[1].replace(/^"|"$/g, ''), // 去掉引号
          field: match[2],
          index: match.index
        }
        matches.push(matchInfo)
      }

      // 为每个匹配的条件确定逻辑操作符并创建条件
      for (let i = 0; i < matches.length; i++) {
        const currentMatch = matches[i]

        // 查找当前条件前面的逻辑操作符
        if (i > 0) {
          const prevMatch = matches[i - 1]
          const betweenText = query.substring(prevMatch.index + prevMatch.fullMatch.length, currentMatch.index)

          if (betweenText.includes(' NOT ')) {
            lastLogic = 'NOT'
          } else if (betweenText.includes(' OR ')) {
            lastLogic = 'OR'
          } else if (betweenText.includes(' AND ')) {
            lastLogic = 'AND'
          }
          // 如果没有找到明确的逻辑操作符，保持上一个逻辑
        }

        let fieldValue = getFieldValueFromIdentifier(currentMatch.field)

        // Normalize fieldValue to match an entry in availableFields
        const findAvailableField = val => availableFields.value.find(f => f.value === val)
        const snakeToCamel = s => s.replace(/_([a-z])/g, (_, c) => c.toUpperCase())
        const camelToSnake = s => s.replace(/([A-Z])/g, '_$1').toLowerCase()

        const normalizeToAvailable = original => {
          if (!availableFields.value || availableFields.value.length === 0) return original
          // direct match
          if (findAvailableField(original)) return original
          // camel from snake
          const camel = snakeToCamel(original)
          if (findAvailableField(camel)) return camel
          // snake from camel
          const snake = camelToSnake(original)
          if (findAvailableField(snake)) return snake
          // try lowercase-first (PublisherName -> publisherName)
          const lowerFirst = original.charAt(0).toLowerCase() + original.slice(1)
          if (findAvailableField(lowerFirst)) return lowerFirst
          // case-insensitive match
          const ci = availableFields.value.find(f => f.value.toLowerCase() === original.toLowerCase())
          if (ci) return ci.value
          // fallback to original
          return original
        }

        fieldValue = normalizeToAvailable(fieldValue)
        const term = currentMatch.term

        const condition = {
          logic: i === 0 ? 'AND' : lastLogic,
          field: fieldValue,
          value: '',
          dateValue: null,
          dateRange: null,
          startDate: null,
          endDate: null,
          // 新增的日期字段
          publishYear: '',
          publishMonth: '',
          publishDay: '',
          startYear: '',
          startMonth: '',
          startDay: '',
          endYear: '',
          endMonth: '',
          endDay: '',
          // MeSH显示名称字段
          meshDisplayName: ''
        }

        // 处理不同类型的字段
        if (fieldValue === 'published_date_range') {
          // 解析日期范围 "20200101 TO 20231231" 或 "20200101 TO *"
          const rangeMatch = term.match(/^(.+?)\s+TO\s+(.+)$/)
          if (rangeMatch) {
            const [, start, end] = rangeMatch
            condition.startDate = start === '*' ? null : start
            condition.endDate = end === '*' ? null : end

            // 解析开始日期到年月日字段
            if (start && start !== '*' && start.length === 8 && /^\d{8}$/.test(start)) {
              condition.startYear = start.substring(0, 4)
              condition.startMonth = start.substring(4, 6)
              condition.startDay = start.substring(6, 8)
            }

            // 解析结束日期到年月日字段
            if (end && end !== '*' && end.length === 8 && /^\d{8}$/.test(end)) {
              condition.endYear = end.substring(0, 4)
              condition.endMonth = end.substring(4, 6)
              condition.endDay = end.substring(6, 8)
            }

            // 同时设置dateRange以保持兼容性
            condition.dateRange = [condition.startDate, condition.endDate]
          }
        } else if (fieldValue === 'meshUi') {
          // MeSH字段：term可能是mesh名称或meshUi
          // 去掉可能存在的双引号
          const cleanTerm = term.trim().replace(/^"(.*)"$/, '$1')

          // 如果term看起来像meshUi格式（如D000001），则需要反向查找显示名称
          if (/^[A-Z]\d{6}$/.test(cleanTerm)) {
            condition.value = cleanTerm
            // 暂时用meshUi作为显示名称，理想情况下应该通过API获取真实名称
            condition.meshDisplayName = cleanTerm
          } else {
            // 如果是mesh名称，则设置为显示名称
            condition.meshDisplayName = cleanTerm
            // 这里需要通过现有的搜索条件找到对应的meshUi
            const existingCondition = searchConditions.value.find(c =>
              c.field === 'meshUi' && c.meshDisplayName === cleanTerm
            )
            condition.value = existingCondition?.value || ''
          }
        } else {
          condition.value = term
        }

        newConditions.push(condition)
      }

      if (newConditions.length > 0) {
        searchConditions.value = newConditions
      } else {
        if (showMessage) {
          ElMessage.error('无法解析查询语句，请检查格式是否正确')
        }
      }

    } catch (error) {
      console.error('解析查询语句失败:', error)
      if (showMessage) {
        ElMessage.error('解析查询语句失败，请检查格式')
      }
    } finally {
      isUpdatingFromQuery.value = false
    }
  }

  // 监听搜索条件变化 - 自动同步到查询语句
  watch(searchConditions, () => {
    if (isUpdatingFromQuery.value) {
      return // 如果正在从查询语句更新条件，则跳过
    }

    isUpdatingFromConditions.value = true
    editableQuery.value = buildQueryPreview()
    // 延迟重置标志，确保查询语句更新完成
    nextTick(() => {
      isUpdatingFromConditions.value = false
    })
  }, { deep: true })

  
  /* watch(editableQuery, (newQuery, oldQuery) => {
    if (isUpdatingFromConditions.value) {
      return
    }

    // 只有当查询语句真正改变时才触发解析
    if (newQuery !== oldQuery) {
      onQueryEdit()
    }
  }) */

  // 初始化时同步查询语句
  onMounted(() => {
    editableQuery.value = buildQueryPreview()
  })

  // 搜索历史
  const searchHistory = ref([])

  // 保存搜索历史
  const saveSearchHistory = searchData => {
    const history = JSON.parse(localStorage.getItem('literatureSearchHistory') || '[]')

    // 避免重复
    const existingIndex = history.findIndex(h => h.query === searchData.query)
    if (existingIndex > -1) {
      history.splice(existingIndex, 1)
    }

    history.unshift(searchData)

    // 只保留最近20条
    if (history.length > 20) {
      history.splice(20)
    }

    localStorage.setItem('literatureSearchHistory', JSON.stringify(history))
    searchHistory.value = history
  }

  // 加载搜索历史
  const loadSearchHistory = () => {
    const history = JSON.parse(localStorage.getItem('literatureSearchHistory') || '[]')
    searchHistory.value = history
  }

  // 检查高级检索是否有有效条件
  const checkValidAdvancedConditions = () => {
    return searchConditions.value.some(condition => {
      if (condition.field === 'published_date_range') {
        return condition.startYear || condition.endYear
      } else if (condition.field === 'meshUi') {
        return condition.meshDisplayName && condition.meshDisplayName.trim()
      } else {
        return condition.value && condition.value.trim()
      } 
    })
  }

  // 分页相关
  const onPageChange = page => {
    // 限制页码不能超过最大页数
    const maxAllowedPage = Math.ceil(displayTotalResults.value / pageSize.value)
    const targetPage = Math.min(page, maxAllowedPage)

    currentPage.value = targetPage
    scrollToTop()
    // 根据当前状态决定使用哪种加载方式
    if (searchKeyword.value.trim()) {
      // 有简单搜索关键词，使用智能检索
      handleSearch()
    } else if (showAdvancedSearch.value && checkValidAdvancedConditions()) {
      // 在高级检索模式且有有效检索条件
      handleAdvancedSearch(true)
    } else if (showProblemSearch.value && problemSearch.value.trim()) {
      // 在语义检索模式且有检索内容
      handleSemanticSearch(true)
    } else {
      // 其他情况（包括检索模式但没有检索条件），使用普通加载
      loadAllLiterature(false)
    }
  }

  const onPageSizeChange = size => {
    pageSize.value = size
    currentPage.value = 1
    scrollToTop()
    if (showAdvancedSearch.value) {
      handleAdvancedSearch()
    } else if (showProblemSearch.value) {
      handleSemanticSearch()
    } else if (searchKeyword.value.trim()) {
      // 使用智能检索，自动判断检索类型
      handleSearch()
    } else if (hasSearched.value) {
      loadAllLiterature(false) // 页面大小切换时不显示消息
    }
  }

  // 排序变化处理
  const onSortChange = () => {
    currentPage.value = 1

    // 根据当前搜索状态决定调用哪个搜索函数
    if (showAdvancedSearch.value) {
      handleAdvancedSearch()
    } else if (showProblemSearch.value) {
      handleSemanticSearch()
    } else if (searchKeyword.value.trim()) {
      // 使用智能检索，自动判断检索类型
      handleSearch()
    } else if (hasSearched.value) {
      // 如果之前已经搜索过（比如默认加载），则重新加载
      loadAllLiterature()
    }
  }

  // 编辑检索历史
  const editSearchHistory = index => {
    const history = searchHistory.value[index]
    if (!history) {
      return
    }

    const query = history.query.trim()

    try {
      // 判断是否为复杂查询语句（包含括号和字段如 ...[Field]）
      const isComplexQuery = /.+\[[^\]]+\]/.test(query)

      if (isComplexQuery) {
        // 复杂查询：切换到高级搜索模式并解析
        showAdvancedSearch.value = true
        showProblemSearch.value = false
        editableQuery.value = query

        // 尝试解析为搜索条件
        try {
          parseToConditions()
        } catch (error) {
          ElMessage.info('已加载历史查询语句，请手动调整搜索条件')
        }
      } else {
        // 简单查询：填充到简单搜索框
        showAdvancedSearch.value = true
        showProblemSearch.value = false
        searchKeyword.value = query
      }
    } catch (error) {
      ElMessage.error('加载历史查询失败')
    }
  }

  // 删除检索历史
  const deleteSearchHistory = index => {
    ElMessageBox.confirm('确定要删除此检索历史记录吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      searchHistory.value.splice(index, 1)
      localStorage.setItem('literatureSearchHistory', JSON.stringify(searchHistory.value))
    }).catch(() => {
      // 取消删除操作
    })
  }

  // 清空所有检索历史
  const clearAllSearchHistory = () => {
    ElMessageBox.confirm('确定要清空所有检索历史记录吗？此操作不可恢复。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      searchHistory.value = []
      localStorage.removeItem('literatureSearchHistory')
    }).catch(() => {
      // 取消删除操作
    })
  }

  // 切换高级检索显示状态
  const toggleAdvancedSearch = () => {
    if (showAdvancedSearch.value) {
      showAdvancedSearch.value = false
    } else {
      showAdvancedSearch.value = true
      showProblemSearch.value = false
    }
  }

  const toggleProblemSearch = () => {
    if (showProblemSearch.value) {
      // 如果语义检索已经打开，则关闭它
      showProblemSearch.value = false
    } else {
      // 如果语义检索未打开，则打开它并关闭高级检索
      showProblemSearch.value = true
      showAdvancedSearch.value = false
    }
  }

  // 加载可用字段
  const loadAvailableFields = async() => {
    try {
      const response = await getSearchFields()
      if (response.data && Array.isArray(response.data)) {
        availableFields.value = response.data.map(item => ({
          label: item.label,
          value: item.value
        }))

        // 设置默认字段 - 高级检索使用第一个非AI字段
        if (availableFields.value.length > 0 && !searchConditions.value[0].field) {
          const firstNonAiField = availableFields.value.find(field => field.value !== 'ai')
          searchConditions.value[0].field = firstNonAiField ? firstNonAiField.value : availableFields.value[0].value
        }
      }
    } catch (error) {

      // 如果是401错误，不显示错误提示（会自动跳转登录）
      if (error.response?.status === 401) {
        // 静默处理401错误
      } else {
        ElMessage.error('加载搜索字段失败')
      }

      // 使用默认字段 - 与枚举类保持一致
      availableFields.value = [
        { label: 'AI智能搜索', value: 'ai' },
        { label: 'PMID', value: 'pmid' },
        { label: 'PMCID', value: 'pmcid' },
        { label: 'DOI', value: 'doi' },
        { label: '标题', value: 'title' },
        { label: '作者', value: 'author' },
        { label: '姓', value: 'lastname' },
        { label: '名', value: 'forename' },
        { label: '机构信息', value: 'affiliation' },
        { label: '摘要', value: 'articleAbstract' },
        { label: '关键词', value: 'keywords' },
        { label: '出版社名称', value: 'publisherName' },
        { label: '文献分类', value: 'journalName' },
        { label: '文献分类', value: 'pubtype' },
        { label: '发表日期', value: 'published_date_range' },
        { label: '语言', value: 'language' },
        { label: '基金与资助', value: 'grant' },
        { label: '数据库', value: 'databank' },
        { label: '文献来源', value: 'source' },
        { label: '发表状态', value: 'pub_status' },
        { label: 'MeSH', value: 'meshUi' }
      ]
    }
  }

  // 字段标识符映射
  const getFieldIdentifier = fieldValue => {
    const fieldMap = {
      ai: 'AI',
      pmid: 'PMID',
      pmcid: 'PMCID',
      doi: 'DOI',
      title: 'Title',
      author: 'Author',
      lastname: 'Lastname',
      forename: 'Forename',
      affiliation: 'Affiliation',
      articleAbstract: 'Abstract',
      keywords: 'Keywords',
      pubtype: 'Publication Type',
      publisher_name: 'PublisherName',
      journal_name: 'JournalName',
      volume: 'Volume',
      issue: 'Issue',
      page: 'Page',
      published_date_range: 'Publication Date Range',
      language: 'Language',
      grant: 'Grant',
      databank: 'Databank',
      source: 'Source',
      pub_status: 'Publication Status',
      meshUi: 'Mesh',
      impactFactor: 'Impact Factor',
      jcr: 'JCR',
      largeCategorySection: 'CAS Category'
    }
    return fieldMap[fieldValue] || fieldValue
  }

  // 构建查询预览
  const buildQueryPreview = () => {
    const validConditions = searchConditions.value.filter(c => {
      if (c.field === 'published_date_range') {
        return c.field && (c.startYear || c.endYear || c.startDate || c.endDate) // 有任何日期就有效
      } else if (c.field === 'meshUi') {
        return c.field && (c.meshDisplayName || c.value) // MeSH字段检查显示名称或值
      } else {
        return c.field && c.value && c.value.trim()
      }
    })
    if (validConditions.length === 0) return ''

    // 构建嵌套括号格式的查询语句
    let preview = ''

    validConditions.forEach((condition, index) => {
      const fieldIdentifier = getFieldIdentifier(condition.field)

      // 为每个条件构建查询片段
      let conditionQuery = ''

      if (condition.field === 'ai') {
        conditionQuery = `(${condition.value})[${fieldIdentifier}]`
      } else if (condition.field === 'published_date_range') {
        // 构建开始日期
        let startDate = ''
        if (condition.startYear && condition.startMonth && condition.startDay) {
          startDate = `${condition.startYear}${condition.startMonth}${condition.startDay}`
        } else if (condition.startYear && condition.startMonth) {
          startDate = `${condition.startYear}${condition.startMonth}01`
        } else if (condition.startYear) {
          startDate = `${condition.startYear}0101`
        } else if (condition.startDate) {
          startDate = condition.startDate
        }

        // 构建结束日期
        let endDate = ''
        if (condition.endYear && condition.endMonth && condition.endDay) {
          endDate = `${condition.endYear}${condition.endMonth}${condition.endDay}`
        } else if (condition.endYear && condition.endMonth) {
          endDate = `${condition.endYear}${condition.endMonth}01`
        } else if (condition.endYear) {
          endDate = `${condition.endYear}0101`
        } else if (condition.endDate) {
          endDate = condition.endDate
        }

        if (startDate && endDate) {
          conditionQuery = `(${startDate} TO ${endDate}[${fieldIdentifier}])`
        } else if (startDate) {
          conditionQuery = `(${startDate} TO *[${fieldIdentifier}])`
        } else if (endDate) {
          conditionQuery = `(* TO ${endDate}[${fieldIdentifier}])`
        }
      } else if (condition.field === 'meshUi') {
        // MeSH字段：前端显示mesh名称，但实际查询时会替换为meshUi
        const displayValue = condition.meshDisplayName || condition.value
        conditionQuery = `(${displayValue}[${fieldIdentifier}])`
      } else {
        conditionQuery = `(${condition.value}[${fieldIdentifier}])`
      }

      if (index === 0) {
        // 第一个条件直接使用
        preview = conditionQuery
      } else if (index === 1) {
        // 第二个条件：第一个条件 LOGIC 第二个条件
        preview = `${preview} ${condition.logic} ${conditionQuery}`
      } else {
        // 第三个及以后的条件：(之前的整体) LOGIC 新条件
        preview = `(${preview}) ${condition.logic} ${conditionQuery}`
      }
    })

    return preview
  }

  // 获取年份选项（1900-当前年份+5年）
  const getYearOptions = () => {
    const currentYear = new Date().getFullYear()
    const years = []
    for (let year = 1900; year <= currentYear + 5; year++) {
      years.push(year.toString().padStart(4, '0'))
    }
    return years.reverse() // 最新年份在前
  }

  // 获取月份选项
  const getMonthOptions = () => {
    const months = []
    for (let month = 1; month <= 12; month++) {
      months.push({
        value: month.toString().padStart(2, '0'),
        label: month.toString().padStart(2, '0')
      })
    }
    return months
  }

  // 获取日期选项
  const getDayOptions = (year, month) => {
    if (!year || !month) return []

    const daysInMonth = new Date(parseInt(year), parseInt(month), 0).getDate()
    const days = []
    for (let day = 1; day <= daysInMonth; day++) {
      days.push({
        value: day.toString().padStart(2, '0'),
        label: day.toString().padStart(2, '0')
      })
    }
    return days
  }

  // 年份变化处理
  const onYearChange = (condition, type) => {
    // 清空月份和日期
    if (type === 'publish') {
      condition.publishMonth = ''
      condition.publishDay = ''
    } else if (type === 'start') {
      condition.startMonth = ''
      condition.startDay = ''
    } else if (type === 'end') {
      condition.endMonth = ''
      condition.endDay = ''
    }
    updateDateValue(condition, type)
  }

  // 月份变化处理
  const onMonthChange = (condition, type) => {
    // 清空日期
    if (type === 'publish') {
      condition.publishDay = ''
    } else if (type === 'start') {
      condition.startDay = ''
    } else if (type === 'end') {
      condition.endDay = ''
    }
    updateDateValue(condition, type)
  }

  // 日期变化处理
  const onDayChange = (condition, type) => {
    updateDateValue(condition, type)
  }

  // 更新日期值（转换为8位数格式）
  const updateDateValue = (condition, type) => {
    condition.value = '' // 清空普通value字段

    if (type === 'publish') {
      if (condition.publishYear) {
        // 如果有年份，自动补充月份和日期为00
        const month = condition.publishMonth || '00'
        const day = condition.publishDay || '00'
        condition.dateValue = `${condition.publishYear}${month}${day}`
      } else {
        condition.dateValue = null
      }
    } else if (type === 'start') {
      if (condition.startYear) {
        // 如果有年份，自动补充月份和日期为00
        const month = condition.startMonth || '00'
        const day = condition.startDay || '00'
        condition.startDate = `${condition.startYear}${month}${day}`
      } else {
        condition.startDate = null
      }
    } else if (type === 'end') {
      if (condition.endYear) {
        // 对于结束日期，如果没有选择月份和日期，默认使用该年的最后一天
        const month = condition.endMonth || '12'
        let day = condition.endDay
        if (!day) {
          // 如果没有选择日期，使用该月的最后一天
          if (month === '12') {
            day = '31'
          } else {
            // 计算该月的最后一天
            const year = parseInt(condition.endYear)
            const monthNum = parseInt(month)
            const lastDay = new Date(year, monthNum, 0).getDate()
            day = lastDay.toString().padStart(2, '0')
          }
        }
        condition.endDate = `${condition.endYear}${month}${day}`
      } else {
        condition.endDate = null
      }
    }
  }

  // 添加检索条件
  const addSearchCondition = () => {
    const firstNonAiField = availableFields.value.find(field => field.value !== 'ai')
    const defaultField = firstNonAiField ? firstNonAiField.value : (availableFields.value.length > 0 ? availableFields.value[0].value : '')
    searchConditions.value.push({
      logic: 'AND',
      field: defaultField,
      value: '',
      dateValue: null,
      dateRange: null,
      startDate: null,
      endDate: null,
      // 新增的日期字段
      publishYear: '',
      publishMonth: '',
      publishDay: '',
      startYear: '',
      startMonth: '',
      startDay: '',
      endYear: '',
      endMonth: '',
      endDay: '',
      // MeSH显示名称字段
      meshDisplayName: ''
    })
  }

  // 移除检索条件
  const removeSearchCondition = index => {
    if (searchConditions.value.length > 1) {
      searchConditions.value.splice(index, 1)
    }
  }

  // 复制查询语句
  const copySearchQuery = async() => {
    try {
      const queryToCopy = editableQuery.value || queryPreview.value
      await navigator.clipboard.writeText(queryToCopy)
      ElMessage.success('查询语句已复制到剪贴板')
    } catch (error) {
      ElMessage.error('复制失败')
    }
  }

  // 默认加载所有文献
  const loadAllLiterature = async(showMessage = true) => {
    // 只在初始加载时清空选择（showMessage为true表示初始加载）
    if (showMessage) {
      clearSelectionOnNewSearch()
    }

    // 使用统一搜索接口，传空查询来获取所有文献
    const searchData = {
      query: '', // 空查询，后端应该返回所有文献
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      sortKey: sort.value
    }

    try {
      await performUnifiedSearch(searchData, true)

      if (totalResults.value === 0) {
        if (showMessage) ElMessage.warning('ES中暂无数据，请等待数据同步完成')
      } 
    } catch (error) {
      // 如果是401错误，不显示错误提示（会自动跳转登录）
      if (error.response?.status === 401) {
        // 静默处理401错误
      } else if (showMessage) {
        ElMessage.error(`加载文献失败: ${error.response?.data?.message || error.message}`)
      }
    }
  }

  // 处理高级检索
  const handleAdvancedSearch = async() => {
    // 清空选择状态
    clearSelectionOnNewSearch()

    // 获取查询字符串
    let queryString = ''

    // 如果用户直接编辑了查询语句，使用编辑后的查询语句
    if (editableQuery.value && editableQuery.value.trim()) {
      queryString = editableQuery.value.trim()
    } else {
      // 否则从搜索条件构建查询语句
      queryString = buildQueryPreview()
    }

    // 将查询字符串中的MeSH名称替换为MeSH UI
    queryString = replaceMeshNamesWithUi(queryString)

    if (!queryString) {
      ElMessage.warning('请至少输入一个检索条件')
      return
    }

    // 使用统一的搜索接口
    const searchData = {
      query: queryString,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      sortKey: sort.value
    }

    await performUnifiedSearch(searchData)
  }

  // 智能检索 - 统一使用list接口
  const handleSearch = async() => {
    const searchQuery = searchKeyword.value.trim()

    // 清空选择状态
    clearSelectionOnNewSearch()

    // 统一使用list接口，传递查询字符串
    const searchData = {
      query: searchQuery,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      sortKey: sort.value
    }

    await performUnifiedSearch(searchData)
  }

  // 获取当前筛选参数，转换为后端 ArticleQueryDTO 格式
  const getFilterParams = () => {
    const params = {}

    // 时间范围筛选
    if (timeRange.value && timeRange.value !== '') {
      params.timeRange = timeRange.value

      // 如果是自定义时间范围，添加开始和结束日期
      if (timeRange.value === 'custom') {
        // 构建开始日期 YYYYMMDD
        if (startYear.value) {
          const startDateStr = startYear.value +
            (startMonth.value || '01').padStart(2, '0') +
            (startDay.value || '01').padStart(2, '0')
          params.startDate = parseInt(startDateStr)
        }

        // 构建结束日期 YYYYMMDD
        if (endYear.value) {
          const endDateStr = endYear.value +
            (endMonth.value || '12').padStart(2, '0') +
            (endDay.value || '31').padStart(2, '0')
          params.endDate = parseInt(endDateStr)
        } else {
          // 如果用户没有选择结束日期，默认使用当前日期
          const now = new Date()
          const currentDateStr = now.getFullYear().toString() +
            (now.getMonth() + 1).toString().padStart(2, '0') +
            now.getDate().toString().padStart(2, '0')
          params.endDate = parseInt(currentDateStr)
        }
      }
    }

    // 可用文献筛选（布尔类型）
    if (selectedFilters.free && selectedFilters.free.length > 0) {
      selectedFilters.free.forEach(filterValue => {
        if (filterValue === 'hasAbstract') {
          params.hasAbstract = true
        } else if (filterValue === 'isFree') {
          params.isFree = true
        } else if (filterValue === 'hasPdf') {
          params.hasPdf = true
        }
      })
    }

    // 文献分类筛选
    if (selectedFilters.pubtype && selectedFilters.pubtype.length > 0) {
      params.pubtypes = selectedFilters.pubtype
    }

    // 语言筛选
    if (selectedFilters.language && selectedFilters.language.length > 0) {
      params.languages = selectedFilters.language
    }

    // 基金支持筛选
    if (selectedFilters.funding && selectedFilters.funding.length > 0) {
      // 将前端的值转换为后端期望的中文字符串
      const grantSupportValues = selectedFilters.funding.map(filterValue => {
        if (filterValue === 'hasFunding') {
          return '有基金支持'
        } else if (filterValue === 'noFunding') {
          return '无基金支持'
        }
        return filterValue // 如果是其他值，直接返回
      })
      params.grantSupport = grantSupportValues
    }

    // 影响因子筛选
    if (selectedFilters.impactFactor && selectedFilters.impactFactor.length > 0) {
      // 将前端的值转换为后端期望的中文字符串（如果需要）
      const impactFactorValues = selectedFilters.impactFactor.map(filterValue => {
        // 如果是英文值，转换为中文值
        if (filterValue === '5to10') return '5-10'
        if (filterValue === 'above10') return '10以上'
        if (filterValue === '3to5') return '3-5'
        if (filterValue === 'below3') return '3以下'
        // 如果已经是中文值或其他值，直接返回
        return filterValue
      })
      params.impactFactorRanges = impactFactorValues
    }

    // 文章属性筛选
    if (selectedFilters.articleAttributes && selectedFilters.articleAttributes.length > 0) {
      selectedFilters.articleAttributes.forEach(filterValue => {
        if (filterValue === 'databank') {
          params.hasDatabank = true
        }
      })
    }

    // JCR分区筛选
    if (jcrQuartiles.value && jcrQuartiles.value.length > 0) {
      params.jcrQuartiles = jcrQuartiles.value
    }

    // 中科院分区筛选
    if (casQuartiles.value && casQuartiles.value.length > 0) {
      params.largeCategorySections = casQuartiles.value
    }

    // 其他筛选条件（排除预印本和PubMed Central收录）
    if (selectedFilters.other && selectedFilters.other.length > 0) {
      selectedFilters.other.forEach(filterValue => {
        if (filterValue === '排除预印本') {
          params.excludePreprints = true
        } else if (filterValue === 'PubMed Central收录') {
          params.pmcOnly = true
        }
      })
    }

    return params
  }

  // 清空语义检索内容 (未被外部使用，重命名以避免 lint 未使用警告)
  const _clearProblemSearch = () => {
    problemSearch.value = ''
  }

  // 更新筛选选项数据
  const updateFilterOptionsFromResponse = (filterOptions, isInitialLoad = false) => {
    // 直接使用后端返回的筛选选项数据
    filterOptionsData.value = filterOptions || {}

    // 如果是初始加载，同时保存为全量数据
    if (isInitialLoad) {
      allFilterOptionsData.value = filterOptions || {}
    }
  }

  // 统一搜索函数 - 使用后端统一的list接口
  const performUnifiedSearch = async(searchData, isInitialLoad = false) => {
    try {
      loading.value = true
      hasSearched.value = true

      // 构建符合 ArticleQueryDTO 结构的请求参数
      const requestParams = {
        // 基础分页参数
        pageNum: searchData.pageNum || 1,
        pageSize: searchData.pageSize || 10,

        // 查询参数
        query: searchData.query,
        sortKey: searchData.sortKey || sort.value,
        sortBy: sort.value === 'latest' ? 'latest' : 'relevance',

        // 筛选参数
        ...getFilterParams()
      }

      const response = await searchArticles(requestParams)

      if (response.data) {
        // 处理 UnifiedSearchResultVO 结构的返回数据
        const unifiedResult = response.data

        // 处理分页数据
        if (unifiedResult.tableData) {
          searchResults.value = unifiedResult.tableData.rows || []
          totalResults.value = unifiedResult.tableData.total || 0
        } else {
          searchResults.value = []
          totalResults.value = 0
        }

        // 处理聚合数据 - 更新筛选选项
        if (unifiedResult.filterOptions) {
          updateFilterOptionsFromResponse(unifiedResult.filterOptions, isInitialLoad)
        }

        // 保存搜索历史 - 语义检索不保存历史，其他检索保存
        const isSemanticSearch = searchData.query && searchData.query.includes('[AI]')

        if (!isSemanticSearch) {
          // 根据实际的搜索类型决定显示查询
          let displayQuery
          if (showAdvancedSearch.value && searchConditions.value.some(c => c.field && (c.value || c.meshDisplayName || c.publishYear))) {
            // 如果是真正的高级检索（有填写条件），使用构建的查询
            displayQuery = buildQueryPreview()
          } else {
            // 否则使用原始查询字符串（简单检索或语义检索）
            displayQuery = searchData.query
          }

          // 只有当显示查询不为空时才保存
          if (displayQuery && displayQuery.trim()) {
            saveSearchHistory({
              query: displayQuery,
              count: totalResults.value,
              timestamp: new Date()
            })
          }
        }
      } else {
        searchResults.value = []
        totalResults.value = 0
      }
    } catch (error) {
      console.error('检索失败:', error)
      ElMessage.error('检索失败，请稍后重试')
      searchResults.value = []
      totalResults.value = 0
    } finally {
      loading.value = false
    }
  }

  // MeSH自动完成相关方法
  const queryMeshSuggestions = async(queryString, callback) => {
    if (!queryString || queryString.trim().length < 2) {
      callback([])
      return
    }

    try {
      const response = await getMeshAutoComplete(queryString.trim())

      if (response.data && Array.isArray(response.data)) {
        const suggestions = response.data.map(item => ({
          value: item.meshName || item.name || item,
          meshUi: item.meshUi || item.id,
          label: item.meshName || item.name || item
        }))
        callback(suggestions)
      } else {
        callback([])
      }
    } catch (error) {
      console.error('获取MeSH建议失败:', error)
      callback([])
    }
  }

  const handleMeshSelect = (item, condition) => {
    // 当用户选择MeSH项时，将meshUi存储到condition.value中，显示名称存储到meshDisplayName中
    if (item && item.meshUi) {
      condition.value = item.meshUi  // 实际传给后端的是meshUi
      condition.meshDisplayName = item.label || item.value  // 显示给用户的是mesh名称
    }
  }

  const handleMeshBlur = (event, condition) => {
    const inputValue = event.target.value
  
    // 如果输入的值不是有效的Mesh项（没有对应的meshUi），清空输入
    if (inputValue && !condition.value) {
      condition.meshDisplayName = ''
    }
  }

  const handleMeshInput = (value, condition) => {
  
    // 如果用户修改了输入内容，清空对应的meshUi
    if (value !== condition.meshDisplayName) {
      condition.value = ''
    }
  }

  // 将查询字符串中的MeSH名称替换为MeSH UI
  const replaceMeshNamesWithUi = queryString => {
    if (!queryString) return queryString

    let result = queryString

    // 遍历所有搜索条件，找到MeSH字段
    searchConditions.value.forEach(condition => {
      if (condition.field === 'meshUi' && condition.meshDisplayName && condition.value) {
        // 将查询字符串中的mesh名称替换为meshUi
        const namePattern = `${condition.meshDisplayName}[Mesh]`
        const uiReplacement = `${condition.value}[Mesh]`
        console.log('替换模式:', namePattern, '→', uiReplacement)
        result = result.replace(namePattern, uiReplacement)
      }
    })

    return result
  }

  // 处理语义搜索 - 使用统一的list接口
  const handleSemanticSearch = async() => {
    if (!problemSearch.value.trim()) {
      ElMessage.warning('请输入问题描述')
      return
    }

    // 清空选择状态
    clearSelectionOnNewSearch()

    // 构建语义搜索的查询格式：(问题描述)[AI]
    const semanticQuery = `(${problemSearch.value.trim()})[AI]`

    // 使用统一的搜索接口
    const searchData = {
      query: semanticQuery,
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      sortKey: sort.value
    }

    await performUnifiedSearch(searchData)
  }

  // 自定义范围相关方法
  const openCustomRangeDialog = () => {
    customRangeDialog.value = true
  }

  const clearCustomRange = () => {
    startYear.value = ''
    startMonth.value = ''
    startDay.value = ''
    endYear.value = ''
    endMonth.value = ''
    endDay.value = ''
  }

  const applyCustomRange = async() => {
    // 验证自定义时间范围 - 至少需要设置开始年份或结束年份
    if (!startYear.value && !endYear.value) {
      ElMessage.warning('请至少设置开始年份或结束年份')
      return
    }

    // 设置时间范围为自定义
    timeRange.value = 'custom'

    // 关闭对话框
    customRangeDialog.value = false

    // 触发筛选更新
    await handleFilterChange()
  }

  // 单选按钮点击处理（支持取消选择）
  const handleRadioClick = async value => {
    if (timeRange.value === value) {
      // 如果点击的是已选中的选项，则取消选择
      timeRange.value = ''
    } else {
      // 如果点击的是未选中的选项，则选择它
      timeRange.value = value
    }

    // 触发筛选更新
    await handleFilterChange()
  }

  // 防抖定时器
  let filterChangeTimer = null

  // 手动触发筛选条件更新 - 重新执行当前搜索，带上筛选参数
  const handleFilterChange = async() => {
    scrollToTop()
    // 清除之前的定时器
    if (filterChangeTimer) {
      clearTimeout(filterChangeTimer)
    }

    // 设置新的定时器，300ms 后执行
    filterChangeTimer = setTimeout(async() => {
      // 重置到第一页
      currentPage.value = 1

      if (searchKeyword.value.trim()) {
        // 有简单搜索关键词，使用智能检索
        handleSearch()
      } else if (showAdvancedSearch.value && checkValidAdvancedConditions()) {
        // 在高级检索模式且有有效检索条件
        handleAdvancedSearch(true)
      } else if (showProblemSearch.value && problemSearch.value.trim()) {
        // 在语义检索模式且有检索内容
        handleSemanticSearch(true)
      } else {
        // 其他情况（包括检索模式但没有检索条件），使用普通加载
        loadAllLiterature(false)
      }
    }, 300)
  }

  // 发布时间范围
  const timeRange = ref('')
  const timeRangeOptions = [
    { label: '最近1年', value: 'recent_1year' },
    { label: '最近5年', value: 'recent_5years' },
    { label: '最近10年', value: 'recent_10years' }
  ]

  // 动态筛选选项（从后端获取）
  const filterOptionsData = ref({})

  // 储存全部数据的筛选选项
  const allFilterOptionsData = ref({})

  // 选中的筛选值
  const selectedFilters = reactive({
    language: [],
    pubtype: [],
    free: [],
    pub_status: [],
    articleAttributes: [],
    source: [],
    journal_name: [],
    publisher_name: [],
    other: [],
    funding: [],
    impactFactor: []
  })

  // 计算属性：将后端数据转换为前端需要的格式，提供后备静态数据
  const languagesOptions = computed(() => {
    // 语言代码到中文名称的映射
    const languageMap = {
      eng: '英语',
      ger: '德语',
      fre: '法语',
      dut: '荷兰语',
      spa: '西班牙语',
      rus: '俄语',
      jpn: '日语',
      srp: '塞尔维亚语',
      chi: '中文',
      ita: '意大利语',
      por: '葡萄牙语',
      kor: '韩语',
      ara: '阿拉伯语',
      pol: '波兰语',
      hun: '匈牙利语',
      cze: '捷克语',
      swe: '瑞典语',
      nor: '挪威语',
      dan: '丹麦语',
      fin: '芬兰语',
      tur: '土耳其语',
      gre: '希腊语',
      heb: '希伯来语',
      tha: '泰语',
      vie: '越南语',
      ind: '印尼语',
      may: '马来语',
      hin: '印地语',
      ben: '孟加拉语',
      per: '波斯语',
      ukr: '乌克兰语',
      bul: '保加利亚语',
      rom: '罗马尼亚语',
      rum: '罗马尼亚语', // 罗马尼亚语的另一种代码
      hrv: '克罗地亚语',
      slv: '斯洛文尼亚语',
      slo: '斯洛伐克语', 
      slk: '斯洛伐克语', // 斯洛伐克语的另一种代码
      est: '爱沙尼亚语',
      lav: '拉脱维亚语',
      lit: '立陶宛语',
      ice: '冰岛语',
      wel: '威尔士语',
      gle: '爱尔兰语',
      sco: '苏格兰语',
      cat: '加泰罗尼亚语',
      baq: '巴斯克语',
      mal: '马耳他语',
      alb: '阿尔巴尼亚语',
      mac: '马其顿语',
      bos: '波斯尼亚语',
      mon: '蒙古语',
      tib: '藏语',
      bur: '缅甸语',
      khm: '高棉语',
      lao: '老挝语',
      sin: '僧伽罗语',
      tam: '泰米尔语',
      tel: '泰卢固语',
      kan: '卡纳达语',
      guj: '古吉拉特语',
      pan: '旁遮普语',
      urd: '乌尔都语',
      nep: '尼泊尔语',
      amh: '阿姆哈拉语',
      swa: '斯瓦希里语',
      afr: '南非荷兰语',
      zul: '祖鲁语',
      xho: '科萨语',
      und: '未定义',
      mul: '多语言',
      epo: '世界语',
      lat: '拉丁语'
    }

    const backendData = (filterOptionsData.value.language || []).map(item => {
      const languageCode = item.value || item
      let label = languageMap[languageCode] || languageCode

      // 处理组合语言代码（如 "eng jpn", "eng spa"）
      if (typeof languageCode === 'string' && languageCode.includes(' ')) {
        const codes = languageCode.split(' ')
        const translatedCodes = codes.map(code => languageMap[code.trim()] || code.trim())
        label = translatedCodes.join(' ')
      }

      return {
        label,
        value: item.value || item,
        count: item.count || 0
      }
      // 暂时不显示“未定义的语言”
    }).filter(item => item.value !== 'und') 

    if (backendData.length > 0) {
      return backendData
    }

    // 如果搜索为空
    const allLanguageData = (allFilterOptionsData.value.language || []).map(item => {
      const languageCode = item.value || item
      let label = languageMap[languageCode] || languageCode

      // 处理组合语言代码（如 "eng jpn", "eng spa"）
      if (typeof languageCode === 'string' && languageCode.includes(' ')) {
        const codes = languageCode.split(' ')
        const translatedCodes = codes.map(code => languageMap[code.trim()] || code.trim())
        label = translatedCodes.join(' ')
      }

      return {
        label,
        value: item.value || item,
        count: 0
      }
    }).filter(item => item.value !== 'und') 

    return allLanguageData
  })

  // 处理MeSH字段清空
  const handleMeshClear = condition => {
    condition.meshDisplayName = ''
    condition.value = ''
  }
  // 显示的语言选项（根据展开状态控制）
  const displayedLanguageOptions = computed(() => {
    if (languagesOptions.value.length <= 3 || showMoreLanguages.value) {
      return languagesOptions.value
    }
    return languagesOptions.value.slice(0, 3)
  })

  const literatureTypesOptions = computed(() => {
    const backendData = (filterOptionsData.value.pubtype || []).map(item => ({
      label: item.label || item.value || item,
      value: item.value || item,
      count: item.count || 0
    }))

    if (backendData.length > 0) {
      return backendData
    }

    // 当搜索结果为0
    const allTypesData = (allFilterOptionsData.value.pubtype || []).map(item => ({
      label: item.label || item.value || item,
      value: item.value || item,
      count: 0
    }))
    
    return allTypesData
  })

  const availableTypesOptions = computed(() => {
    const options = []

    // 1. 先添加摘要选项（从 abstract 字段获取）
    const abstractData = filterOptionsData.value.abstract || []
    const abstractItem = abstractData.find(item =>
      item.value === 'true' || item.value === '1' || item.value === 1
    )
    if (abstractItem) {
      options.push({
        label: '摘要',
        value: 'hasAbstract', // 使用唯一标识符
        count: abstractItem.count || 0
      })
    }

    // 2. 再添加免费全文选项（从 free 字段获取）
    const freeData = (filterOptionsData.value.free || []).filter(item => {
      // 只保留免费全文相关的项目
      if (item.value === 'true' || item.value === '1' || item.value === 1) {
        return true
      } else if (typeof item.value === 'string') {
        const lowerValue = item.value.toLowerCase()
        return lowerValue.includes('free') || lowerValue.includes('免费')
      }
      return false
    })

    if (freeData.length > 0) {
      options.push({
        label: '免费全文',
        value: 'isFree',
        count: freeData[0].count || 0
      })
    }

    // 3. 最后添加拥有全文选项（从 hasPdf 字段获取）
    const hasPdfData = filterOptionsData.value.hasPdf || []
    const hasPdfItem = hasPdfData.find(item =>
      item.value === 'true' || item.value === '1' || item.value === 1
    )
    if (hasPdfItem) {
      options.push({
        label: '拥有全文',
        value: 'hasPdf',
        count: hasPdfItem.count || 0
      })
    }

    // 如果后端没有数据，使用静态后备数据
    return options.length > 0 ? options : [
      { label: '摘要', value: 'hasAbstract', count: 0 },
      { label: '免费全文', value: 'isFree', count: 0 },
      { label: '拥有全文', value: 'hasPdf', count: 0 }
    ]
  })

  const articleAttributesOptions = computed(() => {
    const options = []

    // 1. 添加发布状态选项（从 pub_status 字段获取）
    const pubStatusData = (filterOptionsData.value.pub_status || []).map(item => ({
      label: item.label || item.value || item,
      value: item.value || item,
      count: item.count || 0
    }))
    options.push(...pubStatusData)

    // 2. 添加相关数据选项（从 databank 字段获取）
    const databankData = filterOptionsData.value.databank || []
    const databankItem = databankData.find(item =>
      item.value === 'true' || item.value === '1' || item.value === 1
    )
    if (databankItem) {
      options.push({
        label: '相关数据',
        value: 'databank',
        count: databankItem.count || 0
      })
    }

    // 如果后端没有数据，使用静态后备数据
    return options.length > 0 ? options : [
      { label: '相关数据', value: 'databank', count: 0 }
    ]
  })

  const sourceOptions = computed(() => {
    return (filterOptionsData.value.source || []).map(item => ({
      label: item.label || item.value || item,
      value: item.value || item,
      count: item.count || 0
    }))
  })

  // 获取来源对应的内联样式（只改变颜色）
  const getSourceStyle = sourceValue => {
    const source = sourceValue?.toLowerCase() || ''

    if (source === 'pubmed') {
      return {} // 保持原来的样式，不覆盖
    } else if (source === 'pmc') {
      return { backgroundColor: '#e3f2fd', color: '#1565c0' }
    } else if (source === 'biorxiv') {
      return { backgroundColor: '#fff3e0', color: '#ef6c00' }
    } else if (source === 'medrxiv') {
      return { backgroundColor: '#fce4ec', color: '#c2185b' }
    } else if (source === 'custom') {
      return { backgroundColor: '#f3e5f5', color: '#7b1fa2' }
    } else {
      return { backgroundColor: '#f5f5f5', color: '#616161' }
    }
  }

  const journalOptions = computed(() => {
    return (filterOptionsData.value.journal_name || []).map(item => ({
      label: item.label || item.value || item,
      value: item.value || item,
      count: item.count || 0
    }))
  })

  const publisherOptions = computed(() => {
    return (filterOptionsData.value.publisher_name || []).map(item => ({
      label: item.label || item.value || item,
      value: item.value || item,
      count: item.count || 0
    }))
  })

  // 影响因子选项（从后端聚合数据获取）
  const impactFactorsOptions = computed(() => {
    const backendData = filterOptionsData.value.impactFactor || []

    if (backendData.length > 0) {
      // 直接使用后端返回的聚合数据
      const options = backendData.map(item => ({
        label: item.label || item.value || item,
        value: item.value || item,
        count: item.count || 0
      }))

      return options
    }

    // 如果后端没有数据，使用静态后备数据（使用中文值与后端保持一致）
    return [
      { label: '10以上', value: '10以上', count: 0 },
      { label: '5-10', value: '5-10', count: 0 },
      { label: '3-5', value: '3-5', count: 0 },
      { label: '3以下', value: '3以下', count: 0 }
    ]
  })

  const scrollToTop = () => window.scrollTo({
    top: 0, behavior: 'smooth' 
  })

  const fundingSupportOptions = computed(() => {
    const backendData = filterOptionsData.value.grant || []

    if (backendData.length > 0) {
      // 直接使用后端返回的聚合数据，不再手动计算
      const options = []

      // 查找有基金支持的项目
      const fundedItem = backendData.find(item =>
        item.value === '有基金支持' ||
        (typeof item.value === 'string' && item.value.includes('有基金'))
      )

      // 查找无基金支持的项目
      const unfundedItem = backendData.find(item =>
        item.value === '无基金支持' ||
        (typeof item.value === 'string' && item.value.includes('无基金'))
      )

      if (fundedItem) {
        options.push({
          label: '有基金支持',
          value: 'hasFunding',
          count: fundedItem.count || 0
        })
      }

      if (unfundedItem) {
        options.push({
          label: '无基金支持',
          value: 'noFunding',
          count: unfundedItem.count || 0
        })
      }

      return options
    }
    // 如果后端没有数据，使用静态后备数据
    return [
      { label: '有基金支持', value: 'hasFunding', count: 0 },
      { label: '无基金支持', value: 'noFunding', count: 0 }
    ]
  })

  // JCR分区选项（从后端聚合数据获取）
  const jcrQuartilesOptions = computed(() => {
    const backendData = filterOptionsData.value.jcr || []
    // 期望后端返回形如：[{ value: 'Q1', count: 123 }, ...]
    if (backendData.length > 0) {
      return backendData.map(item => ({
        label: item.label || item.value || item,
        value: item.value || item,
        count: item.count || 0
      }))
    }
    // 如果数据为空
    return [
      { label: 'Q1', value: 'Q1', count: 0 },
      { label: 'Q2', value: 'Q2', count: 0 },
      { label: 'Q3', value: 'Q3', count: 0 },
      { label: 'Q4', value: 'Q4', count: 0 }
    ]
  })

  // 中科院分区选项（从后端聚合数据获取）
  const casQuartilesOptions = computed(() => {
    const backendData = filterOptionsData.value.largeCategorySection || []
    // 后端value通常为 1/2/3/4，标签按“X区”展示
    if (backendData.length > 0) {
      const options = backendData.map(item => {
        const val = item.value ?? item
        // 强制使用"X区"格式，不使用后端的label
        const label = (typeof val === 'number' || (/^\d+$/).test(String(val))) ? `${val}区` : (val || '')
        return {
          label,
          value: val,
          count: item.count || 0
        }
      })
      // 按照1区、2区、3区、4区的顺序排序
      return options.sort((a, b) => {
        const aVal = parseInt(a.value) || 999
        const bVal = parseInt(b.value) || 999
        return aVal - bVal
      })
    }
    
    // 如果没有数据，使用静态中科院分区数据，数量为0
    return [
      { label: '1区', value: '1', count: 0 },
      { label: '2区', value: '2', count: 0 },
      { label: '3区', value: '3', count: 0 },
      { label: '4区', value: '4', count: 0 }
    ] 
  })

  // 其他静态选项
  const otherOptions = [
    { label: '排除预印本', value: '排除预印本' },
    { label: 'PubMed Central收录', value: 'PubMed Central收录' }
  ]

  // 静态选中值（保持兼容性）
  const jcrQuartiles = ref([])
  const casQuartiles = ref([])

  // 文献类型确认方法
  const confirmLiteratureTypes = async() => {
    literTypeDialog.value = false
    // 触发筛选更新
    await handleFilterChange()
  }

  // 收藏相关方法
  const handleBatchFavorite = () => {
    if (selectedArticles.value.size === 0) {
      ElMessage.warning('请先选择要收藏的文献')
      return
    }

    
    // TODO: 实现批量收藏逻辑
    // showCollectionModal.value = true
  }

  const handleCollectionConfirm = _data => {
    // 收藏确认处理
  }

  // 文献选择相关方法
  const handleSelectAll = event => {
    if (event.target.checked) {
      // 全选：将当前页面所有文献添加到选择集合
      searchResults.value.forEach(article => {
        selectedArticles.value.add(article.id)
      })
    } else {
      // 取消全选：从选择集合中移除当前页面所有文献
      searchResults.value.forEach(article => {
        selectedArticles.value.delete(article.id)
      })
    }
  }

  const handleArticleSelectionChange = ({ article, selected }) => {
    if (selected) {
      selectedArticles.value.add(article.id)
    } else {
      selectedArticles.value.delete(article.id)
    }
  }

  // 监听搜索关键词变化，清空选择状态（新搜索时清空）
  watch([searchKeyword, showAdvancedSearch, showProblemSearch], () => {
    selectedArticles.value.clear()
  })

  // 当执行新搜索时清空选择状态
  const clearSelectionOnNewSearch = () => {
    selectedArticles.value.clear()
  }

</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.literature {
  background-color: #F5F5F5;
  min-height: calc(100vh - 520px);
}

// 搜索区域
.search-section {
  padding: 36px 0 20px 0;

  @media (max-width: $breakpoint-md) {
    padding: $spacing-lg 0;
  }
}

.search-container {
  max-width: 893px;
  margin: 0 auto;

  @media (max-width: $breakpoint-md) {
    padding: 0 $spacing-md;
  }
}

.search-bar {
  display: flex;
  height: 60px;
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow;
  overflow: hidden;

  @media (max-width: $breakpoint-md) {
    height: 50px;
    flex-direction: row;
  }
}

.search-field {
  display: flex;
  align-items: center;
  flex: 1;
  padding: 0 $spacing-md 0 0;

  :deep(.el-select__wrapper) {
    box-shadow: none !important;
    padding-left: 20px;
    font-weight: 600;
    width: 130px;

    &:hover {
      box-shadow: none !important;
    }
  }

  :deep(.el-select__selected-item ) {
    color: rgba($primary-color, 1);
    font-size: $font-size-small;
  }

  :deep(.el-select__caret) {
    color: $primary-color;
  }
}

.search-type {
  font-size: $font-size-large;
  font-weight: $font-weight-medium;
  color: $primary-color;
  padding-right: $spacing-md;
  white-space: nowrap;
  width: 130px;
}


.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: $font-size-large;
  padding: $spacing-md;
  color: $primary-color;

  @media (max-width: $breakpoint-md) {
    font-size: $font-size-medium;
    padding: $spacing-sm $spacing-md;
  }

  &::placeholder {
    color: rgba($primary-color, 1);
    font-size: $font-size-small;

    @media (max-width: $breakpoint-md) {
      font-size: 14px;
    }
  }

  &:focus {
    outline: none;
  }
}

.search-button {
  width: 75px;
  height: 100%;
  background-color: $primary-color;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  .el-icon {
    font-size: 20px;
    color: $white;
  }
}

.search-options {
  display: flex;
  justify-content: center;
  margin-top: $spacing-lg;
  gap: $spacing-xxl;

  @media (max-width: $breakpoint-md) {
    gap: $spacing-lg;
    margin-top: $spacing-md;
  }
}

.search-option {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $primary-color;
  font-size: $font-size-small;
  font-weight: $font-weight-medium;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: $border-radius-md;
  transition: all 0.3s ease;

  .option-icon {
    width: 20px;
    height: 22px;
  }

  &:hover {
    background-color: rgba($primary-color, 0.1);
  }

  &.active {
    background-color: #DBE5EB;
    color: $primary-color;
    border: 1px solid rgba(10, 83, 144, 0.16);
  }
}

/* 高级检索样式 */
.advanced-search-container {
  margin-top: $spacing-lg;
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow;
  padding: $spacing-lg;
  width: 100%;
  overflow: hidden;
  transform-origin: top center;

  @media (max-width: $breakpoint-md) {
    margin-top: $spacing-md;
    padding: $spacing-md;
    border-radius: $border-radius-md;
  }

  &.v-enter-active,
  &.v-leave-active {
    transition: all 0.3s ease;
  }

  &.v-enter-from,
  &.v-leave-to {
    opacity: 0;
    transform: scaleY(0);
  }

  &.v-enter-to,
  &.v-leave-from {
    opacity: 1;
    transform: scaleY(1);
  }
}

.advanced-search-content {
  width: 100%;
}

.search-row {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  margin-bottom: $spacing-md;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    gap: $spacing-sm;
    align-items: stretch;
  }

  :deep(.el-select__wrapper), :deep(.el-input__wrapper) {
    border-radius: 8px;
  }
}

.logic-select {
  width: 100px;
}

.logic-placeholder {
  width: 100px;
}

.field-select {
  width: 150px;
}

.keyword-input {
  flex: 1;
}

.remove-condition-btn {
  color: $gray;
}

.add-condition-row {
  margin: $spacing-xs 0;
}

.add-condition-btn {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $primary-color;
  padding: 0;
}

.search-query-container {
  margin-bottom: $spacing-lg;

  :deep(.el-textarea__inner) {
    border-radius: 8px;
  }
}

.search-query-label {
  font-size: $font-size-small;
  color: rgb(55 65 81);
}

.search-query-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-xs;

  .search-query-label {
    font-size: $font-size-small;
    color: $primary-color;
    font-weight: $font-weight-medium;
  }

  .query-actions {
    display: flex;
    gap: $spacing-xs;
  }

  .action-btn {
    color: $primary-color;
    font-size: $font-size-small;
    padding: 4px 8px;
    border-radius: $border-radius-sm;
    transition: all 0.2s;

    &:hover {
      background-color: rgba($primary-color, 0.1);
    }

    .el-icon {
      margin-right: 4px;
    }
  }
}

.query-help {
  display: flex;
  align-items: flex-start;
  gap: $spacing-xs;
  margin-top: $spacing-xs;
  padding: $spacing-xs $spacing-sm;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: $border-radius-sm;
  font-size: 12px;
  color: $gray;
  line-height: 1.4;

  .help-icon {
    font-size: 14px;
    color: #0284c7;
    margin-top: 1px;
    flex-shrink: 0;
  }
}

.search-query-text {
  font-family: monospace;
  color: $primary-color;
}

.copy-btn {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $primary-color;
  font-size: 15px;
}

.search-history-container {
  margin-bottom: $spacing-lg;

  .search-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-xs;

    .clear-all-btn {
      color: #000000;
      font-size: $font-size-small;
      padding: 4px 8px;
      border-radius: $border-radius-sm;
      transition: all 0.2s;
      margin-left: 8px;

      &:hover {
        background-color: rgba(245, 108, 108, 0.1);
      }

      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

.search-history-label {
  font-size: $font-size-small;
  color: rgb(55 65 81);
  margin-bottom: $spacing-xs;
}

.search-history-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;

  .empty-history {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl;
    color: $gray;
    font-size: $font-size-small;
    background-color: #F9FAFB;
    border-radius: $border-radius-md;

    .empty-icon {
      font-size: 32px;
      margin-bottom: $spacing-sm;
      opacity: 0.5;
    }
  }
}

.search-history-item {
  display: flex;
  align-items: center;
  padding: 2px $spacing-xxs;
  font-size: $font-size-small;
  background-color: #F9FAFB;
  border-radius: $border-radius-md;
}

.history-index {
  width: 40px;
  color: $gray;
}

.history-query {
  flex: 1;
  color: rgb(55 65 81);
  font-family: monospace;
}

.history-result-count {
  width: 120px;
  text-align: right;
  color: $gray;
}

.history-actions {
  display: flex;
  gap: $spacing-xs;
  margin-left: 22px;

  .edit-btn, .delete-btn {
    color: $primary-color;
    padding: 0;
    font-size: 15px;
  }
}

.search-actions {
  display: flex;
  justify-content: flex-end;
  gap: $spacing-md;
  margin-top: $spacing-lg;
}

.clear-btn {
  border-color: $gray;
  color: $gray;
}

.search-btn {
  min-width: 100px;
}

// 内容区域

.content-container {
  display: flex;
  gap: $spacing-lg;
  padding-bottom: 30px;

  @media (max-width: $breakpoint-lg) {
    width: 100%;
    flex-direction: row;
    gap: $spacing-md;
  }

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    gap: $spacing-md;
    padding: 0 $spacing-sm;
  }
}

// 左侧筛选条件
.filter-sidebar {
  width: 307px;
  background-color: $white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg $spacing-lg $spacing-xxl;
  box-shadow: $box-shadow;

  @media (max-width: $breakpoint-lg) {
    width: 280px;
  }

  @media (max-width: $breakpoint-md) {
    width: 100%;
    padding: $spacing-md;
    margin-bottom: $spacing-md;

    &.mobile-hidden {
      display: none;
    }
  }
}

// 移动端筛选按钮
.mobile-filter-toggle {
  display: none;

  @media (max-width: $breakpoint-md) {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    background-color: $white;
    border-radius: $border-radius-md;
    padding: $spacing-sm $spacing-md;
    margin-bottom: $spacing-md;
    box-shadow: $box-shadow;
    cursor: pointer;
    font-size: $font-size-small;
    color: $primary-color;
    font-weight: $font-weight-medium;

    .toggle-icon {
      margin-left: auto;
      transition: transform 0.3s ease;

      &.rotated {
        transform: rotate(180deg);
      }
    }
  }
}

// ES数据更新区域
.es-update-section {
  margin-bottom: $spacing-lg;
  padding-bottom: $spacing-md;
  border-bottom: 1px solid $border-color-light;
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;

  .button-row {
    display: flex;
    gap: $spacing-sm;

    .el-button {
      flex: 1;
      height: 40px;
      font-size: 14px;
      font-weight: 500;
      border-radius: $border-radius-md;

      &:hover {
        transform: translateY(-1px);
      }

      &.is-loading {
        cursor: not-allowed;
      }

      .el-icon {
        margin-right: 6px;
      }
    }
  }

  .es-update-btn:hover {
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }

  .vector-generate-btn:hover {
    box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
  }

  .mesh-process-btn:hover {
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
  }

  .task-status-btn:hover {
    box-shadow: 0 4px 12px rgba(144, 147, 153, 0.3);
  }
}

.filter-header {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
  margin-bottom: $spacing-md;

  .filter-icon {
    width: 18px;
  }
}

.filter-title {
  font-size: 19px;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin: 0;
}

.filter-icon {
  width: 24px;
  height: 24px;
}

.filter-group {
  margin-bottom: $spacing-lg;

  @media (max-width: $breakpoint-md) {
    margin-bottom: $spacing-md;
  }
}

.filter-group-title {
  font-size: 17px;
  font-weight: $font-weight-medium;
  color: $primary-color;
  margin: $spacing-md 0 $spacing-sm;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-content: flex-start;
}

.filter-option {
  display: flex;
  // align-items: center;
  font-size: 15px;
  color: $gray;

  :deep(.el-checkbox) {
    flex: 1;
  }

  :deep(.el-checkbox__input) {
    margin-right: $spacing-sm;
  }

  :deep(.el-radio__input) {
    margin-right: $spacing-sm;
  }

  :deep(.el-checkbox__label),
  :deep(.el-radio__label) {
    padding-left: 0;
    font-size: $font-size-small;
    font-weight: $font-weight-regular;
    flex: 1;
  }
}

.filter-option-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 100%;

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: $spacing-xs;
    max-width: 140px; // 减小最大宽度，给数字留出空间
  }

  // 特殊处理：PubMed Central收录不限制文本长度
  &.no-text-limit span {
    max-width: none;
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
  }
}

.custom-range {
  margin-left: $spacing-xl;
  color: $primary-color;
  font-weight: $font-weight-medium;
  font-size: $font-size-small;
}

.count-badge {
  margin-left: $spacing-xs;
  background-color: #F3F6F9;
  color: $gray;
  font-size: $font-size-small;
  padding: 2px 5px;
  border-radius: 9999px;
}

.more-types {
  color: $primary-color;
  font-size: $font-size-small;
  margin-top: $spacing-sm;
  margin-left: $spacing-xl;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  transition: all 0.2s ease;

  &:hover {
    text-decoration: underline;
    opacity: 0.8;
  }

  .el-icon {
    font-size: 16px;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.1);
    }
  }
}

.show-more-btn {
  width: 100%;
  height: 38px;
  background-color: $primary-color;
  color: $white;
  border: none;
  border-radius: $border-radius-md;
  font-size: $font-size-small;
  margin: $spacing-md 0;
  cursor: pointer;
}

// 右侧文献列表
.literature-content {
  flex: 1;
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow;
  padding: $spacing-lg;

  @media (max-width: $breakpoint-md) {
    padding: $spacing-md;
  }
}

.literature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: $spacing-md;
  border-bottom: 1px solid #ECECEC;
  margin-bottom: $spacing-md;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    gap: $spacing-md;
    align-items: flex-start;
  }
}

.literature-header-left {
  display: flex;
  align-items: center;
  gap: $spacing-lg;
  color: $gray;
  font-size: $font-size-small;

  @media (max-width: $breakpoint-md) {
    width: 100%;
    justify-content: space-between;
    gap: $spacing-sm;
  }
}

.select-all {
  display: flex;
  align-items: center;
  gap: $spacing-xs;

  input[type="checkbox"] {
    &:indeterminate {
      background-color: $primary-color;
      border-color: $primary-color;

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 8px;
        height: 2px;
        background-color: white;
      }
    }
  }
}

.selected-count {
  color: $primary-color;
  font-weight: $font-weight-medium;
  margin-left: $spacing-xs;
}

.limit-notice {
  color: #f56c6c;
  font-weight: $font-weight-medium;
  margin-left: $spacing-xs;
  font-size: $font-size-small;
}

.result-count {
  font-size: $font-size-medium;
  color: $primary-color;
}

.literature-header-right {
  display: flex;
  align-items: center;
  gap: $spacing-lg;

  @media (max-width: $breakpoint-md) {
    width: 100%;
    justify-content: space-between;
    gap: $spacing-sm;
    flex-wrap: wrap;
  }
}

.sort-options, .page-size {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-small;
  color: $gray;

  @media (max-width: $breakpoint-md) {
    font-size: 14px;
    gap: 4px;
  }
}

.sort-dropdown, .page-size-dropdown {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $primary-color;
  cursor: pointer;
}

.action-button {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $primary-color;
  font-size: $font-size-small;
  cursor: pointer;

  @media (max-width: $breakpoint-md) {
    font-size: 14px;
    gap: 4px;
    padding: 4px 8px;
    border-radius: $border-radius-sm;
    background-color: rgba($primary-color, 0.1);
  }

  .export {
    font-size: $font-size-small;
    color: $primary-color;

    @media (max-width: $breakpoint-md) {
      font-size: 14px;
    }
  }
}

.article-list-container {
  margin-bottom: $spacing-lg;
}

.loading-state {
  padding: $spacing-xxl;
  :deep(.el-skeleton__item) {
    margin-bottom: $spacing-md;
  }
}

.no-results {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-xxl;
  color: $gray;

  p {
    font-size: $font-size-medium;
    margin: 0;
  }
}

.login-prompt {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $spacing-xxl;
}

.login-card {
  text-align: center;
  padding: 40px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 400px;

  .login-icon {
    font-size: 48px;
    color: #409eff;
    margin-bottom: 20px;
  }

  h3 {
    margin: 0 0 12px 0;
    color: #303133;
    font-size: 20px;
    font-weight: 600;
  }

  p {
    margin: 0 0 24px 0;
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: $spacing-lg;

  @media (max-width: $breakpoint-md) {
    margin-top: $spacing-md;

    :deep(.el-pagination) {
      .el-pager li {
        min-width: 32px;
        height: 32px;
        line-height: 32px;
        font-size: 14px;
      }

      .btn-prev, .btn-next {
        min-width: 32px;
        height: 32px;
        line-height: 32px;
      }
    }
  }
}

:deep(.el-select__wrapper) {
  border-radius: 8px;

  @media (max-width: $breakpoint-md) {
    min-height: 36px;
  }
}

// 文献类型对话框样式
.literature-type-dialog {
  .literature-type-content {
    max-height: 60vh;
    overflow-y: auto;

    @media (max-width: $breakpoint-md) {
      max-height: 50vh;
    }

    // 自定义滚动条样式，使其更细
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 2px;

      &:hover {
        background: rgba(0, 0, 0, 0.3);
      }
    }
  }

  .literature-type-checkboxes {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: $spacing-sm $spacing-lg;

    @media (max-width: $breakpoint-md) {
      grid-template-columns: 1fr;
      gap: $spacing-xs;
    }
  }

  :deep(.el-checkbox.literature-type-checkbox) {
    width: 100%;

    .el-checkbox__label {
      font-size: $font-size-small;
      line-height: 1.4;
      width: 100%;

      @media (max-width: $breakpoint-md) {
        font-size: 14px;
      }
    }

    .filter-option-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        margin-right: 6px;
      }

      .count-badge {
        font-weight: $font-weight-medium !important;
        color: #666 !important;
        white-space: nowrap !important;
        min-width: 50px !important;
        width: 50px !important;
        text-align: right !important;
        font-family: 'Courier New', monospace !important;
        display: inline-block !important;
        flex-shrink: 0 !important;
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-md;

    @media (max-width: $breakpoint-md) {
      flex-direction: column-reverse;
      gap: $spacing-sm;

      .cancel-btn, .confirm-btn {
        width: 100%;
      }
    }
  }

  @media (max-width: $breakpoint-md) {
    :deep(.el-dialog__header) {
      padding: $spacing-md $spacing-md $spacing-sm;
    }

    :deep(.el-dialog__body) {
      padding: $spacing-sm $spacing-md;
    }

    :deep(.el-dialog__footer) {
      padding: $spacing-sm $spacing-md $spacing-md;
    }
  }
}

// 自定义范围对话框样式
.custom-range-content {
  padding: $spacing-md 0;

  @media (max-width: $breakpoint-md) {
    padding: $spacing-sm 0;
  }

  .range-option {
    margin-bottom: $spacing-lg;

    @media (max-width: $breakpoint-md) {
      margin-bottom: $spacing-md;
    }

    :deep(.el-radio) {
      .el-radio__label {
        color: $primary-color;
        font-weight: $font-weight-medium;
      }
    }
  }

  .date-section {
    margin-bottom: $spacing-lg;

    @media (max-width: $breakpoint-md) {
      margin-bottom: $spacing-md;
    }

    .date-label {
      font-size: $font-size-small;
      color: $gray;
      margin-bottom: $spacing-xs;
      text-transform: uppercase;
    }

    .end-time-note {
      display: flex;
      align-items: center;
      height: 32px;
      padding: 0 12px;
      background-color: #f5f7fa;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      color: #909399;
      font-size: 12px;
    }

    .date-inputs {
      display: flex;
      gap: $spacing-sm;

      @media (max-width: $breakpoint-md) {
        gap: $spacing-xs;
      }

      .year-select {
        flex: 2;

        :deep(.el-input__wrapper) {
          border-radius: $border-radius-md;
        }
      }

      .month-select, .day-select {
        flex: 1;

        :deep(.el-input__wrapper) {
          border-radius: $border-radius-md;
        }

        :deep(.el-select__placeholder) {
          color: #c0c4cc;
        }

        &.is-disabled {
          :deep(.el-input__wrapper) {
            background-color: #f5f7fa;
            border-color: #e4e7ed;
            color: #c0c4cc;
          }
        }
      }
    }
  }

  .dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: $spacing-md;
    margin-top: $spacing-xl;

    @media (max-width: $breakpoint-md) {
      margin-top: $spacing-lg;
      gap: $spacing-sm;
    }

    .clear-btn {
      border-color: $gray;
      color: $gray;
    }

    .apply-btn {
      background-color: $primary-color;
      border-color: $primary-color;
      min-width: 80px;
    }
  }
}
// 日期输入容器样式
.date-inputs-container {
  flex: 1;
}

.date-range-container {
  flex: 1;
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex-wrap: wrap;

  .date-separator {
    color: $gray;
    font-weight: $font-weight-medium;
    padding: 0 4px;
    white-space: nowrap;
  }

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: stretch;
    gap: $spacing-sm;

    .date-separator {
      text-align: center;
      padding: $spacing-xs 0;
    }
  }
}

.date-inputs-group {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  min-width: 200px;

  .date-label {
    font-size: $font-size-small;
    color: $gray;
    font-weight: $font-weight-medium;
    white-space: nowrap;
    min-width: 60px;
  }

  .date-inputs {
    display: flex;
    gap: $spacing-xs;

    .year-select {
      flex: 2;
      min-width: 80px;
    }

    .month-select,
    .day-select {
      flex: 1;
      min-width: 60px;
    }

    :deep(.el-select) {
      .el-input__wrapper {
        border-radius: $border-radius-md;
      }
    }
  }

  @media (max-width: $breakpoint-md) {
    min-width: auto;
    flex-direction: column;
    align-items: flex-start;

    .date-label {
      min-width: auto;
    }

    .date-inputs {
      gap: 4px;

      .year-select,
      .month-select,
      .day-select {
        min-width: 50px;
      }
    }
  }
}

.filter-sidebar :deep(.el-radio-group) {
  align-items: flex-start !important;
  display: flex !important;
  flex-direction: column !important;
}


</style>
