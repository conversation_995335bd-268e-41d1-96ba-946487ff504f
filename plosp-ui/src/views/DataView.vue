<template>
  <div class="data">
    <section class="content-section">
      <div class="container">
        <!-- 左侧接口导航 -->
        <el-row :gutter="30" class="data-content">
          <el-col :span="5" :xs="24">
            <div class="api-navigation">
              <div class="nav-header">
                <el-icon><Menu /></el-icon>
                <h2>接口导航</h2>
              </div>

              <div class="nav-list">
                <div
                  v-for="navItem in navItems"
                  :key="navItem.id"
                  class="nav-item"
                  :class="{ active: activeNavItem === navItem.id }"
                  @click="scrollToSection(navItem.id)"
                >
                  <div class="nav-item-bg"></div>
                  <div class="nav-item-content">
                    <span>{{ navItem.label }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="19" :xs="24">
            <div class="api-docs">
              <!-- 权限认证说明 -->
              <div id="authentication" class="api-doc-item">
                <div class="api-header">
                  <div class="api-title">
                    <img src="@/assets/images/authentication.svg" />
                    <h2>权限认证说明</h2>
                  </div>
                </div>

                <div class="api-content">
                  <p>所有 API 请求都需要在请求头（Header）中包含有效的 API 密钥，系统将通过该密钥进行身份验证与权限检验。</p>

                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />请求头格式示例：</h3>
                  <div class="code-block">
                    <pre><code class="language-http">Authorization: Bearer YOUR_API_KEY</code></pre>
                  </div>

                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />获取 API 密钥的步骤：</h3>
                  <div class="steps-list">
                    <div class="step-item">
                      <span class="step-number">1.</span>
                      <span><span class="register-link" @click="goToRegister">注册</span>账号并登录系统；</span>
                    </div>
                    <div class="step-item">
                      <span class="step-number">2.</span>
                      <span>进入个人中心页面；</span>
                    </div>
                    <div class="step-item">
                      <span class="step-number">3.</span>
                      <span>在【API密钥】模块中申请并生成您的专属 API 密钥。</span>
                    </div>
                  </div>
                  <img src="@/assets/images/img_1.png" style="width: 100%" alt="" />


                  <div class="warning-box">
                    <p>请妥善保管您的 API 密钥，避免泄露。一旦发现密钥需要更新，请立即在【API密钥】中进行重新生成。</p>
                  </div>

                  <!--                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />使用限制</h3>-->
                  <!--                  <div class="usage-limits">-->
                  <!--                    <div class="limit-item">-->
                  <!--                      <span class="limit-type">普通用户：</span>-->
                  <!--                      <span>每秒上限3次请求，不支持批量ID请求</span>-->
                  <!--                    </div>-->
                  <!--                    <div class="limit-item">-->
                  <!--                      <span class="limit-type">高级用户：</span>-->
                  <!--                      <span>每秒上限100次请求，支持批量ID请求</span>-->
                  <!--                    </div>-->
                  <!--                  </div>-->
                </div>
              </div>

              <!-- 通过ID获取文献题录等信息 -->
              <div id="literature-by-id" class="api-doc-item">
                <div class="api-header">
                  <div class="api-title">
                    <img src="@/assets/images/literature-download.svg" />
                    <h2>通过ID获取文献题录等信息</h2>
                  </div>
                </div>

                <div class="api-content">
                  <h3 class="api-section-title">
                    <img class="section-icon" src="@/assets/images/arrow.svg" />
                    请求地址
                  </h3>
                  <div class="request">
                    GET /api/article/findById
                  </div>
                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />请求参数</h3>
                  <div class="params-table">
                    <el-table :data="literatureByIdParams" border class="api-table">
                      <el-table-column prop="name" label="参数名" width="100" />
                      <el-table-column prop="type" label="类型" width="80" />
                      <el-table-column prop="required" label="必填" width="80" />
                      <el-table-column
                        prop="example"
                        label="示例值"
                        width="420"
                        show-overflow-tooltip
                      />
                      <el-table-column prop="description" label="说明" show-overflow-tooltip />
                    </el-table>
                  </div>

                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />fields支持字段列表</h3>
                  <div class="fields-table">
                    <el-table :data="fieldsSupport" border class="api-fields-table">
                      <el-table-column prop="field" label="字段名" width="300" />
                      <el-table-column prop="description" label="说明" show-overflow-tooltip />
                    </el-table>
                  </div>

                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />请求示例</h3>
                  <div class="code-block">
                    <pre><code id="request-example" class="language-http">GET /api/article/findById?id=27887644,PMC2206924,10.s0022172400006203&fields=journal,publisher,impact_factor,jcr_quartile,zky_sections,full_text

Header:
Authorization: Bearer YOUR_API_KEY</code></pre>
                  </div>

                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />接口响应结构说明</h3>
                  <div class="response-table">
                    <el-table :data="responseStructure" border class="api-response-table">
                      <el-table-column prop="field" label="字段名" width="160" />
                      <el-table-column prop="type" label="类型" width="120" />
                      <el-table-column prop="description" label="描述" show-overflow-tooltip />
                    </el-table>
                  </div>

                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />成功响应（200）</h3>
                  <div class="code-block expandable-code">
                    <div class="code-header">
                      <div class="code-actions" @click="toggleResponseExample">
                        {{ isResponseExampleExpanded ? '收起' : '展开' }}
                      </div>
                    </div>
                    <div class="code-content" :class="{ 'collapsed': !isResponseExampleExpanded }">
                      <pre><code id="response-example" class="language-json">{
  "code": 200, // 状态码，200 表示请求成功（不论是否查询成功）
  "summary": { // 查询结果概况
    "total": 3, // 总共查询的ID数量
    "success": 1, // 成功查询到的数量
    "error": 2 // 失败的数量
  },
  "data": [{
    "customId": "", // 用户自定义ID，如8000000000001
    "pmid": 27887644, // PMID
    "pmcId": "PMC5124288", // PMC ID
    "source": ["PubMed", "PMC"], // 文献的来源平台
    "doi": "10.1186/s12879-016-2026-9", // DOI
    "title": "Copenhagen comorbidity in HIV infection (COCOMO) study: a study protocol for", // 文章标题
    "author": ["Shipeng Hes", "Guoqiang Dongs", " Chunquan Sheng"], // 作者
    "abs": ["Andreas Ronit", "Judith Haissman", " Ditte Marie Kirkegaard-Klitbo", " Thomas"], // 摘要
    "keyword": ["Cardiovascular diseases", " Comorbidity", " HIV", " Inflammation", " Liver"], // 关键词
    "year": 2016,  // 发表年份
    "volume": "16", // 卷
    "issue": "1", // 期
    "page": "713", // 页码
    "publisherTitle": "BMC", // 出版社名称
    "publisherToc": "", // 出版社位置信息
    "journalTitle": "BMC infectious diseases", // 期刊名称
    "isoAbbreviation": "BMC Infect Dis", // 期刊ISO简称
    "jcrAbbreviation": "BMC Infect Dis", // 期刊JCR简称
    "pmcAbbreviation": "BMC Infect Dis", // 期刊PMC简称
    "journalIssnElectronic": "1471-2334", // 期刊 ISSN Electronic
    "journalIssnPrint": "1471-2334", // 期刊 ISSN Print
    "impactFactor": 3.7, // 当前期刊最新的影响因子
    "jcrQuartile": "Q3", // 当前期刊最新的JCR分区
    "historicalImpactFactors": [ // 当前期刊历年的影响因子
      {
        "year": 2020, // 年份
        "impact_factor": 3.09, // 影响因子
        "jcrQuartile": "Q1" // 期刊的JCR分区
      },
      {
        "year": 2021,
        "impact_factor": 3.667,
        "jcrQuartile": "Q3" // 期刊的JCR分区
      },
      {
        "year": 2022,
        "impact_factor": 3.7
      }
    ],
    "zkySections": { // 中科院分区
        "top": "否",
        "year": "2025",
        "largeCategory": "医学",
        "largeCategorySection": 3,
        "subclass1": "INFECTIOUS DISEASES 传染病学",
        "subclass1Section": 3
    }
  }],
  "errorItems": [ // 失败的ID列表
    {
      "id": "10.s0022172400006203", // URL参数中传的ID
      "errorMsg": "ID格式不对" // 返回没查到数据的错误原因
    },
    {
      "id": "PMC2206924",
      "errorMsg": "数据未找到"
    }
  ]
}</code></pre>
                    </div>
                  </div>
                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />错误响应示例</h3>
                  <div class="error-table">
                    <el-table :data="errorCodes" border class="api-error-table">
                      <el-table-column
                        prop="code"
                        label="状态码 (code)"
                        width="300"
                      />
                      <el-table-column prop="message" label="示例 msg" />
                    </el-table>
                  </div>

                  <div class="code-block">
                    <pre><code id="error-example" class="language-json">{
  "code": 401,
  "msg": "未授权或Token无效"
}</code></pre>
                  </div>
                </div>
              </div>
              <div id="literature-by-title" class="api-doc-item">
                <div class="api-header">
                  <div class="api-title">
                    <img src="@/assets/images/literature-download.svg" />
                    <h2>标题获取文献题录等信息</h2>
                  </div>
                </div>

                <div class="api-content">
                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />请求地址</h3>
                  <div class="request">
                    GET /api/article/findByTitle
                  </div>

                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />请求参数</h3>
                  <div class="params-table">
                    <el-table :data="titleSearchParams" border class="api-params-table">
                      <el-table-column prop="name" label="参数名" width="120" />
                      <el-table-column prop="type" label="类型" width="100" />
                      <el-table-column prop="required" label="必填" width="100" />
                      <el-table-column
                        prop="example"
                        label="示例值"
                        width="420"
                        show-overflow-tooltip
                      />
                      <el-table-column prop="description" label="说明" show-overflow-tooltip />
                    </el-table>
                  </div>
                </div>
                <p class="api-note">请求和响应示例参考上方接口说明</p>

              </div>
              <div id="literature-pdf" class="api-doc-item">
                <div class="api-header">
                  <div class="api-title">
                    <img src="@/assets/images/literature-download.svg" />
                    <h2>获取文献全文PDF</h2>
                  </div>
                </div>

                <div class="api-content">
                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />请求地址</h3>
                  <div class="request">
                    GET /api/article/downPdfById
                  </div>

                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />请求参数</h3>
                  <div class="params-table">
                    <el-table :data="pdfParams" border class="api-params-table">
                      <el-table-column prop="name" label="参数名" width="120" />
                      <el-table-column prop="type" label="类型" width="100" />
                      <el-table-column prop="required" label="必填" width="80" />
                      <el-table-column
                        prop="example"
                        label="示例值"
                        width="420"
                        show-overflow-tooltip
                      />
                      <el-table-column prop="description" label="说明" show-overflow-tooltip />
                    </el-table>
                  </div>

                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />请求示例</h3>
                  <div class="code-block">
                    <pre><code id="pdf-request-example" class="language-http">GET /api/article/findById?id=27887644

Header:
Authorization: Bearer YOUR_API_KEY</code></pre>
                  </div>

                  <p class="api-note">若成功，直接返回文件流，若失败，返回JSON格式字符串，报告错误原因。</p>

                  <div class="error-table">
                    <el-table :data="pdfErrorCodes" border class="api-error-table">
                      <el-table-column
                        prop="code"
                        label="状态码 (code)"
                        width="150"
                      />
                      <el-table-column prop="message" label="示例 msg" />
                    </el-table>
                  </div>

                  <h3 class="api-section-title"> <img class="section-icon" src="@/assets/images/arrow.svg" />失败示例：</h3>
                  <div class="code-block">
                    <pre><code id="pdf-error-example" class="language-json">{
  "code": 403,
  "msg": "该文章未找到PDF附件"
}</code></pre>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

      </div>
    </section>

  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, nextTick } from 'vue'
  import { Menu } from '@element-plus/icons-vue'
  import { useRouter } from 'vue-router'
  import hljs from 'highlight.js/lib/core'
  import 'highlight.js/styles/github.css'

  // 导入需要的语言
  import javascript from 'highlight.js/lib/languages/javascript'
  import bash from 'highlight.js/lib/languages/bash'
  import http from 'highlight.js/lib/languages/http'

  // 注册语言
  hljs.registerLanguage('javascript', javascript)
  hljs.registerLanguage('bash', bash)
  hljs.registerLanguage('http', http)

  const router = useRouter()

  // 导航数据
  const navItems = ref([
    { id: 'authentication', label: '权限认证说明' },
    { id: 'literature-by-id', label: '通过ID获取文献题录等信息' },
    { id: 'literature-by-title', label: '通过标题获取文献题录等信息' },
    { id: 'literature-pdf', label: '获取文献全文PDF' }
  ])

  // 当前激活的导航项
  const activeNavItem = ref('authentication')

  // 通过ID获取文献题录等信息 - 请求参数数据
  const literatureByIdParams = ref([
    {
      name: 'id',
      type: 'string',
      required: '是',
      example: '27887644 或 27887644,PMC2206924,10.1017/s0022172400006203',
      description: '文章ID，支持单个ID、PMID、PMCID、DOI，多个数值之间使用，分隔'
    },
    {
      name: 'db',
      type: 'string',
      required: '否',
      example: 'PubMed / PMC / bioRxiv / medRxiv / Custom',
      description: '文献来源数据库'
    },
    {
      name: 'fields',
      type: 'string',
      required: '否',
      example: 'journal,publisher,impact_factor,jcr_quartile,zky_sections,full_text',
      description: '可选的返回字段列表，默认无需指定，详细见下表'
    }
  ])

  // fields支持字段列表数据
  const fieldsSupport = ref([
    {
      field: 'journal',
      description: '期刊名称，含期刊影响因子、ISSN、journalAbbr'
    },
    {
      field: 'publisher',
      description: '出版社，含期刊期刊数据库、publisherTitle、publisherToc'
    },
    {
      field: 'impact_factor',
      description: '得到期刊数据库，当前影响因子 及 历年影响因子（historical_impact_factors）'
    },
    {
      field: 'jcr_quartile',
      description: '得到期刊数据库，JCR分区'
    },
    {
      field: 'zky_sections',
      description: '得到期刊数据库，中科院分区'
    }
  ])

  // 接口响应结构说明数据
  const responseStructure = ref([
    {
      field: 'code',
      type: 'int',
      description: '状态码，200 表示请求成功'
    },
    {
      field: 'msg',
      type: 'string',
      description: '请求处理的总体描述信息'
    },
    {
      field: 'summary',
      type: 'object',
      description: '当前批量查询的结果概况'
    },
    {
      field: 'data',
      type: 'object[]',
      description: '成功查询到的数据列表'
    },
    {
      field: 'errorItems',
      type: 'object[]',
      description: '查询失败的 ID 和对应的错误信息'
    }
  ])

  // 错误状态码数据
  const errorCodes = ref([
    {
      code: 0,
      message: ''
    },
    {
      code: 400,
      message: '参数非法'
    },
    {
      code: 401,
      message: '未授权或Token无效'
    },
    {
      code: 500,
      message: '服务内部错误'
    }
  ])

  // 标题搜索参数数据
  const titleSearchParams = ref([
    {
      name: 'title',
      type: 'string',
      required: true,
      example: 'Copenhagen comorbidity in HIV infection (COCOMO) study: a study protocol for a longitudinal, non-interventional assessment of non-AIDS comorbidity in HIV infection in Denmark.',
      description: '文献标题（精确查询），不支持批量查询'
    },
    {
      name: 'db',
      type: 'string',
      required: false,
      example: 'PubMed / PMC / bioRxiv / medRxiv / Custom',
      description: '文献来源数据库'
    },
    {
      name: 'fields',
      type: 'string',
      required: false,
      example: 'journal,publisher,impact_factor,jcr_quartile,zky_sections,full_text',
      description: '可选的返回字段列表，默认无需定，详细见下表'
    }
  ])

  // PDF错误状态码数据
  const pdfErrorCodes = ref([
    {
      code: 400,
      message: '参数非法'
    },
    {
      code: 401,
      message: '未授权或Token无效'
    },
    {
      code: 402,
      message: '该文章在数据库中不存在'
    },
    {
      code: 403,
      message: '该文章未找到PDF附件'
    },
    {
      code: 500,
      message: '服务内部错误'
    }
  ])

  // 3. 获取文献全文PDF参数数据
  const pdfParams = ref([
    {
      name: 'id',
      type: 'string',
      required: '是',
      example: '27887644或PMC2206924或10.1017/s0022172400006203',
      description: '文献ID，支持PMID、PMCID、DOI，不支持多个id'
    }
  ])

  // 代码高亮
  const highlightCode = () => {
    nextTick(() => {
      document.querySelectorAll('pre code').forEach(block => {
        hljs.highlightElement(block)
      })
    })
  }

  // 跳转到注册页面
  const goToRegister = () => {
    router.push('/register')
  }



  // 滚动到指定区域
  const scrollToSection = sectionId => {
    const element = document.getElementById(sectionId)

    if (element) {
      // 计算元素位置，考虑固定导航的高度
      const headerOffset = 120 // 根据固定导航的位置调整
      const elementPosition = element.offsetTop
      const offsetPosition = elementPosition - headerOffset

      // 平滑滚动到目标位置
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      })

      // 更新激活状态
      activeNavItem.value = sectionId
    }
  }

  // 监听页面滚动事件，更新激活状态
  const handleScroll = () => {
    const scrollPosition = window.scrollY + 200 // 偏移量，提前激活

    // 找到当前可见的区域
    for (let i = navItems.value.length - 1; i >= 0; i--) {
      const element = document.getElementById(navItems.value[i].id)
      if (element && element.offsetTop <= scrollPosition) {
        activeNavItem.value = navItems.value[i].id
        break
      }
    }
  }

  const throttle = (func, limit) => {
    let inThrottle
    return function() {
      const args = arguments
      const context = this
      if (!inThrottle) {
        func.apply(context, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  const throttledHandleScroll = throttle(handleScroll, 100)

  // 响应示例展开/收起状态
  const isResponseExampleExpanded = ref(false)

  // 切换响应示例展开/收起
  const toggleResponseExample = () => {
    isResponseExampleExpanded.value = !isResponseExampleExpanded.value
  }

  // 复制代码功能
  const copyCode = elementId => {
    const element = document.getElementById(elementId)
    if (element) {
      const text = element.textContent
      navigator.clipboard.writeText(text).then(() => {
        ElMessage.success('代码已复制到剪贴板')
      }).catch(() => {
        ElMessage.error('复制失败')
      })
    }
  }

  onMounted(() => {
    window.addEventListener('scroll', throttledHandleScroll)
    highlightCode()
  })

  onUnmounted(() => {
    window.removeEventListener('scroll', throttledHandleScroll)
  })
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.data {
  background-color: #F5F5F5;
  min-height: calc(100vh - 92px); // 减去头部高度
  padding: $spacing-xxl 0;
}

.page-title {
  font-size: $font-size-xxlarge;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin-bottom: $spacing-xl;
}
// 左侧接口导航
.api-navigation {
  width: 295px;
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow;
  padding: $spacing-lg;
  position: fixed; // 固定定位
  top: 129px; // 距离顶部的距离
}

.nav-header {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  margin-bottom: $spacing-lg;

  h2 {
    font-size: $font-size-medium;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;
  }

  .nav-icon {
    font-size: $font-size-large;
    color: $primary-color;
  }
}

.nav-list {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.nav-item {
  position: relative;
  height: 50px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    .nav-item-bg {
      background-color: rgba($primary-color, 0.1);
    }
  }

  &.active {
    .nav-item-bg {
      background-color: $primary-color;
    }

    .nav-item-content {
      color: $white;
    }

    &:hover {
      .nav-item-bg {
        background-color: $primary-color;
      }
    }
  }
}

.nav-item-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: $border-radius-md;
  z-index: 1;
  transition: background-color 0.3s ease;
}

.nav-item-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  align-items: center;
  padding-left: $spacing-lg;
  font-size: $font-size-small;
  font-weight: $font-weight-regular;
  transition: color 0.3s ease;
}

// 右侧API文档


// 描述区域样式
.description-section {
  margin-bottom: $spacing-lg;
  padding: $spacing-md;
  background: #F9FAFB;
  border-radius: $border-radius-lg;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;
 color: #4B5563;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
  }

  .info-icon {
    font-size: 20px;
    color: #4FC3F7;
    background: rgba(79, 195, 247, 0.1);
    padding: 6px;
    border-radius: 50%;
  }

  .description-title {
    font-size: $font-size-small;
  }

}
.form-section{
  display: flex;
  padding: 20px 0;
  align-items: center;
  .form-label{
    color: #333333;
    width: 60px;
  }
}


@media (max-width: $breakpoint-md) {
  .page-content {
    gap: $spacing-md;
  }



  .description-section {
    padding: $spacing-md;

    .description-header {
      flex-direction: column;
      text-align: center;
      gap: $spacing-xs;

      .description-title {
        font-size: $font-size-small;
      }
    }

    .description-content {
      grid-template-columns: 1fr;
      gap: $spacing-sm;
    }

    .description-item {
      padding: $spacing-xs;

      .item-text {
        font-size: $font-size-small;
      }
    }
  }


}


.api-docs {
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.api-doc-item {
  background-color: $white;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow;
  padding: $spacing-lg;
  border-top: 1px solid #F3F4F6;
  flex-shrink: 0; // 防止在flex容器中被压缩
  scroll-margin-top: 20px; // 为滚动定位预留空间
}

.api-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.api-title {
  display: flex;
  align-items: center;
  gap: $spacing-xs;

  h2 {
    font-size: $font-size-medium;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin: 0;
  }

  .api-icon {
    font-size: $font-size-large;
    color: $primary-color;
  }
  img{
    width: 22px;
  }
}

.api-method {
  background-color: #DCFCE7;
  padding: 2px 16px;
  border-radius: 9999px;

  span {
    color: #166534;
    font-size: $font-size-small;
    font-weight: $font-weight-regular;
  }
}

.api-endpoint {
  background-color: #F9FAFB;
  padding: $spacing-xs  $spacing-md;
  border-radius: $border-radius-md;
  margin-bottom: $spacing-lg;

  code {
    font-family: monospace;
    font-size: $font-size-small;
    color: #1F2937;
  }
}

.api-section-title {
  font-size: $font-size-small;
  font-weight: $font-weight-medium;
  color: #333333;
  margin-bottom: $spacing-md;

  .section-icon {
    margin-right: 8px;
    width: 12px;
  }
}
.request{
  font-size: $font-size-small;
  color: #2563EB;
  margin-bottom: $spacing-sm;
  line-height: 1.6;
  background: #F9FAFB;
  border-radius: 8px;
  padding: 6px 10px;
}

.api-params {
  margin-bottom: $spacing-lg;
}

.param-row, .limit-row {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-xs;
  color: #4B5563;
  font-size: $font-size-small;
  img{
    margin-right: .5rem;
    width: 12px;
  }
}

.param-icon, .limit-icon {
  width: 16px;
  height: 16px;
  margin-right: $spacing-xs;
  color: #333333;
}

.param-name {
  font-size: $font-size-small;
  font-weight: $font-weight-regular;
  color: #4B5563;
  margin-right: 4px;
}

.param-desc {
  font-size: $font-size-small;
  font-weight: $font-weight-regular;
  color: #4B5563;
}

.api-example {
  background-color: #F9FAFB;
  padding:$spacing-xs $spacing-md;
  border-radius: $border-radius-md;

  .example-code {
    font-family: monospace;
    font-size: $font-size-small;
    color: #2563EB;
  }
}

.api-content {
  p {
    font-size: $font-size-small;
    color: #4B5563;
    margin-bottom: $spacing-sm;
    line-height: 1.6;
  }

  .api-steps {
    font-size: $font-size-small;
    color: #4B5563;
    line-height: 1.8;
  }
}

// 代码块样式
.code-block {
  margin: $spacing-md 0;
  border-radius: $border-radius-md;
  overflow: hidden;
  border: 1px solid #E5E7EB;

  .code-header {
    background: linear-gradient(135deg, #F8FAFC, #F1F5F9);
    padding: $spacing-xs $spacing-md;
    border-bottom: 1px solid #E5E7EB;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 16px;
    .code-actions{
      cursor: pointer;
    }
  }

  pre {
    margin: 0;
    padding: $spacing-md;
    background-color: #F9FAFB;
    overflow-x: auto;

    code {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: $font-size-small;
      line-height: 1.5;
      color: #1F2937;
    }
  }
}

// 步骤列表样式
.steps-list {
  margin: $spacing-md 0;

  .step-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: $spacing-sm;

    .step-number {
      margin-right: $spacing-xs;
    }

    span:last-child {
      color: #4B5563;
      font-size: $font-size-small;
      line-height: 1.6;
    }

    .register-link {
      font-weight: $font-weight-bold;
      color: $primary-color;
      cursor: pointer;
      text-decoration: none;
      transition: color 0.3s ease;

      &:hover {
        color: darken($primary-color, 10%);
        text-decoration: underline;
      }
    }
  }
}



// 警告框样式
.warning-box {
  background: #FFFAEC;
  border: 1px solid #FDE047;
  border-radius: $border-radius-md;
  padding: $spacing-md;
  margin: $spacing-lg 0;

  p {
    margin: 0;
    color: #92400E;
    font-size: $font-size-small;
    line-height: 1.6;
  }
}

// 使用限制样式
.usage-limits {
  margin-top: $spacing-md;

  .limit-item {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-sm;
    font-size: $font-size-small;
    color: #4B5563;
  }
}

// 响应式样式
@media (max-width: $breakpoint-lg) {
  .data-content {
    flex-direction: column;
    gap: 20px;
  }

  .api-navigation {
    position: relative; // 移动端取消固定定位
    top: auto;
    transform: none;
    width: 100%;

  }

  .api-docs {
    padding-left: 0; // 移动端移除左侧间距
    //max-width: 100%;
  }
}

@media (max-width: $breakpoint-md) {
  .page-title {
    font-size: $font-size-xlarge;
  }

  .api-title {
    h2 {
      font-size: $font-size-large;
    }
  }

  .api-endpoint {
    code {
      font-size: $font-size-small;
    }
  }

  .param-name, .param-desc {
    font-size: $font-size-small;
  }

  .example-code {
    font-size: $font-size-small;
  }
}

@media (max-width: $breakpoint-sm) {
  .data {
    padding: $spacing-lg 0;
  }

  .container {
    padding: 0 $spacing-sm;
  }

  .api-header {
    gap: $spacing-sm;
  }

  .nav-item-content {
    font-size: $font-size-small;
  }

  .api-content {
    p, .api-steps {
      font-size: $font-size-small;
    }
  }
}

// API 文档表格样式
.params-table,
.fields-table,
.response-table,
.error-table {
  margin: $spacing-md 0;

  .api-params-table,
  .api-fields-table,
  .api-response-table,
  .api-error-table {
    :deep(.el-table__header) {
      background: linear-gradient(135deg, #F8FAFC, #F1F5F9);

      th {
        background: transparent !important;
        color: #666666;
        font-weight: $font-weight-medium;
        font-size: $font-size-small;
        border-bottom: 2px solid #E5E7EB;
      }
    }

    :deep(.el-table__body) {
      tr {
        transition: all 0.2s ease;

        &:hover {
          background: rgba(248, 250, 252, 0.8) !important;
        }

        td {
          border-bottom: 1px solid #F3F4F6;
          padding: $spacing-sm;
          font-size: $font-size-small;
          color: #4B5563;
          line-height: 1.5;

          .required-yes {
            color: #EF4444;
            font-weight: $font-weight-medium;
          }

          .required-no {
            color: #6B7280;
          }
        }
      }
    }
  }
}

// API 说明文本样式
.api-note {
  margin: $spacing-md 0;
  padding: $spacing-sm $spacing-md;
  background: #F8F9FA;
  border-left: 4px solid #6C757D;
  color: #495057;
  font-size: 16px;
  line-height: 1.5;
}

// 可展开代码块样式
.expandable-code {
  .code-content {
    max-height: 600px;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out;
    overflow-y:scroll;

    &.collapsed {
      max-height: 200px;
    }
  }

  .code-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40px;
    background: linear-gradient(transparent, rgba(248, 249, 250, 0.9));
    pointer-events: none;
    transition: opacity 0.3s ease-in-out;
  }

  .code-content:not(.collapsed)::after {
    opacity: 0;
  }
}
</style>
