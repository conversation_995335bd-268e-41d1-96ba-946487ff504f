import request from '@/utils/request'

/**
 * 获取文章详情
 * @param {string} id
 */
export function getArticleDetail(id) {
  return request({
    url: `/detail/${id}`,
    method: 'get'
  })
}

/**
 * 文献解析
 * @param {number} docId - 文档ID
 */
export function interpretArticle(docId) {
  return request({
    url: `/detail/interpret/${docId}`,
    timeout: 300000,
    method: 'post'
  })
}

/**
 * 获取文章引用信息
 * @param {number} docId - 文档ID
 * @param {number} limit - 限制数量，默认5
 */
export function getReferences(docId, limit = 5) {
  return request({
    url: `/detail/${docId}/references`,
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取文章所有引用信息
 * @param {number} docId - 文档ID
 */
export function getAllReferences(docId) {
  return request({
    url: `/detail/${docId}/references`,
    timeout: 500000,
    method: 'get'
  })
}

/**
 * 获取可用搜索字段
 */
export function getSearchFields() {
  return request({
    url: '/es/search/selectItem',
    method: 'get'
  })
}

/**
 * 统一搜索接口
 * @param {Object} searchData - 搜索参数
 */
export function searchArticles(searchData) {
  return request({
    url: '/es/search/list',
    method: 'post',
    data: searchData
  })
}

/**
 * MeSH词汇自动完成
 * @param {string} keyword - 关键词
 */
export function getMeshAutoComplete(keyword) {
  return request({
    url: '/es/search/meshAutoComplete',
    method: 'get',
    params: { keyword }
  })
}

/**
 * 全量更新统一数据处理接口
 */
export function dataProcess(data) {
  return request({
    url: '/es/dataProcess',
    method: 'post',
    data
  })
}

/**
 * 查看任务状态
 */
export function getTaskStatus(params) {
  return request({
    url: '/es/taskStatus',
    method: 'get',
    params
  })
}
