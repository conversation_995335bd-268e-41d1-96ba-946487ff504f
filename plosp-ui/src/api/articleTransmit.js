import request from '@/utils/request'

/**
 * 提交文献传递申请
 * @param {Object} data - 文献传递申请数据
 * @param {number} data.articleId - 文章ID
 * @param {string} data.name - 任务名称
 * @param {string} data.description - 任务描述
 * @param {string} data.captchaCode - 验证码
 * @param {string} data.uuid - 验证码UUID
 */
export function submitArticleTransmit(data) {
  return request({
    url: '/transmit/articleTransmit',
    method: 'post',
    data,
    timeout: 95000
  })
}

/**
 * 获取我的文献传递列表
 * @param {Object} params - 查询参数
 */
export function getTransmitList(params) {
  return request({
    url: '/transmit/transmitList',
    method: 'get',
    params
  })
}
