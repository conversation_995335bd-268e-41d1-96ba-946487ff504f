import request from '@/utils/request'

/**
 * 获取门户信息
 */
export function getPortalInfo() {
  return request({
    url: '/api/public/info',
    method: 'get'
  })
}

/**
 * 健康检查
 */
export function healthCheck() {
  return request({
    url: '/api/public/health',
    method: 'get'
  })
}

/**
 * 获取系统状态
 */
export function getSystemStatus() {
  return request({
    url: '/api/public/status',
    method: 'get'
  })
}

/**
 * 获取支持的用户类型
 */
export function getUserTypes() {
  return request({
    url: '/api/public/user-types',
    method: 'get'
  })
}

/**
 * 获取支持的国家/地区列表
 */
export function getCountries() {
  return request({
    url: '/api/public/countries',
    method: 'get'
  })
}

/**
 * 获取应用配置
 */
export function getAppConfig() {
  return request({
    url: '/api/public/config',
    method: 'get'
  })
}
