import request from '@/utils/request'

/**
 * 获取用户API密钥列表
 */
export function getUserApiKeys() {
  return request({
    url: '/api/user/apikeys',
    method: 'get'
  })
}

/**
 * 创建API密钥
 * @param {string} keyName - 密钥名称
 */
export function createApiKey(keyName) {
  return request({
    url: '/api/user/apikeys',
    method: 'post',
    params: { keyName }
  })
}

/**
 * 删除API密钥
 * @param {number} keyId - 密钥ID
 */
export function deleteApiKey(keyId) {
  return request({
    url: `/api/user/apikeys/${keyId}`,
    method: 'delete'
  })
}

/**
 * 修改API密钥名称
 * @param {number} keyId - 密钥ID
 * @param {string} keyName - 新密钥名称
 */
export function updateApiKeyName(keyId, keyName) {
  return request({
    url: `/api/user/apikeys/${keyId}`,
    method: 'put',
    params: { keyName }
  })
}

/**
 * 获取完整API密钥
 * @param {number} keyId - 密钥ID
 */
export function getFullApiKey(keyId) {
  return request({
    url: `/api/user/apikeys/${keyId}/full`,
    method: 'get'
  })
}

// 获取当前用户的浏览历史
export function getUserDocHistoryList(query) {
  return request({
    url: '/api/user/history/list',
    method: 'get',
    params: query
  })
}

// 新增浏览历史
export function addUserDocHistory(docId) {
  return request({
    url: '/api/user/history/add',
    method: 'post',
    params: {
      docId
    }
  })
}

// 删除浏览历史
export function deleteUserDocHistory(ids) {
  return request({
    url: `/api/user/history/delete/${ids}`,
    method: 'delete'
  })
}
