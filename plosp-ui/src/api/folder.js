import request from '@/utils/request'

/**
 * 获取用户的收藏夹列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码，默认1
 * @param {number} params.pageSize - 每页大小，默认10
 */
export function getFavoriteFolders(params) {
  return request({
    url: '/api/user/folder/list',
    method: 'get',
    params: {
      ...params
    }
  })
}

/**
 * 创建收藏夹
 * @param {Object} data - 创建参数
 * @param {string} data.folderName - 文件夹名称
 */
export function createFavoriteFolder(data) {
  return request({
    url: '/api/user/folder',
    method: 'post',
    params: data
  })
}

/**
 * 更新收藏夹名称
 * @param {Object} data - 更新参数
 * @param {number} data.id - 文件夹ID
 * @param {string} data.folderName - 新的文件夹名称
 */
export function updateFavoriteFolder(data) {
  return request({
    url: '/api/user/folder/update',
    method: 'post',
    params: data
  })
}

/**
 * 删除收藏夹
 * @param {number} id - 文件夹ID
 */
export function deleteFavoriteFolder(id) {
  return request({
    url: `/api/user/folder/delete/${id}`,
    method: 'delete'
  })
}

/**
 * 收藏文献到指定文件夹
 * @param {Object} favoriteDocDto - 收藏文档数据传输对象
 * @param {number} favoriteDocDto.folderId - 文件夹ID
 * @param {number} favoriteDocDto.docId - 文档ID
 */
export function addToFavorites(favoriteDocDto) {
  return request({
    url: '/api/user/doc/add',
    method: 'post',
    data: favoriteDocDto
  })
}

/**
 * 取消收藏文献
 * @param {number[]} ids - 文档ID数组
 */
export function removeFromFavorites(ids) {
  return request({
    url: `/api/user/doc/delete/${ids.join(',')}`,
    method: 'delete'
  })
}

/**
 * 获取收藏的文献列表
 * @param {Object} params - 查询参数
 * @param {number} params.folderId - 文件夹ID
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页大小
 */
export function getFavoriteDocuments(params) {
  return request({
    url: '/api/user/doc/list',
    method: 'get',
    params
  })
}

/**
 * 判断当前文献是否已被收藏
 * @param {number} data.docId - 文献id
 * @returns
 */
export function isCollect(data) {
  return request({
    url: `/api/user/doc/isCollect?docId=${data.docId}`,
    method: 'get'
  })
}
