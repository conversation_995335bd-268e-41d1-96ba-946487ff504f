import request from '@/utils/request'

/**
 * 根据字典类型获取字典数据
 * @param {string} dictType - 字典类型
 */
export function getDictData(dictType) {
  return request({
    url: `/api/dict/data/type/${dictType}`,
    method: 'get'
  })
}

/**
 * 批量获取多个字典类型的数据
 * @param {Array} dictTypes - 字典类型数组
 */
export function getBatchDictData(dictTypes) {
  return request({
    url: '/api/dict/data/batch',
    method: 'post',
    data: dictTypes
  })
}

/**
 * 获取国家字典数据
 */
export function getCountries() {
  return request({
    url: '/api/dict/countries',
    method: 'get'
  })
}

/**
 * 获取机构字典数据
 */
export function getOrganizations() {
  return request({
    url: '/api/dict/organizations',
    method: 'get'
  })
}
