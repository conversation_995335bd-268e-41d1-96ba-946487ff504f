<template>
  <div class="article-item">
    <div class="article-checkbox">
      <input
        :id="`article-${article.id}`"
        type="checkbox"
        :checked="isSelected"
        @change="handleSelectionChange"
      />
    </div>
    <div class="article-content">
      <div class="article-header">
        <div v-if="getSourceList(article).length > 0" class="source-badges">
          <div
            v-for="source in getSourceList(article)"
            :key="source"
            class="source-badge"
            :style="source.toLowerCase() !== 'pubmed' ? getSourceStyle(source) : null"
          >{{ source }}</div>
        </div>
        <router-link 
          :to="`/detail/${ article.id}`"
          target="_blank"
          rel="noopener noreferrer"
        >
          <h3 class="article-title" v-html="article.title">
          </h3>
        </router-link>
      </div>
      <div class="article-authors">{{ formatAuthors(article) }}</div>
      <div class="article-meta">
        <div class="meta-group">
          <div class="meta-item journal">
            <!--            <el-icon></el-icon>-->
            <span>{{ formatJournalInfo(article) }}</span>
          </div>
        </div>
        <div class="meta-group ml-1">
          <div v-if="article.pmid" class="meta-item">
            <span>PMID: </span>
            <a :href="`https://pubmed.ncbi.nlm.nih.gov/${article.pmid}`" target="_blank" class="meta-link">{{ article.pmid }}</a>
          </div>
        </div>
        <div class="meta-group ml-1">
          <div v-if="article.doi" class="meta-item">
            <span>DOI: </span>
            <a :href="`https://doi.org/${article.doi}`" target="_blank" class="meta-link">{{ article.doi }}</a>
          </div>
        </div>

        <div class="meta-badges">
          <div v-if="article.impactFactor" class="meta-badge impact-factor">IF: {{ article.impactFactor }}</div>
          <div v-if="article.jcr" class="meta-badge jcr">JCR: {{ article.jcr }}</div>
          <div v-if="article.hasPdf" class="meta-badge pdf">PDF</div>
        </div>
      </div>
      <!-- <div v-if="getAbstract(article)" class="article-abstract" v-html="getAbstract(article)">
      </div> -->
      <el-popover
        v-if="getAbstract(article)"
        placement="top-start"
        width="1000"
        trigger="hover"
        popper-class="article-abstract-popover"
      >
        <div class="popover-content" v-html="getAbstract(article)"></div>
        <template #reference>
          <div class="article-abstract" v-html="getAbstract(article)"></div>
        </template>
      </el-popover>
      <div v-if="getPubTypes(article).length > 0" class="article-pubtypes">
        <div v-for="pubtype in getPubTypes(article)" :key="pubtype" class="pubtype-badge">
          {{ pubtype }}
        </div>
      </div>
      <div class="article-keywords">
        <div v-for="(keyword, index) in article.keywords" :key="index" class="keyword-badge">
          {{ keyword }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    article: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  })

  const emit = defineEmits(['selection-change'])

  const handleSelectionChange = event => {
    emit('selection-change', {
      article: props.article,
      selected: event.target.checked
    })
  }

  // 格式化期刊信息：动态格式，根据有无数据决定符号
  const formatJournalInfo = article => {

    // 获取各个字段
    const journalName = article.journalName
    const year = article.publishedYear || article.year || (article.publishedDate ? new Date(article.publishedDate).getFullYear() : '')
    const volume = article.volume
    const issue = article.issue
    const page = article.page

    let journalInfo = ''

    // 期刊名称
    if (journalName) {
      journalInfo += journalName
    }

    // 年份 - 如果有年份，前面加点号
    if (year) {
      if (journalInfo) journalInfo += '. '
      journalInfo += year
    }

    // 卷号 - 如果有卷号，前面加分号
    if (volume) {
      if (journalInfo) journalInfo += ';'
      journalInfo += volume
    }

    // 期号 - 如果有期号，用括号包围
    if (issue) {
      journalInfo += `(${issue})`
    }

    // 页码 - 如果有页码，前面加冒号
    if (page) {
      if (journalInfo) journalInfo += ':'
      journalInfo += page
    }

    // 最后加点号（如果有任何内容）
    if (journalInfo) {
      journalInfo += '.'
    }

    return journalInfo || '期刊信息不完整'
  }

  // 获取来源列表
  const getSourceList = article => {
    const sources = []

    // 根据文章的ID字段判断来源
    if (article.pmid) {
      sources.push('PubMed')
    }

    if (article.pmcId) {
      sources.push('PMC')
    }

    // 检查DOI是否来自预印本服务器
    if (article.doi) {
      const doi = article.doi.toLowerCase()
      if (doi.includes('biorxiv')) {
        sources.push('bioRxiv')
      } else if (doi.includes('medrxiv')) {
        sources.push('medRxiv')
      }
    }

    // 如果没有找到任何来源，检查原始的source字段（向后兼容）
    if (sources.length === 0 && article.source) {
      if (Array.isArray(article.source)) {
        return article.source.filter(source => source && source.trim())
      } else {
        return article.source.trim() ? [article.source.trim()] : []
      }
    }

    return sources
  }

  // 获取来源对应的内联样式
  const getSourceStyle = source => {
    const sourceLower = source?.toLowerCase() || ''

    if (sourceLower === 'pubmed') {
      return {}
    } else if (sourceLower === 'pmc') {
      return { backgroundColor: '#e3f2fd', color: '#1565c0' }
    } else if (sourceLower === 'biorxiv') {
      return { backgroundColor: '#fff3e0', color: '#ef6c00' }
    } else if (sourceLower === 'medrxiv') {
      return { backgroundColor: '#fce4ec', color: '#c2185b' }
    } else if (sourceLower === 'custom') {
      return { backgroundColor: '#f3e5f5', color: '#7b1fa2' }
    } else {
      return { backgroundColor: '#f5f5f5', color: '#616161' }
    }
  }

  // 格式化作者信息
  const formatAuthors = article => {
    // 优先使用author字段（数组），其次使用authors字段（可能是字符串）
    const authors = article.author || article.authors

    if (!authors) {
      return ''
    }

    // 如果是数组，用逗号和空格连接
    if (Array.isArray(authors)) {
      return authors.filter(author => author && author.trim()).join(', ')
    }

    // 如果是字符串，直接返回
    return authors.toString()
  }

  // 获取摘要内容
  const getAbstract = article => {
    // 优先使用articleAbstract字段，其次使用abstract字段
    return article.articleAbstract || article.abstract || ''
  }

  // 获取文献类型列表
  const getPubTypes = article => {
    // pubType字段可能是数组或字符串
    const pubType = article.pubType

    if (!pubType) {
      return []
    }

    if (Array.isArray(pubType)) {
      // 如果是数组，过滤掉空值
      return pubType.filter(type => type && type.trim())
    } else {
      // 如果是字符串，可能包含逗号分隔的多个类型
      const typeStr = pubType.toString().trim()
      if (!typeStr) {
        return []
      }

      // 按逗号分割，并过滤掉空值
      return typeStr.split(',')
        .map(type => type.trim())
        .filter(type => type)
    }
  }
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables";

.article-item {
  display: flex;
  gap: $spacing-md;
  border-bottom: 1px solid #ECECEC;
  padding: $spacing-md 0;

  @media (max-width: $breakpoint-md) {
    gap: $spacing-sm;
    padding: $spacing-sm 0;
    flex-direction: column;
  }

  &:last-child {
    border-bottom: none;
  }
}

.article-content {
  flex: 1;
}

.article-header {
  display: flex;
  gap: $spacing-xs;
  align-items: center;
  flex-wrap: nowrap;

  > a {
    flex: 1;
    min-width: 0; // 允许flex item收缩
  }

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;

    > a {
      flex: none;
    }
  }
}

.article-source {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.source-badges {
  display: flex;
  gap: $spacing-xs;
  flex-wrap: nowrap;
  align-items: center;
}

.source-badge {
  background-color: #DCFCE7;
  color: #166534;
  font-size: $font-size-small;
  padding: 4px 12px;
  border-radius: 9999px;
  font-weight: 500;
  white-space: nowrap;
}

.article-title {
  font-size: $font-size-large;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin: 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;

  // HTML标签样式
  italic {
    font-style: italic;
  }

  @media (max-width: $breakpoint-md) {
    font-size: $font-size-medium;
    line-height: 1.4;
    -webkit-line-clamp: 2;
  }
}

.article-authors {
  font-size: $font-size-small;
  color: $gray;
  margin: $spacing-xs 0;
}

.article-meta {
  display: flex;
  gap: $spacing-xs;
  margin-bottom: $spacing-xs;
  align-items: center;
  flex-wrap: wrap;

  @media (max-width: $breakpoint-md) {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }
}

.meta-group {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-small;
  color: $gray;
}

.meta-icon {
  width: 18px;
  height: 18px;
  background-color: $gray;
  border-radius: 50%;
}

.meta-link {
  color: $primary-color;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

.meta-badges {
  display: flex;
  gap: $spacing-sm;
}

.meta-badge {
  font-size: 14px;
  padding: 2px 8px;
  border-radius: 12px;
}

.impact-factor {
  background-color: #EFF6FF;
  color: #1D4ED8;
}

.jcr {
  background-color: #EEF2FF;
  color: #4338CA;
}

.pdf {
  background-color: #FFFBEB;
  color: #B45309;
}

.article-abstract {
  font-size:17px;
  color: #374151;
  line-height: 1.3;
  margin: $spacing-xs 0;
  text-align: justify;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  width: 100%;
  padding-right: $spacing-sm;

  // HTML标签样式
  italic {
    font-style: italic;
  }

  @media (max-width: $breakpoint-md) {
    font-size: $font-size-small;
    line-height: 1.4;
    -webkit-line-clamp: 3;
    padding-right: 0;
    text-align: left;
  }
}

.article-pubtypes {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
  margin-top: $spacing-sm;
}

.pubtype-badge {
  background-color: #f3f4f6;
  color: #6b7280;
  font-size: $font-size-small;
  padding: 4px 12px;
  border-radius: 9999px;
}

.article-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-xs;
  margin-top: $spacing-sm;
}

.keyword-badge {
  background-color: #F2F7FB;
  color: $gray;
  font-size: $font-size-small;
  padding: 4px 12px;
  border-radius: 9999px;
}

@media (max-width: $breakpoint-md) {
  .article-checkbox {
    order: -1;
  }

  .meta-group {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-xs;

    &.ml-1 {
      margin-left: 0!important;
    }
  }

  .meta-badges {
    flex-wrap: wrap;
    margin-top: $spacing-xs;
  }

  .meta-item {
    font-size: 14px;
  }

  .source-badge {
    font-size: 12px;
    padding: 2px 8px;
  }



  .keyword-badge {
    font-size: 12px;
    padding: 2px 8px;
  }
}

/* 摘要预览：一行或两行截断 */
.article-abstract.snippet {
  font-size: 14px;
  color: $gray; /* 参照变量，或直接 #6b7280 等 */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 显示2行预览，按需改为1或3 */
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  cursor: pointer; /* 表示可点击/悬停 */
  white-space: normal;
}

/* popover 内完整摘要样式 */
.article-abstract-popover .popover-content {
  max-height: 60vh;
  overflow: auto;
  font-size: 14px;
  color: #333;
  line-height: 1.6;
  padding: 8px 0;
}

/* 可选：在小屏幕上扩大 popover 宽度或改为全屏 dialog（可媒体查询处理） */
@media (max-width: 768px) {
  .article-abstract-popover {
    width: auto !important;
    max-width: 95vw;
  }
}
</style>
