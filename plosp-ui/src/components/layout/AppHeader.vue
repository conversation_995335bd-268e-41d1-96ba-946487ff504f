<template>
  <header class="header">
    <div class="container header-container">
      <div class="logo">
        <router-link to="/">
          <img src="@/assets/images/logo.png" alt="PLOSP Logo" />
        </router-link>
      </div>

      <nav class="nav-desktop">
        <ul class="nav-list">
          <li v-for="(item, index) in navItems" :key="index" class="nav-item">
            <router-link :to="item.path" class="nav-link" :class="{ active: isActive(item.path) }">
              <img :src="item.icon" :alt="item.name" class="nav-icon" />
              <span>{{ item.name }}</span>
            </router-link>
          </li>
        </ul>
      </nav>

      <!-- 认证区域 -->
      <div class="auth-section">
        <UserAvatar />
      </div>

      <!-- 移动端菜单按钮 -->
      <div class="mobile-menu-toggle" @click="toggleMobileMenu">
        <el-icon size="24"><Menu /></el-icon>
      </div>
    </div>

    <!-- 移动端导航菜单 -->
    <div class="mobile-menu" :class="{ 'mobile-menu-open': isMobileMenuOpen }">
      <ul class="mobile-nav-list">
        <li v-for="(item, index) in navItems" :key="index" class="mobile-nav-item">
          <router-link :to="item.path" class="mobile-nav-link" @click="closeMobileMenu">
            <img :src="item.icon" :alt="item.name" class="nav-icon" />
            <span>{{ item.name }}</span>
          </router-link>
        </li>
      </ul>
      <!-- 移动端认证区域 -->
      <div class="mobile-auth-section">
        <UserAvatar />
      </div>
    </div>
  </header>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRoute } from 'vue-router'
  import { Menu } from '@element-plus/icons-vue'
  import UserAvatar from '@/components/UserAvatar.vue'

  // 导入导航图标
  import homeIcon from '@/assets/icons/home-icon.svg'
  import literatureIcon from '@/assets/icons/literature-icon.svg'
  import dataIcon from '@/assets/icons/data-sheet-icon.svg'
  import statisticsIcon from '@/assets/icons/chart-icon.svg'
  import helpIcon from '@/assets/icons/help-icon.svg'

  const route = useRoute()
  const isMobileMenuOpen = ref(false)

  // 导航项配置
  const navItems = [
    { name: '首页', path: '/', icon: homeIcon },
    { name: '文献', path: '/literature', icon: literatureIcon },
    { name: '获取数据', path: '/api', icon: dataIcon },
    { name: '文献统计', path: '/statistics', icon: statisticsIcon },
    { name: '帮助', path: '/help', icon: helpIcon }
  ]

  // 判断当前路由是否激活
  const isActive = path => {
    return route.path === path
  }

  // 切换移动端菜单
  const toggleMobileMenu = () => {
    isMobileMenuOpen.value = !isMobileMenuOpen.value
    if (isMobileMenuOpen.value) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }
  }

  // 关闭移动端菜单
  const closeMobileMenu = () => {
    isMobileMenuOpen.value = false
    document.body.style.overflow = ''
  }
</script>

<style lang="scss" scoped>
@import "../../assets/styles/variables";

.header {
  background-color: $white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;

  &-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 92px;
    padding: 0 $spacing-xl;

    @media (max-width: $breakpoint-lg) {
      padding: 0 $spacing-md;
    }
  }
}

.logo {
  img {
    height: 44px;
    width: auto;
  }
}

.nav-desktop {
  @media (max-width: $breakpoint-lg) {
    display: none;
  }
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 54px;
}

.nav-item {
  display: flex;
  align-items: center;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 6px;
  color: $primary-color;
  font-size: $font-size-medium;
  font-weight: $font-weight-medium;
  transition: color 0.3s ease;

  &:hover {
    color: $secondary-color;
  }

  &.active {
    font-weight: $font-weight-bold;
  }
}

.nav-icon {
  width: 22px;
  height: 22px;
}

.auth-buttons {
  display: flex;
  gap: $spacing-lg;

  @media (max-width: $breakpoint-lg) {
    display: none;
  }

  .btn {
    padding: 8px 20px;
    font-size: $font-size-small;
  }

  .btn-secondary {
    border: 1px solid $primary-color;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .arrow-icon {
    font-size: 18px;
  }
}

// 认证区域样式
.auth-section {
  @media (max-width: $breakpoint-lg) {
    display: none;
  }
}

// 用户菜单样式
.user-menu {
  .user-info {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    padding: 8px 12px;
    border-radius: $border-radius-md;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: rgba($primary-color, 0.05);
    }

    .user-icon {
      color: $primary-color;
      font-size: 18px;
    }

    .username {
      color: $primary-color;
      font-size: $font-size-medium;
      font-weight: $font-weight-medium;
    }

    .dropdown-icon {
      color: $gray;
      font-size: 14px;
      transition: transform 0.3s ease;
    }
  }
}

:deep(.el-dropdown-menu__item) {
    font-size: 16px!important;
   a{
      color: $primary-color!important;
   }

    &:hover {
      background-color: rgba($primary-color, 0.05);

      span {
        color: $primary-color;
      }
    }
}

// 移动端导航
.mobile-menu-toggle {
  display: none;
  cursor: pointer;

  @media (max-width: $breakpoint-lg) {
    display: block;
  }
}

.mobile-menu {
  position: fixed;
  top: 88px;
  left: 0;
  width: 100%;
  height: calc(100vh - 92px);
  background-color: $white;
  padding: $spacing-xl;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
  z-index: 99;

  &-open {
    transform: translateX(0);
  }
}

.mobile-nav-list {
  list-style: none;
  margin-bottom: $spacing-xl;
}

.mobile-nav-item {
  margin-bottom: $spacing-lg;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  font-size: $font-size-xlarge;
  padding: $spacing-sm 0;
}

// 移动端认证区域
.mobile-auth-section {
  // 未登录按钮样式
  .mobile-auth-buttons {
    display: flex;
    flex-direction: column;
    gap: $spacing-md;

    .btn {
      width: 100%;
    }
  }

  // 已登录用户菜单样式
  .mobile-user-menu {
    .mobile-user-info {
      display: flex;
      align-items: center;
      gap: $spacing-md;
      padding: $spacing-md 0;
      border-bottom: 1px solid rgba($primary-color, 0.1);
      margin-bottom: $spacing-lg;

      .user-icon {
        color: $primary-color;
        font-size: 24px;
      }

      .username {
        color: $primary-color;
        font-size: $font-size-large;
        font-weight: $font-weight-medium;
      }
    }

    .mobile-user-actions {
      display: flex;
      flex-direction: column;
      gap: $spacing-md;

      .mobile-menu-item {
        display: flex;
        align-items: center;
        gap: $spacing-md;
        padding: $spacing-md;
        background: none;
        border: none;
        border-radius: $border-radius-md;
        font-size: $font-size-medium;
        cursor: pointer;
        transition: background-color 0.3s ease;

        .el-icon {
          color: $primary-color;
          font-size: 20px;
        }

        span {
          color: $gray;
          font-weight: $font-weight-medium;
        }

        &:hover {
          background-color: rgba($primary-color, 0.05);

          span {
            color: $primary-color;
          }
        }

        &.logout {
          .el-icon {
            color: #ff4757;
          }

          &:hover {
            background-color: rgba(#ff4757, 0.05);

            span {
              color: #ff4757;
            }
          }
        }
      }
    }
  }
}
</style>
