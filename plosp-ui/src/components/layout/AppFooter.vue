<template>
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-about">
          <div class="footer-logo">
            <img src="@/assets/images/footer-logo.png" alt="PLOSP Logo" />
          </div>
          <div class="footer-about-text">
            <h3>关于我们</h3>
            <p>PLOSP致力于提供优质的文献检索和个性化服务，为科研工作者提供便捷的学术资源。</p>
          </div>
        </div>

        <div class="footer-links">
          <h3>友情链接</h3>
          <div class="links-grid">
            <a href="https://pubmed.ncbi.nlm.nih.gov/" target="_blank" rel="noopener noreferrer">PubMed</a>
            <a href="https://www.ncbi.nlm.nih.gov/pmc/" target="_blank" rel="noopener noreferrer">PMC</a>
            <a href="https://www.webofscience.com/" target="_blank" rel="noopener noreferrer">Web of Science</a>
            <a href="https://www.nlm.nih.gov/" target="_blank" rel="noopener noreferrer">NLM</a>
            <a href="#" target="_blank" rel="noopener noreferrer">易文献</a>
            <a href="#" target="_blank" rel="noopener noreferrer">PLoB</a>
            <a href="#" target="_blank" rel="noopener noreferrer">BiioASK</a>
            <a href="#" target="_blank" rel="noopener noreferrer">NODE</a>
          </div>
        </div>
      </div>

      <div class="footer-divider"></div>

      <div class="footer-copyright">
        <p>© Copyright 2000-2025 PLOSP All Rights Reserved.</p>
      </div>
    </div>
  </footer>
</template>

<script setup>
// 页脚组件不需要特殊逻辑
</script>

<style lang="scss" scoped>
@import "../../assets/styles/variables";

.footer {
  background-color: $primary-color;
  color: $white;
  padding: $spacing-xxl 0;

  &-content {
    display: flex;
    justify-content: space-between;
    gap: $spacing-xl;

    @media (max-width: $breakpoint-md) {
      flex-direction: column;
    }
  }

  &-about {
    display: flex;
    flex-direction: column;
    max-width: 240px;

    @media (max-width: $breakpoint-md) {
      max-width: 100%;
    }
  }

  &-logo {
    margin-bottom: $spacing-xl;

    img {
      height: 48px;
      width: auto;
    }
  }

  &-about-text {
    h3 {
      font-size: $font-size-medium;
      margin-bottom: $spacing-md;
    }

    p {
      font-size: $font-size-small;
      line-height: 1.875;
      color: $light-gray;
    }
  }

  &-links {
    h3 {
      font-size: $font-size-medium;
      margin-bottom: $spacing-xl;
    }
  }

  &-divider {
    height: 1px;
    background-color: #2E4E73;
    margin: $spacing-xl 0;
  }

  &-copyright {
    font-size: $font-size-small;
    text-align: center;
  }
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: $spacing-lg $spacing-xxl;

  a {
    color: $white;
    font-size: $font-size-small;
    transition: color 0.3s ease;

    &:hover {
      color: $secondary-color;
    }
  }
}
</style>
