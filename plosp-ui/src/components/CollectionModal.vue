<template>
  <el-dialog
    v-model="visible"
    title="添加到收藏夹"
    :width="isMobile ? '90%' : '480px'"
    :top="isMobile ? '10vh' : '15vh'"
    class="collection-modal"
    center
    @close="handleClose"
  >
    <div class="collection-content">
      <!-- 加载状态 -->
      <div v-if="foldersLoading" class="loading-state">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <span>加载文件夹中...</span>
      </div>

      <!-- 收藏夹列表 -->
      <div v-else class="folders-list">
        <div v-if="folders.length === 0" class="empty-state">
          <span>暂无收藏夹，请先创建一个</span>
        </div>
        <div
          v-for="folder in folders"
          :key="folder.id"
          :class="['folder-item', { selected: selectedFolderId === folder.id }]"
          @click="selectFolder(folder.id)"
        >
          <el-checkbox
            :model-value="selectedFolderId === folder.id"
            size="large"
            :disabled="showAddFolder"
            @change="selectFolder(folder.id)"
          >
            <template #default>
              <div class="folder-info">
                <span class="folder-name">{{ folder.name }}</span>
                <span class="folder-count">{{ folder.count || 0 }}</span>
              </div>
            </template>
          </el-checkbox>
        </div>
      </div>

      <!-- 新建收藏夹 -->
      <div class="add-folder-section">
        <el-button class="add-folder-btn" text @click="showAddFolder = !showAddFolder">
          <el-icon>
            <Plus />
          </el-icon>
          新建收藏夹
        </el-button>

        <div v-if="showAddFolder" class="add-folder-form">
          <el-input
            v-model="newFolderName"
            placeholder="请输入收藏夹名称"
            maxlength="20"
            show-word-limit
          />
          <el-button type="primary" class="confirm-folder-btn" @click="createNewFolder">确定</el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          type="primary"
          class="confirm-btn"
          :disabled="!selectedFolderId"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue'
  import { Plus, Loading } from '@element-plus/icons-vue'
  import { ElMessage } from 'element-plus'
  import { getFavoriteFolders, createFavoriteFolder } from '@/api/folder'
  import { useAuthStore } from '@/stores/auth'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false
    },
    article: {
      type: Object,
      default: null
    }
  })

  const emit = defineEmits(['update:modelValue', 'confirm'])

  // 用户认证
  const authStore = useAuthStore()
  const currentUserId = computed(() => authStore.userInfo?.userId)

  // 响应式数据
  const visible = computed({
    get: () => {
      return props.modelValue
    },
    set: value => {
      emit('update:modelValue', value)
    }
  })

  const selectedFolderId = ref('')
  const showAddFolder = ref(false)
  const newFolderName = ref('')
  const foldersLoading = ref(false)

  // 移动端检测
  const windowWidth = ref(window.innerWidth)
  const isMobile = computed(() => windowWidth.value <= 768)

  // 收藏夹数据 - 改为动态获取
  const folders = ref([])

  // 加载用户文件夹
  const loadFolders = async() => {
    if (!currentUserId.value) {
      return
    }

    foldersLoading.value = true
    try {
      const response = await getFavoriteFolders()

      if (response.code === 200) {
        folders.value = (response.data || []).map(folder => ({
          id: folder.id,
          name: folder.folderName,
          count: folder.count || 0
        }))
      } else {
        ElMessage.error(response.msg || '获取文件夹列表失败')
      }
    } catch (error) {
      ElMessage.error('获取文件夹列表失败')
    } finally {
      foldersLoading.value = false
    }
  }

  // 创建新文件夹
  const createNewFolder = async() => {
    if (!newFolderName.value.trim()) {
      ElMessage.warning('请输入文件夹名称')
      return
    }

    if (!currentUserId.value) {
      ElMessage.error('请先登录')
      return
    }

    try {
      const response = await createFavoriteFolder({
        folderName: newFolderName.value.trim()
      })

      if (response.code === 200) {
        ElMessage.success('创建成功')
        newFolderName.value = ''
        showAddFolder.value = false
        await loadFolders()
      } else {
        ElMessage.error(response.msg || '创建失败')
      }
    } catch (error) {
      ElMessage.error('创建失败')
    }
  }

  // 方法
  const selectFolder = folderId => {
    selectedFolderId.value = folderId
  }

  const handleClose = () => {
    visible.value = false
    selectedFolderId.value = ''
    showAddFolder.value = false
    newFolderName.value = ''
  }

  const handleConfirm = () => {
    if (!selectedFolderId.value) {
      ElMessage.warning('请选择一个文件夹')
      return
    }

    const selectedFolder = folders.value.find(f => f.id === selectedFolderId.value)
    emit('confirm', {
      folderId: selectedFolderId.value,
      folderName: selectedFolder?.name,
      article: props.article
    })
    handleClose()
  }

  // 监听弹窗显示状态，显示时加载文件夹
  watch(visible, newVisible => {
    if (newVisible && currentUserId.value) {
      loadFolders()
    }
  })

  // 组件挂载时检查用户登录状态
  onMounted(async() => {
    // 如果有token但没有用户信息，尝试获取用户信息
    if (authStore.token && !authStore.userInfo) {
      try {
        await authStore.fetchUserInfo()
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    }
  })
</script>

<style lang="scss" scoped>
  @import "@/assets/styles/variables";

  .collection-modal {
    :deep(.el-dialog__header) {
      padding: $spacing-lg $spacing-lg $spacing-md;
      border-bottom: 1px solid #F3F4F6;

      .el-dialog__title {
        font-size: $font-size-large;
        font-weight: $font-weight-bold;
        color: $primary-color;
      }
    }

    :deep(.el-dialog__body) {
      padding: $spacing-lg;
    }

    :deep(.el-dialog__footer) {
      padding: $spacing-md $spacing-lg $spacing-lg;
      border-top: 1px solid #F3F4F6;
    }
  }

  .collection-content {
    min-height: 200px;
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl;
    color: $gray;

    .el-icon {
      font-size: 24px;
      margin-bottom: $spacing-sm;
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxl;
    color: $gray;
    font-size: $font-size-medium;
  }

  .folders-list {
    margin-bottom: $spacing-lg;
    max-height: 300px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .folder-item {
    padding: 4px $spacing-md;
    border-radius: $border-radius-md;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: $spacing-xs;

    &:hover {
      background-color: #F9FAFB;
    }

    &.selected {
      background-color: rgba($primary-color, 0.05);
      border: 1px solid rgba($primary-color, 0.2);
    }

    :deep(.el-checkbox) {
      width: 100%;

      .el-checkbox__label {
        width: 100%;
        padding-left: 0;
      }
    }
  }

  .folder-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-left: 10px;
  }

  .folder-name {
    font-size: $font-size-medium;
    color: $primary-color;
    font-weight: $font-weight-medium;
  }

  .folder-count {
    font-size: $font-size-small;
    color: $gray;
    background-color: #F3F4F6;
    padding: 2px 8px;
    border-radius: 12px;
  }

  .add-folder-section {
    display: flex;
    flex-direction: column;
    border-top: 1px solid #F3F4F6;
    padding-top: $spacing-md;
  }

  .add-folder-btn {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    color: $primary-color;
    font-size: $font-size-small;
    padding: 0;

    &:hover {
      background-color: transparent;
    }
  }

  .add-folder-form {
    display: flex;
    margin-top: $spacing-md;
    padding: $spacing-md;
    background-color: #F9FAFB;
    border-radius: $border-radius-md;

    :deep(.el-input__wrapper) {
      border-radius: 4px 0 0 4px !important;
      height: 40px;
    }

    .confirm-folder-btn {
      border-radius: 0 04px 4px 0 !important;
      height: 40px;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: $spacing-sm;
      margin-top: $spacing-sm;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    gap: $spacing-md;
    margin-top: 10px;

    .cancel-btn {
      color: $gray;
      border-color: #E5E7EB;
    }

    .confirm-btn {
      background-color: #9CA3AF;
      border-color: #9CA3AF;

      &:not(:disabled) {
        background-color: $primary-color;
        border-color: $primary-color;
      }
    }
  }

  @media (max-width: $breakpoint-md) {
    .dialog-footer {
      flex-direction: column-reverse;
      gap: $spacing-sm;

      .cancel-btn,
      .confirm-btn {
        width: 100%;
      }
    }
  }
</style>
