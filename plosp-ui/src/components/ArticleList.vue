<template>
  <div class="article-list">
    <template v-if="props.articles && articles.length > 0">
      <div
        v-for="(article, index) in articles"
        :key="index"
        class="article-card"
        @click="navigateToLiterature(article)"
      >
        <h3 class="article-title" :title="article.title" v-html="article.title"></h3>
        <p class="article-authors">{{ article.authors }}</p>
        <div class="article-meta">
          <span class="journal-name" :title="article.journal">{{ truncatedJournal(article.journal) }}.</span>
          <span class="volume-info">{{ article.info }}</span>
          <div v-if="article.hitnum" class="hit-info">
            <img v-if="article.hitnum > 0" src="@/assets/images/thumbs.svg" class="meta-icon" />
            <span v-if="article.hitnum > 0" class="hit-count">{{ article.hitnum }}</span>
            <span class="date">{{ article.date }}</span>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="article-list-empty">
        <el-icon>
          <InfoFilled />
        </el-icon>
        <span>暂无文献数据</span>
      </div>
    </template>
  </div>
</template>

<script setup>
  import { InfoFilled } from '@element-plus/icons-vue'
  import { useRouter } from 'vue-router'

  const router = useRouter()

  // 通过props接收文章列表数据
  const props = defineProps({
    articles: {
      type: Array,
      required: true,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  })

  // 跳转到文献页面
  const navigateToLiterature = article => {
    const id = article.id
    if (id) {
      router.push(`/detail/${id}`)
    } else {
      ElMessage.warning('无法获取文献ID')
    }
  }
  // 截断期刊名称，过长时显示省略号
  const truncatedJournal = journal => {
    if (!journal) return ''
    const maxLength = 55
    return journal.length > maxLength ? `${journal.substring(0, maxLength)}...` : journal
  }
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/variables';

  .article-list {
    display: flex;
    flex-direction: column;
    gap: 18px;
  }

  .article-list-loading {
    padding: $spacing-md 0;
  }

  .article-list-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl 0;
    color: $gray;
    text-align: center;

    .el-icon {
      font-size: 24px;
      margin-bottom: $spacing-sm;
    }

    span {
      font-size: $font-size-small;
    }
  }

  .article-card {
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .article-title {
    font-size: $font-size-medium;
    font-weight: $font-weight-bold;
    color: $primary-color;
    margin-bottom: $spacing-xxs;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    margin-top: 0;
    width: calc(100% - 10px);

    &:hover {
      color: $secondary-color;
    }
  }

  .article-authors {
    font-size: $font-size-small;
    color: $black;
    margin: 8px 0;
  }

  .article-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    font-size: $font-size-small;
    color: $gray;

    .hit-info {
      display: flex;
      align-items: center;
      gap: 4px;
      flex-shrink: 0;
      margin-left: auto;

      .date {
        margin-left: 20px;
      }
    }

    .meta-icon {
      width: 22px;
    }
  }
</style>
