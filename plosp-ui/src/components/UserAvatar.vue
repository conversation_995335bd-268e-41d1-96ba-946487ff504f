<template>
  <div class="user-avatar-container">
    <!-- 未登录状态 -->
    <div v-if="!authStore.isLoggedIn" class="auth-buttons">
      <el-button
        type="primary"
        size="default"
        class="login-btn"
        @click="goToLogin"
      >
        登录
      </el-button>
      <el-button
        size="default"
        class="register-btn"
        @click="goToRegister"
      >
        立即注册
      </el-button>
    </div>

    <!-- 已登录状态 -->
    <el-dropdown v-else trigger="click" class="user-dropdown">
      <div class="user-info">
        <el-avatar
          :size="32"
          :src="userAvatar"
          class="user-avatar"
          @error="handleAvatarError"
        >
          <el-icon><User /></el-icon>
        </el-avatar>
        <span class="user-name">{{ authStore.userName || '用户' }}</span>
        <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
      </div>

      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="goToUserCenter">
            <el-icon><User /></el-icon>
            个人中心
          </el-dropdown-item>
          <el-dropdown-item @click="goToEditProfile">
            <el-icon><Edit /></el-icon>
            编辑资料
          </el-dropdown-item>
          <el-dropdown-item divided @click="handleLogout">
            <el-icon><SwitchButton /></el-icon>
            退出登录
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessageBox } from 'element-plus'
  import { User, ArrowDown, Edit, SwitchButton } from '@element-plus/icons-vue'
  import { useAuthStore } from '@/stores/auth'

  // 路由
  const router = useRouter()

  // 认证状态管理
  const authStore = useAuthStore()

  // 用户头像 (可以根据实际需求从用户信息中获取)
  const userAvatar = computed(() => {
    if (authStore.userInfo?.avatar) {
      // 如果是base64数据，转换为可显示的URL
      let avatarUrl = authStore.userInfo.avatar
      if (!avatarUrl.startsWith('http') && !avatarUrl.startsWith('data:')) {
        avatarUrl = `data:image/jpeg;base64,${avatarUrl}`
      } else if (!avatarUrl.startsWith('http')) {
        avatarUrl = `${import.meta.env.VITE_APP_BASE_API}${avatarUrl}`
      }
      return avatarUrl
    }
    return ''
  })

  // 跳转到登录页
  const goToLogin = () => {
    router.push('/login')
  }

  // 跳转到注册页
  const goToRegister = () => {
    router.push('/register')
  }

  // 跳转到用户中心
  const goToUserCenter = () => {
    router.push('/user-center')
  }

  // 跳转到编辑资料页
  const goToEditProfile = () => {
    router.push('/edit-profile')
  }

  // 处理退出登录
  const handleLogout = async() => {
    try {
      await ElMessageBox.confirm(
        '确定要退出登录吗？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await authStore.logout()
      router.push('/')
    } catch (error) {
    // 用户取消退出
    }
  }

  // 头像加载错误处理
  const handleAvatarError = () => {
    // el-avatar 组件会自动显示 slot 内容（User 图标）
    console.log('头像加载失败，显示默认图标')
  }
</script>

<style lang="scss" scoped>
.user-avatar-container {
  display: flex;
  align-items: center;

  .auth-buttons {
    display: flex;
    gap: 12px;
    align-items: center;

    .login-btn {
      height: 40px;
      padding: 0 24px;
      border-radius: 8px;
      font-weight: 500;
      font-size: 14px;
      background: #1D68C2;
      border: none;
      color: white;
      transition: all 0.2s ease;

      &:hover {
        background: #00416F;
      }

      &:focus {
        background: #00416F;
      }
    }

    .register-btn {
      height: 40px;
      padding: 0 20px;
      border-radius: 8px;
      font-weight: 500;
      font-size: 14px;
      border: 1px solid #1D68C2;
      color: #1D68C2;
      background: white;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 4px;

      &:hover {
        background: #eff6ff;
        border-color: #1d4ed8;
        color: #1d4ed8;
      }

      &:focus {
        background: #eff6ff;
        border-color: #1d4ed8;
        color: #1d4ed8;
      }

      .register-arrow {
        font-size: 12px;
        margin-left: 2px;
      }
    }
  }

  .user-dropdown {
    cursor: pointer;

    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 8px;
      border-radius: 6px;
      transition: background-color 0.2s;

      &:hover {
        background-color: var(--el-fill-color-light);
      }

      .user-avatar {
        flex-shrink: 0;
      }

      .user-name {
        height: 16px;
        font-size: 14px;
        color: var(--el-text-color-primary);
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .dropdown-icon {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        transition: transform 0.2s;
      }
    }

    &.is-active .user-info .dropdown-icon {
      transform: rotate(180deg);
    }
  }
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-icon {
    font-size: 14px;
  }
}
</style>
