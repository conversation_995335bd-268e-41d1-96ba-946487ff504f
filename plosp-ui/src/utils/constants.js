/**
 * 本地存储常量定义
 * 统一管理应用中使用的localStorage和sessionStorage的key值
 */

// localStorage 键名常量
export const STORAGE_KEYS = {
  // 认证相关
  TOKEN: 'plosp-portal-token',
  USER_INFO: 'plosp-portal-userInfo',
  AUTH_STORE: 'plosp-portal-auth-store'
}

// sessionStorage 键名常量（如果需要的话）
export const SESSION_STORAGE_KEYS = {
  // 可以在这里添加sessionStorage的键名
}

// 默认导出所有常量
export default {
  STORAGE_KEYS,
  SESSION_STORAGE_KEYS
}
