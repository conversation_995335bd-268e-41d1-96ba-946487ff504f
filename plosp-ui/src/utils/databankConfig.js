/**
 * 数据库URL配置
 * 用于生成关联数据库的访问链接
 */
export const databankUrlConfig = {
  ANZCTR: 'https://anzctr.org.au/Trial/Registration/TrialReview.aspx?ACTRN={value}',
  BioProject: 'https://www.ncbi.nlm.nih.gov/bioproject/{value}',
  CRiS: 'https://cris.nih.go.kr/cris/en/search/basic_search.jsp?searchword={value}',
  CTRI: 'http://ctri.nic.in/Clinicaltrials/showallp.php?mid1=3298&EncHid=&userName={value}',
  ChiCTR: 'https://www.chictr.org.cn/searchprojen.aspx?btngo=btn&regno={value}',
  'ClinicalTrials.gov': 'https://clinicaltrials.gov/study/{value}',
  DRKS: 'https://www.bfarm.de/DE/Das-BfArM/Aufgaben/Deutsches-Register-K<PERSON>-Studien/_node.html?navigationId=trial.HTML&TRIAL_ID={value}',
  Dryad: 'https://doi.org/{value}',
  EudraCT: 'https://www.clinicaltrialsregister.eu/ctr-search/search?query={value}',
  GDB: 'https://www.gdb.org/gdb-bin/genera/accno?accessionNum={value}',
  GENBANK: 'https://www.ncbi.nlm.nih.gov/nuccore/{value}',
  GEO: 'https://www.ncbi.nlm.nih.gov/geo/query/acc.cgi?acc={value}',
  IRCT: 'http://www.irct.ir/search/result?query={value}',
  ISRCTN: 'http://www.controlled-trials.com/{value}',
  JMACCT: 'http://rctportal.niph.go.jp/en/detail?trial_id={value}',
  JPRN: 'http://rctportal.niph.go.jp/en/detail?trial_id={value}',
  JapicCTI: 'http://www.clinicaltrials.jp/user/showCteDetailE.jsp?japicId={name}-{value}',
  NTR: 'http://www.trialregister.nl/trialreg/admin/rctview.asp?TC={value}',
  OMIM: 'https://www.ncbi.nlm.nih.gov/omim?term={value}',
  PACTR: 'https://www.pactr.org/',
  PDB: 'https://www.ncbi.nlm.nih.gov/Structure/pdb/{value}',
  PIR: 'https://www.ncbi.nlm.nih.gov/protein?term={value}',
  'PubChem-BioAssay': 'https://pubchem.ncbi.nlm.nih.gov/bioassay/{value}',
  'PubChem-Compound': 'https://pubchem.ncbi.nlm.nih.gov/compound/{value}',
  'PubChem-Substance': 'https://pubchem.ncbi.nlm.nih.gov/substance/{value}',
  RPCEC: 'http://registroclinico.sld.cu/en/trials/{value}-En',
  ReBec: 'https://ensaiosclinicos.gov.br/rg/{value}',
  RefSeq: 'https://www.ncbi.nlm.nih.gov/nuccore/{value}',
  SLCTR: 'http://www.slctr.lk/',
  SRA: 'https://www.ncbi.nlm.nih.gov/sra/{value}',
  SWISSPROT: 'https://www.ncbi.nlm.nih.gov/protein/{value}',
  TCTR: 'http://www.clinicaltrials.in.th/',
  'UMIN CTR': 'http://rctportal.niph.go.jp/en/detail?trial_id={value}',
  UniMES: 'https://www.uniprot.org/',
  UniProtKB: 'https://www.uniprot.org/uniprotkb/?sort=score&query={value}',
  UniRef: 'https://www.uniprot.org/uniref/{value}',
  dbGaP: 'https://dbgap.ncbi.nlm.nih.gov/home?term={value}',
  dbSNP: 'https://www.ncbi.nlm.nih.gov/projects/SNP/snp_retrieve.cgi?subsnp_id={value}',
  dbVar: 'https://www.ncbi.nlm.nih.gov/dbvar/studies/{value}',
  figshare: 'https://doi.org/{value}'
}

/**
 * 生成数据库访问URL
 * @param {string} name - 数据库名称
 * @param {string} value - 数据库值/ID
 * @returns {string} 完整的访问URL
 */
export const generateDatabankUrl = (name, value) => {
  const urlTemplate = databankUrlConfig[name] || `https://www.ncbi.nlm.nih.gov/${name.toLowerCase()}/${value}`
  return urlTemplate
    .replace('{name}', name)
    .replace('{value}', value)
}
