import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import router from '@/router'
import { blobValidate, tansParams } from '@/utils/util'
import { saveAs } from 'file-saver'
import errorCode from '@/utils/errorCode'
import { ElLoading } from 'element-plus'


let downloadLoadingInstance
// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 85000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    const authStore = useAuthStore()

    // 如果存在 token，则在请求头中添加 Authorization
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }

    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 认证错误处理标志，防止重复弹窗
let isAuthErrorHandling = false

// 统一处理认证错误
const handleAuthError = (message = '登录状态已过期，请重新登录') => {
  if (isAuthErrorHandling) {
    return // 如果正在处理认证错误，避免重复处理
  }

  isAuthErrorHandling = true

  const authStore = useAuthStore()

  // 显示错误提示
  ElMessage.warning(message)

  // 清除认证信息
  authStore.logout()

  // 跳转到登录页
  router.push('/login')

  // 延迟重置标志，避免快速重复请求
  setTimeout(() => {
    isAuthErrorHandling = false
  }, 1000)
}

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 二进制数据则直接返回
    if (
      response.request.responseType === 'blob' ||
      response.request.responseType === 'arraybuffer'
    ) {
      // console.log(response)
      return response.data
    }

    const res = response.data

    if (res.code !== 200) {
      // 401: 未授权，token 过期或无效
      if (res.code === 401) {
        handleAuthError('登录状态已过期，请重新登录')
        return Promise.reject(new Error('登录已过期'))
      }

      // 403: 权限不足
      if (res.code === 403) {
        ElMessage({
          message: res.msg || '权限不足，无法访问该资源',
          type: 'warning',
          duration: 5 * 1000
        })
        return Promise.reject(new Error(res.msg || '权限不足'))
      }

      // 其他错误才显示通用提示
      ElMessage({
        message: res.msg || '请求失败',
        type: 'error',
        duration: 5 * 1000
      })

      return Promise.reject(new Error(res.msg || '请求失败'))
    } else {
      return res
    }
  },
  error => {
    console.error('响应错误:', error)

    let message = '网络错误'

    if (error.response) {
      switch (error.response.status) {
        case 400:
          message = '请求参数错误'
          break
        case 401: {
          // 统一处理401认证错误
          handleAuthError('登录状态已过期，请重新登录')
          return Promise.reject(error)
        }
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址不存在'
          break
        case 408:
          message = '请求超时'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 501:
          message = '服务未实现'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        case 505:
          message = 'HTTP版本不受支持'
          break
        default:
          message = `连接错误${error.response.status}`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.message.includes('Network Error')) {
      message = '网络连接异常'
    }

    // 401错误已经在上面处理过了，不需要再显示错误消息
    if (error.response?.status !== 401) {
      ElMessage({
        message,
        type: 'error',
        duration: 5 * 1000
      })
    }

    return Promise.reject(error)
  }
)


// 通用下载方法
export function download(url, params, filename, config) {
  downloadLoadingInstance = ElLoading.service({
    text: '正在下载数据，请稍候',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  if (!config) {
    config = { timeout: 90000 }
  } else {
    const timeout = config.timeout
    if (!timeout) {
      config.timeout = 90000
    } else {
      config.timeout = timeout < 15000 ? 90000 : timeout
    }
  }

  return service
    .post(url, params, {
      transformRequest: [
        params => {
          return tansParams(params)
        }
      ],
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob',
      ...config
    })
    .then(async data => {
      const isBlob = blobValidate(data)
      if (isBlob) {
        const blob = new Blob([data])
        saveAs(blob, filename)
      } else {
        const resText = await data.text()
        const rspObj = JSON.parse(resText)
        const errMsg =
          errorCode[rspObj.code] || rspObj.msg || errorCode['default']
        ElMessage.error(errMsg)
      }
      downloadLoadingInstance.close()
    })
    .catch(r => {
      console.error(r)
      ElMessage.error('下载文件出现错误，请联系管理员！')
      downloadLoadingInstance.close()
    })
}

export default service
