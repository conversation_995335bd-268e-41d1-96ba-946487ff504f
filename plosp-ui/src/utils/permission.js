import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

/**
 * 检查用户是否已登录
 * @returns {boolean}
 */
export function isAuthenticated() {
  const authStore = useAuthStore()
  return authStore.isLoggedIn
}

/**
 * 检查用户是否有访问权限
 * @param {string|Array} permissions - 权限字符串或权限数组
 * @returns {boolean}
 */
export function hasPermission(permissions) {
  const authStore = useAuthStore()
  
  if (!authStore.isLoggedIn) {
    return false
  }

  // 如果没有指定权限要求，只要登录即可
  if (!permissions) {
    return true
  }

  // 这里可以根据实际需求扩展权限检查逻辑
  // 目前门户系统主要是登录/未登录的区分
  return true
}

/**
 * 路由守卫 - 检查认证状态
 * @param {Object} to - 目标路由
 * @param {Object} from - 来源路由
 * @param {Function} next - 路由跳转函数
 */
export async function authGuard(to, from, next) {
  const authStore = useAuthStore()

  // 检查是否跳过认证
  const skipAuth = to.matched.some(record => record.meta.skipAuth)
  if (skipAuth) {
    next()
    return
  }

  // 需要认证的路由
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

  if (requiresAuth) {
    if (!authStore.isLoggedIn) {
      // 未登录，跳转到登录页
      ElMessage.warning('请先登录')
      next({
        path: '/login',
        query: { redirect: to.fullPath } // 保存目标路由，登录后跳转
      })
      return
    }
    
    // 已登录，验证 token 是否有效
    const isValid = await authStore.checkAuth()
    if (!isValid) {
      ElMessage.error('登录状态已过期，请重新登录')
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }
  
  // 如果是登录页面且用户已登录，重定向到首页或目标页面
  if (to.path === '/login' && authStore.isLoggedIn) {
    const redirect = to.query.redirect || '/'
    next(redirect)
    return
  }
  
  next()
}

/**
 * 权限指令 - 用于 v-permission
 * @param {HTMLElement} el - DOM 元素
 * @param {Object} binding - 指令绑定对象
 */
export function permissionDirective(el, binding) {
  const { value } = binding
  
  if (!hasPermission(value)) {
    el.style.display = 'none'
    // 或者移除元素
    // el.parentNode && el.parentNode.removeChild(el)
  }
}

/**
 * 检查是否需要重新登录
 * @param {number} code - 响应状态码
 * @returns {boolean}
 */
export function needRelogin(code) {
  return code === 401 || code === 403
}

/**
 * 处理未授权访问
 * @param {string} message - 错误消息
 */
export function handleUnauthorized(message = '访问被拒绝') {
  const authStore = useAuthStore()
  ElMessage.error(message)
  authStore.logout()
}

/**
 * 获取重定向路径
 * @param {Object} route - 当前路由
 * @returns {string}
 */
export function getRedirectPath(route) {
  if (route.query.redirect) {
    return route.query.redirect
  }
  return '/'
}
