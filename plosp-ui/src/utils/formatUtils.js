/**
 * 格式化工具类
 */

/**
 * 格式化时间为 YYYY-MM-DD 格式
 * @param {string|Date} time - 时间
 * @returns {string} 格式化后的时间字符串
 */
export const formatTime = time => {
  if (!time) return ''
  const date = new Date(time)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${year}-${month}-${day}`
}

/**
 * 格式化作者名称，去除括号和引号
 * @param {Array|string} authors - 作者信息
 * @returns {string} 格式化后的作者字符串
 */
export const formatAuthors = authors => {
  if (!authors) return ''

  // 如果是数组，转换为字符串
  if (Array.isArray(authors)) {
    return authors.join(', ')
  }

  // 如果不是字符串，转换为字符串
  if (typeof authors !== 'string') {
    return String(authors)
  }

  // 去除字符串开头和结尾的方括号，以及引号
  return authors.replace(/^\[|\]$/g, '').replace(/["']/g, '')
}

// 格式化数字
export const formatNumber = num => {
  if (num === null || num === undefined) return '0'
  return num.toLocaleString()
}
