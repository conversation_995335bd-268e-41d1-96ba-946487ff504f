// 颜色变量
$primary-color: #00416F; // 主色调 蓝色
$secondary-color: #4F9CF9; // 次要色调 浅蓝色
$white: #FFFFFF;
$black: #000000;
$gray: #374151;
$light-gray: #F7F7EE;
$yellow: #FFE492;
$orange: #FF9600;
$red-orange: #FF8F6B;
$light-blue: #70CAFF;
$blue: #1990FF;
$background-color: #FDFDFD;
$background-color-light: #f5f7fa;
$text-color-primary: #303133;
$text-color-secondary: #909399;
$border-color-light: #dcdfe6;
$border-radius-base: 4px;

// 阴影
$box-shadow: 6px 6px 54px 0px rgba(0, 0, 0, 0.05);

// 字体
$font-family-main: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
$font-family-title: '<PERSON><PERSON><PERSON>', $font-family-main;

// 字体大小
$font-size-small: 16px;
$font-size-medium: 18px;
$font-size-large: 20px;
$font-size-xlarge: 24px;
$font-size-xxlarge: 26px;
$font-size-xxxlarge: 28px;
$font-size-xxxxlarge: 30px;
$font-size-xxxxxlarge: 32px;
$font-size-huge: 36px;

// 字体粗细
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-bold: 600;
$font-weight-extrabold: 700;

// 间距
$spacing-xxs: 6px;
$spacing-xs: 8px;
$spacing-sm: 12px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 36px;
$spacing-xxxl: 48px;
$spacing-xxxxl: 64px;

// 边框圆角
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-xxl: 24px;

// 响应式断点
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-xxl: 1400px;
