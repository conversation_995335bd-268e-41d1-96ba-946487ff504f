{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.validate": ["javascript", "javascriptreact", "vue"], "eslint.workingDirectories": ["."], "vetur.validation.template": false, "vetur.validation.script": false, "vetur.validation.style": false, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}