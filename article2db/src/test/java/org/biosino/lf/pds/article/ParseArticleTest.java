package org.biosino.lf.pds.article;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.biosino.lf.pds.article.domain.Article;
import org.biosino.lf.pds.article.dto.ArticleInfoDTO;
import org.biosino.lf.pds.article.service.IArticleService;
import org.biosino.lf.pds.article.service.PmcXmlParse;
import org.biosino.lf.pds.article.service.PubmedXmlParse;
import org.biosino.lf.pds.common.enums.SourceTypeEnums;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;

@SpringBootTest
public class ParseArticleTest {

    @Autowired
    private IArticleService articleService;

    // https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db=pubmed&id=40132213&retmode=xml
    @Test
    public void pubmed() {
        long start = System.currentTimeMillis();

        PubmedXmlParse xmlParse = new PubmedXmlParse();
        String content = FileUtil.readUtf8String("D:\\code\\pds-v2\\doc\\PLOSP XML\\pubmed-26578674.xml");
        ArticleInfoDTO dto = xmlParse.parse(content, SourceTypeEnums.PubMed);
        articleService.saveOne(dto);

        long end = System.currentTimeMillis();
        System.out.println("耗时: " + (end - start) + " ms");
    }

    // https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db=pmc&id=6113747&retmode=xml
    @Test
    public void pmc() {
        long start = System.currentTimeMillis();

        PmcXmlParse xmlParse = new PmcXmlParse();
        String content = FileUtil.readUtf8String("D:\\code\\pds-v2\\doc\\PLOSP XML\\PMC4659460 (同pubmed-26578674).xml");
        ArticleInfoDTO dto = xmlParse.parse(content, SourceTypeEnums.PMC);
        articleService.saveOne(dto);

        long end = System.currentTimeMillis();
        System.out.println("耗时: " + (end - start) + " ms");
    }

    // 元数据：https://api.biorxiv.org/details/biorxiv/10.1101/2025.03.21.644627/full
    // XML：https://www.biorxiv.org/content/early/2025/03/24/2025.03.21.644627.source.xml
    // PDF: https://www.medrxiv.org/content/10.1101/2025.03.21.25324407v1.full.pdf
    @Test
    public void bioRxiv() {
        PmcXmlParse xmlParse = new PmcXmlParse();
        String content = FileUtil.readUtf8String("D:\\code\\pds-v2\\doc\\PLOSP XML\\BioRxiv的jatsxml样例.xml");
        ArticleInfoDTO dto = xmlParse.parse(content, SourceTypeEnums.bioRxiv);
        articleService.saveOne(dto);
    }

    @Test
    public void medRxiv() {
        PmcXmlParse xmlParse = new PmcXmlParse();
        String content = FileUtil.readUtf8String("D:\\code\\pds-v2\\doc\\PLOSP XML\\MedRxiv的jatsxml样例.xml");
        ArticleInfoDTO dto = xmlParse.parse(content, SourceTypeEnums.medRxiv);
        articleService.saveOne(dto);
    }

    @Test
    public void mergeData() {
        articleService.merge(null, 11609424L);
    }

    @Test
    public void deletedByDocId() {
        articleService.deletedAllByDocId(SourceTypeEnums.PubMed.name(), 1952323401354772480L);
        articleService.removeById(1952323401354772480L);
    }

    /**
     * 临时方法
     * 修复：合并未合并的PMCID数据
     */
    @Test
    public void mergePmcData() {
        QueryWrapper<Article> wrapper = new QueryWrapper<>();
        wrapper.select("pmc_id")
                .isNotNull("pmc_id")
                .groupBy("pmc_id")
                .having("COUNT(*) > {0}", 1);

        List<Map<String, Object>> result = articleService.listMaps(wrapper);

        for (Map<String, Object> row : result) {
            Long pmcId = (Long) row.get("pmc_id");
            System.out.println(pmcId);
//            articleService.merge(null, pmcId);
        }
    }

    /**
     * 临时方法
     * 修复：合并未合并的PMID数据
     */
    @Test
    public void mergePmData() {
        QueryWrapper<Article> wrapper = new QueryWrapper<>();
        wrapper.select("pmid")
                .isNotNull("pmid")
                .groupBy("pmid")
                .having("COUNT(*) > {0}", 1);

        List<Map<String, Object>> result = articleService.listMaps(wrapper);

        for (Map<String, Object> row : result) {
            Integer pmId = (Integer) row.get("pmid");
            System.out.println(pmId);
        }

    }

    /**
     * 临时方法
     * 修复：删除重复PMID数据
     * SELECT pmid
     * FROM tb_dds_article
     * WHERE pmid IS NOT NULL
     * GROUP BY pmid
     * HAVING COUNT(*) > 1
     */
    @Test
    public void mergePmidData() {
        QueryWrapper<Article> wrapper = new QueryWrapper<>();
        wrapper.select("pmid")
                .isNotNull("pmid")
                .groupBy("pmid")
                .having("COUNT(*) > {0}", 1);

        List<Map<String, Object>> result = articleService.listMaps(wrapper);

        for (Map<String, Object> row : result) {
            Integer pmid = (Integer) row.get("pmid");
            List<Article> articles = articleService.list(Wrappers.<Article>lambdaQuery().eq(Article::getPmid, pmid).orderByDesc(Article::getCreateTime));
            for (int i = 1; i < articles.size(); i++) {
                articleService.removeById(articles.get(i).getId());
            }
        }

    }

}
