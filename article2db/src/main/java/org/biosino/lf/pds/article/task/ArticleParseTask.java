package org.biosino.lf.pds.article.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.lf.pds.article.domain.ArticleParse;
import org.biosino.lf.pds.article.dto.ArticleInfoDTO;
import org.biosino.lf.pds.article.service.*;
import org.biosino.lf.pds.common.enums.ParseStatusEnums;
import org.biosino.lf.pds.common.enums.SourceTypeEnums;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 文献解析入库定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RestController
@RequiredArgsConstructor
public class ArticleParseTask implements DisposableBean {

    private final PmcXmlParse pmcXmlParse;
    private final PubmedXmlParse pubmedXmlParse;
    private final IArticleService articleService;
    private final IArticleXmlService articleXmlService;
    private final IArticleParseService articleParseService;

    // 定时任务开关
    @Value("${app.scheduled:false}")
    private boolean scheduled;

    // 默认批处理大小
    @Value("${app.batch-size:1000}")
    private int batchSize;

    // 默认线程数
    @Value("${app.thread-count:10}")
    private int threadCount;

    // 任务是否正在运行的标志
    private final AtomicBoolean isRunning = new AtomicBoolean(false);

    // 线程池
    private ThreadPoolExecutor threadPool;

    /**
     * 初始化线程池
     */
    private ThreadPoolExecutor getThreadPool() {
        if (threadPool == null || threadPool.isShutdown()) {
            // 使用自定义线程工厂来命名线程
            ThreadFactory threadFactory = new ThreadFactory() {
                private final AtomicInteger counter = new AtomicInteger(1);

                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r);
                    thread.setName("article-parse-thread-" + counter.getAndIncrement());
                    return thread;
                }
            };

            // 创建线程池
            threadPool = new ThreadPoolExecutor(
                    threadCount, // 核心线程数
                    threadCount, // 最大线程数
                    60L, // 空闲线程存活时间
                    TimeUnit.SECONDS, // 时间单位
                    new LinkedBlockingQueue<>(1000), // 工作队列
                    threadFactory, // 线程工厂
                    new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：由调用线程执行
            );
        }
        return threadPool;
    }

    /**
     * 每1分钟执行一次，扫描未入库的记录并解析入库
     */
    @Scheduled(fixedRate = 60000)
    public void scheduledProcessUnparsedArticles() {
        if (!scheduled) {
            return;
        }
        if (isRunning.compareAndSet(false, true)) {
            try {
                if (threadCount == 1) {
                    // 单线程入库
                    processUnparsedArticles(batchSize, false);
                } else {
                    // 多线程入库
                    processUnparsedArticles(batchSize, true);
                }
            } finally {
                isRunning.set(false);
            }
        } else {
            log.info("上一次任务还在执行中，跳过本次调度");
        }
    }

    /**
     * 处理一批未入库的记录
     *
     * @param batchSize      批处理大小
     * @param useMultiThread 是否使用多线程
     */
    public void processUnparsedArticles(int batchSize, boolean useMultiThread) {
        log.info("开始执行文献解析入库定时任务，批处理大小: {}, 多线程模式: {}", batchSize, useMultiThread);

        List<ArticleParse> unparsedList = articleParseService.findUnprocessedRecords(batchSize);
        int processed = processBatch(unparsedList, useMultiThread);

        log.info("文献解析入库定时任务执行完成，处理{}条数据", processed);
    }

    /**
     * 处理一批数据
     *
     * @param batch          待处理的数据批次
     * @param useMultiThread 是否使用多线程
     * @return 成功处理的数量
     */
    private int processBatch(List<ArticleParse> batch, boolean useMultiThread) {
        if (batch.isEmpty()) {
            log.info("没有未入库的记录需要处理");
            return 0;
        }

        log.info("找到{}条未入库记录，使用{}模式处理", batch.size(), useMultiThread ? "多线程" : "单线程");

        if (useMultiThread) {
            return processWithMultiThread(batch);
        } else {
            return processWithSingleThread(batch);
        }
    }

    /**
     * 使用多线程处理一批数据
     *
     * @param batch 待处理的数据批次
     * @return 成功处理的数量
     */
    private int processWithMultiThread(List<ArticleParse> batch) {
        ThreadPoolExecutor executor = getThreadPool();
        CountDownLatch latch = new CountDownLatch(batch.size());
        AtomicInteger successCount = new AtomicInteger(0);

        log.info("使用{}个线程并行处理{}条数据", threadCount, batch.size());

        for (ArticleParse articleParse : batch) {
            executor.execute(() -> {
                try {
                    if (processArticle(articleParse)) {
                        successCount.incrementAndGet();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            // 等待所有任务完成，最多等待30分钟
            boolean completed = latch.await(30, TimeUnit.MINUTES);
            if (!completed) {
                log.warn("部分任务未在30分钟内完成");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("等待任务完成时被中断");
        }

        return successCount.get();
    }

    /**
     * 使用单线程处理一批数据
     *
     * @param batch 待处理的数据批次
     * @return 成功处理的数量
     */
    private int processWithSingleThread(List<ArticleParse> batch) {
        int successCount = 0;

        for (ArticleParse articleParse : batch) {
            if (processArticle(articleParse)) {
                successCount++;
            }
        }

        return successCount;
    }

    /**
     * 处理单篇文章
     *
     * @param articleParse 文章解析记录
     * @return 是否处理成功
     */
    private boolean processArticle(ArticleParse articleParse) {
        try {
            // 获取XML内容
            String xmlContent = articleXmlService.getContentById(articleParse.getId());
            if (xmlContent == null) {
                articleParseService.updateStatus(articleParse, null, ParseStatusEnums.Fail.getCode(), "XML内容为空");
                return false;
            }

            // 根据来源类型选择解析器
            ArticleInfoDTO articleInfoDTO;
            String source = articleParse.getSource();
            SourceTypeEnums sourceTypeEnums = SourceTypeEnums.getByName(source);

            if (SourceTypeEnums.PubMed.name().equalsIgnoreCase(source)) {
                articleInfoDTO = pubmedXmlParse.parse(xmlContent, SourceTypeEnums.PubMed);
            } else if (sourceTypeEnums != null) {
                articleInfoDTO = pmcXmlParse.parse(xmlContent, sourceTypeEnums);
            } else {
                articleParseService.updateStatus(articleParse, null, ParseStatusEnums.Fail.getCode(), "不支持的来源类型: " + source);
                return false;
            }

            // 保存解析结果
            articleService.saveOne(articleInfoDTO);

            // 更新解析状态为已入库，并回填doc_id
            articleParseService.updateStatus(
                    articleParse,
                    articleInfoDTO.getArticle().getId(),
                    ParseStatusEnums.Complete.getCode(),
                    null
            );

            log.warn("成功解析入库文献：ID={}, 来源={}", articleParse.getId(), source);
            return true;

        } catch (Exception e) {
            log.error("解析文献失败：ID={}, 原因={}", articleParse.getId(), e.getMessage(), e);
            articleParseService.updateStatus(articleParse, null, ParseStatusEnums.Fail.getCode(), e.getMessage());
            return false;
        }
    }

    /**
     * 关闭线程池
     */
    public void shutdown() {
        if (threadPool != null && !threadPool.isShutdown()) {
            log.info("正在关闭文章解析线程池...");
            threadPool.shutdown();
            try {
                if (!threadPool.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.warn("线程池未能在60秒内完全关闭，强制关闭");
                    threadPool.shutdownNow();
                }
                log.warn("文章解析线程池已关闭");
            } catch (InterruptedException e) {
                threadPool.shutdownNow();
                Thread.currentThread().interrupt();
                log.error("关闭线程池时被中断", e);
            }
        }
    }

    /**
     * 应用关闭时自动调用，确保线程池正确关闭
     */
    @Override
    public void destroy() {
        log.warn("应用关闭，释放资源...");
        shutdown();
    }

    // ============================以下为测试在线触发执行的方法，已废弃============================

    /**
     * 手动触发文献解析入库任务
     *
     * @param batchSize      批处理大小
     * @param processAll     是否处理全部未入库数据
     * @param useMultiThread 是否使用多线程处理
     * @param threadNum      线程数量（多线程模式下有效）
     * @return 处理结果
     */
    @GetMapping("/api/article/parse/trigger")
    public String triggerProcessing(
            @RequestParam(defaultValue = "1000") int batchSize,
            @RequestParam(defaultValue = "false") boolean processAll,
            @RequestParam(defaultValue = "false") boolean useMultiThread,
            @RequestParam(defaultValue = "10") int threadNum) {

        if (isRunning.compareAndSet(false, true)) {
            // 如果指定了线程数，则更新线程池配置
            if (useMultiThread && threadNum > 0 && threadNum != threadCount) {
                threadCount = threadNum;
                if (threadPool != null && !threadPool.isShutdown()) {
                    shutdown(); // 调用shutdown方法关闭旧的线程池
                    threadPool = null;
                }
            }

            new Thread(() -> {
                try {
                    if (processAll) {
                        processAllUnparsedArticles(batchSize, useMultiThread);
                    } else {
                        processUnparsedArticles(batchSize, useMultiThread);
                    }
                } finally {
                    isRunning.set(false);
                }
            }).start();

            return "任务已启动，批处理大小: " + batchSize +
                    ", 处理全部: " + processAll +
                    ", 多线程模式: " + useMultiThread +
                    (useMultiThread ? ", 线程数: " + threadCount : "");
        } else {
            return "任务正在执行中，请稍后再试";
        }
    }

    /**
     * 处理所有未入库的数据，分批进行
     *
     * @param batchSize      每批处理的数量
     * @param useMultiThread 是否使用多线程
     */
    public void processAllUnparsedArticles(int batchSize, boolean useMultiThread) {
        log.info("开始处理所有未入库的文献数据，批处理大小: {}, 多线程模式: {}", batchSize, useMultiThread);

        int totalProcessed = 0;
        int batchCount = 0;

        while (true) {
            List<ArticleParse> batch = articleParseService.findUnprocessedRecords(batchSize);
            if (batch.isEmpty()) {
                break;
            }

            int processed = processBatch(batch, useMultiThread);
            totalProcessed += processed;
            batchCount++;

            log.info("完成第{}批处理，本批处理{}条，累计处理{}条", batchCount, processed, totalProcessed);

            // 如果处理的数量少于批大小，说明没有更多数据了
            if (processed < batchSize) {
                break;
            }

            // 防止过度消耗系统资源，每批之间稍微暂停
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("处理过程被中断");
                break;
            }
        }

        log.info("所有未入库文献处理完成，共处理{}批，{}条数据", batchCount, totalProcessed);
    }

}
