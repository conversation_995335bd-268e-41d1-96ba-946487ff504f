package org.biosino.lf.pds.article;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 文献数据解析服务
 *
 * <AUTHOR>
 */
@EnableRetry
@EnableScheduling
@SpringBootApplication
@ComponentScan(basePackages = {"org.biosino.lf.pds"})
public class ArticleApplication {
    public static void main(String[] args) {
        SpringApplication.run(ArticleApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  文献数据解析服务启动成功   ლ(´ڡ`ლ)ﾞ  \n");
    }
}
